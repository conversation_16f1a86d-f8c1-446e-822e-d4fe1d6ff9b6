{"name": "tools", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "cross-env AWP_DEPLOY_ENV=dev vite --open", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "check:type": "tsc -b", "build:hello-world": "cross-env entry=pages/hello-world.html outDirName=dist vite build", "clear:build": "rm -rf build", "clear:deps": "rm -rf node_modules", "clear": "pnpm run --parallel '/^clear:(build|deps)/'"}, "dependencies": {}, "devDependencies": {"@lvyue/vite-config": "workspace:*", "autoprefixer": "^10.4.20", "postcss": "^8.4.49", "tailwindcss": "^3.4.17", "typescript-eslint": "^8.18.2"}}