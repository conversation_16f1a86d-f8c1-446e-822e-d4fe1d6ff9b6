<link rel="icon" type="image/svg+xml" />
<!-- <link rel="shortcut icon" href="//peisong.meituan.com/static/img/logo.png"> -->
<meta name="viewport" content="width=device-width, initial-scale=1.0" />
<!-- owl 预采集模块 -->
<script>
  "use strict";!function(u,d){var t="owl",e="_Owl_",n="Owl",r="start",c="error",p="on"+c,f=u[p],h="addEventListener",l="attachEvent",v="isReady",b="dataSet";u[t]=u[t]||function(){try{u[t].q=u[t].q||[];var e=[].slice.call(arguments);e[0]===r?u[n]&&u[n][r]?u[n][r](e[1]):u[t].q.unshift(e):u[t].q.push(e)}catch(e){}},u[e]=u[e]||{preTasks:[],pageData:[],use:function(e,t){this[v]?u[n][e](t):this.preTasks.push({api:e,data:[t]})},run:function(t){if(!(t=this).runned){t.runned=!0,t[b]=[],u[p]=function(){t[v]||t[b].push({type:"jsError",data:arguments}),f&&f.apply(u,arguments)},u[h]&&u[h]("unhandledrejection",function(e){t[v]||t[b].push({type:"jsError",data:[e]})});var e=function(e){!t[v]&&e&&t[b].push({type:"resError",data:[e]})};u[h]?u[h](c,e,!0):u[l]&&u[l](p,e);var n="MutationObserver",r=u[n]||u["WebKit"+n]||u["Moz"+n],a=u.performance||u.WebKitPerformance,s="disableMutaObserver";if(r&&a&&a.now)try{var i=-1,o=u.navigator.userAgent;-1<o.indexOf("compatible")&&-1<o.indexOf("MSIE")?(new RegExp("MSIE (\\d+\\.\\d+);").test(o),i=parseFloat(RegExp.$1)):-1<o.indexOf("Trident")&&-1<o.indexOf("rv:11.0")&&(i=11),-1!==i&&i<=11?t[s]=!0:(t.observer=new r(function(e){t.pageData.push({mutations:e,startTime:a.now()})})).observe(d,{childList:!0,subtree:!0})}catch(e){}else t[s]=!0}}},u[e].runned||u[e].run()}(window,document);
</script>
<script crossorigin="anonymous" src="//www.dpfile.com/app/owl/static/owl_1.10.1.js"></script>
<!-- owl start -->
<script>
    window.owl('start', {
        project: 'com.sankuai.banmafe.eprinter.platformono',
        devMode: __ENV !== 'production', // 请根据环境来赋值，线上环境设为false，线下环境设为true
        webVersion: ____BuildVersion,
        autoCatch: {
            fetch: true
        },
        enableLogTrace: true,
        resource: {
            enableStatusCheck: true,
            sampleApi: 1  //采样率
        },
        page: {
            sample: 1,
            fstPerfAnalysis: true,
            logSlowView: true,
        },
        error: {
            formatUnhandledRejection: true
        }, 
        ignoreList: {//错误忽略
            js: ['ResizeObserver loop limit exceeded'],
            ajax: ['https?://dreport.meituan.net', 'https?://api.neixin.cn/dxlvs/open/v2/lgservers/json']
        }
        // onErrorPush: function (instance) {
        //     return instance.updateTags({
        //         release: '<%= VUE_APP_RELEASE %>'
        //     });
        // },
        // onBatchPush: function (instance) {
        //       if (instance && instance.firstCategory === 'ajaxError' && instance.logContent === 'from: xhr abort') {
        //           return undefined;
        //       } else {
        //           return instance;
        //       }
        // }
    });
</script>
<!--文枢下载-->
<script src="//s3plus.meituan.net/v1/mss_77a90a9e62374a14a95a15d8334bd502/sec-db-download/lib/sec_db_download.js"></script>
<!--引入h5guard-->
<script src="https://appsec-mobile.meituan.com/h5guard/H5guard.js"></script>
<script>
        try {
          H5guard.init({
            xhrHook: true,//true为开启 false为关闭
            fetchHook: true,//true为开启 false为关闭
            domains: ["localhost","giraffe.banma.test.sankuai.com","giraffe.banma.st.sankuai.com","giraffe.sankuai.com"],
            //域名白名单配合xhrHook和fetchHook使用，开启之后只会对白名单内的域名请求自动加签，避免出现非业务域名跨域问题
          });
        } catch (e) {
          var msg = (typeof e === 'string' || e instanceof Error) ? e : JSON.stringify(e);
          throw new Error('Error occurred in H5guard init: ' + msg);
        }
</script>
<!-- 引入layout-comp header  footer  body-->
<script type="module" src="http://localhost:6783/layout/src/pages/header/index.ts"></script>
<script type="module" src="http://localhost:6783/layout/src/pages/menu/index.ts"></script>
<!--end-->
  <style type="text/css">
    html {
      background-color: #f7f8fc !important;
    }
    html, body {
    font-size: 14px !important;
    }
    .bm-layout {
      display: flex;
      flex-direction: column;
      flex: 1;
      flex-basis: auto;
      box-sizing: border-box;
      min-width: 0;
      background-color: #f7f8fc;
    }
    .bm-layout__header {
      position: fixed;
      left: 0;
      top: 0;
      right: 0;
      display: flex;
      flex-direction: row;
      height: 56px;
      z-index: 1999;
      box-shadow: 0 1px 2px #00152914;
    }
    .bm-layout__sider {
      transition: ease-out 0.4s;
      position: fixed;
      width: 220px;
      top: 56px;
      left: 0;
      bottom: 0;
      z-index: 999;
    }
    .bm-layout__content {
      background: rgb(246, 249, 248);
      transition: ease-out 0.2s;
      margin: 56px 0 0 220px;
  }
  .ant-menu-title-content {
    a:hover {
      text-decoration: none;
    }
  }
    </style>

</head>