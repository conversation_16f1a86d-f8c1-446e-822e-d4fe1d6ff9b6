import { defineConfig, getViteAliasPresets } from '@lvyue/vite-config';
const baseAlias = getViteAliasPresets(import.meta.dirname);

export default defineConfig({
  framework: 'react',
  projectRootDirPath: import.meta.dirname,
  alias: baseA<PERSON><PERSON>,
  // host: 'local.page.banma.test.sankuai.com',
  // port: 5000,
  proxy: {
    '/api': {
      target: 'https://page.banma.test.sankuai.com',
      changeOrigin: true
    }
  },
});