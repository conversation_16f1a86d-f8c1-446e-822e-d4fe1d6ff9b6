/**
  * axios http
  * created by yureturn on 17/9/20
  */
import axios from 'axios';

// axios 配置
axios.defaults.timeout = 5000;
// @ts-ignore
axios.defaults.headers = {
  'X-Requested-with': 'XMLHttpRequest',
  post: {'Content-Type': 'application/x-www-form-urlencoded' }
};
axios.defaults.validateStatus = (status) => status >= 200 && status < 500;
// http response 拦截器
axios.interceptors.response.use(response => {
  const { status, headers, data } = response;
  console.log(data);
  const { code } = data || {}; 
  if (status === 401 || code === 30002) {
    console.log('未登录');
    // 未登录
    const url = `https://ssosv.it.test.sankuai.com/sson/login?t=${+new Date()}&redirect_uri=${encodeURIComponent('https://qingyun-dos.banma.test.sankuai.com/api/sso/callback?original-url=${location.href}&client_id=5f8847e800')}`;
    //window.location.href = url;
    //return;
  }

  return data;
}, error => Promise.reject(error));

// get请求
const rawRequestGet = (url: string, params: any) => axios({
  url,
  method: "get",
  params
});
// query提交
const rawRequestPostAsQuery = (url: string, params: any) => axios({
  url,
  method: "post",
  params
});

// json提交
const rawRequestPostAsJson = (url: string, data: any) => axios({
  url,
  method: "post",
  headers: {
    'Content-Type': 'application/json'
  },
  data
});

// 表单提交
const rawRequestPostAsForm = (url: string, data: any) => axios({
  url,
  method: "post",
  headers: {
    'Content-Type': 'application/x-www-form-urlencoded'
  },
  data: new URLSearchParams(data).
    toString()
});


const http = {
  rawRequestGet,
  rawRequestPostAsQuery,
  rawRequestPostAsJson,
  rawRequestPostAsForm
};


export default http;