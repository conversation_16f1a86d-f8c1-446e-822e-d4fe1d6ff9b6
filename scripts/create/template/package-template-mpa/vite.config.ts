import { defineConfig, getViteAliasPresets } from '@lvyue/vite-config';
const baseAlias = getViteAliasPresets(import.meta.dirname);

export default defineConfig({
  projectRootDirPath: import.meta.dirname,
  alias: base<PERSON><PERSON><PERSON>,
  host: 'local.page.banma.test.sankuai.com',
  port: 80,
  mockFlag: false,
  proxy: {
    '/api': {
      target: 'https://page.banma.test.sankuai.com',
      changeOrigin: true
    }
  },
});