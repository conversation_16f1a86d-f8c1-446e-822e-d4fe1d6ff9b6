import fs from 'fs';
import path from 'path';
// vite 插件
const getBuildVersionScript = () => `<script>window.____BuildVersion='${ process.env.__BuildVersion || ''}'; window.__ENV='${ process.env.AWP_DEPLOY_ENV || ''}';</script>`;


let headerScript = '';
let footerScript = '';
let bodyScript = '';

//读取文件内容
const readHtmlFromDevLionConf = (blockName) => {
  try {
    const fileName = `${blockName}.html`;
    return fs.readFileSync(path.join(process.cwd(), `../../scripts/base-html/lion-conf/dev/${fileName}`));
  } catch (error) {
    console.error(`读取文件失败: ${error}`);
    return '';
  }
};

// 读取文件内容入口
const readFileMain = () => {
  headerScript = readHtmlFromDevLionConf('header');
  footerScript = readHtmlFromDevLionConf('footer');
  bodyScript = readHtmlFromDevLionConf('body');
};

readFileMain();

export default (appKey) => ({
  name: 'pre-html',
  transformIndexHtml: (html) => {
    if (!process.env.CI) {
    // 本地开发
      return html.replace('${context.wslion.htmlHeaderScript!""}', headerScript).
        replace('${context.wslion.htmlFooterScript!""}', footerScript).
        replace('${context.wslion.htmlBodyScript!""}', bodyScript).
        replace('<webstaticVersion-script></webstaticVersion-script>', getBuildVersionScript());
    }
    return html.replace('<webstaticVersion-script></webstaticVersion-script>', getBuildVersionScript());
  }
});