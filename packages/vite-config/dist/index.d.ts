import { UserConfig } from 'vite';
/**
 * 提供vite配置中的alias预设，你必须通过 projectRootDirPath 告知项目根目录的绝对路径，之后本预设会完成如下的alias配置：
 *
 * - `@src/` 映射为 `<project_root_dir>/src/`
 * - `@utils/` 映射为 `<project_root_dir>/../../packages/utils/`
 *
 * # example
 * ```ts
 * // /Users/<USER>/Project/project-a/apps/app-a/vite.config.ts
 * import { useViteAliasPresets, defineConfig } from '@lvyue/vite-config';
 * import { join } from "node:path";
 *
 * // 项目根目录的绝对路径
 * const rootDir = import.meta.dirname;
 *
 * const alias = useViteAliasPresets(rootDir);
 *
 * // 只想要预设中的`@src/`映射
 * const rootAlias = alias[0]
 *
 * // 只想要预设中的`@utils/`映射
 * const pagesAlias = alias[1]
 *
 * // 加入自定义的映射
 * const customAlias = {
 *   find: /^@custom/,
 *   replacement: join(rootDir, "src/custom")
 * }
 *
 * export default defineConfig({
 *  projectRootDirPath: rootDir,
 *  alias: [componentsAlias, customAlias]
 * })
 * ```
 *
 * @param projectRootDirPath
 * @returns
 */
export declare function getViteAliasPresets(projectRootDirPath: string): import("vite").AliasOptions | undefined;
/**
 * 设置vite config.
 *
 * 有几个环境变量非常重要：
 *
 * 1. printConfig, 设置为true, 则在启动或者构建项目的时候，会输出内置的vite配置到终端
 * 2. emptyOutDir, 设置为true，则在每次构建项目之前，清空上一次构建的产物
 * 3. entry, 设置构建入口路径，这个路径是相对于项目根目录而言的，比如"pages/list/index.html",
 * 不要设置为"./pages/list/index.html", 如果一次构建多个页面，这些路径使用英文逗号或者中文逗号连接
 * 4. outDirName, 设置构建产物输出的目录，如果设置为hello，则构建的产物将出现在 build/hello 目录下
 * 5. base, 设置静态资源相对路径参考的基础路径，不设置的话，自动从环境变量PUBLIC_PATH读取，这个变量
 *    是TALOS流水线中WebStatic Build插件自动设置的
 * 6. usePublicPath, 设置为true，则vite config中的base和outDirName强制从中解析，优先级比环境变量
 *    base和outDirName要高
 *
 *
 * 本地构建必须指定的环境变量：outDirName, entry
 *
 * TALOS构建必须指定的环境变量：
 * 1. 项目标识绑定到PUBLIC_PATH的情形：usePublicPath, entry
 * 2. 项目标识没有绑定到PUBLIC_PATH的情形：outDirName(设置为项目标识), entry
 *
 * # example
 * ```ts
 * // /Users/<USER>/Project/project-a/vite.config.ts
 *
 *
 * // 假设我们拥有这样的文件：
 * // /Users/<USER>/Project/project-a/src/main.tsx
 * // /Users/<USER>/Project/project-a/pages/list/index.html
 * // /Users/<USER>/Project/project-a/pages/detail/index.html
 *
 * // 我们想一次编译上边两个html，就可以执行：
 * // entry=pages/list/index.html,pages/detail/index.html vite build
 * ```
 *
 * @param params
 * @returns
 */
export declare function defineConfig(params: {
    /** 项目使用什么框架，默认用 all */
    framework?: 'react' | 'vue' | 'all';
    /** 项目根目录的绝对路径 */
    projectRootDirPath: string;
    /**
     * 设置路径别名, 更多信息见{@link useViteAliasPresets }
     *
     *
     * */
    alias?: Required<UserConfig>['resolve']['alias'];
    /**
     * 设置前端服务器代理
     *
     * # example
     * ```ts
     * // /Users/<USER>/Project/project-a/apps/app-a/vite.config.ts
     * import { defineConfig } from 'vite-config'
     *
     * export default defineConfig({
     *   projectRootDirPath: import.meta.dirname,
     *   port: 8007,
     *   proxy: {
     *     // http://localhost:8007/api/list/120 将被转化为
     *     // http://hello.api.com:6007/api/list/120
     *     '/api': 'http://hello.api.com:6007'
     *   }
     * })
     *
     * ```
     * */
    proxy?: Required<UserConfig>['server']['proxy'];
    /** 设置前端服务器的端口号，不设置的话，内部会自动设置 */
    port?: number;
    /**
     * 设置前端服务器的域名，如果不设置的话，就是localhost。
     *
     * 如果设置的话，不要忘记在 /etc/hosts 中做好域名映射设置。
     */
    host?: string;
    /** 设置是否开启mock */
    mockFlag?: boolean;
    /** 设置是否加载菜单 - 目前只适用于烽火台 */
    loadMenu?: boolean;
}): import("vite").UserConfigFnPromise;
