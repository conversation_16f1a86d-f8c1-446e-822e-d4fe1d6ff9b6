import{defineConfig as D}from"vite";import{join as m,relative as B}from"node:path";import{cwd as y,env as p}from"node:process";import{existsSync as w,statSync as A}from"node:fs";import Y from"@vitejs/plugin-react";import F from"@vitejs/plugin-vue";import{promises as V}from"node:dns";import{glob as x}from"glob";import U from"open";import{set as h}from"ansi-color";import g from"fs";import $ from"path";import{viteMockServe as H}from"vite-plugin-mock";import j from"dayjs";async function I(){const e=y();return w(m(e,"pages"))?await x(m(e,"pages","**/*.html"),{absolute:!1,cwd:y()}).then(t=>{const s=t.slice(0,10);return s.sort(),s},()=>[]):[]}async function M(e){const t=e.httpServer.address(),{port:s}=t,o=e.config.server.https?"https":"http",{base:n}=e.config;let r=e.config.server.host??"localhost";typeof r=="boolean"&&(r="localhost");try{await V.lookup(r)}catch{console.log(h(`Host ${r} is not found`,"ref")),console.log(`instead, we use ${h("localhost","green")}`),r="localhost"}return{urlPrefix:`${o}://${r}:${s}${n}`,protocol:o,host:r,port:s,base:n}}async function O(e){let{urlPrefix:t,protocol:s,port:o,host:n}=await M(e);t.endsWith("/")&&(t=t.slice(0,-1));const r=(await I()).map(f=>`${t}/${f}`);let a=[],i=[];return["127.0.0.1","0.0.0.0","localhost"].includes(n)?a=r:i=r,{local:a,network:i,protocol:s,port:o,host:n}}async function b(e){try{await U(e)}finally{}}function T(e){return["localhost","127.0.0.1","0.0.0.0"].includes(e)}let k=!1;var L=()=>({name:"custom-dev-server-open",configureServer(e){const t=e.config.server.open;return e.config.server.open=!1,()=>{const{restart:s}=e;e.restart=async()=>(k=!0,await s());const o=e.printUrls;e.printUrls=async()=>{const{host:n,protocol:r,port:a,local:i,network:f}=await O(e),l=`${r}://localhost:${a}/_pages`,u=T(n)?"":`${r}://${n}:${a}/_pages`;if(e.resolvedUrls&&(e.resolvedUrls.local=[l],e.resolvedUrls.network=u===""?[]:[u]),o(),!k&&t){if(t===!0){b(u!==""?u:l);return}if(typeof t=="string"){const c=i.find(d=>d.includes(t))||f.find(d=>d.includes(t));if(!c){console.log(h(`${t} is not in the above list`,"red"));const d=u!==""?u:l;console.log(h(`instead, open ${d}`,"green")),b(d);return}b(c)}}}}}});const P=()=>`<script>window.____BuildVersion='${process.env.__BuildVersion||""}'; window.__ENV='${process.env.AWP_DEPLOY_ENV||""}';<\/script>`;let N="",C="",_="";const v=e=>{try{const t=`${e}.html`;return g.readFileSync($.join(process.cwd(),`../../scripts/base-html/lion-conf/dev/${t}`))}catch(t){return console.error(`\u8BFB\u53D6\u6587\u4EF6\u5931\u8D25: ${t}`),""}},z=()=>{N=v("header"),C=v("footer"),_=v("body")};z();var S=e=>({name:"pre-html",transformIndexHtml:t=>process.env.CI?t.replace("<webstaticVersion-script></webstaticVersion-script>",P()):t.replace('${context.wslion.htmlHeaderScript!""}',N).replace('${context.wslion.htmlFooterScript!""}',C).replace('${context.wslion.htmlBodyScript!""}',_).replace("<webstaticVersion-script></webstaticVersion-script>",P())});function J(){return`
<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8" />
  <title>Pages List</title>
  <style>
    :root {
      --primary-color: #646cff;
      --hover-color: #747bff;
      --bg-color: #ffffff;
      --text-color: #213547;
      --border-color: #eaeaea;
    }

    @media (prefers-color-scheme: dark) {
      :root {
        --bg-color: #1a1a1a;
        --text-color: rgba(255, 255, 255, 0.87);
        --border-color: #333;
      }
    }

    body { 
      font-family: -apple-system, BlinkMacSystemFont, sans-serif;
      max-width: 1000px;
      margin: 0 auto;
      padding: 20px;
      background-color: var(--bg-color);
      color: var(--text-color);
    }

    h1 {
      text-align: center;
      font-size: 2rem;
      margin-bottom: 2rem;
      background: linear-gradient(120deg, var(--primary-color), #bc35d9);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    .total-count {
      text-align: center;
      margin-bottom: 1.5rem;
      color: var(--text-color);
      opacity: 0.8;
    }

    .page-row {
      margin-bottom: 12px;
    }

    .page-link {
      display: block;
      padding: 16px;
      border: 1px solid var(--border-color);
      border-radius: 8px;
      text-decoration: none;
      color: var(--text-color);
      transition: all 0.2s ease;
      position: relative;
      overflow: hidden;
      background: var(--bg-color);
      cursor: pointer;
    }

    .page-link:hover {
      transform: translateY(-2px);
      border-color: var(--primary-color);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .page-link::before {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      width: 4px;
      height: 100%;
      background: var(--primary-color);
      transform: scaleY(0);
      transition: transform 0.2s ease;
    }

    .page-link:hover::before {
      transform: scaleY(1);
    }

    .page-link:active {
      transform: translateY(0);
      opacity: 0.8;
    }

    .file-name {
      font-weight: 500;
      color: var(--primary-color);
    }

    .path-separator {
      margin: 0 8px;
      color: var(--text-color);
      opacity: 0.5;
    }

    .full-path {
      opacity: 0.8;
    }

    @media (max-width: 640px) {
      body {
        padding: 16px;
      }

      h1 {
        font-size: 1.5rem;
      }

      .page-link {
        padding: 12px;
      }
    }
  </style>
</head>
<body>
  <h1>\u5B50\u9879\u76EEapp/{{subAppName}}\u9875\u9762\u5217\u8868</h1>
  <div id="app">
    <div class="total-count"></div>
  </div>
  <script>
    const pages = {{pages}};
    const app = document.getElementById('app');
    const totalCount = document.querySelector('.total-count');

    // \u663E\u793A\u603B\u6570
    totalCount.textContent = \`\u5171 \${pages.length} \u4E2A\u9875\u9762\`;

    // \u6392\u5E8F\u9875\u9762
    pages.sort((a, b) => a.fileName.localeCompare(b.fileName));

    pages.forEach(page => { 
      const row = document.createElement('div');
      row.className = 'page-row';
      
      const link = document.createElement('a');
      link.href = page.fullPath;
      link.target = '_blank';
      link.className = 'page-link';
      
      const fileName = document.createElement('span');
      fileName.className = 'file-name';
      fileName.textContent = page.fileName;
      
      const separator = document.createElement('span');
      separator.className = 'path-separator';
      separator.textContent = '>>>>>';
      
      const fullPath = document.createElement('span');
      fullPath.className = 'full-path';
      fullPath.textContent = page.fullPath;
      
      link.appendChild(fileName);
      link.appendChild(separator);
      link.appendChild(fullPath);
      row.appendChild(link);
      app.appendChild(row);
    });
  <\/script>
</body>
</html>`}function W(e,t,s,o){o=`/${o}`;const n=[];function r(a){const i=g.readdirSync(`${process.cwd()}/${a}`);for(const f of i){const l=$.join(a,f),u=g.statSync(l);if(!t.some(c=>l.includes(c))){if(u.isDirectory())r(l);else if(/\.(html)$/.test(f)){const c=l.replace(e,"").replace(/\.(html)$/,"");n.push({fileName:c.replace(/^\//,""),fullPath:`${s+o}/${l}`})}}}}return r(e),n}function q(e={}){let t=!1;const{pagesDir:s="pages",exclude:o=["components","utils","styles"],template:n,pathPrefix:r="/monofe",pathSubAppName:a="personal"}=e;return{name:"vite-plugin-pages-list",configureServer(i){t||(t=!0,i.middlewares.use("/_pages",async(f,l)=>{const u=W(s,o,r,a);let c=n?g.readFileSync(n,"utf-8"):J();c=c.replace("{{pages}}",JSON.stringify(u)),c=c.replace("{{subAppName}}",a),l.setHeader("Content-Type","text/html"),l.end(c)}))}}}const E="";process.env.__BuildVersion=`buildVer-${j().format("YYYY-MM-DD+HH:mm:ss")}`;async function R(e){let t=Promise.resolve([]);const s=[],o=p.entry.split(/,|，/).map(n=>n.trim()).filter(n=>n.length>0).map(n=>{const r=m(e,n);if(!w(r))throw Error(`cannot find ${h(r,"red")}`);return A(r).isDirectory()?x(m(r,"**/*.html")).then(a=>{t=t.then(i=>[...i,...a])}).catch(a=>{}):(s.push(r),Promise.resolve())});return await Promise.allSettled(o),t=t.then(n=>[...s,...n]),await t.then(n=>{const r=new Set(n);return Array.from(r).map(a=>B(e,a)).map(a=>({[a.split(".").slice(0,-1).join("/")]:m(e,a)})).reduce((a,i)=>(a={...a,...i},a),{})})}function G(e){const t=p.PUBLIC_PATH;if(t===void 0)throw Error("cannot find PUBLIC_PATH environment variable");const[s]=t.split("/").slice(-1),o=t.split("/").slice(0,-1).join("/");e.base=o,e.build.outDir=`build/${s}`}function K(e){return[{find:/^@src\//,replacement:m(e,"src/")},{find:/^@utils\//,replacement:m(e,"../../packages/utils/")}]}function Q(e){return D(async({command:t})=>{const s=[Y(),F()];e.framework==="react"&&s.pop(),e.framework==="vue"&&s.shift();const o={plugins:[...s],resolve:{alias:e.alias||[]},build:{cssMinify:"esbuild",minify:"esbuild"},server:{}};if(e.port!==void 0&&(o.server.port=e.port),e.host!==void 0&&(o.server.host=e.host),e.proxy!==void 0&&(o.server.proxy=e.proxy),t==="serve"&&(o.base=`${E}/${p.npm_package_name}/`),process.env.CI?o.plugins.push(S()):(o.plugins.push(S({loadMenu:e.loadMenu})),o.plugins.push(H({mockPath:"mock",enable:e.mockFlag||!1})),o.plugins.push(q({pagesDir:"pages",pathPrefix:E,pathSubAppName:p.npm_package_name})),o.plugins.push(L())),t==="build"){if(p.usePublicPath!==void 0)G(o);else{const r=p.outDirName;if(r===void 0)throw Error("You have to set outDirName environment variable!");o.build.outDir=`build/${r}`;const a=p.PUBLIC_PATH,i=p.base;o.base=a||i||"/",o.base=m(o.base,r),a!==void 0&&(o.base=`/${o.base}`)}if(p.entry===void 0)throw Error("You have to set entry environment variable!");const n=await R(e.projectRootDirPath);o.build.rollupOptions={input:n,output:{entryFileNames:"[name].[hash].entry.js",chunkFileNames:"[name].[hash].chunk.js"}},o.build.emptyOutDir=p.emptyOutDir==="true"}return p.printConfig==="true"&&console.log(`internal vite config: 
`,JSON.stringify(o)),o})}export{Q as defineConfig,K as getViteAliasPresets};
