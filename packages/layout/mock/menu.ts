// 菜单列表接口mock
import { MockMethod } from 'vite-plugin-mock';
export default [
  {
    url: '/api/r/uac/menus',
    method: 'get',
    response: () => (
      {
        "msg": "success",
        "code": 0,
        "data": {
          "mock": {
            "isMock": false
          },
          "menus": [
            {
              "id": 15306,
              "parenId": 15302,
              "title": "首页",
              "url": "/",
              "code": "home",
              "sort": 1,
              "type": 1,
              "createTime": 0,
              "links": [
                "/"
              ],
              "redPoint": 0,
              "icon": null,
              "menus": [],
              "attributes": null
            },
            {
              "id": 15303,
              "parenId": 15302,
              "title": "应用管理",
              "url": null,
              "code": "app_manage",
              "sort": 2,
              "type": 1,
              "createTime": 0,
              "links": [],
              "redPoint": 0,
              "icon": null,
              "menus": [
                {
                  "id": 15308,
                  "parenId": 15303,
                  "title": "申请接入",
                  "url": "/app/apply/add",
                  "code": "create_apply",
                  "sort": 1,
                  "type": 1,
                  "createTime": 0,
                  "links": [
                    "/app/apply/add"
                  ],
                  "redPoint": 0,
                  "icon": null,
                  "menus": [],
                  "attributes": null
                },
                {
                  "id": 15309,
                  "parenId": 15303,
                  "title": "我的申请",
                  "url": "/app/apply/list",
                  "code": "apply_list",
                  "sort": 2,
                  "type": 1,
                  "createTime": 0,
                  "links": [
                    "/app/apply/list"
                  ],
                  "redPoint": 0,
                  "icon": null,
                  "menus": [],
                  "attributes": null
                },
                {
                  "id": 15310,
                  "parenId": 15303,
                  "title": "我的应用",
                  "url": "/app/list",
                  "code": "app_list",
                  "sort": 3,
                  "type": 1,
                  "createTime": 0,
                  "links": [
                    "/app/list"
                  ],
                  "redPoint": 0,
                  "icon": null,
                  "menus": [],
                  "attributes": null
                }
              ],
              "attributes": null
            },
            {
              "id": 15305,
              "parenId": 15302,
              "title": "基础设置",
              "url": null,
              "code": "basis_manage",
              "sort": 3,
              "type": 1,
              "createTime": 0,
              "links": [],
              "redPoint": 0,
              "icon": null,
              "menus": [
                {
                  "id": 15312,
                  "parenId": 15305,
                  "title": "回调地址设置",
                  "url": "/basic/callback",
                  "code": "callback_edit",
                  "sort": 1,
                  "type": 1,
                  "createTime": 0,
                  "links": [
                    "/basic/callback"
                  ],
                  "redPoint": 0,
                  "icon": null,
                  "menus": [],
                  "attributes": null
                }
              ],
              "attributes": null
            },
            {
              "id": 28544,
              "parenId": 15302,
              "title": "设备管理",
              "url": null,
              "code": "device_manage",
              "sort": 4,
              "type": 1,
              "createTime": 0,
              "links": [],
              "redPoint": 0,
              "icon": null,
              "menus": [
                {
                  "id": 29312,
                  "parenId": 28544,
                  "title": "设备查询",
                  "url": "/fe/template/giraffe/devicemanage-device_list",
                  "code": "device_list",
                  "sort": 1,
                  "type": 1,
                  "createTime": 0,
                  "links": [
                    "/fe/template/giraffe/devicemanage-device_list"
                  ],
                  "redPoint": 0,
                  "icon": null,
                  "menus": [],
                  "attributes": null
                },
                {
                  "id": 28545,
                  "parenId": 28544,
                  "title": "升级任务",
                  "url": "/fe/template/giraffe/devicemanage-ota_task_list",
                  "code": "ota_task_list",
                  "sort": 3,
                  "type": 1,
                  "createTime": 0,
                  "links": [
                    "/fe/template/giraffe/devicemanage-ota_task_list"
                  ],
                  "redPoint": 0,
                  "icon": null,
                  "menus": [],
                  "attributes": null
                },
                {
                  "id": 83307,
                  "parenId": 28544,
                  "title": "设备日志查询",
                  "url": "/fe/template/giraffe/devicemanage-device-report-log",
                  "code": "devicemanage-device-report-log",
                  "sort": 8,
                  "type": 1,
                  "createTime": 0,
                  "links": [
                    "/fe/template/giraffe/devicemanage-device-report-log"
                  ],
                  "redPoint": 0,
                  "icon": null,
                  "menus": [],
                  "attributes": null
                }
              ],
              "attributes": null
            },
            {
              "id": 10018171,
              "parenId": 15302,
              "title": "任务管理",
              "url": null,
              "code": "task-manage",
              "sort": 5,
              "type": 1,
              "createTime": 0,
              "links": [],
              "redPoint": 0,
              "icon": null,
              "menus": [
                {
                  "id": 28546,
                  "parenId": 10018171,
                  "title": "打印任务管理",
                  "url": "/fe/template/giraffe/taskManage-print_task_list",
                  "code": "print_task_list",
                  "sort": 1,
                  "type": 1,
                  "createTime": 0,
                  "links": [
                    "/fe/template/giraffe/taskManage-print_task_list"
                  ],
                  "redPoint": 0,
                  "icon": null,
                  "menus": [],
                  "attributes": null
                },
                {
                  "id": 10018172,
                  "parenId": 10018171,
                  "title": "出餐任务管理",
                  "url": "/fe/template/giraffe/taskmanage-ccb_task_list",
                  "code": "ccb_task_list",
                  "sort": 2,
                  "type": 1,
                  "createTime": 0,
                  "links": [
                    "/fe/template/giraffe/taskmanage-ccb_task_list"
                  ],
                  "redPoint": 0,
                  "icon": null,
                  "menus": [],
                  "attributes": null
                }
              ],
              "attributes": null
            },
            {
              "id": 17767,
              "parenId": 15302,
              "title": "小票模板",
              "url": null,
              "code": "template",
              "sort": 6,
              "type": 1,
              "createTime": 0,
              "links": [],
              "redPoint": 0,
              "icon": null,
              "menus": [
                {
                  "id": 18550,
                  "parenId": 17767,
                  "title": "模板列表",
                  "url": " /dashboard/device#/template/list/preview",
                  "code": "templateList",
                  "sort": 1,
                  "type": 1,
                  "createTime": 0,
                  "links": [
                    " /dashboard/device#/template/list/preview"
                  ],
                  "redPoint": 0,
                  "icon": null,
                  "menus": [],
                  "attributes": null
                },
                {
                  "id": 18551,
                  "parenId": 17767,
                  "title": "审核列表",
                  "url": " /dashboard/device#/template/audit/list",
                  "code": "templateAudit",
                  "sort": 2,
                  "type": 1,
                  "createTime": 0,
                  "links": [
                    " /dashboard/device#/template/audit/list"
                  ],
                  "redPoint": 0,
                  "icon": null,
                  "menus": [],
                  "attributes": null
                }
              ],
              "attributes": null
            },
            {
              "id": 16060,
              "parenId": 15302,
              "title": "业务监控",
              "url": null,
              "code": "monitorDashboard",
              "sort": 7,
              "type": 1,
              "createTime": 0,
              "links": [],
              "redPoint": 0,
              "icon": null,
              "menus": [
                {
                  "id": 27638,
                  "parenId": 16060,
                  "title": "在线设备数",
                  "url": "/fe/template/giraffe/monitor-device_online_count",
                  "code": "device_online_count",
                  "sort": 1,
                  "type": 1,
                  "createTime": 0,
                  "links": [
                    "/fe/template/giraffe/monitor-device_online_count"
                  ],
                  "redPoint": 0,
                  "icon": null,
                  "menus": [],
                  "attributes": null
                },
                {
                  "id": 16061,
                  "parenId": 16060,
                  "title": "在线与信号",
                  "url": " /dashboard/device",
                  "code": "onlineAndSignal",
                  "sort": 2,
                  "type": 1,
                  "createTime": 0,
                  "links": [
                    " /dashboard/device"
                  ],
                  "redPoint": 0,
                  "icon": null,
                  "menus": [],
                  "attributes": null
                }
              ],
              "attributes": null
            },
            {
              "id": 15307,
              "parenId": 15302,
              "title": "常用工具",
              "url": null,
              "code": "common_tools",
              "sort": 9,
              "type": 1,
              "createTime": 0,
              "links": [],
              "redPoint": 0,
              "icon": null,
              "menus": [
                {
                  "id": 19588,
                  "parenId": 15307,
                  "title": "任务轨迹",
                  "url": "/tools/task/trace",
                  "code": "task_trace",
                  "sort": 2,
                  "type": 1,
                  "createTime": 0,
                  "links": [
                    "/tools/task/trace"
                  ],
                  "redPoint": 0,
                  "icon": null,
                  "menus": [],
                  "attributes": null
                }
              ],
              "attributes": null
            }
          ],
          "user": {
            "code": "03143242",
            "isVerified": false,
            "roles": null,
            "verified": false,
            "withoutReport": true,
            "login": "zhangjiapeng04",
            "passport": "<EMAIL>",
            "name": "张加鹏",
            "tenantId": "1",
            "verifyExpireTime": 0,
            "id": 2889370,
            "email": "<EMAIL>",
            "verifyType": ""
          },
          "imitate": {
            "isImitate": false
          }
        }
      }
    ),
  }
] as MockMethod[];
