<template>
  <mtd-sidebar 
    hidden-header
    :data="menuList" 
    v-model="activeMenu"
    :default-active-key="activeMenu"
    :default-expand-keys="defaultExpandKeys"
    @menu-item-click="handleMenuItemClick"
    @menu-expand-change="handleMenuExpandChange"
    style="height: 100%;"
  />
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'

interface MenuItem {
  id: string
  title: string
  icon?: string
  route?: string
  children?: MenuItem[]
  tooltip?: string
}

// 定义props
const props = defineProps({
  menus: {
    type: Array as () => MenuItem[],
    default: () => []
  }
})

// 定义事件
const emit = defineEmits(['select'])

// 如果没有传入菜单数据，使用默认菜单
const menuList = computed(() => {
  if (props.menus.length > 0) {
    return props.menus
  }

  return [
    {
      id: '1',
      title: '数据注册',
      icon: 'database-o',
      tooltip: '数据注册管理',
      children: [
        {
          id: '1-1',
          title: '数据源注册',
          route: '/tools/pages/datasource.html',
          tooltip: '注册和管理数据源'
        },
        {
          id: '1-2', 
          title: 'Tool生产',
          route: '/tools/pages/tool.html',
          tooltip: '生产和管理工具'
        }
      ]
    },
    {
      id: '2',
      title: 'MCP市场',
      icon: 'laser-pen',
      tooltip: 'MCP服务市场',
      children: [
        {
          id: '2-1',
          title: 'MCP Server',
          route: '/tools/pages/mcp-server.html',
          tooltip: 'MCP服务器管理'
        }
      ]
    }
  ] as MenuItem[]
})

// 根据当前页面URL动态计算active菜单项
const activeMenu = computed(() => {
  const currentPath = window.location.pathname
  
  // 遍历菜单数据，找到匹配当前路径的菜单项
  const findActiveMenu = (items: MenuItem[]): string => {
    for (const item of items) {
      if (item.route && currentPath.includes(item.route.replace('.html', ''))) {
        return item.id
      }
      if (item.children) {
        for (const child of item.children) {
          if (child.route && currentPath.includes(child.route.replace('.html', ''))) {
            return child.id
          }
        }
      }
    }
    return '1-1' // 默认值
  }
  
  return findActiveMenu(menuList.value)
})

// 从localStorage获取菜单展开状态，如果没有则使用默认值
const getStoredExpandKeys = (): string[] => {
  try {
    const stored = localStorage.getItem('menu-expand-keys')
    return stored ? JSON.parse(stored) : []
  } catch {
    return []
  }
}

// 默认展开的菜单项，使用持久化的状态
const defaultExpandKeys = ref<string[]>(getStoredExpandKeys())

// 保存菜单展开状态到localStorage
const saveExpandKeys = (keys: string[]) => {
  try {
    localStorage.setItem('menu-expand-keys', JSON.stringify(keys))
    defaultExpandKeys.value = keys
  } catch {
    // localStorage不可用时忽略错误
  }
}

// 菜单选择处理
const handleMenuItemClick = ({ key, item }: { key: string, item: MenuItem }) => {
  emit('select', key)

  // 因为这是多页面应用，使用location.href进行页面跳转
  if (item.route) {
    window.location.href = item.route
  }
}

// 菜单展开状态变化处理
const handleMenuExpandChange = ({ expandKeys }: { expandKeys: string[] }) => {
  // 保存当前展开状态到localStorage
  saveExpandKeys(expandKeys)
}

// 组件挂载时确保菜单状态正确
onMounted(() => {
  // 如果localStorage中没有保存状态，设置默认展开状态
  if (!localStorage.getItem('menu-expand-keys')) {
    saveExpandKeys([])
  }
})
</script>