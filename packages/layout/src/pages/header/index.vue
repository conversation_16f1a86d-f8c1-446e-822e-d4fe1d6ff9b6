<template>
    <div class="layout-header">
      <div class="header-left">
        <h1 class="platform-title">图灵tools平台</h1>
      </div>
      <div class="header-right">
        <mtd-dropdown>
          <mtd-button type="text-primary" class="user-button">
            {{ userName }}
            <mtd-icon name="down"></mtd-icon>
          </mtd-button>
          <template #dropdown>
            <mtd-dropdown-menu>
              <mtd-dropdown-menu-item @click="handleLogout">退出登录</mtd-dropdown-menu-item>
            </mtd-dropdown-menu>
          </template>
        </mtd-dropdown>
      </div>
    </div>
  </template>
  
  <script setup lang="ts">
  import { ref, getCurrentInstance } from 'vue'
  import { useRouter } from 'vue-router'
  
  // 定义事件
  const emit = defineEmits(['logout'])
  
  const router = useRouter()
  const instance = getCurrentInstance()
  const userName = ref('xxx已登录')
  
  // 退出登录
  const handleLogout = () => {
    // 提示退出成功
    instance?.proxy?.$mtd.message({ message: '退出登录成功', type: 'success' })
    
    // 触发退出事件，让父组件可以处理额外的退出逻辑
    emit('logout')
    
    // 可以在这里添加跳转到登录页的逻辑
    // router.push('/login')
  }
  </script>
  
  <style lang="scss" scoped>
  .layout-header {
    height: 100%;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    background-color: #fff;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
    
    .header-left {
      display: flex;
      align-items: center;
      
      .platform-title {
        font-size: 18px;
        font-weight: 500;
        margin: 0;
        color: #166ff7;
      }
    }
    
    .header-right {
      display: flex;
      align-items: center;
      
      .user-button {
        display: flex;
        align-items: center;
        
        .mtd-icon {
          margin-left: 4px;
        }
      }
    }
  }
  </style> 