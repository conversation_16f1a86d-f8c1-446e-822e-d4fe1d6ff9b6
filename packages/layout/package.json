{"name": "layout", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "cross-env AWP_DEPLOY_ENV=dev vite --open", "build": "vite build", "dist": "cross-env outDirName=layout entry=pages/layout/header.html vite build", "lint": "eslint .", "preview": "vite preview", "check:type": "tsc -b", "build:order-index": "cross-env entry=pages/order/index.html outDirName=build vite build", "build:order-detail": "cross-env entry=pages/order/detail.html vite build", "build:index": "cross-env entry=pages/index/index.html vite build", "clear:build": "rm -rf build", "clear:deps": "rm -rf node_modules", "clear": "pnpm run --parallel '/^clear:(build|deps)/'"}, "dependencies": {"antd": "^5.22.7", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router": "^7.1.1", "@ss/mtd-vue3": "1.2.36"}, "devDependencies": {"@eslint/js": "^9.17.0", "@lvyue/vite-config": "workspace:*", "@types/react": "^18.3.0", "@types/react-dom": "^18.3.5", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.20", "eslint": "^9.17.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.16", "globals": "^15.14.0", "postcss": "^8.4.49", "sass-embedded": "^1.83.0", "tailwindcss": "^3.4.17", "typescript": "~5.6.2", "typescript-eslint": "^8.18.2", "vite": "^6.0.5"}}