/**
  * axios http 完成http接口请求和响应
  * 支持：图灵本地开发时sso验证和登录
  */
import axios from 'axios';
import { message } from 'antd';

const getSsoLoginUrl = () => {
  // 线下 SSO 登录页的 baseUrl, 线上 SSO 环境替换为 https://ssosv.sankuai.com/sson/login
  const ssoLoginBaseUrl = 'https://ssosv.it.test.sankuai.com/sson/login';
    
  // ⚠️请替换为项目自身使用的 clientId
  const clientId = 'waimai_hardware';
  const timestamp = Date.now();
  const originalUrl = window.location.href;
  const callbackUrl = `https://turing.banma.test.sankuai.com/api/carrier/sso/callback4550?original-url=${encodeURIComponent(originalUrl)}`;
  // eslint-disable-next-line @stylistic/max-len
  const ssoLoginUrl = `${ssoLoginBaseUrl}?client_id=${clientId}&t=${timestamp}&redirect_uri=${encodeURIComponent(callbackUrl)}`;
  return ssoLoginUrl;
};

// 获取 swimlaneKey
const getSwimlaneKey = () => {
  const {host} = window.location;
  let swimlaneKey = '';
  // 生产环境不添加 swimlaneKey
  // @ts-ignore
  if (window.__ENV === 'production') {
    return {};
  }
  if (host.includes('-sl-')) {
    // eslint-disable-next-line prefer-destructuring
    swimlaneKey = host.split('-sl-')[0];
    return {
      'Swimlane': swimlaneKey,
    };
  }
  return {};
};

// axios 配置
axios.defaults.timeout = 5000;
// @ts-ignore
axios.defaults.headers = {
  'X-Requested-with': 'XMLHttpRequest',
  'X-Custom-Header': '1010',
  // 添加 swimlaneKey
  ...getSwimlaneKey(),
  post: {'Content-Type': 'application/x-www-form-urlencoded' }
};
axios.defaults.validateStatus = (status) => status >= 200 && status < 500;
// http response 拦截器
axios.interceptors.response.use(response => {
  const { status, headers, data } = response;
  const { code, status: _status } = data || {};
  // @ts-expect-error
  if (window.__ENV === 'dev' || window.__ENV === '') {
    if (_status === 401 || code === 30002) {
      console.log('未登录');
      if (location.host.toLocaleLowerCase().
        indexOf('localhost') > -1) {
        message.error('sso无效，请通过本地host域名访问!');
        return;
      }
      // 未登录
      window.location.href = getSsoLoginUrl();
      return;
    }
  }
  return data;
}, error => Promise.reject(error));

// get请求
const rawRequestGet = (url: string, params: any) => axios({
  url,
  method: "get",
  params
});
// query提交
const rawRequestPostAsQuery = (url: string, params: any) => axios({
  url,
  method: "post",
  params
});

// json提交
const rawRequestPostAsJson = (url: string, data: any) => axios({
  url,
  method: "post",
  headers: {
    'Content-Type': 'application/json'
  },
  data
});

// 表单提交
const rawRequestPostAsForm = (url: string, data: any) => axios({
  url,
  method: "post",
  headers: {
    'Content-Type': 'application/x-www-form-urlencoded'
  },
  data: new URLSearchParams(data).
    toString()
});


const http = {
  rawRequestGet,
  rawRequestPostAsQuery,
  rawRequestPostAsJson,
  rawRequestPostAsForm
};


export default http;