lockfileVersion: '9.0'

settings:
  autoInstallPeers: true
  excludeLinksFromLockfile: false

importers:

  .:
    dependencies:
      '@ant-design/icons':
        specifier: 5.5.2
        version: 5.5.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@ss/mtd-vue3':
        specifier: 1.2.36
        version: 1.2.36(@babel/core@7.26.0)(vue-router@4.5.0(vue@3.5.13(typescript@4.9.5)))(vue@3.5.13(typescript@4.9.5))
      '@vitejs/plugin-react':
        specifier: 4.3.4
        version: 4.3.4(vite@6.0.6(@types/node@22.10.2)(jiti@1.21.7)(sass-embedded@1.83.0)(yaml@2.6.1))
      '@vitejs/plugin-vue':
        specifier: 5.2.1
        version: 5.2.1(vite@6.0.6(@types/node@22.10.2)(jiti@1.21.7)(sass-embedded@1.83.0)(yaml@2.6.1))(vue@3.5.13(typescript@4.9.5))
      antd:
        specifier: 5.22.7
        version: 5.22.7(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      axios:
        specifier: ^1.7.9
        version: 1.7.9
      dayjs:
        specifier: ^1.11.13
        version: 1.11.13
      esbuild:
        specifier: 0.24.2
        version: 0.24.2
      react:
        specifier: 18.3.1
        version: 18.3.1
      react-dom:
        specifier: 18.3.1
        version: 18.3.1(react@18.3.1)
      react-router:
        specifier: 7.1.1
        version: 7.1.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      vite:
        specifier: 6.0.6
        version: 6.0.6(@types/node@22.10.2)(jiti@1.21.7)(sass-embedded@1.83.0)(yaml@2.6.1)
      vue:
        specifier: 3.5.13
        version: 3.5.13(typescript@4.9.5)
      vue-router:
        specifier: 4.5.0
        version: 4.5.0(vue@3.5.13(typescript@4.9.5))
    devDependencies:
      '@eslint/js':
        specifier: ^9.16.0
        version: 9.17.0
      '@lvyue/vite-config':
        specifier: workspace:*
        version: link:packages/vite-config
      '@rollup/plugin-typescript':
        specifier: ^12.1.2
        version: 12.1.2(rollup@4.30.1)(tslib@2.8.1)(typescript@4.9.5)
      '@stylistic/eslint-plugin':
        specifier: ^2.12.1
        version: 2.12.1(eslint@8.57.1)(typescript@4.9.5)
      '@types/node':
        specifier: ^22.10.2
        version: 22.10.2
      '@types/react':
        specifier: 18.3.18
        version: 18.3.18
      '@types/react-dom':
        specifier: 18.3.5
        version: 18.3.5(@types/react@18.3.18)
      '@typescript-eslint/parser':
        specifier: ^5.1.0
        version: 5.62.0(eslint@8.57.1)(typescript@4.9.5)
      ansi-color:
        specifier: ^0.2.1
        version: 0.2.1
      cross-env:
        specifier: ^7.0.3
        version: 7.0.3
      eslint:
        specifier: ^8.57.1
        version: 8.57.1
      eslint-plugin-react:
        specifier: ^7.37.2
        version: 7.37.3(eslint@8.57.1)
      eslint-plugin-react-hooks:
        specifier: ^5.1.0
        version: 5.1.0(eslint@8.57.1)
      eslint-plugin-react-refresh:
        specifier: ^0.4.16
        version: 0.4.16(eslint@8.57.1)
      eslint-plugin-vue:
        specifier: ^9.32.0
        version: 9.33.0(eslint@8.57.1)
      globals:
        specifier: ^15.13.0
        version: 15.14.0
      inquirer:
        specifier: ^12.4.3
        version: 12.4.3(@types/node@22.10.2)
      lint-staged:
        specifier: ^15.2.10
        version: 15.3.0
      ora:
        specifier: ^8.1.1
        version: 8.1.1
      pkg-dir:
        specifier: ^8.0.0
        version: 8.0.0
      prettier:
        specifier: ^3.4.2
        version: 3.4.2
      rollup:
        specifier: ^4.30.0
        version: 4.30.1
      rollup-plugin-cpy:
        specifier: ^2.0.1
        version: 2.0.1(rollup@4.30.1)
      rollup-plugin-esbuild:
        specifier: ^6.1.1
        version: 6.1.1(esbuild@0.24.2)(rollup@4.30.1)
      sass-embedded:
        specifier: 1.83.0
        version: 1.83.0
      simple-git-hooks:
        specifier: ^2.11.1
        version: 2.11.1
      typescript:
        specifier: ^4.8.4
        version: 4.9.5
      vite-plugin-mock:
        specifier: ^3.0.2
        version: 3.0.2(esbuild@0.24.2)(mockjs@1.1.0)(vite@6.0.6(@types/node@22.10.2)(jiti@1.21.7)(sass-embedded@1.83.0)(yaml@2.6.1))
      vue-eslint-parser:
        specifier: ^9.4.3
        version: 9.4.3(eslint@8.57.1)

  apps/tools:
    devDependencies:
      '@lvyue/vite-config':
        specifier: workspace:*
        version: link:../../packages/vite-config
      autoprefixer:
        specifier: ^10.4.20
        version: 10.4.20(postcss@8.4.49)
      postcss:
        specifier: ^8.4.49
        version: 8.4.49
      tailwindcss:
        specifier: ^3.4.17
        version: 3.4.17
      typescript-eslint:
        specifier: ^8.18.2
        version: 8.18.2(eslint@9.17.0(jiti@1.21.7))(typescript@5.6.3)

  packages/layout:
    dependencies:
      '@ss/mtd-vue3':
        specifier: 1.2.36
        version: 1.2.36(@babel/core@7.26.0)(vue-router@4.5.0(vue@3.5.13(typescript@5.6.3)))(vue@3.5.13(typescript@5.6.3))
      antd:
        specifier: ^5.22.7
        version: 5.22.7(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react:
        specifier: ^18.3.1
        version: 18.3.1
      react-dom:
        specifier: ^18.3.1
        version: 18.3.1(react@18.3.1)
      react-router:
        specifier: ^7.1.1
        version: 7.1.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
    devDependencies:
      '@eslint/js':
        specifier: ^9.17.0
        version: 9.17.0
      '@lvyue/vite-config':
        specifier: workspace:*
        version: link:../vite-config
      '@types/react':
        specifier: ^18.3.0
        version: 18.3.18
      '@types/react-dom':
        specifier: ^18.3.5
        version: 18.3.5(@types/react@18.3.18)
      '@vitejs/plugin-react':
        specifier: ^4.3.4
        version: 4.3.4(vite@6.0.6(@types/node@22.10.2)(jiti@1.21.7)(sass-embedded@1.83.0)(yaml@2.6.1))
      autoprefixer:
        specifier: ^10.4.20
        version: 10.4.20(postcss@8.4.49)
      eslint:
        specifier: ^9.17.0
        version: 9.17.0(jiti@1.21.7)
      eslint-plugin-react-hooks:
        specifier: ^5.0.0
        version: 5.1.0(eslint@9.17.0(jiti@1.21.7))
      eslint-plugin-react-refresh:
        specifier: ^0.4.16
        version: 0.4.16(eslint@9.17.0(jiti@1.21.7))
      globals:
        specifier: ^15.14.0
        version: 15.14.0
      postcss:
        specifier: ^8.4.49
        version: 8.4.49
      sass-embedded:
        specifier: ^1.83.0
        version: 1.83.0
      tailwindcss:
        specifier: ^3.4.17
        version: 3.4.17
      typescript:
        specifier: ~5.6.2
        version: 5.6.3
      typescript-eslint:
        specifier: ^8.18.2
        version: 8.18.2(eslint@9.17.0(jiti@1.21.7))(typescript@5.6.3)
      vite:
        specifier: ^6.0.5
        version: 6.0.6(@types/node@22.10.2)(jiti@1.21.7)(sass-embedded@1.83.0)(yaml@2.6.1)

  packages/utils: {}

  packages/vite-config:
    dependencies:
      '@types/node':
        specifier: ^22.10.2
        version: 22.10.2
      '@vitejs/plugin-react':
        specifier: 4.3.4
        version: 4.3.4(vite@6.0.6(@types/node@22.10.2)(jiti@1.21.7)(sass-embedded@1.83.0)(yaml@2.6.1))
      ansi-color:
        specifier: ^0.2.1
        version: 0.2.1
      glob:
        specifier: ^11.0.0
        version: 11.0.0
      open:
        specifier: ^10.1.0
        version: 10.1.0
      vite:
        specifier: 6.0.6
        version: 6.0.6(@types/node@22.10.2)(jiti@1.21.7)(sass-embedded@1.83.0)(yaml@2.6.1)

packages:

  '@alloc/quick-lru@5.2.0':
    resolution: {integrity: sha1-e/aLIMCjUPk2kV/K4G9Y4yAHzjA=, tarball: http://r.npm.sankuai.com/@alloc/quick-lru/download/@alloc/quick-lru-5.2.0.tgz}
    engines: {node: '>=10'}

  '@ampproject/remapping@2.3.0':
    resolution: {integrity: sha1-7UQbb6YAByUgzhi0PSyMyMrsx/Q=, tarball: http://r.npm.sankuai.com/@ampproject/remapping/download/@ampproject/remapping-2.3.0.tgz}
    engines: {node: '>=6.0.0'}

  '@ant-design/colors@7.1.0':
    resolution: {integrity: sha1-YOrfouIYcdiUjaxdULnwVgYvivM=, tarball: http://r.npm.sankuai.com/@ant-design/colors/download/@ant-design/colors-7.1.0.tgz}

  '@ant-design/cssinjs-utils@1.1.3':
    resolution: {integrity: sha1-XdeRJgV5IKaZLVezjdhOLAtweXc=, tarball: http://r.npm.sankuai.com/@ant-design/cssinjs-utils/download/@ant-design/cssinjs-utils-1.1.3.tgz}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'

  '@ant-design/cssinjs@1.22.1':
    resolution: {integrity: sha1-AOlDpjh6gICrqLkn34I23z4H6WQ=, tarball: http://r.npm.sankuai.com/@ant-design/cssinjs/download/@ant-design/cssinjs-1.22.1.tgz}
    peerDependencies:
      react: '>=16.0.0'
      react-dom: '>=16.0.0'

  '@ant-design/fast-color@2.0.6':
    resolution: {integrity: sha1-q01EVcFULJAX02fC+oyj5CFdC6I=, tarball: http://r.npm.sankuai.com/@ant-design/fast-color/download/@ant-design/fast-color-2.0.6.tgz}
    engines: {node: '>=8.x'}

  '@ant-design/icons-svg@4.4.2':
    resolution: {integrity: sha1-7Svn+02CrH4dRaVKWwbWzs+L5vY=, tarball: http://r.npm.sankuai.com/@ant-design/icons-svg/download/@ant-design/icons-svg-4.4.2.tgz}

  '@ant-design/icons@5.5.2':
    resolution: {integrity: sha1-xFZ5Q8wrfG2+nK5owG/6NfdV3A0=, tarball: http://r.npm.sankuai.com/@ant-design/icons/download/@ant-design/icons-5.5.2.tgz}
    engines: {node: '>=8'}
    peerDependencies:
      react: '>=16.0.0'
      react-dom: '>=16.0.0'

  '@ant-design/react-slick@1.1.2':
    resolution: {integrity: sha1-+Ezj5NDclB8CsW8dHW16Nx/7tPE=, tarball: http://r.npm.sankuai.com/@ant-design/react-slick/download/@ant-design/react-slick-1.1.2.tgz}
    peerDependencies:
      react: '>=16.9.0'

  '@babel/code-frame@7.26.2':
    resolution: {integrity: sha1-S1+rl9MzOO/5FiNQVfDrwh5XOoU=, tarball: http://r.npm.sankuai.com/@babel/code-frame/download/@babel/code-frame-7.26.2.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/compat-data@7.26.3':
    resolution: {integrity: sha1-mUiCZKVrKt7WOYOr1qQX8DuS7QI=, tarball: http://r.npm.sankuai.com/@babel/compat-data/download/@babel/compat-data-7.26.3.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/core@7.26.0':
    resolution: {integrity: sha1-14tgI8yPMRTM8EnrIZYT90p0e0A=, tarball: http://r.npm.sankuai.com/@babel/core/download/@babel/core-7.26.0.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/generator@7.26.3':
    resolution: {integrity: sha1-q41DYFRKQlyQwkjfcFmIH0ss4Bk=, tarball: http://r.npm.sankuai.com/@babel/generator/download/@babel/generator-7.26.3.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/helper-compilation-targets@7.25.9':
    resolution: {integrity: sha1-Va8CXONlvjzcDBweVsavYXzoiHU=, tarball: http://r.npm.sankuai.com/@babel/helper-compilation-targets/download/@babel/helper-compilation-targets-7.25.9.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/helper-module-imports@7.25.9':
    resolution: {integrity: sha1-5/jSBgLr2/nrvqCgdR+w8qQUFxU=, tarball: http://r.npm.sankuai.com/@babel/helper-module-imports/download/@babel/helper-module-imports-7.25.9.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/helper-module-transforms@7.26.0':
    resolution: {integrity: sha1-jOVOydWSaV5Y2EzYhLe1xqL97q4=, tarball: http://r.npm.sankuai.com/@babel/helper-module-transforms/download/@babel/helper-module-transforms-7.26.0.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/helper-plugin-utils@7.25.9':
    resolution: {integrity: sha1-nL3WOpRDoskqclzKfryhLMjdn0Y=, tarball: http://r.npm.sankuai.com/@babel/helper-plugin-utils/download/@babel/helper-plugin-utils-7.25.9.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/helper-string-parser@7.25.9':
    resolution: {integrity: sha1-Gqu3Lucu01eJtLvK08ooYs5hTow=, tarball: http://r.npm.sankuai.com/@babel/helper-string-parser/download/@babel/helper-string-parser-7.25.9.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/helper-validator-identifier@7.25.9':
    resolution: {integrity: sha1-JLZOLD7HzTs8VHcpuNFocfIsvcc=, tarball: http://r.npm.sankuai.com/@babel/helper-validator-identifier/download/@babel/helper-validator-identifier-7.25.9.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/helper-validator-option@7.25.9':
    resolution: {integrity: sha1-huRb2KSat+A/J2V3+WF5ZT1B2nI=, tarball: http://r.npm.sankuai.com/@babel/helper-validator-option/download/@babel/helper-validator-option-7.25.9.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/helpers@7.26.0':
    resolution: {integrity: sha1-MOYh8eulqkX+b0ho0ukVTYhBGaQ=, tarball: http://r.npm.sankuai.com/@babel/helpers/download/@babel/helpers-7.26.0.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/parser@7.26.3':
    resolution: {integrity: sha1-jFHF223fCBNK8d26zxaqq0i6wjQ=, tarball: http://r.npm.sankuai.com/@babel/parser/download/@babel/parser-7.26.3.tgz}
    engines: {node: '>=6.0.0'}
    hasBin: true

  '@babel/plugin-syntax-jsx@7.25.9':
    resolution: {integrity: sha1-o0MToXjqVvGVFZm5KcHOrO5xkpA=, tarball: http://r.npm.sankuai.com/@babel/plugin-syntax-jsx/download/@babel/plugin-syntax-jsx-7.25.9.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-react-jsx-self@7.25.9':
    resolution: {integrity: sha1-wLbK6cG3OWf3+esvypU2ui+tKFg=, tarball: http://r.npm.sankuai.com/@babel/plugin-transform-react-jsx-self/download/@babel/plugin-transform-react-jsx-self-7.25.9.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-react-jsx-source@7.25.9':
    resolution: {integrity: sha1-TGuNqlILXxVbX7VVR9fJ+pFBdQM=, tarball: http://r.npm.sankuai.com/@babel/plugin-transform-react-jsx-source/download/@babel/plugin-transform-react-jsx-source-7.25.9.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/runtime@7.26.0':
    resolution: {integrity: sha1-hgDC9ZXyd8YIFSVkGLhTVqZRc8E=, tarball: http://r.npm.sankuai.com/@babel/runtime/download/@babel/runtime-7.26.0.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/template@7.25.9':
    resolution: {integrity: sha1-7LYtgaim9dxf6Kv8OQH8Ut3xUBY=, tarball: http://r.npm.sankuai.com/@babel/template/download/@babel/template-7.25.9.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/traverse@7.26.4':
    resolution: {integrity: sha1-rDoqhLkI3ebUY8O/osX9wWU1dL0=, tarball: http://r.npm.sankuai.com/@babel/traverse/download/@babel/traverse-7.26.4.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/types@7.26.3':
    resolution: {integrity: sha1-N+eYMPBMK1aHrMd9uX+8dfuB88A=, tarball: http://r.npm.sankuai.com/@babel/types/download/@babel/types-7.26.3.tgz}
    engines: {node: '>=6.9.0'}

  '@bufbuild/protobuf@2.2.3':
    resolution: {integrity: sha1-nNE29raH5j6bUXs6VCEezpQol+4=, tarball: http://r.npm.sankuai.com/@bufbuild/protobuf/download/@bufbuild/protobuf-2.2.3.tgz}

  '@ctrl/tinycolor@3.6.1':
    resolution: {integrity: sha1-tsdaVqGUfMkW6gWHctZmosiTLzE=, tarball: http://r.npm.sankuai.com/@ctrl/tinycolor/download/@ctrl/tinycolor-3.6.1.tgz}
    engines: {node: '>=10'}

  '@emotion/hash@0.8.0':
    resolution: {integrity: sha1-u7/2iXj+/b5ozLUzvIy+HRr7VBM=, tarball: http://r.npm.sankuai.com/@emotion/hash/download/@emotion/hash-0.8.0.tgz}

  '@emotion/unitless@0.7.5':
    resolution: {integrity: sha1-dyESkcGQCnALinjPr9oxYNdpSe0=, tarball: http://r.npm.sankuai.com/@emotion/unitless/download/@emotion/unitless-0.7.5.tgz}

  '@esbuild/aix-ppc64@0.24.2':
    resolution: {integrity: sha1-OISNPiWv6EKnlDZDy804fMbhNGE=, tarball: http://r.npm.sankuai.com/@esbuild/aix-ppc64/download/@esbuild/aix-ppc64-0.24.2.tgz}
    engines: {node: '>=18'}
    cpu: [ppc64]
    os: [aix]

  '@esbuild/android-arm64@0.24.2':
    resolution: {integrity: sha1-9ZKVeui1ZDEp+oiceeac2GabuJQ=, tarball: http://r.npm.sankuai.com/@esbuild/android-arm64/download/@esbuild/android-arm64-0.24.2.tgz}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [android]

  '@esbuild/android-arm@0.24.2':
    resolution: {integrity: sha1-ctiiBjqmMDCK9Ian5cvNHhNDNbM=, tarball: http://r.npm.sankuai.com/@esbuild/android-arm/download/@esbuild/android-arm-0.24.2.tgz}
    engines: {node: '>=18'}
    cpu: [arm]
    os: [android]

  '@esbuild/android-x64@0.24.2':
    resolution: {integrity: sha1-mncTUE1fBHkvM76cGXqIKy2I/rs=, tarball: http://r.npm.sankuai.com/@esbuild/android-x64/download/@esbuild/android-x64-0.24.2.tgz}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [android]

  '@esbuild/darwin-arm64@0.24.2':
    resolution: {integrity: sha1-Aq4ErY6//W4uoJYYGzNmgWsrWTY=, tarball: http://r.npm.sankuai.com/@esbuild/darwin-arm64/download/@esbuild/darwin-arm64-0.24.2.tgz}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [darwin]

  '@esbuild/darwin-x64@0.24.2':
    resolution: {integrity: sha1-nsMSvCnGDhts7K3IK9UE2K2qGek=, tarball: http://r.npm.sankuai.com/@esbuild/darwin-x64/download/@esbuild/darwin-x64-0.24.2.tgz}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [darwin]

  '@esbuild/freebsd-arm64@0.24.2':
    resolution: {integrity: sha1-XoL0TLSQbWrr8kSX1qBoz8FS+gA=, tarball: http://r.npm.sankuai.com/@esbuild/freebsd-arm64/download/@esbuild/freebsd-arm64-0.24.2.tgz}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [freebsd]

  '@esbuild/freebsd-x64@0.24.2':
    resolution: {integrity: sha1-P7HOkvJ2Fot1B0tOUaoNgUHszn8=, tarball: http://r.npm.sankuai.com/@esbuild/freebsd-x64/download/@esbuild/freebsd-x64-0.24.2.tgz}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [freebsd]

  '@esbuild/linux-arm64@0.24.2':
    resolution: {integrity: sha1-hWtjLXnrgK7AhkOB79Kd6P0LH0M=, tarball: http://r.npm.sankuai.com/@esbuild/linux-arm64/download/@esbuild/linux-arm64-0.24.2.tgz}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [linux]

  '@esbuild/linux-arm@0.24.2':
    resolution: {integrity: sha1-yEa0aU3Fp10URPUiV8zFZZAhtzY=, tarball: http://r.npm.sankuai.com/@esbuild/linux-arm/download/@esbuild/linux-arm-0.24.2.tgz}
    engines: {node: '>=18'}
    cpu: [arm]
    os: [linux]

  '@esbuild/linux-ia32@0.24.2':
    resolution: {integrity: sha1-+KFmFaeIJsy7ZWb6ualgbP1KN9U=, tarball: http://r.npm.sankuai.com/@esbuild/linux-ia32/download/@esbuild/linux-ia32-0.24.2.tgz}
    engines: {node: '>=18'}
    cpu: [ia32]
    os: [linux]

  '@esbuild/linux-loong64@0.24.2':
    resolution: {integrity: sha1-HEUVOMdlvxSRNRLHbtijUeGLCfw=, tarball: http://r.npm.sankuai.com/@esbuild/linux-loong64/download/@esbuild/linux-loong64-0.24.2.tgz}
    engines: {node: '>=18'}
    cpu: [loong64]
    os: [linux]

  '@esbuild/linux-mips64el@0.24.2':
    resolution: {integrity: sha1-CEbt7vvD2NUGRcUYacxkQB2SOcs=, tarball: http://r.npm.sankuai.com/@esbuild/linux-mips64el/download/@esbuild/linux-mips64el-0.24.2.tgz}
    engines: {node: '>=18'}
    cpu: [mips64el]
    os: [linux]

  '@esbuild/linux-ppc64@0.24.2':
    resolution: {integrity: sha1-jj/FRQVnHRkzN6Nt/UwaI7ikFBI=, tarball: http://r.npm.sankuai.com/@esbuild/linux-ppc64/download/@esbuild/linux-ppc64-0.24.2.tgz}
    engines: {node: '>=18'}
    cpu: [ppc64]
    os: [linux]

  '@esbuild/linux-riscv64@0.24.2':
    resolution: {integrity: sha1-ah6SCW1eaPe7EKDWS7W20dr5ppQ=, tarball: http://r.npm.sankuai.com/@esbuild/linux-riscv64/download/@esbuild/linux-riscv64-0.24.2.tgz}
    engines: {node: '>=18'}
    cpu: [riscv64]
    os: [linux]

  '@esbuild/linux-s390x@0.24.2':
    resolution: {integrity: sha1-qxjlbmb3o8ScuX0zfNCm/qKKhXc=, tarball: http://r.npm.sankuai.com/@esbuild/linux-s390x/download/@esbuild/linux-s390x-0.24.2.tgz}
    engines: {node: '>=18'}
    cpu: [s390x]
    os: [linux]

  '@esbuild/linux-x64@0.24.2':
    resolution: {integrity: sha1-gUDJtA2mNNOAsLKcg3oLQmev848=, tarball: http://r.npm.sankuai.com/@esbuild/linux-x64/download/@esbuild/linux-x64-0.24.2.tgz}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [linux]

  '@esbuild/netbsd-arm64@0.24.2':
    resolution: {integrity: sha1-ZfGRYUMrr7OYH18gp/9Fq7LnCOY=, tarball: http://r.npm.sankuai.com/@esbuild/netbsd-arm64/download/@esbuild/netbsd-arm64-0.24.2.tgz}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [netbsd]

  '@esbuild/netbsd-x64@0.24.2':
    resolution: {integrity: sha1-ejqX13q/0Rdlpy8cb5sY9TlrzEA=, tarball: http://r.npm.sankuai.com/@esbuild/netbsd-x64/download/@esbuild/netbsd-x64-0.24.2.tgz}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [netbsd]

  '@esbuild/openbsd-arm64@0.24.2':
    resolution: {integrity: sha1-WLACON2PEjv/9o06zFOm7jaa+J8=, tarball: http://r.npm.sankuai.com/@esbuild/openbsd-arm64/download/@esbuild/openbsd-arm64-0.24.2.tgz}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [openbsd]

  '@esbuild/openbsd-x64@0.24.2':
    resolution: {integrity: sha1-CshD/aD+uFqT4oiEKTbCGgCoogU=, tarball: http://r.npm.sankuai.com/@esbuild/openbsd-x64/download/@esbuild/openbsd-x64-0.24.2.tgz}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [openbsd]

  '@esbuild/sunos-x64@0.24.2':
    resolution: {integrity: sha1-i3qoleB4KNNsQipEBMwuzyf7FcY=, tarball: http://r.npm.sankuai.com/@esbuild/sunos-x64/download/@esbuild/sunos-x64-0.24.2.tgz}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [sunos]

  '@esbuild/win32-arm64@0.24.2':
    resolution: {integrity: sha1-wCOvtkfKvww+0T8O3fxPHWHGaoU=, tarball: http://r.npm.sankuai.com/@esbuild/win32-arm64/download/@esbuild/win32-arm64-0.24.2.tgz}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [win32]

  '@esbuild/win32-ia32@0.24.2':
    resolution: {integrity: sha1-lsNWEy0t2pkAmMi4uVEgnDzXQ8I=, tarball: http://r.npm.sankuai.com/@esbuild/win32-ia32/download/@esbuild/win32-ia32-0.24.2.tgz}
    engines: {node: '>=18'}
    cpu: [ia32]
    os: [win32]

  '@esbuild/win32-x64@0.24.2':
    resolution: {integrity: sha1-NKoLUtD7saZUtZas+llfDHt3p3s=, tarball: http://r.npm.sankuai.com/@esbuild/win32-x64/download/@esbuild/win32-x64-0.24.2.tgz}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [win32]

  '@eslint-community/eslint-utils@4.4.1':
    resolution: {integrity: sha1-0RRb8sIBMtZABJXW30v1k2L9nVY=, tarball: http://r.npm.sankuai.com/@eslint-community/eslint-utils/download/@eslint-community/eslint-utils-4.4.1.tgz}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    peerDependencies:
      eslint: ^6.0.0 || ^7.0.0 || >=8.0.0

  '@eslint-community/regexpp@4.12.1':
    resolution: {integrity: sha1-z8bP/jnfOQo4Qc3iq8z5Lqp64OA=, tarball: http://r.npm.sankuai.com/@eslint-community/regexpp/download/@eslint-community/regexpp-4.12.1.tgz}
    engines: {node: ^12.0.0 || ^14.0.0 || >=16.0.0}

  '@eslint/config-array@0.19.1':
    resolution: {integrity: sha1-c0quosQL4iu7HyqdrGh8V6akyYQ=, tarball: http://r.npm.sankuai.com/@eslint/config-array/download/@eslint/config-array-0.19.1.tgz}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@eslint/core@0.9.1':
    resolution: {integrity: sha1-MXY4RzCO9rcISkUFVzrJQCxR+dE=, tarball: http://r.npm.sankuai.com/@eslint/core/download/@eslint/core-0.9.1.tgz}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@eslint/eslintrc@2.1.4':
    resolution: {integrity: sha1-OIomnw8lwbatwxe1osVXFIlMcK0=, tarball: http://r.npm.sankuai.com/@eslint/eslintrc/download/@eslint/eslintrc-2.1.4.tgz}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}

  '@eslint/eslintrc@3.2.0':
    resolution: {integrity: sha1-V0cKxOLig6a/dgRNYygRluNwVCw=, tarball: http://r.npm.sankuai.com/@eslint/eslintrc/download/@eslint/eslintrc-3.2.0.tgz}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@eslint/js@8.57.1':
    resolution: {integrity: sha1-3mM9s+wu9qPIni8ZA4Bj6KEi4sI=, tarball: http://r.npm.sankuai.com/@eslint/js/download/@eslint/js-8.57.1.tgz}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}

  '@eslint/js@9.17.0':
    resolution: {integrity: sha1-FSPlhnkfgDdqb4OYo5ZEVezGUew=, tarball: http://r.npm.sankuai.com/@eslint/js/download/@eslint/js-9.17.0.tgz}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@eslint/object-schema@2.1.5':
    resolution: {integrity: sha1-hnCo9iWKK+WyxiD/MUodmEwj6y4=, tarball: http://r.npm.sankuai.com/@eslint/object-schema/download/@eslint/object-schema-2.1.5.tgz}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@eslint/plugin-kit@0.2.4':
    resolution: {integrity: sha1-K3jnuzdVeEuxP6qJMqHZlNZTd5I=, tarball: http://r.npm.sankuai.com/@eslint/plugin-kit/download/@eslint/plugin-kit-0.2.4.tgz}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@humanfs/core@0.19.1':
    resolution: {integrity: sha1-F8Vcp9Qmcz/jxWGQa4Fzwza0Cnc=, tarball: http://r.npm.sankuai.com/@humanfs/core/download/@humanfs/core-0.19.1.tgz}
    engines: {node: '>=18.18.0'}

  '@humanfs/node@0.16.6':
    resolution: {integrity: sha1-7ioQ6qvRExmHvwSI/ZuCAXTNdl4=, tarball: http://r.npm.sankuai.com/@humanfs/node/download/@humanfs/node-0.16.6.tgz}
    engines: {node: '>=18.18.0'}

  '@humanwhocodes/config-array@0.13.0':
    resolution: {integrity: sha1-+5B2JN8yVtBLmqLfUNeql+xkh0g=, tarball: http://r.npm.sankuai.com/@humanwhocodes/config-array/download/@humanwhocodes/config-array-0.13.0.tgz}
    engines: {node: '>=10.10.0'}

  '@humanwhocodes/module-importer@1.0.1':
    resolution: {integrity: sha1-r1smkaIrRL6EewyoFkHF+2rQFyw=, tarball: http://r.npm.sankuai.com/@humanwhocodes/module-importer/download/@humanwhocodes/module-importer-1.0.1.tgz}
    engines: {node: '>=12.22'}

  '@humanwhocodes/object-schema@2.0.3':
    resolution: {integrity: sha1-Siho111taWPkI7z5C3/RvjQ0CdM=, tarball: http://r.npm.sankuai.com/@humanwhocodes/object-schema/download/@humanwhocodes/object-schema-2.0.3.tgz}

  '@humanwhocodes/retry@0.3.1':
    resolution: {integrity: sha1-xypcdqn7rzSI4jGxPcUsDae6tCo=, tarball: http://r.npm.sankuai.com/@humanwhocodes/retry/download/@humanwhocodes/retry-0.3.1.tgz}
    engines: {node: '>=18.18'}

  '@humanwhocodes/retry@0.4.1':
    resolution: {integrity: sha1-mpbOUBvGLfRsQDH72XDjzGsQ8Hs=, tarball: http://r.npm.sankuai.com/@humanwhocodes/retry/download/@humanwhocodes/retry-0.4.1.tgz}
    engines: {node: '>=18.18'}

  '@inquirer/checkbox@4.1.3':
    resolution: {integrity: sha1-sXf7YmcMbRYIA15j24BZcjT+QTA=, tarball: http://r.npm.sankuai.com/@inquirer/checkbox/download/@inquirer/checkbox-4.1.3.tgz}
    engines: {node: '>=18'}
    peerDependencies:
      '@types/node': '>=18'
    peerDependenciesMeta:
      '@types/node':
        optional: true

  '@inquirer/confirm@5.1.7':
    resolution: {integrity: sha1-Yflw4lW2YO3yoMkBxZnX+dJaWN8=, tarball: http://r.npm.sankuai.com/@inquirer/confirm/download/@inquirer/confirm-5.1.7.tgz}
    engines: {node: '>=18'}
    peerDependencies:
      '@types/node': '>=18'
    peerDependenciesMeta:
      '@types/node':
        optional: true

  '@inquirer/core@10.1.8':
    resolution: {integrity: sha1-sueaw5ob7C+APZwgodMEdZ+DX1E=, tarball: http://r.npm.sankuai.com/@inquirer/core/download/@inquirer/core-10.1.8.tgz}
    engines: {node: '>=18'}
    peerDependencies:
      '@types/node': '>=18'
    peerDependenciesMeta:
      '@types/node':
        optional: true

  '@inquirer/editor@4.2.8':
    resolution: {integrity: sha1-+LVTaySMhK7RmOgEQITErtaZXOs=, tarball: http://r.npm.sankuai.com/@inquirer/editor/download/@inquirer/editor-4.2.8.tgz}
    engines: {node: '>=18'}
    peerDependencies:
      '@types/node': '>=18'
    peerDependenciesMeta:
      '@types/node':
        optional: true

  '@inquirer/expand@4.0.10':
    resolution: {integrity: sha1-YwCgLssa4VFCRTxvOGz4knif8Ho=, tarball: http://r.npm.sankuai.com/@inquirer/expand/download/@inquirer/expand-4.0.10.tgz}
    engines: {node: '>=18'}
    peerDependencies:
      '@types/node': '>=18'
    peerDependenciesMeta:
      '@types/node':
        optional: true

  '@inquirer/figures@1.0.11':
    resolution: {integrity: sha1-R0Tm25Uoj+od6td5VUhZcQqVmiE=, tarball: http://r.npm.sankuai.com/@inquirer/figures/download/@inquirer/figures-1.0.11.tgz}
    engines: {node: '>=18'}

  '@inquirer/input@4.1.7':
    resolution: {integrity: sha1-2eclwAr+JFAxN3FMeNen4PFtZ60=, tarball: http://r.npm.sankuai.com/@inquirer/input/download/@inquirer/input-4.1.7.tgz}
    engines: {node: '>=18'}
    peerDependencies:
      '@types/node': '>=18'
    peerDependenciesMeta:
      '@types/node':
        optional: true

  '@inquirer/number@3.0.10':
    resolution: {integrity: sha1-OtHStphJUhFpr4s+/oOPl7oBA1A=, tarball: http://r.npm.sankuai.com/@inquirer/number/download/@inquirer/number-3.0.10.tgz}
    engines: {node: '>=18'}
    peerDependencies:
      '@types/node': '>=18'
    peerDependenciesMeta:
      '@types/node':
        optional: true

  '@inquirer/password@4.0.10':
    resolution: {integrity: sha1-b5gcQZQ2belGc6nc3PYGjjX0fDU=, tarball: http://r.npm.sankuai.com/@inquirer/password/download/@inquirer/password-4.0.10.tgz}
    engines: {node: '>=18'}
    peerDependencies:
      '@types/node': '>=18'
    peerDependenciesMeta:
      '@types/node':
        optional: true

  '@inquirer/prompts@7.3.3':
    resolution: {integrity: sha1-eIrCMBzryyqAiUmj4ceIGaJ+4aE=, tarball: http://r.npm.sankuai.com/@inquirer/prompts/download/@inquirer/prompts-7.3.3.tgz}
    engines: {node: '>=18'}
    peerDependencies:
      '@types/node': '>=18'
    peerDependenciesMeta:
      '@types/node':
        optional: true

  '@inquirer/rawlist@4.0.10':
    resolution: {integrity: sha1-NYqVMO+LREmhg8k0o2YCFYVeXoc=, tarball: http://r.npm.sankuai.com/@inquirer/rawlist/download/@inquirer/rawlist-4.0.10.tgz}
    engines: {node: '>=18'}
    peerDependencies:
      '@types/node': '>=18'
    peerDependenciesMeta:
      '@types/node':
        optional: true

  '@inquirer/search@3.0.10':
    resolution: {integrity: sha1-XjNUf5U9S4sw3NqhBIeMRapB1DM=, tarball: http://r.npm.sankuai.com/@inquirer/search/download/@inquirer/search-3.0.10.tgz}
    engines: {node: '>=18'}
    peerDependencies:
      '@types/node': '>=18'
    peerDependenciesMeta:
      '@types/node':
        optional: true

  '@inquirer/select@4.0.10':
    resolution: {integrity: sha1-8UucGIBK4q74DAAZX76BG1/YU2Q=, tarball: http://r.npm.sankuai.com/@inquirer/select/download/@inquirer/select-4.0.10.tgz}
    engines: {node: '>=18'}
    peerDependencies:
      '@types/node': '>=18'
    peerDependenciesMeta:
      '@types/node':
        optional: true

  '@inquirer/type@3.0.5':
    resolution: {integrity: sha1-/gAgflfV8EDlsY6AnI56vDoq3jo=, tarball: http://r.npm.sankuai.com/@inquirer/type/download/@inquirer/type-3.0.5.tgz}
    engines: {node: '>=18'}
    peerDependencies:
      '@types/node': '>=18'
    peerDependenciesMeta:
      '@types/node':
        optional: true

  '@isaacs/cliui@8.0.2':
    resolution: {integrity: sha1-s3Znt7wYHBaHgiWbq0JHT79StVA=, tarball: http://r.npm.sankuai.com/@isaacs/cliui/download/@isaacs/cliui-8.0.2.tgz}
    engines: {node: '>=12'}

  '@jridgewell/gen-mapping@0.3.8':
    resolution: {integrity: sha1-Tw4GNi4BNi+CPTSPGHKwj2ZtgUI=, tarball: http://r.npm.sankuai.com/@jridgewell/gen-mapping/download/@jridgewell/gen-mapping-0.3.8.tgz}
    engines: {node: '>=6.0.0'}

  '@jridgewell/resolve-uri@3.1.2':
    resolution: {integrity: sha1-eg7mAfYPmaIMfHxf8MgDiMEYm9Y=, tarball: http://r.npm.sankuai.com/@jridgewell/resolve-uri/download/@jridgewell/resolve-uri-3.1.2.tgz}
    engines: {node: '>=6.0.0'}

  '@jridgewell/set-array@1.2.1':
    resolution: {integrity: sha1-VY+2Ry7RakyFC4iVMOazZDjEkoA=, tarball: http://r.npm.sankuai.com/@jridgewell/set-array/download/@jridgewell/set-array-1.2.1.tgz}
    engines: {node: '>=6.0.0'}

  '@jridgewell/sourcemap-codec@1.5.0':
    resolution: {integrity: sha1-MYi8snOkFLDSFf0ipYVAuYm5QJo=, tarball: http://r.npm.sankuai.com/@jridgewell/sourcemap-codec/download/@jridgewell/sourcemap-codec-1.5.0.tgz}

  '@jridgewell/trace-mapping@0.3.25':
    resolution: {integrity: sha1-FfGQ6YiV8/wjJ27hS8drZ1wuUPA=, tarball: http://r.npm.sankuai.com/@jridgewell/trace-mapping/download/@jridgewell/trace-mapping-0.3.25.tgz}

  '@mrmlnc/readdir-enhanced@2.2.1':
    resolution: {integrity: sha1-UkryQNGjYFJ7cwR17PoTRKpUDd4=, tarball: http://r.npm.sankuai.com/@mrmlnc/readdir-enhanced/download/@mrmlnc/readdir-enhanced-2.2.1.tgz}
    engines: {node: '>=4'}

  '@nodelib/fs.scandir@2.1.5':
    resolution: {integrity: sha1-dhnC6yGyVIP20WdUi0z9WnSIw9U=, tarball: http://r.npm.sankuai.com/@nodelib/fs.scandir/download/@nodelib/fs.scandir-2.1.5.tgz}
    engines: {node: '>= 8'}

  '@nodelib/fs.stat@1.1.3':
    resolution: {integrity: sha1-K1o6s/kYzKSKjHVMCBaOPwPrphs=, tarball: http://r.npm.sankuai.com/@nodelib/fs.stat/download/@nodelib/fs.stat-1.1.3.tgz}
    engines: {node: '>= 6'}

  '@nodelib/fs.stat@2.0.5':
    resolution: {integrity: sha1-W9Jir5Tp0lvR5xsF3u1Eh2oiLos=, tarball: http://r.npm.sankuai.com/@nodelib/fs.stat/download/@nodelib/fs.stat-2.0.5.tgz}
    engines: {node: '>= 8'}

  '@nodelib/fs.walk@1.2.8':
    resolution: {integrity: sha1-6Vc36LtnRt3t9pxVaVNJTxlv5po=, tarball: http://r.npm.sankuai.com/@nodelib/fs.walk/download/@nodelib/fs.walk-1.2.8.tgz}
    engines: {node: '>= 8'}

  '@pkgjs/parseargs@0.11.0':
    resolution: {integrity: sha1-p36nQvqyV3UUVDTrHSMoz1ATrDM=, tarball: http://r.npm.sankuai.com/@pkgjs/parseargs/download/@pkgjs/parseargs-0.11.0.tgz}
    engines: {node: '>=14'}

  '@rc-component/async-validator@5.0.4':
    resolution: {integrity: sha1-UpGtkvAKFLZ2b8gXNcI0J3+D6Ug=, tarball: http://r.npm.sankuai.com/@rc-component/async-validator/download/@rc-component/async-validator-5.0.4.tgz}
    engines: {node: '>=14.x'}

  '@rc-component/color-picker@2.0.1':
    resolution: {integrity: sha1-a5uWFSRmqdRHXL5ytAtZS/2hZL4=, tarball: http://r.npm.sankuai.com/@rc-component/color-picker/download/@rc-component/color-picker-2.0.1.tgz}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'

  '@rc-component/context@1.4.0':
    resolution: {integrity: sha1-3G+wIdZ3NUavjwFq5M6a6giDleg=, tarball: http://r.npm.sankuai.com/@rc-component/context/download/@rc-component/context-1.4.0.tgz}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'

  '@rc-component/mini-decimal@1.1.0':
    resolution: {integrity: sha1-e3o2KxSgpUy1vG/SuCcx8p8R2bA=, tarball: http://r.npm.sankuai.com/@rc-component/mini-decimal/download/@rc-component/mini-decimal-1.1.0.tgz}
    engines: {node: '>=8.x'}

  '@rc-component/mutate-observer@1.1.0':
    resolution: {integrity: sha1-7lPMiLeKrePNBlNgkhWkR3k4b9g=, tarball: http://r.npm.sankuai.com/@rc-component/mutate-observer/download/@rc-component/mutate-observer-1.1.0.tgz}
    engines: {node: '>=8.x'}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'

  '@rc-component/portal@1.1.2':
    resolution: {integrity: sha1-VdseUdeE4DRELpcAU2+qpqtj/HE=, tarball: http://r.npm.sankuai.com/@rc-component/portal/download/@rc-component/portal-1.1.2.tgz}
    engines: {node: '>=8.x'}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'

  '@rc-component/qrcode@1.0.0':
    resolution: {integrity: sha1-SKjeXrEdDmWSbxN3xLHvTIiJl/U=, tarball: http://r.npm.sankuai.com/@rc-component/qrcode/download/@rc-component/qrcode-1.0.0.tgz}
    engines: {node: '>=8.x'}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'

  '@rc-component/tour@1.15.1':
    resolution: {integrity: sha1-m3mAglQYX8GelkFy2Z4l6MaADe0=, tarball: http://r.npm.sankuai.com/@rc-component/tour/download/@rc-component/tour-1.15.1.tgz}
    engines: {node: '>=8.x'}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'

  '@rc-component/trigger@2.2.6':
    resolution: {integrity: sha1-v+ZgIxOz+t1llod0ZRH4EymdXqQ=, tarball: http://r.npm.sankuai.com/@rc-component/trigger/download/@rc-component/trigger-2.2.6.tgz}
    engines: {node: '>=8.x'}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'

  '@rollup/plugin-typescript@12.1.2':
    resolution: {integrity: sha1-667sLnN2+qiJAwzNfLSFpknmMRg=, tarball: http://r.npm.sankuai.com/@rollup/plugin-typescript/download/@rollup/plugin-typescript-12.1.2.tgz}
    engines: {node: '>=14.0.0'}
    peerDependencies:
      rollup: ^2.14.0||^3.0.0||^4.0.0
      tslib: '*'
      typescript: '>=3.7.0'
    peerDependenciesMeta:
      rollup:
        optional: true
      tslib:
        optional: true

  '@rollup/pluginutils@5.1.4':
    resolution: {integrity: sha1-u5Tx+eqqyUTaI3dnzf7mxbImLUo=, tarball: http://r.npm.sankuai.com/@rollup/pluginutils/download/@rollup/pluginutils-5.1.4.tgz}
    engines: {node: '>=14.0.0'}
    peerDependencies:
      rollup: ^1.20.0||^2.0.0||^3.0.0||^4.0.0
    peerDependenciesMeta:
      rollup:
        optional: true

  '@rollup/rollup-android-arm-eabi@4.30.1':
    resolution: {integrity: sha1-FMc33BlgOgllaAROrapgOV7vuAk=, tarball: http://r.npm.sankuai.com/@rollup/rollup-android-arm-eabi/download/@rollup/rollup-android-arm-eabi-4.30.1.tgz}
    cpu: [arm]
    os: [android]

  '@rollup/rollup-android-arm64@4.30.1':
    resolution: {integrity: sha1-nYHqVPxWUOtOu8Cn2EzuMxv6MK0=, tarball: http://r.npm.sankuai.com/@rollup/rollup-android-arm64/download/@rollup/rollup-android-arm64-4.30.1.tgz}
    cpu: [arm64]
    os: [android]

  '@rollup/rollup-darwin-arm64@4.30.1':
    resolution: {integrity: sha1-KUSMsTcM9ni1B0PS45K+GEcKvCM=, tarball: http://r.npm.sankuai.com/@rollup/rollup-darwin-arm64/download/@rollup/rollup-darwin-arm64-4.30.1.tgz}
    cpu: [arm64]
    os: [darwin]

  '@rollup/rollup-darwin-x64@4.30.1':
    resolution: {integrity: sha1-DKmXQcPtCWcAVXpDuwM1lFDHhX0=, tarball: http://r.npm.sankuai.com/@rollup/rollup-darwin-x64/download/@rollup/rollup-darwin-x64-4.30.1.tgz}
    cpu: [x64]
    os: [darwin]

  '@rollup/rollup-freebsd-arm64@4.30.1':
    resolution: {integrity: sha1-Iz+OTC9UrZtxnNlkWIfcvRKzgAM=, tarball: http://r.npm.sankuai.com/@rollup/rollup-freebsd-arm64/download/@rollup/rollup-freebsd-arm64-4.30.1.tgz}
    cpu: [arm64]
    os: [freebsd]

  '@rollup/rollup-freebsd-x64@4.30.1':
    resolution: {integrity: sha1-37p2KgIwY9yQFhByKZUobfSkg2A=, tarball: http://r.npm.sankuai.com/@rollup/rollup-freebsd-x64/download/@rollup/rollup-freebsd-x64-4.30.1.tgz}
    cpu: [x64]
    os: [freebsd]

  '@rollup/rollup-linux-arm-gnueabihf@4.30.1':
    resolution: {integrity: sha1-udpUFxcmJmxe9CN/RiqFs8PPask=, tarball: http://r.npm.sankuai.com/@rollup/rollup-linux-arm-gnueabihf/download/@rollup/rollup-linux-arm-gnueabihf-4.30.1.tgz}
    cpu: [arm]
    os: [linux]
    libc: [glibc]

  '@rollup/rollup-linux-arm-musleabihf@4.30.1':
    resolution: {integrity: sha1-udtps/hfVSnrmSk22PQR7m0EKXs=, tarball: http://r.npm.sankuai.com/@rollup/rollup-linux-arm-musleabihf/download/@rollup/rollup-linux-arm-musleabihf-4.30.1.tgz}
    cpu: [arm]
    os: [linux]
    libc: [musl]

  '@rollup/rollup-linux-arm64-gnu@4.30.1':
    resolution: {integrity: sha1-JVDPm7TUfZF/0atK91bXu8PuFSg=, tarball: http://r.npm.sankuai.com/@rollup/rollup-linux-arm64-gnu/download/@rollup/rollup-linux-arm64-gnu-4.30.1.tgz}
    cpu: [arm64]
    os: [linux]
    libc: [glibc]

  '@rollup/rollup-linux-arm64-musl@4.30.1':
    resolution: {integrity: sha1-nQaybShsfd7WM2lhovg+SDMODIA=, tarball: http://r.npm.sankuai.com/@rollup/rollup-linux-arm64-musl/download/@rollup/rollup-linux-arm64-musl-4.30.1.tgz}
    cpu: [arm64]
    os: [linux]
    libc: [musl]

  '@rollup/rollup-linux-loongarch64-gnu@4.30.1':
    resolution: {integrity: sha1-6Ve7j+4MgCEymjTKjfqCWCbuDi4=, tarball: http://r.npm.sankuai.com/@rollup/rollup-linux-loongarch64-gnu/download/@rollup/rollup-linux-loongarch64-gnu-4.30.1.tgz}
    cpu: [loong64]
    os: [linux]
    libc: [glibc]

  '@rollup/rollup-linux-powerpc64le-gnu@4.30.1':
    resolution: {integrity: sha1-6FhQdd37OJIixaraOepi1tJRHMw=, tarball: http://r.npm.sankuai.com/@rollup/rollup-linux-powerpc64le-gnu/download/@rollup/rollup-linux-powerpc64le-gnu-4.30.1.tgz}
    cpu: [ppc64]
    os: [linux]
    libc: [glibc]

  '@rollup/rollup-linux-riscv64-gnu@4.30.1':
    resolution: {integrity: sha1-fQ1AzueUbMqlpOGaNcaSVERpap4=, tarball: http://r.npm.sankuai.com/@rollup/rollup-linux-riscv64-gnu/download/@rollup/rollup-linux-riscv64-gnu-4.30.1.tgz}
    cpu: [riscv64]
    os: [linux]
    libc: [glibc]

  '@rollup/rollup-linux-s390x-gnu@4.30.1':
    resolution: {integrity: sha1-wtzYpLCLLyd47Ot6Wl395iQOveo=, tarball: http://r.npm.sankuai.com/@rollup/rollup-linux-s390x-gnu/download/@rollup/rollup-linux-s390x-gnu-4.30.1.tgz}
    cpu: [s390x]
    os: [linux]
    libc: [glibc]

  '@rollup/rollup-linux-x64-gnu@4.30.1':
    resolution: {integrity: sha1-GDY32RRWh3y4PQoDFetHiFc6pYg=, tarball: http://r.npm.sankuai.com/@rollup/rollup-linux-x64-gnu/download/@rollup/rollup-linux-x64-gnu-4.30.1.tgz}
    cpu: [x64]
    os: [linux]
    libc: [glibc]

  '@rollup/rollup-linux-x64-musl@4.30.1':
    resolution: {integrity: sha1-A2pMhgZiUZ8flFOAdUf9KhHVuwE=, tarball: http://r.npm.sankuai.com/@rollup/rollup-linux-x64-musl/download/@rollup/rollup-linux-x64-musl-4.30.1.tgz}
    cpu: [x64]
    os: [linux]
    libc: [musl]

  '@rollup/rollup-win32-arm64-msvc@4.30.1':
    resolution: {integrity: sha1-UcrYEkVuYWv+TbUjj7nHSX4EKlI=, tarball: http://r.npm.sankuai.com/@rollup/rollup-win32-arm64-msvc/download/@rollup/rollup-win32-arm64-msvc-4.30.1.tgz}
    cpu: [arm64]
    os: [win32]

  '@rollup/rollup-win32-ia32-msvc@4.30.1':
    resolution: {integrity: sha1-ZhyLPkzWD1HeqjnRU6rEVm50jl4=, tarball: http://r.npm.sankuai.com/@rollup/rollup-win32-ia32-msvc/download/@rollup/rollup-win32-ia32-msvc-4.30.1.tgz}
    cpu: [ia32]
    os: [win32]

  '@rollup/rollup-win32-x64-msvc@4.30.1':
    resolution: {integrity: sha1-c78Yhf8FK4L7sPgvhnH3PDbpE3w=, tarball: http://r.npm.sankuai.com/@rollup/rollup-win32-x64-msvc/download/@rollup/rollup-win32-x64-msvc-4.30.1.tgz}
    cpu: [x64]
    os: [win32]

  '@ss/mtd-adapter-vue3@1.2.19':
    resolution: {integrity: sha1-hW6DQ6pAj6wc65kWuMIxk/3uccQ=, tarball: http://r.npm.sankuai.com/@ss/mtd-adapter-vue3/download/@ss/mtd-adapter-vue3-1.2.19.tgz}
    peerDependencies:
      vue: ^3.2.0
      vue-router: ^4.1.6

  '@ss/mtd-vue3@1.2.36':
    resolution: {integrity: sha1-A9g196APPMSkvEPXF0P7USOlWSM=, tarball: http://r.npm.sankuai.com/@ss/mtd-vue3/download/@ss/mtd-vue3-1.2.36.tgz}
    peerDependencies:
      vue: ^3.2.0

  '@stylistic/eslint-plugin@2.12.1':
    resolution: {integrity: sha1-40G+tOQxUITYviC87u2n2KRvB58=, tarball: http://r.npm.sankuai.com/@stylistic/eslint-plugin/download/@stylistic/eslint-plugin-2.12.1.tgz}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      eslint: '>=8.40.0'

  '@types/babel__core@7.20.5':
    resolution: {integrity: sha1-PfFfJ7qFMZyqB7oI0HIYibs5wBc=, tarball: http://r.npm.sankuai.com/@types/babel__core/download/@types/babel__core-7.20.5.tgz}

  '@types/babel__generator@7.6.8':
    resolution: {integrity: sha1-+DbGH0ixNG59Kw2TxtrMW5U106s=, tarball: http://r.npm.sankuai.com/@types/babel__generator/download/@types/babel__generator-7.6.8.tgz}

  '@types/babel__template@7.4.4':
    resolution: {integrity: sha1-VnJRNwHBshmbxtrWNqnXSRWGdm8=, tarball: http://r.npm.sankuai.com/@types/babel__template/download/@types/babel__template-7.4.4.tgz}

  '@types/babel__traverse@7.20.6':
    resolution: {integrity: sha1-jcnwrg8gLAjY1Nq2SJEsjWA44/c=, tarball: http://r.npm.sankuai.com/@types/babel__traverse/download/@types/babel__traverse-7.20.6.tgz}

  '@types/cookie@0.6.0':
    resolution: {integrity: sha1-6sOX8ovx1q4K4IE2PsovQlvt8NU=, tarball: http://r.npm.sankuai.com/@types/cookie/download/@types/cookie-0.6.0.tgz}

  '@types/estree@1.0.6':
    resolution: {integrity: sha1-Yo7/7q4gZKG055946B2Ht+X8e1A=, tarball: http://r.npm.sankuai.com/@types/estree/download/@types/estree-1.0.6.tgz}

  '@types/glob@7.2.0':
    resolution: {integrity: sha1-vBtb86qS8lvV3TnzXFc2G9zlsus=, tarball: http://r.npm.sankuai.com/@types/glob/download/@types/glob-7.2.0.tgz}

  '@types/json-schema@7.0.15':
    resolution: {integrity: sha1-WWoXRyM2lNUPatinhp/Lb1bPWEE=, tarball: http://r.npm.sankuai.com/@types/json-schema/download/@types/json-schema-7.0.15.tgz}

  '@types/minimatch@5.1.2':
    resolution: {integrity: sha1-B1CLRXl8uB7D8nMBGwVM0HVe3co=, tarball: http://r.npm.sankuai.com/@types/minimatch/download/@types/minimatch-5.1.2.tgz}

  '@types/node@22.10.2':
    resolution: {integrity: sha1-pIVCbm0f2vx7DUx7JOLHgYLdq7k=, tarball: http://r.npm.sankuai.com/@types/node/download/@types/node-22.10.2.tgz}

  '@types/prop-types@15.7.14':
    resolution: {integrity: sha1-FDNBnXOyp+v8aRjc79LsDVzWmPI=, tarball: http://r.npm.sankuai.com/@types/prop-types/download/@types/prop-types-15.7.14.tgz}

  '@types/react-dom@18.3.5':
    resolution: {integrity: sha1-Rfn4c5jF3OoIW3FcWN3PH69l9xY=, tarball: http://r.npm.sankuai.com/@types/react-dom/download/@types/react-dom-18.3.5.tgz}
    peerDependencies:
      '@types/react': ^18.0.0

  '@types/react@18.3.18':
    resolution: {integrity: sha1-mzgsTNMuE+Rj+X3wfC7ju80mkEs=, tarball: http://r.npm.sankuai.com/@types/react/download/@types/react-18.3.18.tgz}

  '@typescript-eslint/eslint-plugin@8.18.2':
    resolution: {integrity: sha1-x442OrX+OyHdHJDYvpWBU0QX944=, tarball: http://r.npm.sankuai.com/@typescript-eslint/eslint-plugin/download/@typescript-eslint/eslint-plugin-8.18.2.tgz}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      '@typescript-eslint/parser': ^8.0.0 || ^8.0.0-alpha.0
      eslint: ^8.57.0 || ^9.0.0
      typescript: '>=4.8.4 <5.8.0'

  '@typescript-eslint/parser@5.62.0':
    resolution: {integrity: sha1-G2PQgthJovyuilaSSPvi7huKVsc=, tarball: http://r.npm.sankuai.com/@typescript-eslint/parser/download/@typescript-eslint/parser-5.62.0.tgz}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    peerDependencies:
      eslint: ^6.0.0 || ^7.0.0 || ^8.0.0
      typescript: '*'
    peerDependenciesMeta:
      typescript:
        optional: true

  '@typescript-eslint/parser@8.18.2':
    resolution: {integrity: sha1-A3mi6IHVHY/Pfr36DdGO7nkYLOI=, tarball: http://r.npm.sankuai.com/@typescript-eslint/parser/download/@typescript-eslint/parser-8.18.2.tgz}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      eslint: ^8.57.0 || ^9.0.0
      typescript: '>=4.8.4 <5.8.0'

  '@typescript-eslint/scope-manager@5.62.0':
    resolution: {integrity: sha1-2UV8zGoLjWs30OslKiMCJHjFRgw=, tarball: http://r.npm.sankuai.com/@typescript-eslint/scope-manager/download/@typescript-eslint/scope-manager-5.62.0.tgz}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}

  '@typescript-eslint/scope-manager@8.18.2':
    resolution: {integrity: sha1-0ZPCANYesN3sWYfI5IydThwFEL0=, tarball: http://r.npm.sankuai.com/@typescript-eslint/scope-manager/download/@typescript-eslint/scope-manager-8.18.2.tgz}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@typescript-eslint/type-utils@8.18.2':
    resolution: {integrity: sha1-WtB+CQAu7iN1kYgd9nTBwMkcpS8=, tarball: http://r.npm.sankuai.com/@typescript-eslint/type-utils/download/@typescript-eslint/type-utils-8.18.2.tgz}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      eslint: ^8.57.0 || ^9.0.0
      typescript: '>=4.8.4 <5.8.0'

  '@typescript-eslint/types@5.62.0':
    resolution: {integrity: sha1-JYYH5g7/ownwZ2CJMcPfb+1B/S8=, tarball: http://r.npm.sankuai.com/@typescript-eslint/types/download/@typescript-eslint/types-5.62.0.tgz}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}

  '@typescript-eslint/types@8.18.2':
    resolution: {integrity: sha1-XrrVs4TIqhwPhs7hxhvNvnUR9Uc=, tarball: http://r.npm.sankuai.com/@typescript-eslint/types/download/@typescript-eslint/types-8.18.2.tgz}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@typescript-eslint/typescript-estree@5.62.0':
    resolution: {integrity: sha1-fRd5S3f6vKxhXWpI+xQzMNli65s=, tarball: http://r.npm.sankuai.com/@typescript-eslint/typescript-estree/download/@typescript-eslint/typescript-estree-5.62.0.tgz}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    peerDependencies:
      typescript: '*'
    peerDependenciesMeta:
      typescript:
        optional: true

  '@typescript-eslint/typescript-estree@8.18.2':
    resolution: {integrity: sha1-//uFUn+DBOKb+73HEvRRXan4tHw=, tarball: http://r.npm.sankuai.com/@typescript-eslint/typescript-estree/download/@typescript-eslint/typescript-estree-8.18.2.tgz}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      typescript: '>=4.8.4 <5.8.0'

  '@typescript-eslint/utils@8.18.2':
    resolution: {integrity: sha1-omNfcZBKhPnkf+G29lptlE/xrfk=, tarball: http://r.npm.sankuai.com/@typescript-eslint/utils/download/@typescript-eslint/utils-8.18.2.tgz}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      eslint: ^8.57.0 || ^9.0.0
      typescript: '>=4.8.4 <5.8.0'

  '@typescript-eslint/visitor-keys@5.62.0':
    resolution: {integrity: sha1-IXQBGRfOWCh1lU/+L2kS1ZMeNT4=, tarball: http://r.npm.sankuai.com/@typescript-eslint/visitor-keys/download/@typescript-eslint/visitor-keys-5.62.0.tgz}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}

  '@typescript-eslint/visitor-keys@8.18.2':
    resolution: {integrity: sha1-s+Q0twHwhrEKfIJBbrxWiZ0n7y8=, tarball: http://r.npm.sankuai.com/@typescript-eslint/visitor-keys/download/@typescript-eslint/visitor-keys-8.18.2.tgz}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@ungap/structured-clone@1.2.1':
    resolution: {integrity: sha1-KPoYX2far3t6GowdRFEyxdl5+L0=, tarball: http://r.npm.sankuai.com/@ungap/structured-clone/download/@ungap/structured-clone-1.2.1.tgz}

  '@vitejs/plugin-react@4.3.4':
    resolution: {integrity: sha1-xkvhC1TEZAE1pbKKJDIzDoitfCA=, tarball: http://r.npm.sankuai.com/@vitejs/plugin-react/download/@vitejs/plugin-react-4.3.4.tgz}
    engines: {node: ^14.18.0 || >=16.0.0}
    peerDependencies:
      vite: ^4.2.0 || ^5.0.0 || ^6.0.0

  '@vitejs/plugin-vue@5.2.1':
    resolution: {integrity: sha1-0UkfZ47jr4mfeuV9nCHcUqZccTM=, tarball: http://r.npm.sankuai.com/@vitejs/plugin-vue/download/@vitejs/plugin-vue-5.2.1.tgz}
    engines: {node: ^18.0.0 || >=20.0.0}
    peerDependencies:
      vite: ^5.0.0 || ^6.0.0
      vue: ^3.2.25

  '@vue/babel-helper-vue-transform-on@1.2.5':
    resolution: {integrity: sha1-ueGVuSv6jRXVqpWBygHLcC28wZ0=, tarball: http://r.npm.sankuai.com/@vue/babel-helper-vue-transform-on/download/@vue/babel-helper-vue-transform-on-1.2.5.tgz}

  '@vue/babel-plugin-jsx@1.2.5':
    resolution: {integrity: sha1-d/T58YnQDCTr1YerhK5hXfocOrs=, tarball: http://r.npm.sankuai.com/@vue/babel-plugin-jsx/download/@vue/babel-plugin-jsx-1.2.5.tgz}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    peerDependenciesMeta:
      '@babel/core':
        optional: true

  '@vue/babel-plugin-resolve-type@1.2.5':
    resolution: {integrity: sha1-9u0NOZh/4BWDcGWbcxVsVegNF7U=, tarball: http://r.npm.sankuai.com/@vue/babel-plugin-resolve-type/download/@vue/babel-plugin-resolve-type-1.2.5.tgz}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@vue/compiler-core@3.5.13':
    resolution: {integrity: sha1-sK5sQ0f2DAPoSaBdNOW/dHyb2gU=, tarball: http://r.npm.sankuai.com/@vue/compiler-core/download/@vue/compiler-core-3.5.13.tgz}

  '@vue/compiler-dom@3.5.13':
    resolution: {integrity: sha1-uxuHWNvFQrNljdqXO5ihyTEailg=, tarball: http://r.npm.sankuai.com/@vue/compiler-dom/download/@vue/compiler-dom-3.5.13.tgz}

  '@vue/compiler-sfc@3.5.13':
    resolution: {integrity: sha1-Rh+L00O1wG+sQYnE/vivMt6oK0Y=, tarball: http://r.npm.sankuai.com/@vue/compiler-sfc/download/@vue/compiler-sfc-3.5.13.tgz}

  '@vue/compiler-ssr@3.5.13':
    resolution: {integrity: sha1-53GtzKbT0AD5GkJ3yXKpltB/Q7o=, tarball: http://r.npm.sankuai.com/@vue/compiler-ssr/download/@vue/compiler-ssr-3.5.13.tgz}

  '@vue/devtools-api@6.6.4':
    resolution: {integrity: sha1-y+l/4BYrNl7cHbqA4XP5BJJTU0M=, tarball: http://r.npm.sankuai.com/@vue/devtools-api/download/@vue/devtools-api-6.6.4.tgz}

  '@vue/reactivity@3.5.13':
    resolution: {integrity: sha1-tB/yu4ZeCTiZoiIZ9bJfl7b+FV8=, tarball: http://r.npm.sankuai.com/@vue/reactivity/download/@vue/reactivity-3.5.13.tgz}

  '@vue/runtime-core@3.5.13':
    resolution: {integrity: sha1-H6+kvwuXrw692dv+mM1jDaNjpFU=, tarball: http://r.npm.sankuai.com/@vue/runtime-core/download/@vue/runtime-core-3.5.13.tgz}

  '@vue/runtime-dom@3.5.13':
    resolution: {integrity: sha1-YQ/Hld6SRjAOiuiGWTDVNOEkYhU=, tarball: http://r.npm.sankuai.com/@vue/runtime-dom/download/@vue/runtime-dom-3.5.13.tgz}

  '@vue/server-renderer@3.5.13':
    resolution: {integrity: sha1-Qp6tYu5R3niWRsIu/pCOSJqtRvc=, tarball: http://r.npm.sankuai.com/@vue/server-renderer/download/@vue/server-renderer-3.5.13.tgz}
    peerDependencies:
      vue: 3.5.13

  '@vue/shared@3.5.13':
    resolution: {integrity: sha1-h7MJpjecIrkm5paJMjeCb2Qzm28=, tarball: http://r.npm.sankuai.com/@vue/shared/download/@vue/shared-3.5.13.tgz}

  acorn-jsx@5.3.2:
    resolution: {integrity: sha1-ftW7VZCLOy8bxVxq8WU7rafweTc=, tarball: http://r.npm.sankuai.com/acorn-jsx/download/acorn-jsx-5.3.2.tgz}
    peerDependencies:
      acorn: ^6.0.0 || ^7.0.0 || ^8.0.0

  acorn@8.14.0:
    resolution: {integrity: sha1-Bj4scMrF+09kZ/CxEVLgTGgnlbA=, tarball: http://r.npm.sankuai.com/acorn/download/acorn-8.14.0.tgz}
    engines: {node: '>=0.4.0'}
    hasBin: true

  ajv@6.12.6:
    resolution: {integrity: sha1-uvWmLoArB9l3A0WG+MO69a3ybfQ=, tarball: http://r.npm.sankuai.com/ajv/download/ajv-6.12.6.tgz}

  ansi-color@0.2.1:
    resolution: {integrity: sha1-PnXAN0dSF1RO12Oo21cJ+prlv5o=, tarball: http://r.npm.sankuai.com/ansi-color/download/ansi-color-0.2.1.tgz}

  ansi-escapes@4.3.2:
    resolution: {integrity: sha1-ayKR0dt9mLZSHV8e+kLQ86n+tl4=, tarball: http://r.npm.sankuai.com/ansi-escapes/download/ansi-escapes-4.3.2.tgz}
    engines: {node: '>=8'}

  ansi-escapes@7.0.0:
    resolution: {integrity: sha1-APwZ9JG7sY4dSBuXhoIE+SEJv+c=, tarball: http://r.npm.sankuai.com/ansi-escapes/download/ansi-escapes-7.0.0.tgz}
    engines: {node: '>=18'}

  ansi-regex@5.0.1:
    resolution: {integrity: sha1-CCyyyJyf6GWaMRpTvWpNxTAdswQ=, tarball: http://r.npm.sankuai.com/ansi-regex/download/ansi-regex-5.0.1.tgz}
    engines: {node: '>=8'}

  ansi-regex@6.1.0:
    resolution: {integrity: sha1-lexAnGlhnWyxuLNPFLZg7yjr1lQ=, tarball: http://r.npm.sankuai.com/ansi-regex/download/ansi-regex-6.1.0.tgz}
    engines: {node: '>=12'}

  ansi-styles@3.2.1:
    resolution: {integrity: sha1-QfuyAkPlCxK+DwS43tvwdSDOhB0=, tarball: http://r.npm.sankuai.com/ansi-styles/download/ansi-styles-3.2.1.tgz}
    engines: {node: '>=4'}

  ansi-styles@4.3.0:
    resolution: {integrity: sha1-7dgDYornHATIWuegkG7a00tkiTc=, tarball: http://r.npm.sankuai.com/ansi-styles/download/ansi-styles-4.3.0.tgz}
    engines: {node: '>=8'}

  ansi-styles@6.2.1:
    resolution: {integrity: sha1-DmIyDPmcIa//OzASGSVGqsv7BcU=, tarball: http://r.npm.sankuai.com/ansi-styles/download/ansi-styles-6.2.1.tgz}
    engines: {node: '>=12'}

  antd@5.22.7:
    resolution: {integrity: sha1-kKDrPkzWW3/AQjGBMtNLfEkng90=, tarball: http://r.npm.sankuai.com/antd/download/antd-5.22.7.tgz}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'

  any-promise@1.3.0:
    resolution: {integrity: sha1-q8av7tzqUugJzcA3au0845Y10X8=, tarball: http://r.npm.sankuai.com/any-promise/download/any-promise-1.3.0.tgz}

  anymatch@3.1.3:
    resolution: {integrity: sha1-eQxYsZuhcgqEIFtXxhjVrYUklz4=, tarball: http://r.npm.sankuai.com/anymatch/download/anymatch-3.1.3.tgz}
    engines: {node: '>= 8'}

  arg@5.0.2:
    resolution: {integrity: sha1-yBQzzEJ8ksTc9IZRQtvKbxWs1Zw=, tarball: http://r.npm.sankuai.com/arg/download/arg-5.0.2.tgz}

  argparse@2.0.1:
    resolution: {integrity: sha1-JG9Q88p4oyQPbJl+ipvR6sSeSzg=, tarball: http://r.npm.sankuai.com/argparse/download/argparse-2.0.1.tgz}

  arr-diff@4.0.0:
    resolution: {integrity: sha1-1kYQdP6/7HHn4VI1dhoyml3HxSA=, tarball: http://r.npm.sankuai.com/arr-diff/download/arr-diff-4.0.0.tgz}
    engines: {node: '>=0.10.0'}

  arr-flatten@1.1.0:
    resolution: {integrity: sha1-NgSLv/TntH4TZkQxbJlmnqWukfE=, tarball: http://r.npm.sankuai.com/arr-flatten/download/arr-flatten-1.1.0.tgz}
    engines: {node: '>=0.10.0'}

  arr-union@3.1.0:
    resolution: {integrity: sha1-45sJrqne+Gao8gbiiK9jkZuuOcQ=, tarball: http://r.npm.sankuai.com/arr-union/download/arr-union-3.1.0.tgz}
    engines: {node: '>=0.10.0'}

  array-buffer-byte-length@1.0.2:
    resolution: {integrity: sha1-OE0So3KVrsN2mrAirTI6GKUcz4s=, tarball: http://r.npm.sankuai.com/array-buffer-byte-length/download/array-buffer-byte-length-1.0.2.tgz}
    engines: {node: '>= 0.4'}

  array-includes@3.1.8:
    resolution: {integrity: sha1-XjcMvhcv3V3WUwwdSq3aJSgbqX0=, tarball: http://r.npm.sankuai.com/array-includes/download/array-includes-3.1.8.tgz}
    engines: {node: '>= 0.4'}

  array-union@1.0.2:
    resolution: {integrity: sha1-mjRBDk9OPaI96jdb5b5w8kd47Dk=, tarball: http://r.npm.sankuai.com/array-union/download/array-union-1.0.2.tgz}
    engines: {node: '>=0.10.0'}

  array-union@2.1.0:
    resolution: {integrity: sha1-t5hCCtvrHego2ErNii4j0+/oXo0=, tarball: http://r.npm.sankuai.com/array-union/download/array-union-2.1.0.tgz}
    engines: {node: '>=8'}

  array-uniq@1.0.3:
    resolution: {integrity: sha1-r2rId6Jcx/dOBYiUdThY39sk/bY=, tarball: http://r.npm.sankuai.com/array-uniq/download/array-uniq-1.0.3.tgz}
    engines: {node: '>=0.10.0'}

  array-unique@0.3.2:
    resolution: {integrity: sha1-qJS3XUvE9s1nnvMkSp/Y9Gri1Cg=, tarball: http://r.npm.sankuai.com/array-unique/download/array-unique-0.3.2.tgz}
    engines: {node: '>=0.10.0'}

  array.prototype.findlast@1.2.5:
    resolution: {integrity: sha1-Pk+8swoVp/W/ZM8vquItE5wuSQQ=, tarball: http://r.npm.sankuai.com/array.prototype.findlast/download/array.prototype.findlast-1.2.5.tgz}
    engines: {node: '>= 0.4'}

  array.prototype.flat@1.3.3:
    resolution: {integrity: sha1-U0qvnm6N15+2uamRf4Oe8exjr+U=, tarball: http://r.npm.sankuai.com/array.prototype.flat/download/array.prototype.flat-1.3.3.tgz}
    engines: {node: '>= 0.4'}

  array.prototype.flatmap@1.3.3:
    resolution: {integrity: sha1-cSzHkq5wNwrkBYYmRinjOqtd04s=, tarball: http://r.npm.sankuai.com/array.prototype.flatmap/download/array.prototype.flatmap-1.3.3.tgz}
    engines: {node: '>= 0.4'}

  array.prototype.tosorted@1.1.4:
    resolution: {integrity: sha1-/pVGeP9TA05xfqM1KgPwsLhvf/w=, tarball: http://r.npm.sankuai.com/array.prototype.tosorted/download/array.prototype.tosorted-1.1.4.tgz}
    engines: {node: '>= 0.4'}

  arraybuffer.prototype.slice@1.0.4:
    resolution: {integrity: sha1-nXYNhNvdBtDL+SyISWFaGnqzGDw=, tarball: http://r.npm.sankuai.com/arraybuffer.prototype.slice/download/arraybuffer.prototype.slice-1.0.4.tgz}
    engines: {node: '>= 0.4'}

  arrify@1.0.1:
    resolution: {integrity: sha1-iYUI2iIm84DfkEcoRWhJwVAaSw0=, tarball: http://r.npm.sankuai.com/arrify/download/arrify-1.0.1.tgz}
    engines: {node: '>=0.10.0'}

  assign-symbols@1.0.0:
    resolution: {integrity: sha1-WWZ/QfrdTyDMvCu5a41Pf3jsA2c=, tarball: http://r.npm.sankuai.com/assign-symbols/download/assign-symbols-1.0.0.tgz}
    engines: {node: '>=0.10.0'}

  async-validator@4.2.5:
    resolution: {integrity: sha1-yW6jMypSFpnQr6rO7VEKVGVsYzk=, tarball: http://r.npm.sankuai.com/async-validator/download/async-validator-4.2.5.tgz}

  asynckit@0.4.0:
    resolution: {integrity: sha1-x57Zf380y48robyXkLzDZkdLS3k=, tarball: http://r.npm.sankuai.com/asynckit/download/asynckit-0.4.0.tgz}

  atob@2.1.2:
    resolution: {integrity: sha1-bZUX654DDSQ2ZmZR6GvZ9vE1M8k=, tarball: http://r.npm.sankuai.com/atob/download/atob-2.1.2.tgz}
    engines: {node: '>= 4.5.0'}
    hasBin: true

  autoprefixer@10.4.20:
    resolution: {integrity: sha1-XK7BTUOXbvQuMty0vWKHjpa+Wzs=, tarball: http://r.npm.sankuai.com/autoprefixer/download/autoprefixer-10.4.20.tgz}
    engines: {node: ^10 || ^12 || >=14}
    hasBin: true
    peerDependencies:
      postcss: ^8.1.0

  available-typed-arrays@1.0.7:
    resolution: {integrity: sha1-pcw3XWoDwu/IelU/PgsVIt7xSEY=, tarball: http://r.npm.sankuai.com/available-typed-arrays/download/available-typed-arrays-1.0.7.tgz}
    engines: {node: '>= 0.4'}

  axios@1.7.9:
    resolution: {integrity: sha1-19BxOAwTKiSszaGyz8FTW3nsZQo=, tarball: http://r.npm.sankuai.com/axios/download/axios-1.7.9.tgz}

  balanced-match@1.0.2:
    resolution: {integrity: sha1-6D46fj8wCzTLnYf2FfoMvzV2kO4=, tarball: http://r.npm.sankuai.com/balanced-match/download/balanced-match-1.0.2.tgz}

  base@0.11.2:
    resolution: {integrity: sha1-e95c7RRbbVUakNuH+DxVi060io8=, tarball: http://r.npm.sankuai.com/base/download/base-0.11.2.tgz}
    engines: {node: '>=0.10.0'}

  binary-extensions@2.3.0:
    resolution: {integrity: sha1-9uFKl4WNMnJSIAJC1Mz+UixEVSI=, tarball: http://r.npm.sankuai.com/binary-extensions/download/binary-extensions-2.3.0.tgz}
    engines: {node: '>=8'}

  boolbase@1.0.0:
    resolution: {integrity: sha1-aN/1++YMUes3cl6p4+0xDcwed24=, tarball: http://r.npm.sankuai.com/boolbase/download/boolbase-1.0.0.tgz}

  brace-expansion@1.1.11:
    resolution: {integrity: sha1-PH/L9SnYcibz0vUrlm/1Jx60Qd0=, tarball: http://r.npm.sankuai.com/brace-expansion/download/brace-expansion-1.1.11.tgz}

  brace-expansion@2.0.1:
    resolution: {integrity: sha1-HtxFng8MVISG7Pn8mfIiE2S5oK4=, tarball: http://r.npm.sankuai.com/brace-expansion/download/brace-expansion-2.0.1.tgz}

  braces@2.3.2:
    resolution: {integrity: sha1-WXn9PxTNUxVl5fot8av/8d+u5yk=, tarball: http://r.npm.sankuai.com/braces/download/braces-2.3.2.tgz}
    engines: {node: '>=0.10.0'}

  braces@3.0.3:
    resolution: {integrity: sha1-SQMy9AkZRSJy1VqEgK3AxEE1h4k=, tarball: http://r.npm.sankuai.com/braces/download/braces-3.0.3.tgz}
    engines: {node: '>=8'}

  browserslist@4.24.3:
    resolution: {integrity: sha1-X8JyXKj7PBQy4T2sJ4x8wQPgJtI=, tarball: http://r.npm.sankuai.com/browserslist/download/browserslist-4.24.3.tgz}
    engines: {node: ^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7}
    hasBin: true

  buffer-builder@0.2.0:
    resolution: {integrity: sha1-MyLNMH2Cltqx9gRhhZOyYaP63o8=, tarball: http://r.npm.sankuai.com/buffer-builder/download/buffer-builder-0.2.0.tgz}

  bundle-name@4.1.0:
    resolution: {integrity: sha1-87lrNBYNZDGhnXaIE1r3z7h5eIk=, tarball: http://r.npm.sankuai.com/bundle-name/download/bundle-name-4.1.0.tgz}
    engines: {node: '>=18'}

  bundle-require@4.2.1:
    resolution: {integrity: sha1-TEUKWAc4HSCt6Ye96Kw5FUQleRk=, tarball: http://r.npm.sankuai.com/bundle-require/download/bundle-require-4.2.1.tgz}
    engines: {node: ^12.20.0 || ^14.13.1 || >=16.0.0}
    peerDependencies:
      esbuild: '>=0.17'

  cache-base@1.0.1:
    resolution: {integrity: sha1-Cn9GQWgxyLZi7jb+TnxZ129marI=, tarball: http://r.npm.sankuai.com/cache-base/download/cache-base-1.0.1.tgz}
    engines: {node: '>=0.10.0'}

  call-bind-apply-helpers@1.0.1:
    resolution: {integrity: sha1-MuWJLmNhspsLVFum93YzeNrKKEA=, tarball: http://r.npm.sankuai.com/call-bind-apply-helpers/download/call-bind-apply-helpers-1.0.1.tgz}
    engines: {node: '>= 0.4'}

  call-bind@1.0.8:
    resolution: {integrity: sha1-BzapZg9TfjOIgm9EDV7EX3ROqkw=, tarball: http://r.npm.sankuai.com/call-bind/download/call-bind-1.0.8.tgz}
    engines: {node: '>= 0.4'}

  call-bound@1.0.3:
    resolution: {integrity: sha1-Qc/QMrWT45F2pxUzq084SqBP1oE=, tarball: http://r.npm.sankuai.com/call-bound/download/call-bound-1.0.3.tgz}
    engines: {node: '>= 0.4'}

  call-me-maybe@1.0.2:
    resolution: {integrity: sha1-A/lk8ZUiumQ7GwaTrLkVL+IHS6o=, tarball: http://r.npm.sankuai.com/call-me-maybe/download/call-me-maybe-1.0.2.tgz}

  callsites@3.1.0:
    resolution: {integrity: sha1-s2MKvYlDQy9Us/BRkjjjPNffL3M=, tarball: http://r.npm.sankuai.com/callsites/download/callsites-3.1.0.tgz}
    engines: {node: '>=6'}

  camelcase-css@2.0.1:
    resolution: {integrity: sha1-7pePaUeRTMMMa0R0G27R338EP9U=, tarball: http://r.npm.sankuai.com/camelcase-css/download/camelcase-css-2.0.1.tgz}
    engines: {node: '>= 6'}

  caniuse-lite@1.0.30001690:
    resolution: {integrity: sha1-8tFeOq+OGPdrK4wUgaveBjuBBMg=, tarball: http://r.npm.sankuai.com/caniuse-lite/download/caniuse-lite-1.0.30001690.tgz}

  chalk@2.4.2:
    resolution: {integrity: sha1-zUJUFnelQzPPVBpJEIwUMrRMlCQ=, tarball: http://r.npm.sankuai.com/chalk/download/chalk-2.4.2.tgz}
    engines: {node: '>=4'}

  chalk@4.1.2:
    resolution: {integrity: sha1-qsTit3NKdAhnrrFr8CqtVWoeegE=, tarball: http://r.npm.sankuai.com/chalk/download/chalk-4.1.2.tgz}
    engines: {node: '>=10'}

  chalk@5.4.1:
    resolution: {integrity: sha1-G0i/CWPsFY3OKqz2nAk64t0gktg=, tarball: http://r.npm.sankuai.com/chalk/download/chalk-5.4.1.tgz}
    engines: {node: ^12.17.0 || ^14.13 || >=16.0.0}

  chardet@0.7.0:
    resolution: {integrity: sha1-kAlISfCTfy7twkJdDSip5fDLrZ4=, tarball: http://r.npm.sankuai.com/chardet/download/chardet-0.7.0.tgz}

  chokidar@3.6.0:
    resolution: {integrity: sha1-GXxsxmnvKo3F57TZfuTgksPrDVs=, tarball: http://r.npm.sankuai.com/chokidar/download/chokidar-3.6.0.tgz}
    engines: {node: '>= 8.10.0'}

  class-utils@0.3.6:
    resolution: {integrity: sha1-+TNprouafOAv1B+q0MqDAzGQxGM=, tarball: http://r.npm.sankuai.com/class-utils/download/class-utils-0.3.6.tgz}
    engines: {node: '>=0.10.0'}

  classnames@2.5.1:
    resolution: {integrity: sha1-undMYUvg8BbaEFyFjnFZ6ujnaHs=, tarball: http://r.npm.sankuai.com/classnames/download/classnames-2.5.1.tgz}

  cli-cursor@5.0.0:
    resolution: {integrity: sha1-JKSDHs9aawHd6zL7caSyCIsNzjg=, tarball: http://r.npm.sankuai.com/cli-cursor/download/cli-cursor-5.0.0.tgz}
    engines: {node: '>=18'}

  cli-spinners@2.9.2:
    resolution: {integrity: sha1-F3Oo9LnE1qwxVj31Oz/B15Ri/kE=, tarball: http://r.npm.sankuai.com/cli-spinners/download/cli-spinners-2.9.2.tgz}
    engines: {node: '>=6'}

  cli-truncate@4.0.0:
    resolution: {integrity: sha1-bMKKKST+6eJc6R6XPbVscGbmFyo=, tarball: http://r.npm.sankuai.com/cli-truncate/download/cli-truncate-4.0.0.tgz}
    engines: {node: '>=18'}

  cli-width@4.1.0:
    resolution: {integrity: sha1-QtqsQdPCVO84rYrAN2chMBc2kcU=, tarball: http://r.npm.sankuai.com/cli-width/download/cli-width-4.1.0.tgz}
    engines: {node: '>= 12'}

  collection-visit@1.0.0:
    resolution: {integrity: sha1-S8A3PBZLwykbTTaMgpzxqApZ3KA=, tarball: http://r.npm.sankuai.com/collection-visit/download/collection-visit-1.0.0.tgz}
    engines: {node: '>=0.10.0'}

  color-convert@1.9.3:
    resolution: {integrity: sha1-u3GFBpDh8TZWfeYp0tVHHe2kweg=, tarball: http://r.npm.sankuai.com/color-convert/download/color-convert-1.9.3.tgz}

  color-convert@2.0.1:
    resolution: {integrity: sha1-ctOmjVmMm9s68q0ehPIdiWq9TeM=, tarball: http://r.npm.sankuai.com/color-convert/download/color-convert-2.0.1.tgz}
    engines: {node: '>=7.0.0'}

  color-name@1.1.3:
    resolution: {integrity: sha1-p9BVi9icQveV3UIyj3QIMcpTvCU=, tarball: http://r.npm.sankuai.com/color-name/download/color-name-1.1.3.tgz}

  color-name@1.1.4:
    resolution: {integrity: sha1-wqCah6y95pVD3m9j+jmVyCbFNqI=, tarball: http://r.npm.sankuai.com/color-name/download/color-name-1.1.4.tgz}

  colorette@2.0.20:
    resolution: {integrity: sha1-nreT5oMwZ/cjWQL807CZF6AAqVo=, tarball: http://r.npm.sankuai.com/colorette/download/colorette-2.0.20.tgz}

  colorjs.io@0.5.2:
    resolution: {integrity: sha1-Y7IBObAHWR68M1mTK++EYo6z/O8=, tarball: http://r.npm.sankuai.com/colorjs.io/download/colorjs.io-0.5.2.tgz}

  combined-stream@1.0.8:
    resolution: {integrity: sha1-w9RaizT9cwYxoRCoolIGgrMdWn8=, tarball: http://r.npm.sankuai.com/combined-stream/download/combined-stream-1.0.8.tgz}
    engines: {node: '>= 0.8'}

  commander@12.1.0:
    resolution: {integrity: sha1-AUI7NvUBJZ/arE0OTWDJbJkVhdM=, tarball: http://r.npm.sankuai.com/commander/download/commander-12.1.0.tgz}
    engines: {node: '>=18'}

  commander@4.1.1:
    resolution: {integrity: sha1-n9YCvZNilOnp70aj9NaWQESxgGg=, tarball: http://r.npm.sankuai.com/commander/download/commander-4.1.1.tgz}
    engines: {node: '>= 6'}

  component-emitter@1.3.1:
    resolution: {integrity: sha1-7x1XlvfZPxNe5vtoQ0CyZAPJfRc=, tarball: http://r.npm.sankuai.com/component-emitter/download/component-emitter-1.3.1.tgz}

  compute-scroll-into-view@3.1.0:
    resolution: {integrity: sha1-dT8R2XJZZVjY/nxry8hJdpCrTIc=, tarball: http://r.npm.sankuai.com/compute-scroll-into-view/download/compute-scroll-into-view-3.1.0.tgz}

  concat-map@0.0.1:
    resolution: {integrity: sha1-2Klr13/Wjfd5OnMDajug1UBdR3s=, tarball: http://r.npm.sankuai.com/concat-map/download/concat-map-0.0.1.tgz}

  connect@3.7.0:
    resolution: {integrity: sha1-XUk0iRDKpeB6AYALAw0MNfIEhPg=, tarball: http://r.npm.sankuai.com/connect/download/connect-3.7.0.tgz}
    engines: {node: '>= 0.10.0'}

  convert-source-map@2.0.0:
    resolution: {integrity: sha1-S1YPZJ/E6RjdCrdc9JYei8iC2Co=, tarball: http://r.npm.sankuai.com/convert-source-map/download/convert-source-map-2.0.0.tgz}

  cookie@1.0.2:
    resolution: {integrity: sha1-JzYHAVMhFr0/H5QWkp0Xav4eRhA=, tarball: http://r.npm.sankuai.com/cookie/download/cookie-1.0.2.tgz}
    engines: {node: '>=18'}

  copy-descriptor@0.1.1:
    resolution: {integrity: sha1-Z29us8OZl8LuGsOpJP1hJHSPV40=, tarball: http://r.npm.sankuai.com/copy-descriptor/download/copy-descriptor-0.1.1.tgz}
    engines: {node: '>=0.10.0'}

  copy-to-clipboard@3.3.3:
    resolution: {integrity: sha1-VaxDoduK5jmkvZlRHBSM3RuDobA=, tarball: http://r.npm.sankuai.com/copy-to-clipboard/download/copy-to-clipboard-3.3.3.tgz}

  cp-file@6.2.0:
    resolution: {integrity: sha1-QNXqSh3vKprN0HulwLAkbvc9wQ0=, tarball: http://r.npm.sankuai.com/cp-file/download/cp-file-6.2.0.tgz}
    engines: {node: '>=6'}

  cpy@7.3.0:
    resolution: {integrity: sha1-YvKEeYa0/50ClxBWiknpqataIQ4=, tarball: http://r.npm.sankuai.com/cpy/download/cpy-7.3.0.tgz}
    engines: {node: '>=6'}

  cross-env@7.0.3:
    resolution: {integrity: sha1-hlJkspZ33AFbqEGJGJZd0jL8VM8=, tarball: http://r.npm.sankuai.com/cross-env/download/cross-env-7.0.3.tgz}
    engines: {node: '>=10.14', npm: '>=6', yarn: '>=1'}
    hasBin: true

  cross-spawn@7.0.6:
    resolution: {integrity: sha1-ilj+ePANzXDDcEUXWd+/rwPo7p8=, tarball: http://r.npm.sankuai.com/cross-spawn/download/cross-spawn-7.0.6.tgz}
    engines: {node: '>= 8'}

  cssesc@3.0.0:
    resolution: {integrity: sha1-N3QZGZA7hoVl4cCep0dEXNGJg+4=, tarball: http://r.npm.sankuai.com/cssesc/download/cssesc-3.0.0.tgz}
    engines: {node: '>=4'}
    hasBin: true

  csstype@3.1.3:
    resolution: {integrity: sha1-2A/ylNEU+w5qxQD7+FtgE31+/4E=, tarball: http://r.npm.sankuai.com/csstype/download/csstype-3.1.3.tgz}

  data-view-buffer@1.0.2:
    resolution: {integrity: sha1-IRoDupXsr3eYqMcZjXlTYhH4hXA=, tarball: http://r.npm.sankuai.com/data-view-buffer/download/data-view-buffer-1.0.2.tgz}
    engines: {node: '>= 0.4'}

  data-view-byte-length@1.0.2:
    resolution: {integrity: sha1-noD3ylJFPOPpPSWjUxh2fqdwRzU=, tarball: http://r.npm.sankuai.com/data-view-byte-length/download/data-view-byte-length-1.0.2.tgz}
    engines: {node: '>= 0.4'}

  data-view-byte-offset@1.0.1:
    resolution: {integrity: sha1-BoMH+bcat2274QKROJ4CCFZgYZE=, tarball: http://r.npm.sankuai.com/data-view-byte-offset/download/data-view-byte-offset-1.0.1.tgz}
    engines: {node: '>= 0.4'}

  dayjs@1.11.13:
    resolution: {integrity: sha1-kkMLATkFXD67YBUKoT6GCktaNmw=, tarball: http://r.npm.sankuai.com/dayjs/download/dayjs-1.11.13.tgz}

  debug@2.6.9:
    resolution: {integrity: sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=, tarball: http://r.npm.sankuai.com/debug/download/debug-2.6.9.tgz}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true

  debug@4.4.0:
    resolution: {integrity: sha1-Kz8q6i/+t3ZHdGAmc3fchxD6uoo=, tarball: http://r.npm.sankuai.com/debug/download/debug-4.4.0.tgz}
    engines: {node: '>=6.0'}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true

  decode-uri-component@0.2.2:
    resolution: {integrity: sha1-5p2+JdN5QRcd1UDgJMREzVGI4ek=, tarball: http://r.npm.sankuai.com/decode-uri-component/download/decode-uri-component-0.2.2.tgz}
    engines: {node: '>=0.10'}

  deep-is@0.1.4:
    resolution: {integrity: sha1-pvLc5hL63S7x9Rm3NVHxfoUZmDE=, tarball: http://r.npm.sankuai.com/deep-is/download/deep-is-0.1.4.tgz}

  default-browser-id@5.0.0:
    resolution: {integrity: sha1-odmL+WDBUILYo/pp6DFQzMzDryY=, tarball: http://r.npm.sankuai.com/default-browser-id/download/default-browser-id-5.0.0.tgz}
    engines: {node: '>=18'}

  default-browser@5.2.1:
    resolution: {integrity: sha1-e3umEgT/PkJbVWhprm0+nZ8XEs8=, tarball: http://r.npm.sankuai.com/default-browser/download/default-browser-5.2.1.tgz}
    engines: {node: '>=18'}

  define-data-property@1.1.4:
    resolution: {integrity: sha1-iU3BQbt9MGCuQ2b2oBB+aPvkjF4=, tarball: http://r.npm.sankuai.com/define-data-property/download/define-data-property-1.1.4.tgz}
    engines: {node: '>= 0.4'}

  define-lazy-prop@3.0.0:
    resolution: {integrity: sha1-27Ga37dG1/xtc0oGty9KANAhJV8=, tarball: http://r.npm.sankuai.com/define-lazy-prop/download/define-lazy-prop-3.0.0.tgz}
    engines: {node: '>=12'}

  define-properties@1.2.1:
    resolution: {integrity: sha1-EHgcxhbrlRqAoDS6/Kpzd/avK2w=, tarball: http://r.npm.sankuai.com/define-properties/download/define-properties-1.2.1.tgz}
    engines: {node: '>= 0.4'}

  define-property@0.2.5:
    resolution: {integrity: sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=, tarball: http://r.npm.sankuai.com/define-property/download/define-property-0.2.5.tgz}
    engines: {node: '>=0.10.0'}

  define-property@1.0.0:
    resolution: {integrity: sha1-dp66rz9KY6rTr56NMEybvnm/sOY=, tarball: http://r.npm.sankuai.com/define-property/download/define-property-1.0.0.tgz}
    engines: {node: '>=0.10.0'}

  define-property@2.0.2:
    resolution: {integrity: sha1-1Flono1lS6d+AqgX+HENcCyxbp0=, tarball: http://r.npm.sankuai.com/define-property/download/define-property-2.0.2.tgz}
    engines: {node: '>=0.10.0'}

  delayed-stream@1.0.0:
    resolution: {integrity: sha1-3zrhmayt+31ECqrgsp4icrJOxhk=, tarball: http://r.npm.sankuai.com/delayed-stream/download/delayed-stream-1.0.0.tgz}
    engines: {node: '>=0.4.0'}

  didyoumean@1.2.2:
    resolution: {integrity: sha1-mJNG/+noObRVXs9WZu3qDT6K0Dc=, tarball: http://r.npm.sankuai.com/didyoumean/download/didyoumean-1.2.2.tgz}

  dir-glob@2.2.2:
    resolution: {integrity: sha1-+gnwaUFTyJGLGLoN6vrpR2n8UMQ=, tarball: http://r.npm.sankuai.com/dir-glob/download/dir-glob-2.2.2.tgz}
    engines: {node: '>=4'}

  dir-glob@3.0.1:
    resolution: {integrity: sha1-Vtv3PZkqSpO6FYT0U0Bj/S5BcX8=, tarball: http://r.npm.sankuai.com/dir-glob/download/dir-glob-3.0.1.tgz}
    engines: {node: '>=8'}

  dlv@1.1.3:
    resolution: {integrity: sha1-XBmKihFFNZbnUUlNSYdLx3MvLnk=, tarball: http://r.npm.sankuai.com/dlv/download/dlv-1.1.3.tgz}

  doctrine@2.1.0:
    resolution: {integrity: sha1-XNAfwQFiG0LEzX9dGmYkNxbT850=, tarball: http://r.npm.sankuai.com/doctrine/download/doctrine-2.1.0.tgz}
    engines: {node: '>=0.10.0'}

  doctrine@3.0.0:
    resolution: {integrity: sha1-rd6+rXKmV023g2OdyHoSF3OXOWE=, tarball: http://r.npm.sankuai.com/doctrine/download/doctrine-3.0.0.tgz}
    engines: {node: '>=6.0.0'}

  dunder-proto@1.0.1:
    resolution: {integrity: sha1-165mfh3INIL4tw/Q9u78UNow9Yo=, tarball: http://r.npm.sankuai.com/dunder-proto/download/dunder-proto-1.0.1.tgz}
    engines: {node: '>= 0.4'}

  eastasianwidth@0.2.0:
    resolution: {integrity: sha1-aWzi7Aqg5uqTo5f/zySqeEDIJ8s=, tarball: http://r.npm.sankuai.com/eastasianwidth/download/eastasianwidth-0.2.0.tgz}

  ee-first@1.1.1:
    resolution: {integrity: sha1-WQxhFWsK4vTwJVcyoViyZrxWsh0=, tarball: http://r.npm.sankuai.com/ee-first/download/ee-first-1.1.1.tgz}

  electron-to-chromium@1.5.76:
    resolution: {integrity: sha1-2yApXFBhto8HyOpN/L1wFIXZSj0=, tarball: http://r.npm.sankuai.com/electron-to-chromium/download/electron-to-chromium-1.5.76.tgz}

  emoji-regex@10.4.0:
    resolution: {integrity: sha1-A1U6/qgLOXV0nPyzb3dsomjkE9Q=, tarball: http://r.npm.sankuai.com/emoji-regex/download/emoji-regex-10.4.0.tgz}

  emoji-regex@8.0.0:
    resolution: {integrity: sha1-6Bj9ac5cz8tARZT4QpY79TFkzDc=, tarball: http://r.npm.sankuai.com/emoji-regex/download/emoji-regex-8.0.0.tgz}

  emoji-regex@9.2.2:
    resolution: {integrity: sha1-hAyIA7DYBH9P8M+WMXazLU7z7XI=, tarball: http://r.npm.sankuai.com/emoji-regex/download/emoji-regex-9.2.2.tgz}

  encodeurl@1.0.2:
    resolution: {integrity: sha1-rT/0yG7C0CkyL1oCw6mmBslbP1k=, tarball: http://r.npm.sankuai.com/encodeurl/download/encodeurl-1.0.2.tgz}
    engines: {node: '>= 0.8'}

  entities@4.5.0:
    resolution: {integrity: sha1-XSaOpecRPsdMTQM7eepaNaSI+0g=, tarball: http://r.npm.sankuai.com/entities/download/entities-4.5.0.tgz}
    engines: {node: '>=0.12'}

  environment@1.1.0:
    resolution: {integrity: sha1-jobGaxgPNjx6sxF4fgJZZl9FqfE=, tarball: http://r.npm.sankuai.com/environment/download/environment-1.1.0.tgz}
    engines: {node: '>=18'}

  es-abstract@1.23.7:
    resolution: {integrity: sha1-NuPaRv2w0q47nfQjXjoxZ8FgWzY=, tarball: http://r.npm.sankuai.com/es-abstract/download/es-abstract-1.23.7.tgz}
    engines: {node: '>= 0.4'}

  es-define-property@1.0.1:
    resolution: {integrity: sha1-mD6y+aZyTpMD9hrd8BHHLgngsPo=, tarball: http://r.npm.sankuai.com/es-define-property/download/es-define-property-1.0.1.tgz}
    engines: {node: '>= 0.4'}

  es-errors@1.3.0:
    resolution: {integrity: sha1-BfdaJdq5jk+x3NXhRywFRtUFfI8=, tarball: http://r.npm.sankuai.com/es-errors/download/es-errors-1.3.0.tgz}
    engines: {node: '>= 0.4'}

  es-iterator-helpers@1.2.1:
    resolution: {integrity: sha1-0d0PWBKQVMCtki5qmh5l7vQ1/nU=, tarball: http://r.npm.sankuai.com/es-iterator-helpers/download/es-iterator-helpers-1.2.1.tgz}
    engines: {node: '>= 0.4'}

  es-module-lexer@1.6.0:
    resolution: {integrity: sha1-2kn1h/2eaO4kBP5OJWwMfTqBviE=, tarball: http://r.npm.sankuai.com/es-module-lexer/download/es-module-lexer-1.6.0.tgz}

  es-object-atoms@1.0.0:
    resolution: {integrity: sha1-3bVc1HrC4kBwEmC8Ko4x7LZD2UE=, tarball: http://r.npm.sankuai.com/es-object-atoms/download/es-object-atoms-1.0.0.tgz}
    engines: {node: '>= 0.4'}

  es-set-tostringtag@2.0.3:
    resolution: {integrity: sha1-i7YPCkQMLkKBliQoQ41YVFrzl3c=, tarball: http://r.npm.sankuai.com/es-set-tostringtag/download/es-set-tostringtag-2.0.3.tgz}
    engines: {node: '>= 0.4'}

  es-shim-unscopables@1.0.2:
    resolution: {integrity: sha1-H2lC5x7MeDXtHIqDAG2HcaY6N2M=, tarball: http://r.npm.sankuai.com/es-shim-unscopables/download/es-shim-unscopables-1.0.2.tgz}

  es-to-primitive@1.3.0:
    resolution: {integrity: sha1-lsicgsxJ/YeUokg1uj4f+H8hThg=, tarball: http://r.npm.sankuai.com/es-to-primitive/download/es-to-primitive-1.3.0.tgz}
    engines: {node: '>= 0.4'}

  esbuild@0.24.2:
    resolution: {integrity: sha1-tbVb7n3gF7/1+4pOPkTy6+LDVn0=, tarball: http://r.npm.sankuai.com/esbuild/download/esbuild-0.24.2.tgz}
    engines: {node: '>=18'}
    hasBin: true

  escalade@3.2.0:
    resolution: {integrity: sha1-ARo/aYVroYnf+n3I/M6Z0qh5A+U=, tarball: http://r.npm.sankuai.com/escalade/download/escalade-3.2.0.tgz}
    engines: {node: '>=6'}

  escape-html@1.0.3:
    resolution: {integrity: sha1-Aljq5NPQwJdN4cFpGI7wBR0dGYg=, tarball: http://r.npm.sankuai.com/escape-html/download/escape-html-1.0.3.tgz}

  escape-string-regexp@1.0.5:
    resolution: {integrity: sha1-G2HAViGQqN/2rjuyzwIAyhMLhtQ=, tarball: http://r.npm.sankuai.com/escape-string-regexp/download/escape-string-regexp-1.0.5.tgz}
    engines: {node: '>=0.8.0'}

  escape-string-regexp@4.0.0:
    resolution: {integrity: sha1-FLqDpdNz49MR5a/KKc9b+tllvzQ=, tarball: http://r.npm.sankuai.com/escape-string-regexp/download/escape-string-regexp-4.0.0.tgz}
    engines: {node: '>=10'}

  eslint-plugin-react-hooks@5.1.0:
    resolution: {integrity: sha1-PTTjfVdwhmw0uH1bSZ9fC1O/CFQ=, tarball: http://r.npm.sankuai.com/eslint-plugin-react-hooks/download/eslint-plugin-react-hooks-5.1.0.tgz}
    engines: {node: '>=10'}
    peerDependencies:
      eslint: ^3.0.0 || ^4.0.0 || ^5.0.0 || ^6.0.0 || ^7.0.0 || ^8.0.0-0 || ^9.0.0

  eslint-plugin-react-refresh@0.4.16:
    resolution: {integrity: sha1-FJ28knm9FpQkCfHB0vDc4ymUMO8=, tarball: http://r.npm.sankuai.com/eslint-plugin-react-refresh/download/eslint-plugin-react-refresh-0.4.16.tgz}
    peerDependencies:
      eslint: '>=8.40'

  eslint-plugin-react@7.37.3:
    resolution: {integrity: sha1-VnVJ6SUVM5dcTqlwb5hsOmSDIDE=, tarball: http://r.npm.sankuai.com/eslint-plugin-react/download/eslint-plugin-react-7.37.3.tgz}
    engines: {node: '>=4'}
    peerDependencies:
      eslint: ^3 || ^4 || ^5 || ^6 || ^7 || ^8 || ^9.7

  eslint-plugin-vue@9.33.0:
    resolution: {integrity: sha1-3jPrqPeOHRcsWcjsf7/WDGyjXDk=, tarball: http://r.npm.sankuai.com/eslint-plugin-vue/download/eslint-plugin-vue-9.33.0.tgz}
    engines: {node: ^14.17.0 || >=16.0.0}
    peerDependencies:
      eslint: ^6.2.0 || ^7.0.0 || ^8.0.0 || ^9.0.0

  eslint-scope@7.2.2:
    resolution: {integrity: sha1-3rT5JWM5DzIAaJSvYqItuhxGQj8=, tarball: http://r.npm.sankuai.com/eslint-scope/download/eslint-scope-7.2.2.tgz}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}

  eslint-scope@8.2.0:
    resolution: {integrity: sha1-N3qm8ctdx1ks/Qt/iS/QzzUs5EI=, tarball: http://r.npm.sankuai.com/eslint-scope/download/eslint-scope-8.2.0.tgz}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  eslint-visitor-keys@3.4.3:
    resolution: {integrity: sha1-DNcv6FUOPC6uFWqWpN3c0cisWAA=, tarball: http://r.npm.sankuai.com/eslint-visitor-keys/download/eslint-visitor-keys-3.4.3.tgz}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}

  eslint-visitor-keys@4.2.0:
    resolution: {integrity: sha1-aHussq+IT83aim59ZcYG9GoUzUU=, tarball: http://r.npm.sankuai.com/eslint-visitor-keys/download/eslint-visitor-keys-4.2.0.tgz}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  eslint@8.57.1:
    resolution: {integrity: sha1-ffEJZUq6fju+XI6uUzxeRh08bKk=, tarball: http://r.npm.sankuai.com/eslint/download/eslint-8.57.1.tgz}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    deprecated: This version is no longer supported. Please see https://eslint.org/version-support for other options.
    hasBin: true

  eslint@9.17.0:
    resolution: {integrity: sha1-+qH6y13QQhcv3FIBBphLXCQhuww=, tarball: http://r.npm.sankuai.com/eslint/download/eslint-9.17.0.tgz}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    hasBin: true
    peerDependencies:
      jiti: '*'
    peerDependenciesMeta:
      jiti:
        optional: true

  espree@10.3.0:
    resolution: {integrity: sha1-KSZ89bDLmHNbZeZLoH4O1J0e7Yo=, tarball: http://r.npm.sankuai.com/espree/download/espree-10.3.0.tgz}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  espree@9.6.1:
    resolution: {integrity: sha1-oqF7jkNGkKVDLy+AGM5x0zGkjG8=, tarball: http://r.npm.sankuai.com/espree/download/espree-9.6.1.tgz}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}

  esquery@1.6.0:
    resolution: {integrity: sha1-kUGSNPgE2FKoLc7sPhbNwiz52uc=, tarball: http://r.npm.sankuai.com/esquery/download/esquery-1.6.0.tgz}
    engines: {node: '>=0.10'}

  esrecurse@4.3.0:
    resolution: {integrity: sha1-eteWTWeauyi+5yzsY3WLHF0smSE=, tarball: http://r.npm.sankuai.com/esrecurse/download/esrecurse-4.3.0.tgz}
    engines: {node: '>=4.0'}

  estraverse@5.3.0:
    resolution: {integrity: sha1-LupSkHAvJquP5TcDcP+GyWXSESM=, tarball: http://r.npm.sankuai.com/estraverse/download/estraverse-5.3.0.tgz}
    engines: {node: '>=4.0'}

  estree-walker@2.0.2:
    resolution: {integrity: sha1-UvAQF4wqTBF6d1fP6UKtt9LaTKw=, tarball: http://r.npm.sankuai.com/estree-walker/download/estree-walker-2.0.2.tgz}

  esutils@2.0.3:
    resolution: {integrity: sha1-dNLrTeC42hKTcRkQ1Qd1ubcQ72Q=, tarball: http://r.npm.sankuai.com/esutils/download/esutils-2.0.3.tgz}
    engines: {node: '>=0.10.0'}

  eventemitter3@5.0.1:
    resolution: {integrity: sha1-U/X/0KSSrIAHIbtCxmuEHelkI8Q=, tarball: http://r.npm.sankuai.com/eventemitter3/download/eventemitter3-5.0.1.tgz}

  execa@8.0.1:
    resolution: {integrity: sha1-UfallDtYD5Y8PKnGMheW24zDm4w=, tarball: http://r.npm.sankuai.com/execa/download/execa-8.0.1.tgz}
    engines: {node: '>=16.17'}

  expand-brackets@2.1.4:
    resolution: {integrity: sha1-t3c14xXOMPa27/D4OwQVGiJEliI=, tarball: http://r.npm.sankuai.com/expand-brackets/download/expand-brackets-2.1.4.tgz}
    engines: {node: '>=0.10.0'}

  extend-shallow@2.0.1:
    resolution: {integrity: sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=, tarball: http://r.npm.sankuai.com/extend-shallow/download/extend-shallow-2.0.1.tgz}
    engines: {node: '>=0.10.0'}

  extend-shallow@3.0.2:
    resolution: {integrity: sha1-Jqcarwc7OfshJxcnRhMcJwQCjbg=, tarball: http://r.npm.sankuai.com/extend-shallow/download/extend-shallow-3.0.2.tgz}
    engines: {node: '>=0.10.0'}

  external-editor@3.1.0:
    resolution: {integrity: sha1-ywP3QL764D6k0oPK7SdBqD8zVJU=, tarball: http://r.npm.sankuai.com/external-editor/download/external-editor-3.1.0.tgz}
    engines: {node: '>=4'}

  extglob@2.0.4:
    resolution: {integrity: sha1-rQD+TcYSqSMuhxhxHcXLWrAoVUM=, tarball: http://r.npm.sankuai.com/extglob/download/extglob-2.0.4.tgz}
    engines: {node: '>=0.10.0'}

  fast-deep-equal@3.1.3:
    resolution: {integrity: sha1-On1WtVnWy8PrUSMlJE5hmmXGxSU=, tarball: http://r.npm.sankuai.com/fast-deep-equal/download/fast-deep-equal-3.1.3.tgz}

  fast-glob@2.2.7:
    resolution: {integrity: sha1-aVOFfDr6R1//ku5gFdUtpwpM050=, tarball: http://r.npm.sankuai.com/fast-glob/download/fast-glob-2.2.7.tgz}
    engines: {node: '>=4.0.0'}

  fast-glob@3.3.2:
    resolution: {integrity: sha1-qQRQHlfP3S/83tRemaVP71XkYSk=, tarball: http://r.npm.sankuai.com/fast-glob/download/fast-glob-3.3.2.tgz}
    engines: {node: '>=8.6.0'}

  fast-json-stable-stringify@2.1.0:
    resolution: {integrity: sha1-h0v2nG9ATCtdmcSBNBOZ/VWJJjM=, tarball: http://r.npm.sankuai.com/fast-json-stable-stringify/download/fast-json-stable-stringify-2.1.0.tgz}

  fast-levenshtein@2.0.6:
    resolution: {integrity: sha1-PYpcZog6FqMMqGQ+hR8Zuqd5eRc=, tarball: http://r.npm.sankuai.com/fast-levenshtein/download/fast-levenshtein-2.0.6.tgz}

  fastq@1.18.0:
    resolution: {integrity: sha1-1jHX4l+v/qgYh/5eqMkBDhs2/uA=, tarball: http://r.npm.sankuai.com/fastq/download/fastq-1.18.0.tgz}

  file-entry-cache@6.0.1:
    resolution: {integrity: sha1-IRst2WWcsDlLBz5zI6w8kz1SICc=, tarball: http://r.npm.sankuai.com/file-entry-cache/download/file-entry-cache-6.0.1.tgz}
    engines: {node: ^10.12.0 || >=12.0.0}

  file-entry-cache@8.0.0:
    resolution: {integrity: sha1-d4e93PETG/+5JjbGlFe7wO3W2B8=, tarball: http://r.npm.sankuai.com/file-entry-cache/download/file-entry-cache-8.0.0.tgz}
    engines: {node: '>=16.0.0'}

  fill-range@4.0.0:
    resolution: {integrity: sha1-1USBHUKPmOsGpj3EAtJAPDKMOPc=, tarball: http://r.npm.sankuai.com/fill-range/download/fill-range-4.0.0.tgz}
    engines: {node: '>=0.10.0'}

  fill-range@7.1.1:
    resolution: {integrity: sha1-RCZdPKwH4+p9wkdRY4BkN1SgUpI=, tarball: http://r.npm.sankuai.com/fill-range/download/fill-range-7.1.1.tgz}
    engines: {node: '>=8'}

  finalhandler@1.1.2:
    resolution: {integrity: sha1-t+fQAP/RGTjQ/bBTUG9uur6fWH0=, tarball: http://r.npm.sankuai.com/finalhandler/download/finalhandler-1.1.2.tgz}
    engines: {node: '>= 0.8'}

  find-up-simple@1.0.0:
    resolution: {integrity: sha1-IdA1/en9vVbI9NL2PzL9k6HPw2g=, tarball: http://r.npm.sankuai.com/find-up-simple/download/find-up-simple-1.0.0.tgz}
    engines: {node: '>=18'}

  find-up@5.0.0:
    resolution: {integrity: sha1-TJKBnstwg1YeT0okCoa+UZj1Nvw=, tarball: http://r.npm.sankuai.com/find-up/download/find-up-5.0.0.tgz}
    engines: {node: '>=10'}

  flat-cache@3.2.0:
    resolution: {integrity: sha1-LAwtUEDJmxYydxqdEFclwBFTY+4=, tarball: http://r.npm.sankuai.com/flat-cache/download/flat-cache-3.2.0.tgz}
    engines: {node: ^10.12.0 || >=12.0.0}

  flat-cache@4.0.1:
    resolution: {integrity: sha1-Ds45/LFO4BL0sEEL0z3ZwfAREnw=, tarball: http://r.npm.sankuai.com/flat-cache/download/flat-cache-4.0.1.tgz}
    engines: {node: '>=16'}

  flatted@3.3.2:
    resolution: {integrity: sha1-rboUSKmEG+xytCxTLqI9u+3vGic=, tarball: http://r.npm.sankuai.com/flatted/download/flatted-3.3.2.tgz}

  follow-redirects@1.15.9:
    resolution: {integrity: sha1-pgT6EORDv5jKlCKNnuvMLoosjuE=, tarball: http://r.npm.sankuai.com/follow-redirects/download/follow-redirects-1.15.9.tgz}
    engines: {node: '>=4.0'}
    peerDependencies:
      debug: '*'
    peerDependenciesMeta:
      debug:
        optional: true

  for-each@0.3.3:
    resolution: {integrity: sha1-abRH6IoKXTLD5whPPxcQA0shN24=, tarball: http://r.npm.sankuai.com/for-each/download/for-each-0.3.3.tgz}

  for-in@1.0.2:
    resolution: {integrity: sha1-gQaNKVqBQuwKxybG4iAMMPttXoA=, tarball: http://r.npm.sankuai.com/for-in/download/for-in-1.0.2.tgz}
    engines: {node: '>=0.10.0'}

  foreground-child@3.3.0:
    resolution: {integrity: sha1-CshkTAbkMUOfhWHbjs8pp7VRnHc=, tarball: http://r.npm.sankuai.com/foreground-child/download/foreground-child-3.3.0.tgz}
    engines: {node: '>=14'}

  form-data@4.0.1:
    resolution: {integrity: sha1-uhB22qqlv9fpnBpssCqgpc/5DUg=, tarball: http://r.npm.sankuai.com/form-data/download/form-data-4.0.1.tgz}
    engines: {node: '>= 6'}

  fraction.js@4.3.7:
    resolution: {integrity: sha1-BsoAhRV+Qv2n+ecm55/vxAaIQPc=, tarball: http://r.npm.sankuai.com/fraction.js/download/fraction.js-4.3.7.tgz}

  fragment-cache@0.2.1:
    resolution: {integrity: sha1-QpD60n8T6Jvn8zeZxrxaCr//DRk=, tarball: http://r.npm.sankuai.com/fragment-cache/download/fragment-cache-0.2.1.tgz}
    engines: {node: '>=0.10.0'}

  fs.realpath@1.0.0:
    resolution: {integrity: sha1-FQStJSMVjKpA20onh8sBQRmU6k8=, tarball: http://r.npm.sankuai.com/fs.realpath/download/fs.realpath-1.0.0.tgz}

  fsevents@2.3.3:
    resolution: {integrity: sha1-ysZAd4XQNnWipeGlMFxpezR9kNY=, tarball: http://r.npm.sankuai.com/fsevents/download/fsevents-2.3.3.tgz}
    engines: {node: ^8.16.0 || ^10.6.0 || >=11.0.0}
    os: [darwin]

  function-bind@1.1.2:
    resolution: {integrity: sha1-LALYZNl/PqbIgwxGTL0Rq26rehw=, tarball: http://r.npm.sankuai.com/function-bind/download/function-bind-1.1.2.tgz}

  function.prototype.name@1.1.8:
    resolution: {integrity: sha1-5o4d97JZpclJ7u+Vzb3lPt/6u3g=, tarball: http://r.npm.sankuai.com/function.prototype.name/download/function.prototype.name-1.1.8.tgz}
    engines: {node: '>= 0.4'}

  functions-have-names@1.2.3:
    resolution: {integrity: sha1-BAT+TuK6L2B/Dg7DyAuumUEzuDQ=, tarball: http://r.npm.sankuai.com/functions-have-names/download/functions-have-names-1.2.3.tgz}

  gensync@1.0.0-beta.2:
    resolution: {integrity: sha1-MqbudsPX9S1GsrGuXZP+qFgKJeA=, tarball: http://r.npm.sankuai.com/gensync/download/gensync-1.0.0-beta.2.tgz}
    engines: {node: '>=6.9.0'}

  get-east-asian-width@1.3.0:
    resolution: {integrity: sha1-IbQHHuWO0E7g22UzcbVbQpmHU4k=, tarball: http://r.npm.sankuai.com/get-east-asian-width/download/get-east-asian-width-1.3.0.tgz}
    engines: {node: '>=18'}

  get-intrinsic@1.2.6:
    resolution: {integrity: sha1-Q9090Oe0m4Ky38rRDcgkv3/CZdU=, tarball: http://r.npm.sankuai.com/get-intrinsic/download/get-intrinsic-1.2.6.tgz}
    engines: {node: '>= 0.4'}

  get-stream@8.0.1:
    resolution: {integrity: sha1-3vnf1xdCzXdUp3Ye1DdJon0C7KI=, tarball: http://r.npm.sankuai.com/get-stream/download/get-stream-8.0.1.tgz}
    engines: {node: '>=16'}

  get-symbol-description@1.1.0:
    resolution: {integrity: sha1-e91U4L7+j/yfO04gMiDZ8eiBtu4=, tarball: http://r.npm.sankuai.com/get-symbol-description/download/get-symbol-description-1.1.0.tgz}
    engines: {node: '>= 0.4'}

  get-tsconfig@4.8.1:
    resolution: {integrity: sha1-iZXrORrm4WONJREYx7Vt5+tCVHE=, tarball: http://r.npm.sankuai.com/get-tsconfig/download/get-tsconfig-4.8.1.tgz}

  get-value@2.0.6:
    resolution: {integrity: sha1-3BXKHGcjh8p2vTesCjlbogQqLCg=, tarball: http://r.npm.sankuai.com/get-value/download/get-value-2.0.6.tgz}
    engines: {node: '>=0.10.0'}

  glob-parent@3.1.0:
    resolution: {integrity: sha1-nmr2KZ2NO9K9QEMIMr0RPfkGxa4=, tarball: http://r.npm.sankuai.com/glob-parent/download/glob-parent-3.1.0.tgz}

  glob-parent@5.1.2:
    resolution: {integrity: sha1-hpgyxYA0/mikCTwX3BXoNA2EAcQ=, tarball: http://r.npm.sankuai.com/glob-parent/download/glob-parent-5.1.2.tgz}
    engines: {node: '>= 6'}

  glob-parent@6.0.2:
    resolution: {integrity: sha1-bSN9mQg5UMeSkPJMdkKj3poo+eM=, tarball: http://r.npm.sankuai.com/glob-parent/download/glob-parent-6.0.2.tgz}
    engines: {node: '>=10.13.0'}

  glob-to-regexp@0.3.0:
    resolution: {integrity: sha1-jFoUlNIGbFcMw7/kSWF1rMTVAqs=, tarball: http://r.npm.sankuai.com/glob-to-regexp/download/glob-to-regexp-0.3.0.tgz}

  glob@10.4.5:
    resolution: {integrity: sha1-9NnwuQ/9urCcnXf18ptCYlF7CVY=, tarball: http://r.npm.sankuai.com/glob/download/glob-10.4.5.tgz}
    hasBin: true

  glob@11.0.0:
    resolution: {integrity: sha1-YDHfDXtl6qHMubKbXO0WzqZY534=, tarball: http://r.npm.sankuai.com/glob/download/glob-11.0.0.tgz}
    engines: {node: 20 || >=22}
    hasBin: true

  glob@7.2.3:
    resolution: {integrity: sha1-uN8PuAK7+o6JvR2Ti04WV47UTys=, tarball: http://r.npm.sankuai.com/glob/download/glob-7.2.3.tgz}
    deprecated: Glob versions prior to v9 are no longer supported

  globals@11.12.0:
    resolution: {integrity: sha1-q4eVM4hooLq9hSV1gBjCp+uVxC4=, tarball: http://r.npm.sankuai.com/globals/download/globals-11.12.0.tgz}
    engines: {node: '>=4'}

  globals@13.24.0:
    resolution: {integrity: sha1-hDKhnXjODB6DOUnDats0VAC7EXE=, tarball: http://r.npm.sankuai.com/globals/download/globals-13.24.0.tgz}
    engines: {node: '>=8'}

  globals@14.0.0:
    resolution: {integrity: sha1-iY10E8Kbq89rr+Vvyt3thYrack4=, tarball: http://r.npm.sankuai.com/globals/download/globals-14.0.0.tgz}
    engines: {node: '>=18'}

  globals@15.14.0:
    resolution: {integrity: sha1-uP06iUH/O0048zGdQzthu7SC5z8=, tarball: http://r.npm.sankuai.com/globals/download/globals-15.14.0.tgz}
    engines: {node: '>=18'}

  globalthis@1.0.4:
    resolution: {integrity: sha1-dDDtOpddl7+1m8zkH1yruvplEjY=, tarball: http://r.npm.sankuai.com/globalthis/download/globalthis-1.0.4.tgz}
    engines: {node: '>= 0.4'}

  globby@11.1.0:
    resolution: {integrity: sha1-vUvpi7BC+D15b344EZkfvoKg00s=, tarball: http://r.npm.sankuai.com/globby/download/globby-11.1.0.tgz}
    engines: {node: '>=10'}

  globby@9.2.0:
    resolution: {integrity: sha1-/QKacGxwPSm90XD0tts6P3p8tj0=, tarball: http://r.npm.sankuai.com/globby/download/globby-9.2.0.tgz}
    engines: {node: '>=6'}

  gopd@1.2.0:
    resolution: {integrity: sha1-ifVrghe9vIgCvSmd9tfxCB1+UaE=, tarball: http://r.npm.sankuai.com/gopd/download/gopd-1.2.0.tgz}
    engines: {node: '>= 0.4'}

  graceful-fs@4.2.11:
    resolution: {integrity: sha1-QYPk6L8Iu24Fu7L30uDI9xLKQOM=, tarball: http://r.npm.sankuai.com/graceful-fs/download/graceful-fs-4.2.11.tgz}

  graphemer@1.4.0:
    resolution: {integrity: sha1-+y8dVeDjoYSa7/yQxPoN1ToOZsY=, tarball: http://r.npm.sankuai.com/graphemer/download/graphemer-1.4.0.tgz}

  has-bigints@1.1.0:
    resolution: {integrity: sha1-KGB+llrJZ+A80qLHCiY2oe2tSf4=, tarball: http://r.npm.sankuai.com/has-bigints/download/has-bigints-1.1.0.tgz}
    engines: {node: '>= 0.4'}

  has-flag@3.0.0:
    resolution: {integrity: sha1-tdRU3CGZriJWmfNGfloH87lVuv0=, tarball: http://r.npm.sankuai.com/has-flag/download/has-flag-3.0.0.tgz}
    engines: {node: '>=4'}

  has-flag@4.0.0:
    resolution: {integrity: sha1-lEdx/ZyByBJlxNaUGGDaBrtZR5s=, tarball: http://r.npm.sankuai.com/has-flag/download/has-flag-4.0.0.tgz}
    engines: {node: '>=8'}

  has-property-descriptors@1.0.2:
    resolution: {integrity: sha1-lj7X0HHce/XwhMW/vg0bYiJYaFQ=, tarball: http://r.npm.sankuai.com/has-property-descriptors/download/has-property-descriptors-1.0.2.tgz}

  has-proto@1.2.0:
    resolution: {integrity: sha1-XeWm6r2V/f/ZgYtDBV6AZeOf6dU=, tarball: http://r.npm.sankuai.com/has-proto/download/has-proto-1.2.0.tgz}
    engines: {node: '>= 0.4'}

  has-symbols@1.1.0:
    resolution: {integrity: sha1-/JxqeDoISVHQuXH+EBjegTcHozg=, tarball: http://r.npm.sankuai.com/has-symbols/download/has-symbols-1.1.0.tgz}
    engines: {node: '>= 0.4'}

  has-tostringtag@1.0.2:
    resolution: {integrity: sha1-LNxC1AvvLltO6rfAGnPFTOerWrw=, tarball: http://r.npm.sankuai.com/has-tostringtag/download/has-tostringtag-1.0.2.tgz}
    engines: {node: '>= 0.4'}

  has-value@0.3.1:
    resolution: {integrity: sha1-ex9YutpiyoJ+wKIHgCVlSEWZXh8=, tarball: http://r.npm.sankuai.com/has-value/download/has-value-0.3.1.tgz}
    engines: {node: '>=0.10.0'}

  has-value@1.0.0:
    resolution: {integrity: sha1-GLKB2lhbHFxR3vJMkw7SmgvmsXc=, tarball: http://r.npm.sankuai.com/has-value/download/has-value-1.0.0.tgz}
    engines: {node: '>=0.10.0'}

  has-values@0.1.4:
    resolution: {integrity: sha1-bWHeldkd/Km5oCCJrThL/49it3E=, tarball: http://r.npm.sankuai.com/has-values/download/has-values-0.1.4.tgz}
    engines: {node: '>=0.10.0'}

  has-values@1.0.0:
    resolution: {integrity: sha1-lbC2P+whRmGab+V/51Yo1aOe/k8=, tarball: http://r.npm.sankuai.com/has-values/download/has-values-1.0.0.tgz}
    engines: {node: '>=0.10.0'}

  hasown@2.0.2:
    resolution: {integrity: sha1-AD6vkb563DcuhOxZ3DclLO24AAM=, tarball: http://r.npm.sankuai.com/hasown/download/hasown-2.0.2.tgz}
    engines: {node: '>= 0.4'}

  html-tags@3.3.1:
    resolution: {integrity: sha1-oEAmoYyILku6igGj05z+Rl1Atc4=, tarball: http://r.npm.sankuai.com/html-tags/download/html-tags-3.3.1.tgz}
    engines: {node: '>=8'}

  human-signals@5.0.0:
    resolution: {integrity: sha1-QmZaKE+a4NreO6QevDfrS4UvOig=, tarball: http://r.npm.sankuai.com/human-signals/download/human-signals-5.0.0.tgz}
    engines: {node: '>=16.17.0'}

  iconv-lite@0.4.24:
    resolution: {integrity: sha1-ICK0sl+93CHS9SSXSkdKr+czkIs=, tarball: http://r.npm.sankuai.com/iconv-lite/download/iconv-lite-0.4.24.tgz}
    engines: {node: '>=0.10.0'}

  ignore@4.0.6:
    resolution: {integrity: sha1-dQ49tYYgh7RzfrrIIH/9HvJ7Jfw=, tarball: http://r.npm.sankuai.com/ignore/download/ignore-4.0.6.tgz}
    engines: {node: '>= 4'}

  ignore@5.3.2:
    resolution: {integrity: sha1-PNQOcp82Q/2HywTlC/DrcivFlvU=, tarball: http://r.npm.sankuai.com/ignore/download/ignore-5.3.2.tgz}
    engines: {node: '>= 4'}

  immutable@5.0.3:
    resolution: {integrity: sha1-qgN+IxPqe11ADNkpj6FOQEyTPbE=, tarball: http://r.npm.sankuai.com/immutable/download/immutable-5.0.3.tgz}

  import-fresh@3.3.0:
    resolution: {integrity: sha1-NxYsJfy566oublPVtNiM4X2eDCs=, tarball: http://r.npm.sankuai.com/import-fresh/download/import-fresh-3.3.0.tgz}
    engines: {node: '>=6'}

  imurmurhash@0.1.4:
    resolution: {integrity: sha1-khi5srkoojixPcT7a21XbyMUU+o=, tarball: http://r.npm.sankuai.com/imurmurhash/download/imurmurhash-0.1.4.tgz}
    engines: {node: '>=0.8.19'}

  inflight@1.0.6:
    resolution: {integrity: sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk=, tarball: http://r.npm.sankuai.com/inflight/download/inflight-1.0.6.tgz}

  inherits@2.0.4:
    resolution: {integrity: sha1-D6LGT5MpF8NDOg3tVTY6rjdBa3w=, tarball: http://r.npm.sankuai.com/inherits/download/inherits-2.0.4.tgz}

  inquirer@12.4.3:
    resolution: {integrity: sha1-20uT+8PIjkTnD+olkvAnJJDMeck=, tarball: http://r.npm.sankuai.com/inquirer/download/inquirer-12.4.3.tgz}
    engines: {node: '>=18'}
    peerDependencies:
      '@types/node': '>=18'
    peerDependenciesMeta:
      '@types/node':
        optional: true

  internal-slot@1.1.0:
    resolution: {integrity: sha1-HqyRdilH0vcFa8g42T4TsulgSWE=, tarball: http://r.npm.sankuai.com/internal-slot/download/internal-slot-1.1.0.tgz}
    engines: {node: '>= 0.4'}

  is-accessor-descriptor@1.0.1:
    resolution: {integrity: sha1-MiOxBig1RkS4YmDbKbPmk/XO7dQ=, tarball: http://r.npm.sankuai.com/is-accessor-descriptor/download/is-accessor-descriptor-1.0.1.tgz}
    engines: {node: '>= 0.10'}

  is-array-buffer@3.0.5:
    resolution: {integrity: sha1-ZXQuHmh70sxmYlMGj9hwf+TUQoA=, tarball: http://r.npm.sankuai.com/is-array-buffer/download/is-array-buffer-3.0.5.tgz}
    engines: {node: '>= 0.4'}

  is-async-function@2.0.0:
    resolution: {integrity: sha1-jkQY79Pl06brsBZMBe9a+2mqlkY=, tarball: http://r.npm.sankuai.com/is-async-function/download/is-async-function-2.0.0.tgz}
    engines: {node: '>= 0.4'}

  is-bigint@1.1.0:
    resolution: {integrity: sha1-3aejRF31ekJYPbQihoLrp8QXBnI=, tarball: http://r.npm.sankuai.com/is-bigint/download/is-bigint-1.1.0.tgz}
    engines: {node: '>= 0.4'}

  is-binary-path@2.1.0:
    resolution: {integrity: sha1-6h9/O4DwZCNug0cPhsCcJU+0Wwk=, tarball: http://r.npm.sankuai.com/is-binary-path/download/is-binary-path-2.1.0.tgz}
    engines: {node: '>=8'}

  is-boolean-object@1.2.1:
    resolution: {integrity: sha1-wg0MZUvgXaT7wjxWJjXAGek9r4k=, tarball: http://r.npm.sankuai.com/is-boolean-object/download/is-boolean-object-1.2.1.tgz}
    engines: {node: '>= 0.4'}

  is-buffer@1.1.6:
    resolution: {integrity: sha1-76ouqdqg16suoTqXsritUf776L4=, tarball: http://r.npm.sankuai.com/is-buffer/download/is-buffer-1.1.6.tgz}

  is-callable@1.2.7:
    resolution: {integrity: sha1-O8KoXqdC2eNiBdys3XLKH9xRsFU=, tarball: http://r.npm.sankuai.com/is-callable/download/is-callable-1.2.7.tgz}
    engines: {node: '>= 0.4'}

  is-core-module@2.16.1:
    resolution: {integrity: sha1-KpiAGoSfQ+Kt1kT7trxiKbGaTvQ=, tarball: http://r.npm.sankuai.com/is-core-module/download/is-core-module-2.16.1.tgz}
    engines: {node: '>= 0.4'}

  is-data-descriptor@1.0.1:
    resolution: {integrity: sha1-IQkWRCYWbTLqOMQFweCUXZ5qTus=, tarball: http://r.npm.sankuai.com/is-data-descriptor/download/is-data-descriptor-1.0.1.tgz}
    engines: {node: '>= 0.4'}

  is-data-view@1.0.2:
    resolution: {integrity: sha1-uuCkG5aImGwhiN2mZX5WuPnmO44=, tarball: http://r.npm.sankuai.com/is-data-view/download/is-data-view-1.0.2.tgz}
    engines: {node: '>= 0.4'}

  is-date-object@1.1.0:
    resolution: {integrity: sha1-rYVUGZb8eqiycpcB0ntzGfldgvc=, tarball: http://r.npm.sankuai.com/is-date-object/download/is-date-object-1.1.0.tgz}
    engines: {node: '>= 0.4'}

  is-descriptor@0.1.7:
    resolution: {integrity: sha1-JyfrYf14nc1b3w7UVp9VHS/jvjM=, tarball: http://r.npm.sankuai.com/is-descriptor/download/is-descriptor-0.1.7.tgz}
    engines: {node: '>= 0.4'}

  is-descriptor@1.0.3:
    resolution: {integrity: sha1-ktJ8s80xHEl3pNtH30VyNKE8swY=, tarball: http://r.npm.sankuai.com/is-descriptor/download/is-descriptor-1.0.3.tgz}
    engines: {node: '>= 0.4'}

  is-docker@3.0.0:
    resolution: {integrity: sha1-kAk6oxBid9inelkQ265xdH4VogA=, tarball: http://r.npm.sankuai.com/is-docker/download/is-docker-3.0.0.tgz}
    engines: {node: ^12.20.0 || ^14.13.1 || >=16.0.0}
    hasBin: true

  is-extendable@0.1.1:
    resolution: {integrity: sha1-YrEQ4omkcUGOPsNqYX1HLjAd/Ik=, tarball: http://r.npm.sankuai.com/is-extendable/download/is-extendable-0.1.1.tgz}
    engines: {node: '>=0.10.0'}

  is-extendable@1.0.1:
    resolution: {integrity: sha1-p0cPnkJnM9gb2B4RVSZOOjUHyrQ=, tarball: http://r.npm.sankuai.com/is-extendable/download/is-extendable-1.0.1.tgz}
    engines: {node: '>=0.10.0'}

  is-extglob@2.1.1:
    resolution: {integrity: sha1-qIwCU1eR8C7TfHahueqXc8gz+MI=, tarball: http://r.npm.sankuai.com/is-extglob/download/is-extglob-2.1.1.tgz}
    engines: {node: '>=0.10.0'}

  is-finalizationregistry@1.1.1:
    resolution: {integrity: sha1-7v3NxslN3QZ02chYh7+T+USpfJA=, tarball: http://r.npm.sankuai.com/is-finalizationregistry/download/is-finalizationregistry-1.1.1.tgz}
    engines: {node: '>= 0.4'}

  is-fullwidth-code-point@3.0.0:
    resolution: {integrity: sha1-8Rb4Bk/pCz94RKOJl8C3UFEmnx0=, tarball: http://r.npm.sankuai.com/is-fullwidth-code-point/download/is-fullwidth-code-point-3.0.0.tgz}
    engines: {node: '>=8'}

  is-fullwidth-code-point@4.0.0:
    resolution: {integrity: sha1-+uMWfHKedGP4RhzlErCApJJoqog=, tarball: http://r.npm.sankuai.com/is-fullwidth-code-point/download/is-fullwidth-code-point-4.0.0.tgz}
    engines: {node: '>=12'}

  is-fullwidth-code-point@5.0.0:
    resolution: {integrity: sha1-lgnvztfC+X2ntgFF70gceHx7pwQ=, tarball: http://r.npm.sankuai.com/is-fullwidth-code-point/download/is-fullwidth-code-point-5.0.0.tgz}
    engines: {node: '>=18'}

  is-generator-function@1.0.10:
    resolution: {integrity: sha1-8VWLrxrBfg3up8BBXEODUf8rPHI=, tarball: http://r.npm.sankuai.com/is-generator-function/download/is-generator-function-1.0.10.tgz}
    engines: {node: '>= 0.4'}

  is-glob@3.1.0:
    resolution: {integrity: sha1-e6WuJCF4BKxwcHuWkiVnSGzD6Eo=, tarball: http://r.npm.sankuai.com/is-glob/download/is-glob-3.1.0.tgz}
    engines: {node: '>=0.10.0'}

  is-glob@4.0.3:
    resolution: {integrity: sha1-ZPYeQsu7LuwgcanawLKLoeZdUIQ=, tarball: http://r.npm.sankuai.com/is-glob/download/is-glob-4.0.3.tgz}
    engines: {node: '>=0.10.0'}

  is-inside-container@1.0.0:
    resolution: {integrity: sha1-6B+6aZZi6zHb2vJnZqYdSBRxfqQ=, tarball: http://r.npm.sankuai.com/is-inside-container/download/is-inside-container-1.0.0.tgz}
    engines: {node: '>=14.16'}
    hasBin: true

  is-interactive@2.0.0:
    resolution: {integrity: sha1-QMV2FFk4JtoRAK3mBZd41ZfxbpA=, tarball: http://r.npm.sankuai.com/is-interactive/download/is-interactive-2.0.0.tgz}
    engines: {node: '>=12'}

  is-map@2.0.3:
    resolution: {integrity: sha1-7elrf+HicLPERl46RlZYdkkm1i4=, tarball: http://r.npm.sankuai.com/is-map/download/is-map-2.0.3.tgz}
    engines: {node: '>= 0.4'}

  is-number-object@1.1.1:
    resolution: {integrity: sha1-FEsh6VobwUggXcwoFKkTTsQbJUE=, tarball: http://r.npm.sankuai.com/is-number-object/download/is-number-object-1.1.1.tgz}
    engines: {node: '>= 0.4'}

  is-number@3.0.0:
    resolution: {integrity: sha1-JP1iAaR4LPUFYcgQJ2r8fRLXEZU=, tarball: http://r.npm.sankuai.com/is-number/download/is-number-3.0.0.tgz}
    engines: {node: '>=0.10.0'}

  is-number@7.0.0:
    resolution: {integrity: sha1-dTU0W4lnNNX4DE0GxQlVUnoU8Ss=, tarball: http://r.npm.sankuai.com/is-number/download/is-number-7.0.0.tgz}
    engines: {node: '>=0.12.0'}

  is-path-inside@3.0.3:
    resolution: {integrity: sha1-0jE2LlOgf/Kw4Op/7QSRYf/RYoM=, tarball: http://r.npm.sankuai.com/is-path-inside/download/is-path-inside-3.0.3.tgz}
    engines: {node: '>=8'}

  is-plain-object@2.0.4:
    resolution: {integrity: sha1-LBY7P6+xtgbZ0Xko8FwqHDjgdnc=, tarball: http://r.npm.sankuai.com/is-plain-object/download/is-plain-object-2.0.4.tgz}
    engines: {node: '>=0.10.0'}

  is-regex@1.2.1:
    resolution: {integrity: sha1-dtcKPtEO+b5I61d4h9dCBb8MrSI=, tarball: http://r.npm.sankuai.com/is-regex/download/is-regex-1.2.1.tgz}
    engines: {node: '>= 0.4'}

  is-set@2.0.3:
    resolution: {integrity: sha1-irIJ6kJGCBQTct7W4MsgDvHZ0B0=, tarball: http://r.npm.sankuai.com/is-set/download/is-set-2.0.3.tgz}
    engines: {node: '>= 0.4'}

  is-shared-array-buffer@1.0.4:
    resolution: {integrity: sha1-m2eES9m38ka6BwjDqT40Jpx3T28=, tarball: http://r.npm.sankuai.com/is-shared-array-buffer/download/is-shared-array-buffer-1.0.4.tgz}
    engines: {node: '>= 0.4'}

  is-stream@3.0.0:
    resolution: {integrity: sha1-5r/XqmvvafT0cs6btoHj5XtDGaw=, tarball: http://r.npm.sankuai.com/is-stream/download/is-stream-3.0.0.tgz}
    engines: {node: ^12.20.0 || ^14.13.1 || >=16.0.0}

  is-string@1.1.1:
    resolution: {integrity: sha1-kuo/PVxbbgOcqGd+WsjQfqdzy7k=, tarball: http://r.npm.sankuai.com/is-string/download/is-string-1.1.1.tgz}
    engines: {node: '>= 0.4'}

  is-symbol@1.1.1:
    resolution: {integrity: sha1-9HdhJ59TLisFpwJKdQbbvtrNBjQ=, tarball: http://r.npm.sankuai.com/is-symbol/download/is-symbol-1.1.1.tgz}
    engines: {node: '>= 0.4'}

  is-typed-array@1.1.15:
    resolution: {integrity: sha1-S/tKRbYc7oOlpG+6d45OjVnAzgs=, tarball: http://r.npm.sankuai.com/is-typed-array/download/is-typed-array-1.1.15.tgz}
    engines: {node: '>= 0.4'}

  is-unicode-supported@1.3.0:
    resolution: {integrity: sha1-2CSYS2FsKSouGYIH1KYJmDhC9xQ=, tarball: http://r.npm.sankuai.com/is-unicode-supported/download/is-unicode-supported-1.3.0.tgz}
    engines: {node: '>=12'}

  is-unicode-supported@2.1.0:
    resolution: {integrity: sha1-CfCrDebTdE1I0mXruY9l0R8qmzo=, tarball: http://r.npm.sankuai.com/is-unicode-supported/download/is-unicode-supported-2.1.0.tgz}
    engines: {node: '>=18'}

  is-weakmap@2.0.2:
    resolution: {integrity: sha1-v3JhXWSd/l9pkHnFS4PkfRrhnP0=, tarball: http://r.npm.sankuai.com/is-weakmap/download/is-weakmap-2.0.2.tgz}
    engines: {node: '>= 0.4'}

  is-weakref@1.1.0:
    resolution: {integrity: sha1-R+NHKulaY/qc8lZgvPDBgcOXcO8=, tarball: http://r.npm.sankuai.com/is-weakref/download/is-weakref-1.1.0.tgz}
    engines: {node: '>= 0.4'}

  is-weakset@2.0.4:
    resolution: {integrity: sha1-yfXesLwZBsbW8QJ/KE3fRZJJ2so=, tarball: http://r.npm.sankuai.com/is-weakset/download/is-weakset-2.0.4.tgz}
    engines: {node: '>= 0.4'}

  is-windows@1.0.2:
    resolution: {integrity: sha1-0YUOuXkezRjmGCzhKjDzlmNLsZ0=, tarball: http://r.npm.sankuai.com/is-windows/download/is-windows-1.0.2.tgz}
    engines: {node: '>=0.10.0'}

  is-wsl@3.1.0:
    resolution: {integrity: sha1-4cZX45wQCQr8vt7GFyD2uSTDy9I=, tarball: http://r.npm.sankuai.com/is-wsl/download/is-wsl-3.1.0.tgz}
    engines: {node: '>=16'}

  isarray@1.0.0:
    resolution: {integrity: sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE=, tarball: http://r.npm.sankuai.com/isarray/download/isarray-1.0.0.tgz}

  isarray@2.0.5:
    resolution: {integrity: sha1-ivHkwSISRMxiRZ+vOJQNTmRKVyM=, tarball: http://r.npm.sankuai.com/isarray/download/isarray-2.0.5.tgz}

  isexe@2.0.0:
    resolution: {integrity: sha1-6PvzdNxVb/iUehDcsFctYz8s+hA=, tarball: http://r.npm.sankuai.com/isexe/download/isexe-2.0.0.tgz}

  isobject@2.1.0:
    resolution: {integrity: sha1-8GVWEJaj8dou9GJy+BXIQNh+DIk=, tarball: http://r.npm.sankuai.com/isobject/download/isobject-2.1.0.tgz}
    engines: {node: '>=0.10.0'}

  isobject@3.0.1:
    resolution: {integrity: sha1-TkMekrEalzFjaqH5yNHMvP2reN8=, tarball: http://r.npm.sankuai.com/isobject/download/isobject-3.0.1.tgz}
    engines: {node: '>=0.10.0'}

  iterator.prototype@1.1.4:
    resolution: {integrity: sha1-SubPmLl/3HF7fhWdedwl+PyUgvE=, tarball: http://r.npm.sankuai.com/iterator.prototype/download/iterator.prototype-1.1.4.tgz}
    engines: {node: '>= 0.4'}

  jackspeak@3.4.3:
    resolution: {integrity: sha1-iDOp2Jq0rN5hiJQr0cU7Y5DtWoo=, tarball: http://r.npm.sankuai.com/jackspeak/download/jackspeak-3.4.3.tgz}

  jackspeak@4.0.2:
    resolution: {integrity: sha1-EflGijcwxv9vVoI6gg1+O+m+8BU=, tarball: http://r.npm.sankuai.com/jackspeak/download/jackspeak-4.0.2.tgz}
    engines: {node: 20 || >=22}

  jiti@1.21.7:
    resolution: {integrity: sha1-ndgQQ0JKPShFixk9ll8NGKIwC6k=, tarball: http://r.npm.sankuai.com/jiti/download/jiti-1.21.7.tgz}
    hasBin: true

  js-calendar@1.2.3:
    resolution: {integrity: sha1-pYOwZEtOaVujlPNE0QPbzHp6fT4=, tarball: http://r.npm.sankuai.com/js-calendar/download/js-calendar-1.2.3.tgz}

  js-tokens@4.0.0:
    resolution: {integrity: sha1-GSA/tZmR35jjoocFDUZHzerzJJk=, tarball: http://r.npm.sankuai.com/js-tokens/download/js-tokens-4.0.0.tgz}

  js-yaml@4.1.0:
    resolution: {integrity: sha1-wftl+PUBeQHN0slRhkuhhFihBgI=, tarball: http://r.npm.sankuai.com/js-yaml/download/js-yaml-4.1.0.tgz}
    hasBin: true

  jsesc@3.1.0:
    resolution: {integrity: sha1-dNM1ojT2ftGZB/2t+sfM+dQJgl0=, tarball: http://r.npm.sankuai.com/jsesc/download/jsesc-3.1.0.tgz}
    engines: {node: '>=6'}
    hasBin: true

  json-buffer@3.0.1:
    resolution: {integrity: sha1-kziAKjDTtmBfvgYT4JQAjKjAWhM=, tarball: http://r.npm.sankuai.com/json-buffer/download/json-buffer-3.0.1.tgz}

  json-schema-traverse@0.4.1:
    resolution: {integrity: sha1-afaofZUTq4u4/mO9sJecRI5oRmA=, tarball: http://r.npm.sankuai.com/json-schema-traverse/download/json-schema-traverse-0.4.1.tgz}

  json-stable-stringify-without-jsonify@1.0.1:
    resolution: {integrity: sha1-nbe1lJatPzz+8wp1FC0tkwrXJlE=, tarball: http://r.npm.sankuai.com/json-stable-stringify-without-jsonify/download/json-stable-stringify-without-jsonify-1.0.1.tgz}

  json2mq@0.2.0:
    resolution: {integrity: sha1-tje9O6nqvhIsg+lyBIOusQ0skEo=, tarball: http://r.npm.sankuai.com/json2mq/download/json2mq-0.2.0.tgz}

  json5@2.2.3:
    resolution: {integrity: sha1-eM1vGhm9wStz21rQxh79ZsHikoM=, tarball: http://r.npm.sankuai.com/json5/download/json5-2.2.3.tgz}
    engines: {node: '>=6'}
    hasBin: true

  jsx-ast-utils@3.3.5:
    resolution: {integrity: sha1-R2a9BajioRryIr7NGeFVdeUqhTo=, tarball: http://r.npm.sankuai.com/jsx-ast-utils/download/jsx-ast-utils-3.3.5.tgz}
    engines: {node: '>=4.0'}

  keyv@4.5.4:
    resolution: {integrity: sha1-qHmpnilFL5QkOfKkBeOvizHU3pM=, tarball: http://r.npm.sankuai.com/keyv/download/keyv-4.5.4.tgz}

  kind-of@3.2.2:
    resolution: {integrity: sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=, tarball: http://r.npm.sankuai.com/kind-of/download/kind-of-3.2.2.tgz}
    engines: {node: '>=0.10.0'}

  kind-of@4.0.0:
    resolution: {integrity: sha1-IIE989cSkosgc3hpGkUGb65y3Vc=, tarball: http://r.npm.sankuai.com/kind-of/download/kind-of-4.0.0.tgz}
    engines: {node: '>=0.10.0'}

  kind-of@6.0.3:
    resolution: {integrity: sha1-B8BQNKbDSfoG4k+jWqdttFgM5N0=, tarball: http://r.npm.sankuai.com/kind-of/download/kind-of-6.0.3.tgz}
    engines: {node: '>=0.10.0'}

  levn@0.4.1:
    resolution: {integrity: sha1-rkViwAdHO5MqYgDUAyaN0v/8at4=, tarball: http://r.npm.sankuai.com/levn/download/levn-0.4.1.tgz}
    engines: {node: '>= 0.8.0'}

  lilconfig@3.1.3:
    resolution: {integrity: sha1-obz9Ylf5WFv1rhTO7rt7VZAl5MQ=, tarball: http://r.npm.sankuai.com/lilconfig/download/lilconfig-3.1.3.tgz}
    engines: {node: '>=14'}

  lines-and-columns@1.2.4:
    resolution: {integrity: sha1-7KKE910pZQeTCdwK2SVauy68FjI=, tarball: http://r.npm.sankuai.com/lines-and-columns/download/lines-and-columns-1.2.4.tgz}

  lint-staged@15.3.0:
    resolution: {integrity: sha1-MqCz8vK4gllQvTufsJPgRTU736M=, tarball: http://r.npm.sankuai.com/lint-staged/download/lint-staged-15.3.0.tgz}
    engines: {node: '>=18.12.0'}
    hasBin: true

  listr2@8.2.5:
    resolution: {integrity: sha1-XJ25luGv6wXbBEgZbT1fZP7CWT0=, tarball: http://r.npm.sankuai.com/listr2/download/listr2-8.2.5.tgz}
    engines: {node: '>=18.0.0'}

  load-tsconfig@0.2.5:
    resolution: {integrity: sha1-RTuM2JYb+5Et6nfrbBaP6Myj06E=, tarball: http://r.npm.sankuai.com/load-tsconfig/download/load-tsconfig-0.2.5.tgz}
    engines: {node: ^12.20.0 || ^14.13.1 || >=16.0.0}

  locate-path@6.0.0:
    resolution: {integrity: sha1-VTIeswn+u8WcSAHZMackUqaB0oY=, tarball: http://r.npm.sankuai.com/locate-path/download/locate-path-6.0.0.tgz}
    engines: {node: '>=10'}

  lodash.isobject@3.0.2:
    resolution: {integrity: sha1-PI+41bW/S/kK4G4U8qUwpO2TXh0=, tarball: http://r.npm.sankuai.com/lodash.isobject/download/lodash.isobject-3.0.2.tgz}

  lodash.merge@4.6.2:
    resolution: {integrity: sha1-VYqlO0O2YeGSWgr9+japoQhf5Xo=, tarball: http://r.npm.sankuai.com/lodash.merge/download/lodash.merge-4.6.2.tgz}

  lodash@4.17.21:
    resolution: {integrity: sha1-Z5WRxWTDv/quhFTPCz3zcMPWkRw=, tarball: http://r.npm.sankuai.com/lodash/download/lodash-4.17.21.tgz}

  log-symbols@6.0.0:
    resolution: {integrity: sha1-u5Xl8FMiZRysMMD+tkBPnyqKlDk=, tarball: http://r.npm.sankuai.com/log-symbols/download/log-symbols-6.0.0.tgz}
    engines: {node: '>=18'}

  log-update@6.1.0:
    resolution: {integrity: sha1-GgT/OBZvlGR64a9WL0vWoVsbfNQ=, tarball: http://r.npm.sankuai.com/log-update/download/log-update-6.1.0.tgz}
    engines: {node: '>=18'}

  loose-envify@1.4.0:
    resolution: {integrity: sha1-ce5R+nvkyuwaY4OffmgtgTLTDK8=, tarball: http://r.npm.sankuai.com/loose-envify/download/loose-envify-1.4.0.tgz}
    hasBin: true

  lru-cache@10.4.3:
    resolution: {integrity: sha1-QQ/IoXtw5ZgBPfJXwkRrfzOD8Rk=, tarball: http://r.npm.sankuai.com/lru-cache/download/lru-cache-10.4.3.tgz}

  lru-cache@11.0.2:
    resolution: {integrity: sha1-+9jnz4IR9efl2RkFxBWj9VdVyjk=, tarball: http://r.npm.sankuai.com/lru-cache/download/lru-cache-11.0.2.tgz}
    engines: {node: 20 || >=22}

  lru-cache@5.1.1:
    resolution: {integrity: sha1-HaJ+ZxAnGUdpXa9oSOhH8B2EuSA=, tarball: http://r.npm.sankuai.com/lru-cache/download/lru-cache-5.1.1.tgz}

  magic-string@0.30.17:
    resolution: {integrity: sha1-RQpElnPSRg5bvPupphkWoXFMdFM=, tarball: http://r.npm.sankuai.com/magic-string/download/magic-string-0.30.17.tgz}

  make-dir@2.1.0:
    resolution: {integrity: sha1-XwMQ4YuL6JjMBwCSlaMK5B6R5vU=, tarball: http://r.npm.sankuai.com/make-dir/download/make-dir-2.1.0.tgz}
    engines: {node: '>=6'}

  map-cache@0.2.2:
    resolution: {integrity: sha1-wyq9C9ZSXZsFFkW7TyasXcmKDb8=, tarball: http://r.npm.sankuai.com/map-cache/download/map-cache-0.2.2.tgz}
    engines: {node: '>=0.10.0'}

  map-visit@1.0.0:
    resolution: {integrity: sha1-7Nyo8TFE5mDxtb1B8S80edmN+48=, tarball: http://r.npm.sankuai.com/map-visit/download/map-visit-1.0.0.tgz}
    engines: {node: '>=0.10.0'}

  math-intrinsics@1.1.0:
    resolution: {integrity: sha1-oN10voHiqlwvJ+Zc4oNgXuTit/k=, tarball: http://r.npm.sankuai.com/math-intrinsics/download/math-intrinsics-1.1.0.tgz}
    engines: {node: '>= 0.4'}

  merge-stream@2.0.0:
    resolution: {integrity: sha1-UoI2KaFN0AyXcPtq1H3GMQ8sH2A=, tarball: http://r.npm.sankuai.com/merge-stream/download/merge-stream-2.0.0.tgz}

  merge2@1.4.1:
    resolution: {integrity: sha1-Q2iJL4hekHRVpv19xVwMnUBJkK4=, tarball: http://r.npm.sankuai.com/merge2/download/merge2-1.4.1.tgz}
    engines: {node: '>= 8'}

  micromatch@3.1.10:
    resolution: {integrity: sha1-cIWbyVyYQJUvNZoGij/En57PrCM=, tarball: http://r.npm.sankuai.com/micromatch/download/micromatch-3.1.10.tgz}
    engines: {node: '>=0.10.0'}

  micromatch@4.0.8:
    resolution: {integrity: sha1-1m+hjzpHB2eJMgubGvMr2G2fogI=, tarball: http://r.npm.sankuai.com/micromatch/download/micromatch-4.0.8.tgz}
    engines: {node: '>=8.6'}

  mime-db@1.52.0:
    resolution: {integrity: sha1-u6vNwChZ9JhzAchW4zh85exDv3A=, tarball: http://r.npm.sankuai.com/mime-db/download/mime-db-1.52.0.tgz}
    engines: {node: '>= 0.6'}

  mime-types@2.1.35:
    resolution: {integrity: sha1-OBqHG2KnNEUGYK497uRIE/cNlZo=, tarball: http://r.npm.sankuai.com/mime-types/download/mime-types-2.1.35.tgz}
    engines: {node: '>= 0.6'}

  mimic-fn@4.0.0:
    resolution: {integrity: sha1-YKkFUNXLCyOcymXYk7GlOymHHsw=, tarball: http://r.npm.sankuai.com/mimic-fn/download/mimic-fn-4.0.0.tgz}
    engines: {node: '>=12'}

  mimic-function@5.0.1:
    resolution: {integrity: sha1-rL4rM0n5m53qyn+3Dki4PpTmcHY=, tarball: http://r.npm.sankuai.com/mimic-function/download/mimic-function-5.0.1.tgz}
    engines: {node: '>=18'}

  minimatch@10.0.1:
    resolution: {integrity: sha1-zgUhhWtFPIbiXyxMDQPm/33cRAs=, tarball: http://r.npm.sankuai.com/minimatch/download/minimatch-10.0.1.tgz}
    engines: {node: 20 || >=22}

  minimatch@3.1.2:
    resolution: {integrity: sha1-Gc0ZS/0+Qo8EmnCBfAONiatL41s=, tarball: http://r.npm.sankuai.com/minimatch/download/minimatch-3.1.2.tgz}

  minimatch@9.0.5:
    resolution: {integrity: sha1-10+d1rV9g9jpjPuCEzsDl4vJKeU=, tarball: http://r.npm.sankuai.com/minimatch/download/minimatch-9.0.5.tgz}
    engines: {node: '>=16 || 14 >=14.17'}

  minimist@1.2.8:
    resolution: {integrity: sha1-waRk52kzAuCCoHXO4MBXdBrEdyw=, tarball: http://r.npm.sankuai.com/minimist/download/minimist-1.2.8.tgz}

  minipass@7.1.2:
    resolution: {integrity: sha1-k6libOXl5mvU24aEnnUV6SNApwc=, tarball: http://r.npm.sankuai.com/minipass/download/minipass-7.1.2.tgz}
    engines: {node: '>=16 || 14 >=14.17'}

  mixin-deep@1.3.2:
    resolution: {integrity: sha1-ESC0PcNZp4Xc5ltVuC4lfM9HlWY=, tarball: http://r.npm.sankuai.com/mixin-deep/download/mixin-deep-1.3.2.tgz}
    engines: {node: '>=0.10.0'}

  mkdirp@0.5.6:
    resolution: {integrity: sha1-fe8D0kMtyuS6HWEURcSDlgYiVfY=, tarball: http://r.npm.sankuai.com/mkdirp/download/mkdirp-0.5.6.tgz}
    hasBin: true

  mockjs@1.1.0:
    resolution: {integrity: sha1-5qDDeOkZBtuv8gkRzAJzs8fXWwY=, tarball: http://r.npm.sankuai.com/mockjs/download/mockjs-1.1.0.tgz}
    hasBin: true

  ms@2.0.0:
    resolution: {integrity: sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=, tarball: http://r.npm.sankuai.com/ms/download/ms-2.0.0.tgz}

  ms@2.1.3:
    resolution: {integrity: sha1-V0yBOM4dK1hh8LRFedut1gxmFbI=, tarball: http://r.npm.sankuai.com/ms/download/ms-2.1.3.tgz}

  mute-stream@2.0.0:
    resolution: {integrity: sha1-pURvwMUStxyDxE2QjVx7e0xJOys=, tarball: http://r.npm.sankuai.com/mute-stream/download/mute-stream-2.0.0.tgz}
    engines: {node: ^18.17.0 || >=20.5.0}

  mz@2.7.0:
    resolution: {integrity: sha1-lQCAV6Vsr63CvGPd5/n/aVWUjjI=, tarball: http://r.npm.sankuai.com/mz/download/mz-2.7.0.tgz}

  nanoid@3.3.8:
    resolution: {integrity: sha1-sb4wML7jaq/xi6yzdeXM5SFoS68=, tarball: http://r.npm.sankuai.com/nanoid/download/nanoid-3.3.8.tgz}
    engines: {node: ^10 || ^12 || ^13.7 || ^14 || >=15.0.1}
    hasBin: true

  nanomatch@1.2.13:
    resolution: {integrity: sha1-uHqKpPwN6P5r6IiVs4mD/yZb0Rk=, tarball: http://r.npm.sankuai.com/nanomatch/download/nanomatch-1.2.13.tgz}
    engines: {node: '>=0.10.0'}

  natural-compare@1.4.0:
    resolution: {integrity: sha1-Sr6/7tdUHywnrPspvbvRXI1bpPc=, tarball: http://r.npm.sankuai.com/natural-compare/download/natural-compare-1.4.0.tgz}

  nested-error-stacks@2.1.1:
    resolution: {integrity: sha1-JsijzubMBfvPHjM80vw+ADMmwLU=, tarball: http://r.npm.sankuai.com/nested-error-stacks/download/nested-error-stacks-2.1.1.tgz}

  node-releases@2.0.19:
    resolution: {integrity: sha1-nkRaUpUJUexNF32EOvNwtBHK8xQ=, tarball: http://r.npm.sankuai.com/node-releases/download/node-releases-2.0.19.tgz}

  normalize-path@3.0.0:
    resolution: {integrity: sha1-Dc1p/yOhybEf0JeDFmRKA4ghamU=, tarball: http://r.npm.sankuai.com/normalize-path/download/normalize-path-3.0.0.tgz}
    engines: {node: '>=0.10.0'}

  normalize-range@0.1.2:
    resolution: {integrity: sha1-LRDAa9/TEuqXd2laTShDlFa3WUI=, tarball: http://r.npm.sankuai.com/normalize-range/download/normalize-range-0.1.2.tgz}
    engines: {node: '>=0.10.0'}

  normalize-wheel@1.0.1:
    resolution: {integrity: sha1-rsiGr/2wRQcNhWRH32Ls+GFG7EU=, tarball: http://r.npm.sankuai.com/normalize-wheel/download/normalize-wheel-1.0.1.tgz}

  npm-run-path@5.3.0:
    resolution: {integrity: sha1-4jNT0Ou5MX8XTpNBfkpNgtAknp8=, tarball: http://r.npm.sankuai.com/npm-run-path/download/npm-run-path-5.3.0.tgz}
    engines: {node: ^12.20.0 || ^14.13.1 || >=16.0.0}

  nth-check@2.1.1:
    resolution: {integrity: sha1-yeq0KO/842zWuSySS9sADvHx7R0=, tarball: http://r.npm.sankuai.com/nth-check/download/nth-check-2.1.1.tgz}

  object-assign@4.1.1:
    resolution: {integrity: sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM=, tarball: http://r.npm.sankuai.com/object-assign/download/object-assign-4.1.1.tgz}
    engines: {node: '>=0.10.0'}

  object-copy@0.1.0:
    resolution: {integrity: sha1-fn2Fi3gb18mRpBupde04EnVOmYw=, tarball: http://r.npm.sankuai.com/object-copy/download/object-copy-0.1.0.tgz}
    engines: {node: '>=0.10.0'}

  object-hash@3.0.0:
    resolution: {integrity: sha1-c/l/dT57r/wOLMnW4HkHl0Ssguk=, tarball: http://r.npm.sankuai.com/object-hash/download/object-hash-3.0.0.tgz}
    engines: {node: '>= 6'}

  object-inspect@1.13.3:
    resolution: {integrity: sha1-8UwYPeURMCQ9bRiuFJN1/1DqSIo=, tarball: http://r.npm.sankuai.com/object-inspect/download/object-inspect-1.13.3.tgz}
    engines: {node: '>= 0.4'}

  object-keys@1.1.1:
    resolution: {integrity: sha1-HEfyct8nfzsdrwYWd9nILiMixg4=, tarball: http://r.npm.sankuai.com/object-keys/download/object-keys-1.1.1.tgz}
    engines: {node: '>= 0.4'}

  object-visit@1.0.1:
    resolution: {integrity: sha1-95xEk68MU3e1n+OdOV5BBC3QRbs=, tarball: http://r.npm.sankuai.com/object-visit/download/object-visit-1.0.1.tgz}
    engines: {node: '>=0.10.0'}

  object.assign@4.1.7:
    resolution: {integrity: sha1-jBTKGkJMalYbC7KiL2b1BJqUXT0=, tarball: http://r.npm.sankuai.com/object.assign/download/object.assign-4.1.7.tgz}
    engines: {node: '>= 0.4'}

  object.entries@1.1.8:
    resolution: {integrity: sha1-v/5vKC4B9NF4ByBKJPjt2CNZnEE=, tarball: http://r.npm.sankuai.com/object.entries/download/object.entries-1.1.8.tgz}
    engines: {node: '>= 0.4'}

  object.fromentries@2.0.8:
    resolution: {integrity: sha1-9xldipuXvZXLwZmeqTns0aKwDGU=, tarball: http://r.npm.sankuai.com/object.fromentries/download/object.fromentries-2.0.8.tgz}
    engines: {node: '>= 0.4'}

  object.pick@1.3.0:
    resolution: {integrity: sha1-h6EKxMFpS9Lhy/U1kaZhQftd10c=, tarball: http://r.npm.sankuai.com/object.pick/download/object.pick-1.3.0.tgz}
    engines: {node: '>=0.10.0'}

  object.values@1.2.1:
    resolution: {integrity: sha1-3u1SClCAn/f3Wnz9S8ZMegOMYhY=, tarball: http://r.npm.sankuai.com/object.values/download/object.values-1.2.1.tgz}
    engines: {node: '>= 0.4'}

  on-finished@2.3.0:
    resolution: {integrity: sha1-IPEzZIGwg811M3mSoWlxqi2QaUc=, tarball: http://r.npm.sankuai.com/on-finished/download/on-finished-2.3.0.tgz}
    engines: {node: '>= 0.8'}

  once@1.4.0:
    resolution: {integrity: sha1-WDsap3WWHUsROsF9nFC6753Xa9E=, tarball: http://r.npm.sankuai.com/once/download/once-1.4.0.tgz}

  onetime@6.0.0:
    resolution: {integrity: sha1-fCTBjtH9LpvKS9JoBqM2E8d9NLQ=, tarball: http://r.npm.sankuai.com/onetime/download/onetime-6.0.0.tgz}
    engines: {node: '>=12'}

  onetime@7.0.0:
    resolution: {integrity: sha1-nxbJLYye9RIOOs2d2ZV8zuzBq2A=, tarball: http://r.npm.sankuai.com/onetime/download/onetime-7.0.0.tgz}
    engines: {node: '>=18'}

  open@10.1.0:
    resolution: {integrity: sha1-p3lebl1Rmr5ChtmTe7JLURIlmOE=, tarball: http://r.npm.sankuai.com/open/download/open-10.1.0.tgz}
    engines: {node: '>=18'}

  optionator@0.9.4:
    resolution: {integrity: sha1-fqHBpdkddk+yghOciP4R4YKjpzQ=, tarball: http://r.npm.sankuai.com/optionator/download/optionator-0.9.4.tgz}
    engines: {node: '>= 0.8.0'}

  ora@8.1.1:
    resolution: {integrity: sha1-jvyIZeRMh+S1VGikfoCgPmeLDlQ=, tarball: http://r.npm.sankuai.com/ora/download/ora-8.1.1.tgz}
    engines: {node: '>=18'}

  os-tmpdir@1.0.2:
    resolution: {integrity: sha1-u+Z0BseaqFxc/sdm/lc0VV36EnQ=, tarball: http://r.npm.sankuai.com/os-tmpdir/download/os-tmpdir-1.0.2.tgz}
    engines: {node: '>=0.10.0'}

  p-limit@3.1.0:
    resolution: {integrity: sha1-4drMvnjQ0TiMoYxk/qOOPlfjcGs=, tarball: http://r.npm.sankuai.com/p-limit/download/p-limit-3.1.0.tgz}
    engines: {node: '>=10'}

  p-locate@5.0.0:
    resolution: {integrity: sha1-g8gxXGeFAF470CGDlBHJ4RDm2DQ=, tarball: http://r.npm.sankuai.com/p-locate/download/p-locate-5.0.0.tgz}
    engines: {node: '>=10'}

  package-json-from-dist@1.0.1:
    resolution: {integrity: sha1-TxRxoBCCeob5TP2bByfjbSZ95QU=, tarball: http://r.npm.sankuai.com/package-json-from-dist/download/package-json-from-dist-1.0.1.tgz}

  parent-module@1.0.1:
    resolution: {integrity: sha1-aR0nCeeMefrjoVZiJFLQB2LKqqI=, tarball: http://r.npm.sankuai.com/parent-module/download/parent-module-1.0.1.tgz}
    engines: {node: '>=6'}

  parseurl@1.3.3:
    resolution: {integrity: sha1-naGee+6NEt/wUT7Vt2lXeTvC6NQ=, tarball: http://r.npm.sankuai.com/parseurl/download/parseurl-1.3.3.tgz}
    engines: {node: '>= 0.8'}

  pascalcase@0.1.1:
    resolution: {integrity: sha1-s2PlXoAGym/iF4TS2yK9FdeRfxQ=, tarball: http://r.npm.sankuai.com/pascalcase/download/pascalcase-0.1.1.tgz}
    engines: {node: '>=0.10.0'}

  path-dirname@1.0.2:
    resolution: {integrity: sha1-zDPSTVJeCZpTiMAzbG4yuRYGCeA=, tarball: http://r.npm.sankuai.com/path-dirname/download/path-dirname-1.0.2.tgz}

  path-exists@4.0.0:
    resolution: {integrity: sha1-UTvb4tO5XXdi6METfvoZXGxhtbM=, tarball: http://r.npm.sankuai.com/path-exists/download/path-exists-4.0.0.tgz}
    engines: {node: '>=8'}

  path-is-absolute@1.0.1:
    resolution: {integrity: sha1-F0uSaHNVNP+8es5r9TpanhtcX18=, tarball: http://r.npm.sankuai.com/path-is-absolute/download/path-is-absolute-1.0.1.tgz}
    engines: {node: '>=0.10.0'}

  path-key@3.1.1:
    resolution: {integrity: sha1-WB9q3mWMu6ZaDTOA3ndTKVBU83U=, tarball: http://r.npm.sankuai.com/path-key/download/path-key-3.1.1.tgz}
    engines: {node: '>=8'}

  path-key@4.0.0:
    resolution: {integrity: sha1-KVWI3DruZBVPh3rbnXgLgcVUvxg=, tarball: http://r.npm.sankuai.com/path-key/download/path-key-4.0.0.tgz}
    engines: {node: '>=12'}

  path-parse@1.0.7:
    resolution: {integrity: sha1-+8EUtgykKzDZ2vWFjkvWi77bZzU=, tarball: http://r.npm.sankuai.com/path-parse/download/path-parse-1.0.7.tgz}

  path-scurry@1.11.1:
    resolution: {integrity: sha1-eWCmaIiFlKByCxKpEdGnQqufEdI=, tarball: http://r.npm.sankuai.com/path-scurry/download/path-scurry-1.11.1.tgz}
    engines: {node: '>=16 || 14 >=14.18'}

  path-scurry@2.0.0:
    resolution: {integrity: sha1-nwUiifI62L+Tl6KgQl57hhXFhYA=, tarball: http://r.npm.sankuai.com/path-scurry/download/path-scurry-2.0.0.tgz}
    engines: {node: 20 || >=22}

  path-to-regexp@6.3.0:
    resolution: {integrity: sha1-K2omozdzeo4UFvknLtB2axwDifQ=, tarball: http://r.npm.sankuai.com/path-to-regexp/download/path-to-regexp-6.3.0.tgz}

  path-type@3.0.0:
    resolution: {integrity: sha1-zvMdyOCho7sNEFwM2Xzzv0f0428=, tarball: http://r.npm.sankuai.com/path-type/download/path-type-3.0.0.tgz}
    engines: {node: '>=4'}

  path-type@4.0.0:
    resolution: {integrity: sha1-hO0BwKe6OAr+CdkKjBgNzZ0DBDs=, tarball: http://r.npm.sankuai.com/path-type/download/path-type-4.0.0.tgz}
    engines: {node: '>=8'}

  picocolors@1.1.1:
    resolution: {integrity: sha1-PTIa8+q5ObCDyPkpodEs2oHCa2s=, tarball: http://r.npm.sankuai.com/picocolors/download/picocolors-1.1.1.tgz}

  picomatch@2.3.1:
    resolution: {integrity: sha1-O6ODNzNkbZ0+SZWUbBNlpn+wekI=, tarball: http://r.npm.sankuai.com/picomatch/download/picomatch-2.3.1.tgz}
    engines: {node: '>=8.6'}

  picomatch@4.0.2:
    resolution: {integrity: sha1-d8dCkx6PO4gglGx2zQwfE3MNHas=, tarball: http://r.npm.sankuai.com/picomatch/download/picomatch-4.0.2.tgz}
    engines: {node: '>=12'}

  pidtree@0.6.0:
    resolution: {integrity: sha1-kK17bULVhB5p4KJBnvOPiIOqBXw=, tarball: http://r.npm.sankuai.com/pidtree/download/pidtree-0.6.0.tgz}
    engines: {node: '>=0.10'}
    hasBin: true

  pify@2.3.0:
    resolution: {integrity: sha1-7RQaasBDqEnqWISY59yosVMw6Qw=, tarball: http://r.npm.sankuai.com/pify/download/pify-2.3.0.tgz}
    engines: {node: '>=0.10.0'}

  pify@3.0.0:
    resolution: {integrity: sha1-5aSs0sEB/fPZpNB/DbxNtJ3SgXY=, tarball: http://r.npm.sankuai.com/pify/download/pify-3.0.0.tgz}
    engines: {node: '>=4'}

  pify@4.0.1:
    resolution: {integrity: sha1-SyzSXFDVmHNcUCkiJP2MbfQeMjE=, tarball: http://r.npm.sankuai.com/pify/download/pify-4.0.1.tgz}
    engines: {node: '>=6'}

  pirates@4.0.6:
    resolution: {integrity: sha1-MBiuMuz8/2wpuiJny/IRZqwfNrk=, tarball: http://r.npm.sankuai.com/pirates/download/pirates-4.0.6.tgz}
    engines: {node: '>= 6'}

  pkg-dir@8.0.0:
    resolution: {integrity: sha1-jz3ouoPUa3KgXIC/1OV58GD6keI=, tarball: http://r.npm.sankuai.com/pkg-dir/download/pkg-dir-8.0.0.tgz}
    engines: {node: '>=18'}

  popper.js@1.16.1:
    resolution: {integrity: sha1-KiI8s9x7YhPXQOQDcr5A3kPmWxs=, tarball: http://r.npm.sankuai.com/popper.js/download/popper.js-1.16.1.tgz}
    deprecated: You can find the new Popper v2 at @popperjs/core, this package is dedicated to the legacy v1

  posix-character-classes@0.1.1:
    resolution: {integrity: sha1-AerA/jta9xoqbAL+q7jB/vfgDqs=, tarball: http://r.npm.sankuai.com/posix-character-classes/download/posix-character-classes-0.1.1.tgz}
    engines: {node: '>=0.10.0'}

  possible-typed-array-names@1.0.0:
    resolution: {integrity: sha1-ibtjxvraLD6QrcSmR77us5zHv48=, tarball: http://r.npm.sankuai.com/possible-typed-array-names/download/possible-typed-array-names-1.0.0.tgz}
    engines: {node: '>= 0.4'}

  postcss-import@15.1.0:
    resolution: {integrity: sha1-QcZO2MwOI3NalpizJJ/9v3BK3HA=, tarball: http://r.npm.sankuai.com/postcss-import/download/postcss-import-15.1.0.tgz}
    engines: {node: '>=14.0.0'}
    peerDependencies:
      postcss: ^8.0.0

  postcss-js@4.0.1:
    resolution: {integrity: sha1-YVmBhvNwO6sFLxxPfYBfOZG+6dI=, tarball: http://r.npm.sankuai.com/postcss-js/download/postcss-js-4.0.1.tgz}
    engines: {node: ^12 || ^14 || >= 16}
    peerDependencies:
      postcss: ^8.4.21

  postcss-load-config@4.0.2:
    resolution: {integrity: sha1-cVnc9iYRjTPimfSF1q/kr/fEo+M=, tarball: http://r.npm.sankuai.com/postcss-load-config/download/postcss-load-config-4.0.2.tgz}
    engines: {node: '>= 14'}
    peerDependencies:
      postcss: '>=8.0.9'
      ts-node: '>=9.0.0'
    peerDependenciesMeta:
      postcss:
        optional: true
      ts-node:
        optional: true

  postcss-nested@6.2.0:
    resolution: {integrity: sha1-TC0iq18gucth4sXFkVlQeE0GgTE=, tarball: http://r.npm.sankuai.com/postcss-nested/download/postcss-nested-6.2.0.tgz}
    engines: {node: '>=12.0'}
    peerDependencies:
      postcss: ^8.2.14

  postcss-selector-parser@6.1.2:
    resolution: {integrity: sha1-J+y0H7Djtrp6HshP/zR/c0x5Kd4=, tarball: http://r.npm.sankuai.com/postcss-selector-parser/download/postcss-selector-parser-6.1.2.tgz}
    engines: {node: '>=4'}

  postcss-value-parser@4.2.0:
    resolution: {integrity: sha1-cjwJkgg2um0+WvAZ+SvAlxwC5RQ=, tarball: http://r.npm.sankuai.com/postcss-value-parser/download/postcss-value-parser-4.2.0.tgz}

  postcss@8.4.49:
    resolution: {integrity: sha1-TqR5BIqwWas65h0IIZD6v9mU/hk=, tarball: http://r.npm.sankuai.com/postcss/download/postcss-8.4.49.tgz}
    engines: {node: ^10 || ^12 || >=14}

  prelude-ls@1.2.1:
    resolution: {integrity: sha1-3rxkidem5rDnYRiIzsiAM30xY5Y=, tarball: http://r.npm.sankuai.com/prelude-ls/download/prelude-ls-1.2.1.tgz}
    engines: {node: '>= 0.8.0'}

  prettier@3.4.2:
    resolution: {integrity: sha1-pc4ftSKliL8reMpExub+WqWisT8=, tarball: http://r.npm.sankuai.com/prettier/download/prettier-3.4.2.tgz}
    engines: {node: '>=14'}
    hasBin: true

  prop-types@15.8.1:
    resolution: {integrity: sha1-Z9h78aaU9IQ1zzMsJK8QIUoxQLU=, tarball: http://r.npm.sankuai.com/prop-types/download/prop-types-15.8.1.tgz}

  proxy-from-env@1.1.0:
    resolution: {integrity: sha1-4QLxbKNVQkhldV0sno6k8k1Yw+I=, tarball: http://r.npm.sankuai.com/proxy-from-env/download/proxy-from-env-1.1.0.tgz}

  punycode@2.3.1:
    resolution: {integrity: sha1-AnQi4vrsCyXhVJw+G9gwm5EztuU=, tarball: http://r.npm.sankuai.com/punycode/download/punycode-2.3.1.tgz}
    engines: {node: '>=6'}

  queue-microtask@1.2.3:
    resolution: {integrity: sha1-SSkii7xyTfrEPg77BYyve2z7YkM=, tarball: http://r.npm.sankuai.com/queue-microtask/download/queue-microtask-1.2.3.tgz}

  rc-cascader@3.30.0:
    resolution: {integrity: sha1-2j41ytzADFjGKmdX7KbHFH/5Tqg=, tarball: http://r.npm.sankuai.com/rc-cascader/download/rc-cascader-3.30.0.tgz}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'

  rc-checkbox@3.3.0:
    resolution: {integrity: sha1-D/y2WreMfS/NGg1lVK82eGUWvQI=, tarball: http://r.npm.sankuai.com/rc-checkbox/download/rc-checkbox-3.3.0.tgz}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'

  rc-collapse@3.9.0:
    resolution: {integrity: sha1-lyQEznck4cnR0kdlQ+EXVASjaAY=, tarball: http://r.npm.sankuai.com/rc-collapse/download/rc-collapse-3.9.0.tgz}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'

  rc-dialog@9.6.0:
    resolution: {integrity: sha1-3HolXGrRy1YCHDphx96G7ojHw3E=, tarball: http://r.npm.sankuai.com/rc-dialog/download/rc-dialog-9.6.0.tgz}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'

  rc-drawer@7.2.0:
    resolution: {integrity: sha1-jX3i8f1S86xaJfVK+7isFMYuVmM=, tarball: http://r.npm.sankuai.com/rc-drawer/download/rc-drawer-7.2.0.tgz}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'

  rc-dropdown@4.2.1:
    resolution: {integrity: sha1-RHKesqQnLgNT0xrAYNoh5gasyxw=, tarball: http://r.npm.sankuai.com/rc-dropdown/download/rc-dropdown-4.2.1.tgz}
    peerDependencies:
      react: '>=16.11.0'
      react-dom: '>=16.11.0'

  rc-field-form@2.7.0:
    resolution: {integrity: sha1-IkE+eT81v8HzWw7EYndNcnf1o5k=, tarball: http://r.npm.sankuai.com/rc-field-form/download/rc-field-form-2.7.0.tgz}
    engines: {node: '>=8.x'}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'

  rc-image@7.11.0:
    resolution: {integrity: sha1-GMd+pVem/b4mhWxoipqs4VBcDnc=, tarball: http://r.npm.sankuai.com/rc-image/download/rc-image-7.11.0.tgz}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'

  rc-input-number@9.3.0:
    resolution: {integrity: sha1-NAPBBx/Lnb+RBz+t3YDqg705dN8=, tarball: http://r.npm.sankuai.com/rc-input-number/download/rc-input-number-9.3.0.tgz}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'

  rc-input@1.6.4:
    resolution: {integrity: sha1-CNkUYPa3Wz+lKUFU6Jd1eEwjMSk=, tarball: http://r.npm.sankuai.com/rc-input/download/rc-input-1.6.4.tgz}
    peerDependencies:
      react: '>=16.0.0'
      react-dom: '>=16.0.0'

  rc-mentions@2.17.0:
    resolution: {integrity: sha1-0W3VyOTbh4YsEAf3GVsK6pJHzc0=, tarball: http://r.npm.sankuai.com/rc-mentions/download/rc-mentions-2.17.0.tgz}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'

  rc-menu@9.16.0:
    resolution: {integrity: sha1-U2R/YPUTv6Cb/BrMvZao3ySQASE=, tarball: http://r.npm.sankuai.com/rc-menu/download/rc-menu-9.16.0.tgz}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'

  rc-motion@2.9.5:
    resolution: {integrity: sha1-Esbq1P01X5TwDem7TxXfV21nfgw=, tarball: http://r.npm.sankuai.com/rc-motion/download/rc-motion-2.9.5.tgz}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'

  rc-notification@5.6.2:
    resolution: {integrity: sha1-hSWzLUndluyXSsrmHR0eq95hRjo=, tarball: http://r.npm.sankuai.com/rc-notification/download/rc-notification-5.6.2.tgz}
    engines: {node: '>=8.x'}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'

  rc-overflow@1.3.2:
    resolution: {integrity: sha1-cu5J6FoTCNjU471TKF3B8+C8ziw=, tarball: http://r.npm.sankuai.com/rc-overflow/download/rc-overflow-1.3.2.tgz}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'

  rc-pagination@5.0.0:
    resolution: {integrity: sha1-djPh8P83KteMA+hrzveLZgN00ZY=, tarball: http://r.npm.sankuai.com/rc-pagination/download/rc-pagination-5.0.0.tgz}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'

  rc-picker@4.8.3:
    resolution: {integrity: sha1-Bs/9WiIB/I0nThL37jLqi6bz9g8=, tarball: http://r.npm.sankuai.com/rc-picker/download/rc-picker-4.8.3.tgz}
    engines: {node: '>=8.x'}
    peerDependencies:
      date-fns: '>= 2.x'
      dayjs: '>= 1.x'
      luxon: '>= 3.x'
      moment: '>= 2.x'
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    peerDependenciesMeta:
      date-fns:
        optional: true
      dayjs:
        optional: true
      luxon:
        optional: true
      moment:
        optional: true

  rc-progress@4.0.0:
    resolution: {integrity: sha1-U4IUfZrdM9Ol+9JkABNz32RA4SY=, tarball: http://r.npm.sankuai.com/rc-progress/download/rc-progress-4.0.0.tgz}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'

  rc-rate@2.13.0:
    resolution: {integrity: sha1-ZC9ZHM9Vw6XYTY0hLK8feVHSA6g=, tarball: http://r.npm.sankuai.com/rc-rate/download/rc-rate-2.13.0.tgz}
    engines: {node: '>=8.x'}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'

  rc-resize-observer@1.4.3:
    resolution: {integrity: sha1-T9QfpWG6UTYrUVWgfDXXyJoepWk=, tarball: http://r.npm.sankuai.com/rc-resize-observer/download/rc-resize-observer-1.4.3.tgz}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'

  rc-segmented@2.5.0:
    resolution: {integrity: sha1-O1QjrfV0WTRcd8OcdYH954ahbBE=, tarball: http://r.npm.sankuai.com/rc-segmented/download/rc-segmented-2.5.0.tgz}
    peerDependencies:
      react: '>=16.0.0'
      react-dom: '>=16.0.0'

  rc-select@14.16.4:
    resolution: {integrity: sha1-qYhAxM+5biY8dQ5ZM06gooYuBPw=, tarball: http://r.npm.sankuai.com/rc-select/download/rc-select-14.16.4.tgz}
    engines: {node: '>=8.x'}
    peerDependencies:
      react: '*'
      react-dom: '*'

  rc-slider@11.1.7:
    resolution: {integrity: sha1-PeMzseyE1Tp72i+Ba7R3lCNijwk=, tarball: http://r.npm.sankuai.com/rc-slider/download/rc-slider-11.1.7.tgz}
    engines: {node: '>=8.x'}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'

  rc-steps@6.0.1:
    resolution: {integrity: sha1-whNs0Ah3M/bVCSCahKXIDcKaJ00=, tarball: http://r.npm.sankuai.com/rc-steps/download/rc-steps-6.0.1.tgz}
    engines: {node: '>=8.x'}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'

  rc-switch@4.1.0:
    resolution: {integrity: sha1-832BtODFr9EnT9hTZ7FzBr8l59c=, tarball: http://r.npm.sankuai.com/rc-switch/download/rc-switch-4.1.0.tgz}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'

  rc-table@7.49.0:
    resolution: {integrity: sha1-9aSIDZUn0snkL19yG1Qj56HKR1s=, tarball: http://r.npm.sankuai.com/rc-table/download/rc-table-7.49.0.tgz}
    engines: {node: '>=8.x'}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'

  rc-tabs@15.4.0:
    resolution: {integrity: sha1-qCnKvLM/k1JbVIAQ9bv5He56wdY=, tarball: http://r.npm.sankuai.com/rc-tabs/download/rc-tabs-15.4.0.tgz}
    engines: {node: '>=8.x'}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'

  rc-textarea@1.8.2:
    resolution: {integrity: sha1-V6aEcwRVHBiD/D+wxQdtWH9wv38=, tarball: http://r.npm.sankuai.com/rc-textarea/download/rc-textarea-1.8.2.tgz}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'

  rc-tooltip@6.2.1:
    resolution: {integrity: sha1-mo8DNchkQ6DCDCVXkzIF9kWjgbc=, tarball: http://r.npm.sankuai.com/rc-tooltip/download/rc-tooltip-6.2.1.tgz}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'

  rc-tree-select@5.24.5:
    resolution: {integrity: sha1-ob+Fx9Xkl5iAz7B0i7a6uTftNIM=, tarball: http://r.npm.sankuai.com/rc-tree-select/download/rc-tree-select-5.24.5.tgz}
    peerDependencies:
      react: '*'
      react-dom: '*'

  rc-tree@5.10.1:
    resolution: {integrity: sha1-iAdhTFSqo57cBTkvD1mCtgnZUlU=, tarball: http://r.npm.sankuai.com/rc-tree/download/rc-tree-5.10.1.tgz}
    engines: {node: '>=10.x'}
    peerDependencies:
      react: '*'
      react-dom: '*'

  rc-upload@4.8.1:
    resolution: {integrity: sha1-rFXyvBAblbUqbkfzwY8PVbVOFtI=, tarball: http://r.npm.sankuai.com/rc-upload/download/rc-upload-4.8.1.tgz}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'

  rc-util@5.44.3:
    resolution: {integrity: sha1-nspQOZBkRhE8QDKFn4jBUjRUeWE=, tarball: http://r.npm.sankuai.com/rc-util/download/rc-util-5.44.3.tgz}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'

  rc-virtual-list@3.16.1:
    resolution: {integrity: sha1-Bz11zAKVSXzdmjXW9dG3G081Iz4=, tarball: http://r.npm.sankuai.com/rc-virtual-list/download/rc-virtual-list-3.16.1.tgz}
    engines: {node: '>=8.x'}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'

  react-dom@18.3.1:
    resolution: {integrity: sha1-wiZdeVEbV9R5s90/36UVNklMXLQ=, tarball: http://r.npm.sankuai.com/react-dom/download/react-dom-18.3.1.tgz}
    peerDependencies:
      react: ^18.3.1

  react-is@16.13.1:
    resolution: {integrity: sha1-eJcppNw23imZ3BVt1sHZwYzqVqQ=, tarball: http://r.npm.sankuai.com/react-is/download/react-is-16.13.1.tgz}

  react-is@18.3.1:
    resolution: {integrity: sha1-6DVX3BLq5jqZ4AOkY4ix3LtE234=, tarball: http://r.npm.sankuai.com/react-is/download/react-is-18.3.1.tgz}

  react-refresh@0.14.2:
    resolution: {integrity: sha1-ODPaAc4y2kcPH5NrnUd9pccCi/k=, tarball: http://r.npm.sankuai.com/react-refresh/download/react-refresh-0.14.2.tgz}
    engines: {node: '>=0.10.0'}

  react-router@7.1.1:
    resolution: {integrity: sha1-iPVlf6W48LkYxyIuxxDeAnTQCy4=, tarball: http://r.npm.sankuai.com/react-router/download/react-router-7.1.1.tgz}
    engines: {node: '>=20.0.0'}
    peerDependencies:
      react: '>=18'
      react-dom: '>=18'
    peerDependenciesMeta:
      react-dom:
        optional: true

  react@18.3.1:
    resolution: {integrity: sha1-SauJIAnFOTNiW9FrJTP8dUyrKJE=, tarball: http://r.npm.sankuai.com/react/download/react-18.3.1.tgz}
    engines: {node: '>=0.10.0'}

  read-cache@1.0.0:
    resolution: {integrity: sha1-5mTvMRYRZsl1HNvo28+GtftY93Q=, tarball: http://r.npm.sankuai.com/read-cache/download/read-cache-1.0.0.tgz}

  readdirp@3.6.0:
    resolution: {integrity: sha1-dKNwvYVxFuJFspzJc0DNQxoCpsc=, tarball: http://r.npm.sankuai.com/readdirp/download/readdirp-3.6.0.tgz}
    engines: {node: '>=8.10.0'}

  reflect.getprototypeof@1.0.9:
    resolution: {integrity: sha1-yQXzOGAI3pWmIxXz6oYwQEvhni8=, tarball: http://r.npm.sankuai.com/reflect.getprototypeof/download/reflect.getprototypeof-1.0.9.tgz}
    engines: {node: '>= 0.4'}

  regenerator-runtime@0.14.1:
    resolution: {integrity: sha1-NWreECY/aF3aElEAzYYsHbiVMn8=, tarball: http://r.npm.sankuai.com/regenerator-runtime/download/regenerator-runtime-0.14.1.tgz}

  regex-not@1.0.2:
    resolution: {integrity: sha1-H07OJ+ALC2XgJHpoEOaoXYOldSw=, tarball: http://r.npm.sankuai.com/regex-not/download/regex-not-1.0.2.tgz}
    engines: {node: '>=0.10.0'}

  regexp.prototype.flags@1.5.3:
    resolution: {integrity: sha1-s65AsdJJm4NQqyw/5u84RdOpb0I=, tarball: http://r.npm.sankuai.com/regexp.prototype.flags/download/regexp.prototype.flags-1.5.3.tgz}
    engines: {node: '>= 0.4'}

  repeat-element@1.1.4:
    resolution: {integrity: sha1-vmgVIIR6tYx1aKx1+/rSjtQtOek=, tarball: http://r.npm.sankuai.com/repeat-element/download/repeat-element-1.1.4.tgz}
    engines: {node: '>=0.10.0'}

  repeat-string@1.6.1:
    resolution: {integrity: sha1-jcrkcOHIirwtYA//Sndihtp15jc=, tarball: http://r.npm.sankuai.com/repeat-string/download/repeat-string-1.6.1.tgz}
    engines: {node: '>=0.10'}

  resize-observer-polyfill@1.5.1:
    resolution: {integrity: sha1-DpAg3T0hAkRY1OvSfiPkAmmBBGQ=, tarball: http://r.npm.sankuai.com/resize-observer-polyfill/download/resize-observer-polyfill-1.5.1.tgz}

  resolve-from@4.0.0:
    resolution: {integrity: sha1-SrzYUq0y3Xuqv+m0DgCjbbXzkuY=, tarball: http://r.npm.sankuai.com/resolve-from/download/resolve-from-4.0.0.tgz}
    engines: {node: '>=4'}

  resolve-pkg-maps@1.0.0:
    resolution: {integrity: sha1-YWs9wsVwVrVYjDHN9LPWTbEzcg8=, tarball: http://r.npm.sankuai.com/resolve-pkg-maps/download/resolve-pkg-maps-1.0.0.tgz}

  resolve-url@0.2.1:
    resolution: {integrity: sha1-LGN/53yJOv0qZj/iGqkIAGjiBSo=, tarball: http://r.npm.sankuai.com/resolve-url/download/resolve-url-0.2.1.tgz}
    deprecated: https://github.com/lydell/resolve-url#deprecated

  resolve@1.22.10:
    resolution: {integrity: sha1-tmPoP/sJu/I4aURza6roAwKbizk=, tarball: http://r.npm.sankuai.com/resolve/download/resolve-1.22.10.tgz}
    engines: {node: '>= 0.4'}
    hasBin: true

  resolve@2.0.0-next.5:
    resolution: {integrity: sha1-aw7DEH5nHlK2jNBo7zJxc7kNwDw=, tarball: http://r.npm.sankuai.com/resolve/download/resolve-2.0.0-next.5.tgz}
    hasBin: true

  restore-cursor@5.1.0:
    resolution: {integrity: sha1-B2bZVpnvrLFBUJk/VbrwlT6h6+c=, tarball: http://r.npm.sankuai.com/restore-cursor/download/restore-cursor-5.1.0.tgz}
    engines: {node: '>=18'}

  ret@0.1.15:
    resolution: {integrity: sha1-uKSCXVvbH8P29Twrwz+BOIaBx7w=, tarball: http://r.npm.sankuai.com/ret/download/ret-0.1.15.tgz}
    engines: {node: '>=0.12'}

  reusify@1.0.4:
    resolution: {integrity: sha1-kNo4Kx4SbvwCFG6QhFqI2xKSXXY=, tarball: http://r.npm.sankuai.com/reusify/download/reusify-1.0.4.tgz}
    engines: {iojs: '>=1.0.0', node: '>=0.10.0'}

  rfdc@1.4.1:
    resolution: {integrity: sha1-d492xPtzHZNBTo+SX77PZMzn9so=, tarball: http://r.npm.sankuai.com/rfdc/download/rfdc-1.4.1.tgz}

  rimraf@3.0.2:
    resolution: {integrity: sha1-8aVAK6YiCtUswSgrrBrjqkn9Bho=, tarball: http://r.npm.sankuai.com/rimraf/download/rimraf-3.0.2.tgz}
    deprecated: Rimraf versions prior to v4 are no longer supported
    hasBin: true

  rollup-plugin-cpy@2.0.1:
    resolution: {integrity: sha1-U1vgq71JOXJoqHvxkm2Ez5iLco8=, tarball: http://r.npm.sankuai.com/rollup-plugin-cpy/download/rollup-plugin-cpy-2.0.1.tgz}
    engines: {node: '> 8.0'}
    peerDependencies:
      rollup: '>= 1.0'

  rollup-plugin-esbuild@6.1.1:
    resolution: {integrity: sha1-7B26ZH2+GXT3YZLHXpB6putjY5k=, tarball: http://r.npm.sankuai.com/rollup-plugin-esbuild/download/rollup-plugin-esbuild-6.1.1.tgz}
    engines: {node: '>=14.18.0'}
    peerDependencies:
      esbuild: '>=0.18.0'
      rollup: ^1.20.0 || ^2.0.0 || ^3.0.0 || ^4.0.0

  rollup@4.30.1:
    resolution: {integrity: sha1-1cPQZgVSWTZs3D628dBRxdavr3Q=, tarball: http://r.npm.sankuai.com/rollup/download/rollup-4.30.1.tgz}
    engines: {node: '>=18.0.0', npm: '>=8.0.0'}
    hasBin: true

  run-applescript@7.0.0:
    resolution: {integrity: sha1-5aVTwr/9Yg4WnSdsHNjxtkd4++s=, tarball: http://r.npm.sankuai.com/run-applescript/download/run-applescript-7.0.0.tgz}
    engines: {node: '>=18'}

  run-async@3.0.0:
    resolution: {integrity: sha1-QqQy9tdsaJUiBYmEOE3yi+N52q0=, tarball: http://r.npm.sankuai.com/run-async/download/run-async-3.0.0.tgz}
    engines: {node: '>=0.12.0'}

  run-parallel@1.2.0:
    resolution: {integrity: sha1-ZtE2jae9+SHrnZW9GpIp5/IaQ+4=, tarball: http://r.npm.sankuai.com/run-parallel/download/run-parallel-1.2.0.tgz}

  rxjs@7.8.1:
    resolution: {integrity: sha1-b289meqARCke/ZLnx/z1YsQFdUM=, tarball: http://r.npm.sankuai.com/rxjs/download/rxjs-7.8.1.tgz}

  rxjs@7.8.2:
    resolution: {integrity: sha1-lVvEc+2K8RoAKivlIHG/R1Y4YHs=, tarball: http://r.npm.sankuai.com/rxjs/download/rxjs-7.8.2.tgz}

  safe-array-concat@1.1.3:
    resolution: {integrity: sha1-yeVOxPYDsLu45+UAel7nrs0VOMM=, tarball: http://r.npm.sankuai.com/safe-array-concat/download/safe-array-concat-1.1.3.tgz}
    engines: {node: '>=0.4'}

  safe-buffer@5.2.1:
    resolution: {integrity: sha1-Hq+fqb2x/dTsdfWPnNtOa3gn7sY=, tarball: http://r.npm.sankuai.com/safe-buffer/download/safe-buffer-5.2.1.tgz}

  safe-regex-test@1.1.0:
    resolution: {integrity: sha1-f4fftnoxUHguqvGFg/9dFxGsEME=, tarball: http://r.npm.sankuai.com/safe-regex-test/download/safe-regex-test-1.1.0.tgz}
    engines: {node: '>= 0.4'}

  safe-regex@1.1.0:
    resolution: {integrity: sha1-QKNmnzsHfR6UPURinhV91IAjvy4=, tarball: http://r.npm.sankuai.com/safe-regex/download/safe-regex-1.1.0.tgz}

  safer-buffer@2.1.2:
    resolution: {integrity: sha1-RPoWGwGHuVSd2Eu5GAL5vYOFzWo=, tarball: http://r.npm.sankuai.com/safer-buffer/download/safer-buffer-2.1.2.tgz}

  sass-embedded-android-arm64@1.83.0:
    resolution: {integrity: sha1-1VLSWfrW5YzjIITvWkKsih+Ig7o=, tarball: http://r.npm.sankuai.com/sass-embedded-android-arm64/download/sass-embedded-android-arm64-1.83.0.tgz}
    engines: {node: '>=14.0.0'}
    cpu: [arm64]
    os: [android]

  sass-embedded-android-arm@1.83.0:
    resolution: {integrity: sha1-L1Ykbyw+HcEjR2HKADFmwT8h3FM=, tarball: http://r.npm.sankuai.com/sass-embedded-android-arm/download/sass-embedded-android-arm-1.83.0.tgz}
    engines: {node: '>=14.0.0'}
    cpu: [arm]
    os: [android]

  sass-embedded-android-ia32@1.83.0:
    resolution: {integrity: sha1-SijvGlOFmD7YYNfl75iFn7YEaK4=, tarball: http://r.npm.sankuai.com/sass-embedded-android-ia32/download/sass-embedded-android-ia32-1.83.0.tgz}
    engines: {node: '>=14.0.0'}
    cpu: [ia32]
    os: [android]

  sass-embedded-android-riscv64@1.83.0:
    resolution: {integrity: sha1-xVsv1kyRUyWvlz7zaL3HhyE4KBo=, tarball: http://r.npm.sankuai.com/sass-embedded-android-riscv64/download/sass-embedded-android-riscv64-1.83.0.tgz}
    engines: {node: '>=14.0.0'}
    cpu: [riscv64]
    os: [android]

  sass-embedded-android-x64@1.83.0:
    resolution: {integrity: sha1-3T9JdH4rDhibCzSi5iz8+z47dXY=, tarball: http://r.npm.sankuai.com/sass-embedded-android-x64/download/sass-embedded-android-x64-1.83.0.tgz}
    engines: {node: '>=14.0.0'}
    cpu: [x64]
    os: [android]

  sass-embedded-darwin-arm64@1.83.0:
    resolution: {integrity: sha1-N3vrAKE1hgrFWDQu5PSEfHxFUQc=, tarball: http://r.npm.sankuai.com/sass-embedded-darwin-arm64/download/sass-embedded-darwin-arm64-1.83.0.tgz}
    engines: {node: '>=14.0.0'}
    cpu: [arm64]
    os: [darwin]

  sass-embedded-darwin-x64@1.83.0:
    resolution: {integrity: sha1-IyOxryMix6YaH9buIEut0W/Mh90=, tarball: http://r.npm.sankuai.com/sass-embedded-darwin-x64/download/sass-embedded-darwin-x64-1.83.0.tgz}
    engines: {node: '>=14.0.0'}
    cpu: [x64]
    os: [darwin]

  sass-embedded-linux-arm64@1.83.0:
    resolution: {integrity: sha1-6WhpAw/vJ8c/LAGyON6vikG1Nvo=, tarball: http://r.npm.sankuai.com/sass-embedded-linux-arm64/download/sass-embedded-linux-arm64-1.83.0.tgz}
    engines: {node: '>=14.0.0'}
    cpu: [arm64]
    os: [linux]

  sass-embedded-linux-arm@1.83.0:
    resolution: {integrity: sha1-BvcQ+8HsdR9qsKdY8jwj536J68s=, tarball: http://r.npm.sankuai.com/sass-embedded-linux-arm/download/sass-embedded-linux-arm-1.83.0.tgz}
    engines: {node: '>=14.0.0'}
    cpu: [arm]
    os: [linux]

  sass-embedded-linux-ia32@1.83.0:
    resolution: {integrity: sha1-n9jrtHF4h2im5IEfJkZu1j04+TQ=, tarball: http://r.npm.sankuai.com/sass-embedded-linux-ia32/download/sass-embedded-linux-ia32-1.83.0.tgz}
    engines: {node: '>=14.0.0'}
    cpu: [ia32]
    os: [linux]

  sass-embedded-linux-musl-arm64@1.83.0:
    resolution: {integrity: sha1-9HU56U8ZqfeUXvVC2RuHRnfWnRo=, tarball: http://r.npm.sankuai.com/sass-embedded-linux-musl-arm64/download/sass-embedded-linux-musl-arm64-1.83.0.tgz}
    engines: {node: '>=14.0.0'}
    cpu: [arm64]
    os: [linux]

  sass-embedded-linux-musl-arm@1.83.0:
    resolution: {integrity: sha1-gU2bFOm1x6yx+b2hngsQzIwHRFA=, tarball: http://r.npm.sankuai.com/sass-embedded-linux-musl-arm/download/sass-embedded-linux-musl-arm-1.83.0.tgz}
    engines: {node: '>=14.0.0'}
    cpu: [arm]
    os: [linux]

  sass-embedded-linux-musl-ia32@1.83.0:
    resolution: {integrity: sha1-cPc8ovGc4NIxSsqh98UJAS+lDVY=, tarball: http://r.npm.sankuai.com/sass-embedded-linux-musl-ia32/download/sass-embedded-linux-musl-ia32-1.83.0.tgz}
    engines: {node: '>=14.0.0'}
    cpu: [ia32]
    os: [linux]

  sass-embedded-linux-musl-riscv64@1.83.0:
    resolution: {integrity: sha1-Uzkv+3kD5+pDy7agVBdirT5K6rA=, tarball: http://r.npm.sankuai.com/sass-embedded-linux-musl-riscv64/download/sass-embedded-linux-musl-riscv64-1.83.0.tgz}
    engines: {node: '>=14.0.0'}
    cpu: [riscv64]
    os: [linux]

  sass-embedded-linux-musl-x64@1.83.0:
    resolution: {integrity: sha1-bdnejOTVoBrDpq5b2RptVG2xyDc=, tarball: http://r.npm.sankuai.com/sass-embedded-linux-musl-x64/download/sass-embedded-linux-musl-x64-1.83.0.tgz}
    engines: {node: '>=14.0.0'}
    cpu: [x64]
    os: [linux]

  sass-embedded-linux-riscv64@1.83.0:
    resolution: {integrity: sha1-fVaJmNnkSdvVWocBtHk4ztXaBiA=, tarball: http://r.npm.sankuai.com/sass-embedded-linux-riscv64/download/sass-embedded-linux-riscv64-1.83.0.tgz}
    engines: {node: '>=14.0.0'}
    cpu: [riscv64]
    os: [linux]

  sass-embedded-linux-x64@1.83.0:
    resolution: {integrity: sha1-8nPNkVh0UEup/mq2kaMCPbrorqM=, tarball: http://r.npm.sankuai.com/sass-embedded-linux-x64/download/sass-embedded-linux-x64-1.83.0.tgz}
    engines: {node: '>=14.0.0'}
    cpu: [x64]
    os: [linux]

  sass-embedded-win32-arm64@1.83.0:
    resolution: {integrity: sha1-JqWAikt8cHerp0ni/6i+hjPbLw8=, tarball: http://r.npm.sankuai.com/sass-embedded-win32-arm64/download/sass-embedded-win32-arm64-1.83.0.tgz}
    engines: {node: '>=14.0.0'}
    cpu: [arm64]
    os: [win32]

  sass-embedded-win32-ia32@1.83.0:
    resolution: {integrity: sha1-qXUbd+i5FmXQTpTvRlGnlhcTtUI=, tarball: http://r.npm.sankuai.com/sass-embedded-win32-ia32/download/sass-embedded-win32-ia32-1.83.0.tgz}
    engines: {node: '>=14.0.0'}
    cpu: [ia32]
    os: [win32]

  sass-embedded-win32-x64@1.83.0:
    resolution: {integrity: sha1-99TJGWVosBc/M0QqylpKAuF8cAU=, tarball: http://r.npm.sankuai.com/sass-embedded-win32-x64/download/sass-embedded-win32-x64-1.83.0.tgz}
    engines: {node: '>=14.0.0'}
    cpu: [x64]
    os: [win32]

  sass-embedded@1.83.0:
    resolution: {integrity: sha1-4kAOr3Qy3qYbrGGpJMCwIJn+yVQ=, tarball: http://r.npm.sankuai.com/sass-embedded/download/sass-embedded-1.83.0.tgz}
    engines: {node: '>=16.0.0'}
    hasBin: true

  scheduler@0.23.2:
    resolution: {integrity: sha1-QUumSjsoKJLpRM8hCOzAeNEVzcM=, tarball: http://r.npm.sankuai.com/scheduler/download/scheduler-0.23.2.tgz}

  scroll-into-view-if-needed@3.1.0:
    resolution: {integrity: sha1-+pUkUYx5m0Wi72u/+5K8rQKW0B8=, tarball: http://r.npm.sankuai.com/scroll-into-view-if-needed/download/scroll-into-view-if-needed-3.1.0.tgz}

  semver@5.7.2:
    resolution: {integrity: sha1-SNVdtzfDKHzUg14X+hP+rOHEHvg=, tarball: http://r.npm.sankuai.com/semver/download/semver-5.7.2.tgz}
    hasBin: true

  semver@6.3.1:
    resolution: {integrity: sha1-VW0u+GiRRuRtzqS/3QlfNDTf/LQ=, tarball: http://r.npm.sankuai.com/semver/download/semver-6.3.1.tgz}
    hasBin: true

  semver@7.6.3:
    resolution: {integrity: sha1-mA97VVC8F1+03AlAMIVif56zMUM=, tarball: http://r.npm.sankuai.com/semver/download/semver-7.6.3.tgz}
    engines: {node: '>=10'}
    hasBin: true

  set-cookie-parser@2.7.1:
    resolution: {integrity: sha1-MBbxUAciAt++kPre4FNXPMidKUM=, tarball: http://r.npm.sankuai.com/set-cookie-parser/download/set-cookie-parser-2.7.1.tgz}

  set-function-length@1.2.2:
    resolution: {integrity: sha1-qscjFBmOrtl1z3eyw7a4gGleVEk=, tarball: http://r.npm.sankuai.com/set-function-length/download/set-function-length-1.2.2.tgz}
    engines: {node: '>= 0.4'}

  set-function-name@2.0.2:
    resolution: {integrity: sha1-FqcFxaDcL15jjKltiozU4cK5CYU=, tarball: http://r.npm.sankuai.com/set-function-name/download/set-function-name-2.0.2.tgz}
    engines: {node: '>= 0.4'}

  set-value@2.0.1:
    resolution: {integrity: sha1-oY1AUw5vB95CKMfe/kInr4ytAFs=, tarball: http://r.npm.sankuai.com/set-value/download/set-value-2.0.1.tgz}
    engines: {node: '>=0.10.0'}

  shebang-command@2.0.0:
    resolution: {integrity: sha1-zNCvT4g1+9wmW4JGGq8MNmY/NOo=, tarball: http://r.npm.sankuai.com/shebang-command/download/shebang-command-2.0.0.tgz}
    engines: {node: '>=8'}

  shebang-regex@3.0.0:
    resolution: {integrity: sha1-rhbxZE2HPsrYQ7AwexQzYtTEIXI=, tarball: http://r.npm.sankuai.com/shebang-regex/download/shebang-regex-3.0.0.tgz}
    engines: {node: '>=8'}

  side-channel-list@1.0.0:
    resolution: {integrity: sha1-EMtZhCYxFdO3oOM2WR4pCoMK+K0=, tarball: http://r.npm.sankuai.com/side-channel-list/download/side-channel-list-1.0.0.tgz}
    engines: {node: '>= 0.4'}

  side-channel-map@1.0.1:
    resolution: {integrity: sha1-1rtrN5Asb+9RdOX1M/q0xzKib0I=, tarball: http://r.npm.sankuai.com/side-channel-map/download/side-channel-map-1.0.1.tgz}
    engines: {node: '>= 0.4'}

  side-channel-weakmap@1.0.2:
    resolution: {integrity: sha1-Ed2hnVNo5Azp7CvcH7DsvAeQ7Oo=, tarball: http://r.npm.sankuai.com/side-channel-weakmap/download/side-channel-weakmap-1.0.2.tgz}
    engines: {node: '>= 0.4'}

  side-channel@1.1.0:
    resolution: {integrity: sha1-w/z/nE2pMnhIczNeyXZfqU/2a8k=, tarball: http://r.npm.sankuai.com/side-channel/download/side-channel-1.1.0.tgz}
    engines: {node: '>= 0.4'}

  signal-exit@4.1.0:
    resolution: {integrity: sha1-lSGIwcvVRgcOLdIND0HArgUwywQ=, tarball: http://r.npm.sankuai.com/signal-exit/download/signal-exit-4.1.0.tgz}
    engines: {node: '>=14'}

  simple-git-hooks@2.11.1:
    resolution: {integrity: sha1-QQLwtJ3XLxSDlKREapWK1e1GGZE=, tarball: http://r.npm.sankuai.com/simple-git-hooks/download/simple-git-hooks-2.11.1.tgz}
    hasBin: true

  slash@2.0.0:
    resolution: {integrity: sha1-3lUoUaF1nfOo8gZTVEL17E3eq0Q=, tarball: http://r.npm.sankuai.com/slash/download/slash-2.0.0.tgz}
    engines: {node: '>=6'}

  slash@3.0.0:
    resolution: {integrity: sha1-ZTm+hwwWWtvVJAIg2+Nh8bxNRjQ=, tarball: http://r.npm.sankuai.com/slash/download/slash-3.0.0.tgz}
    engines: {node: '>=8'}

  slice-ansi@5.0.0:
    resolution: {integrity: sha1-tzBjxXqpb5zYgWVLFSlNldKFxCo=, tarball: http://r.npm.sankuai.com/slice-ansi/download/slice-ansi-5.0.0.tgz}
    engines: {node: '>=12'}

  slice-ansi@7.1.0:
    resolution: {integrity: sha1-zWtGVeKYqNG96wQlCkMwlLNHuak=, tarball: http://r.npm.sankuai.com/slice-ansi/download/slice-ansi-7.1.0.tgz}
    engines: {node: '>=18'}

  snapdragon-node@2.1.1:
    resolution: {integrity: sha1-bBdfhv8UvbByRWPo88GwIaKGhTs=, tarball: http://r.npm.sankuai.com/snapdragon-node/download/snapdragon-node-2.1.1.tgz}
    engines: {node: '>=0.10.0'}

  snapdragon-util@3.0.1:
    resolution: {integrity: sha1-+VZHlIbyrNeXAGk/b3uAXkWrVuI=, tarball: http://r.npm.sankuai.com/snapdragon-util/download/snapdragon-util-3.0.1.tgz}
    engines: {node: '>=0.10.0'}

  snapdragon@0.8.2:
    resolution: {integrity: sha1-ZJIufFZbDhQgS6GqfWlkJ40lGC0=, tarball: http://r.npm.sankuai.com/snapdragon/download/snapdragon-0.8.2.tgz}
    engines: {node: '>=0.10.0'}

  source-map-js@1.2.1:
    resolution: {integrity: sha1-HOVlD93YerwJnto33P8CTCZnrkY=, tarball: http://r.npm.sankuai.com/source-map-js/download/source-map-js-1.2.1.tgz}
    engines: {node: '>=0.10.0'}

  source-map-resolve@0.5.3:
    resolution: {integrity: sha1-GQhmvs51U+H48mei7oLGBrVQmho=, tarball: http://r.npm.sankuai.com/source-map-resolve/download/source-map-resolve-0.5.3.tgz}
    deprecated: See https://github.com/lydell/source-map-resolve#deprecated

  source-map-url@0.4.1:
    resolution: {integrity: sha1-CvZmBadFpaL5HPG7+KevvCg97FY=, tarball: http://r.npm.sankuai.com/source-map-url/download/source-map-url-0.4.1.tgz}
    deprecated: See https://github.com/lydell/source-map-url#deprecated

  source-map@0.5.7:
    resolution: {integrity: sha1-igOdLRAh0i0eoUyA2OpGi6LvP8w=, tarball: http://r.npm.sankuai.com/source-map/download/source-map-0.5.7.tgz}
    engines: {node: '>=0.10.0'}

  split-string@3.1.0:
    resolution: {integrity: sha1-fLCd2jqGWFcFxks5pkZgOGguj+I=, tarball: http://r.npm.sankuai.com/split-string/download/split-string-3.1.0.tgz}
    engines: {node: '>=0.10.0'}

  static-extend@0.1.2:
    resolution: {integrity: sha1-YICcOcv/VTNyJv1eC1IPNB8ftcY=, tarball: http://r.npm.sankuai.com/static-extend/download/static-extend-0.1.2.tgz}
    engines: {node: '>=0.10.0'}

  statuses@1.5.0:
    resolution: {integrity: sha1-Fhx9rBd2Wf2YEfQ3cfqZOBR4Yow=, tarball: http://r.npm.sankuai.com/statuses/download/statuses-1.5.0.tgz}
    engines: {node: '>= 0.6'}

  stdin-discarder@0.2.2:
    resolution: {integrity: sha1-OQA39ExK4aGuU1xf443Dq6jZl74=, tarball: http://r.npm.sankuai.com/stdin-discarder/download/stdin-discarder-0.2.2.tgz}
    engines: {node: '>=18'}

  string-argv@0.3.2:
    resolution: {integrity: sha1-K20O8ktlYnTZV9VOCku/YVPcArY=, tarball: http://r.npm.sankuai.com/string-argv/download/string-argv-0.3.2.tgz}
    engines: {node: '>=0.6.19'}

  string-convert@0.2.1:
    resolution: {integrity: sha1-aYLMMEn7tM2F+LJFaLnZvznu/5c=, tarball: http://r.npm.sankuai.com/string-convert/download/string-convert-0.2.1.tgz}

  string-width@4.2.3:
    resolution: {integrity: sha1-JpxxF9J7Ba0uU2gwqOyJXvnG0BA=, tarball: http://r.npm.sankuai.com/string-width/download/string-width-4.2.3.tgz}
    engines: {node: '>=8'}

  string-width@5.1.2:
    resolution: {integrity: sha1-FPja7G2B5yIdKjV+Zoyrc728p5Q=, tarball: http://r.npm.sankuai.com/string-width/download/string-width-5.1.2.tgz}
    engines: {node: '>=12'}

  string-width@7.2.0:
    resolution: {integrity: sha1-tbuOIWXOJ11NQ0dt0nAK2Qkdttw=, tarball: http://r.npm.sankuai.com/string-width/download/string-width-7.2.0.tgz}
    engines: {node: '>=18'}

  string.prototype.matchall@4.0.12:
    resolution: {integrity: sha1-bIh0DkmtSVaxMyqRHpSVg6J11MA=, tarball: http://r.npm.sankuai.com/string.prototype.matchall/download/string.prototype.matchall-4.0.12.tgz}
    engines: {node: '>= 0.4'}

  string.prototype.repeat@1.0.0:
    resolution: {integrity: sha1-6Qhy7gMIspQ1qiYnX24bdi2u4Bo=, tarball: http://r.npm.sankuai.com/string.prototype.repeat/download/string.prototype.repeat-1.0.0.tgz}

  string.prototype.trim@1.2.10:
    resolution: {integrity: sha1-QLLdXulMlZtNz7HWXOcukNpIDIE=, tarball: http://r.npm.sankuai.com/string.prototype.trim/download/string.prototype.trim-1.2.10.tgz}
    engines: {node: '>= 0.4'}

  string.prototype.trimend@1.0.9:
    resolution: {integrity: sha1-YuJzEnLNKFBBs2WWBU6fZlabaUI=, tarball: http://r.npm.sankuai.com/string.prototype.trimend/download/string.prototype.trimend-1.0.9.tgz}
    engines: {node: '>= 0.4'}

  string.prototype.trimstart@1.0.8:
    resolution: {integrity: sha1-fug03ajHwX7/MRhHK7Nb/tqjTd4=, tarball: http://r.npm.sankuai.com/string.prototype.trimstart/download/string.prototype.trimstart-1.0.8.tgz}
    engines: {node: '>= 0.4'}

  strip-ansi@6.0.1:
    resolution: {integrity: sha1-nibGPTD1NEPpSJSVshBdN7Z6hdk=, tarball: http://r.npm.sankuai.com/strip-ansi/download/strip-ansi-6.0.1.tgz}
    engines: {node: '>=8'}

  strip-ansi@7.1.0:
    resolution: {integrity: sha1-1bZWjKaJ2FYTcLBwdoXSJDT6/0U=, tarball: http://r.npm.sankuai.com/strip-ansi/download/strip-ansi-7.1.0.tgz}
    engines: {node: '>=12'}

  strip-final-newline@3.0.0:
    resolution: {integrity: sha1-UolMMT+/8xiDUoCu1g/3Hr8SuP0=, tarball: http://r.npm.sankuai.com/strip-final-newline/download/strip-final-newline-3.0.0.tgz}
    engines: {node: '>=12'}

  strip-json-comments@3.1.1:
    resolution: {integrity: sha1-MfEoGzgyYwQ0gxwxDAHMzajL4AY=, tarball: http://r.npm.sankuai.com/strip-json-comments/download/strip-json-comments-3.1.1.tgz}
    engines: {node: '>=8'}

  stylis@4.3.4:
    resolution: {integrity: sha1-ylxsSjXEeE5Ok6KiTcTp+gdSUKQ=, tarball: http://r.npm.sankuai.com/stylis/download/stylis-4.3.4.tgz}

  sucrase@3.35.0:
    resolution: {integrity: sha1-V/F6PX4Zs22JlfBmedEhvpFK4mM=, tarball: http://r.npm.sankuai.com/sucrase/download/sucrase-3.35.0.tgz}
    engines: {node: '>=16 || 14 >=14.17'}
    hasBin: true

  supports-color@5.5.0:
    resolution: {integrity: sha1-4uaaRKyHcveKHsCzW2id9lMO/I8=, tarball: http://r.npm.sankuai.com/supports-color/download/supports-color-5.5.0.tgz}
    engines: {node: '>=4'}

  supports-color@7.2.0:
    resolution: {integrity: sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=, tarball: http://r.npm.sankuai.com/supports-color/download/supports-color-7.2.0.tgz}
    engines: {node: '>=8'}

  supports-color@8.1.1:
    resolution: {integrity: sha1-zW/BfihQDP9WwbhsCn/UpUpzAFw=, tarball: http://r.npm.sankuai.com/supports-color/download/supports-color-8.1.1.tgz}
    engines: {node: '>=10'}

  supports-preserve-symlinks-flag@1.0.0:
    resolution: {integrity: sha1-btpL00SjyUrqN21MwxvHcxEDngk=, tarball: http://r.npm.sankuai.com/supports-preserve-symlinks-flag/download/supports-preserve-symlinks-flag-1.0.0.tgz}
    engines: {node: '>= 0.4'}

  svg-tags@1.0.0:
    resolution: {integrity: sha1-WPcc7jvVGbWdSyqEO2x95krAR2Q=, tarball: http://r.npm.sankuai.com/svg-tags/download/svg-tags-1.0.0.tgz}

  sync-child-process@1.0.2:
    resolution: {integrity: sha1-RefHLnVtEkPoC1R+ouF5V6ueNn8=, tarball: http://r.npm.sankuai.com/sync-child-process/download/sync-child-process-1.0.2.tgz}
    engines: {node: '>=16.0.0'}

  sync-message-port@1.1.3:
    resolution: {integrity: sha1-YFXFZe6MgdL57lqufbdX5tkIjAw=, tarball: http://r.npm.sankuai.com/sync-message-port/download/sync-message-port-1.1.3.tgz}
    engines: {node: '>=16.0.0'}

  tailwindcss@3.4.17:
    resolution: {integrity: sha1-roQGwPlmlqYxx5B2j/MZ1G1eWmM=, tarball: http://r.npm.sankuai.com/tailwindcss/download/tailwindcss-3.4.17.tgz}
    engines: {node: '>=14.0.0'}
    hasBin: true

  text-table@0.2.0:
    resolution: {integrity: sha1-f17oI66AUgfACvLfSoTsP8+lcLQ=, tarball: http://r.npm.sankuai.com/text-table/download/text-table-0.2.0.tgz}

  thenify-all@1.6.0:
    resolution: {integrity: sha1-GhkY1ALY/D+Y+/I02wvMjMEOlyY=, tarball: http://r.npm.sankuai.com/thenify-all/download/thenify-all-1.6.0.tgz}
    engines: {node: '>=0.8'}

  thenify@3.3.1:
    resolution: {integrity: sha1-iTLmhqQGYDigFt2eLKRq3Zg4qV8=, tarball: http://r.npm.sankuai.com/thenify/download/thenify-3.3.1.tgz}

  throttle-debounce@3.0.1:
    resolution: {integrity: sha1-MvlNhN+olPeGyaHykOemRbahmrs=, tarball: http://r.npm.sankuai.com/throttle-debounce/download/throttle-debounce-3.0.1.tgz}
    engines: {node: '>=10'}

  throttle-debounce@5.0.2:
    resolution: {integrity: sha1-7FVJ2E4FPwQ8n9Dypt2JL/hEVrE=, tarball: http://r.npm.sankuai.com/throttle-debounce/download/throttle-debounce-5.0.2.tgz}
    engines: {node: '>=12.22'}

  tmp@0.0.33:
    resolution: {integrity: sha1-bTQzWIl2jSGyvNoKonfO07G/rfk=, tarball: http://r.npm.sankuai.com/tmp/download/tmp-0.0.33.tgz}
    engines: {node: '>=0.6.0'}

  to-object-path@0.3.0:
    resolution: {integrity: sha1-KXWIt7Dn4KwI4E5nL4XB9JmeF68=, tarball: http://r.npm.sankuai.com/to-object-path/download/to-object-path-0.3.0.tgz}
    engines: {node: '>=0.10.0'}

  to-regex-range@2.1.1:
    resolution: {integrity: sha1-fIDBe53+vlmeJzZ+DU3VWQFB2zg=, tarball: http://r.npm.sankuai.com/to-regex-range/download/to-regex-range-2.1.1.tgz}
    engines: {node: '>=0.10.0'}

  to-regex-range@5.0.1:
    resolution: {integrity: sha1-FkjESq58jZiKMmAY7XL1tN0DkuQ=, tarball: http://r.npm.sankuai.com/to-regex-range/download/to-regex-range-5.0.1.tgz}
    engines: {node: '>=8.0'}

  to-regex@3.0.2:
    resolution: {integrity: sha1-E8/dmzNlUvMLUfM6iuG0Knp1mc4=, tarball: http://r.npm.sankuai.com/to-regex/download/to-regex-3.0.2.tgz}
    engines: {node: '>=0.10.0'}

  toggle-selection@1.0.6:
    resolution: {integrity: sha1-bkWxJj8gF/oKzH2J14sVuL932jI=, tarball: http://r.npm.sankuai.com/toggle-selection/download/toggle-selection-1.0.6.tgz}

  ts-api-utils@1.4.3:
    resolution: {integrity: sha1-v8IhX+ZSj+yrKw+6VwouikJjsGQ=, tarball: http://r.npm.sankuai.com/ts-api-utils/download/ts-api-utils-1.4.3.tgz}
    engines: {node: '>=16'}
    peerDependencies:
      typescript: '>=4.2.0'

  ts-interface-checker@0.1.13:
    resolution: {integrity: sha1-eE/T1nlyK8EDsbS4AwvN212yppk=, tarball: http://r.npm.sankuai.com/ts-interface-checker/download/ts-interface-checker-0.1.13.tgz}

  tslib@1.14.1:
    resolution: {integrity: sha1-zy04vcNKE0vK8QkcQfZhni9nLQA=, tarball: http://r.npm.sankuai.com/tslib/download/tslib-1.14.1.tgz}

  tslib@2.8.1:
    resolution: {integrity: sha1-YS7+TtI11Wfoq6Xypfq3AoCt6D8=, tarball: http://r.npm.sankuai.com/tslib/download/tslib-2.8.1.tgz}

  tsutils@3.21.0:
    resolution: {integrity: sha1-tIcX05TOpsHglpg+7Vjp1hcVtiM=, tarball: http://r.npm.sankuai.com/tsutils/download/tsutils-3.21.0.tgz}
    engines: {node: '>= 6'}
    peerDependencies:
      typescript: '>=2.8.0 || >= 3.2.0-dev || >= 3.3.0-dev || >= 3.4.0-dev || >= 3.5.0-dev || >= 3.6.0-dev || >= 3.6.0-beta || >= 3.7.0-dev || >= 3.7.0-beta'

  turbo-stream@2.4.0:
    resolution: {integrity: sha1-Hk/KZyXpD6FKxK23gvLTdZpWlfA=, tarball: http://r.npm.sankuai.com/turbo-stream/download/turbo-stream-2.4.0.tgz}

  type-check@0.4.0:
    resolution: {integrity: sha1-B7ggO/pwVsBlcFDjzNLDdzC6uPE=, tarball: http://r.npm.sankuai.com/type-check/download/type-check-0.4.0.tgz}
    engines: {node: '>= 0.8.0'}

  type-fest@0.20.2:
    resolution: {integrity: sha1-G/IH9LKPkVg2ZstfvTJ4hzAc1fQ=, tarball: http://r.npm.sankuai.com/type-fest/download/type-fest-0.20.2.tgz}
    engines: {node: '>=10'}

  type-fest@0.21.3:
    resolution: {integrity: sha1-0mCiSwGYQ24TP6JqUkptZfo7Ljc=, tarball: http://r.npm.sankuai.com/type-fest/download/type-fest-0.21.3.tgz}
    engines: {node: '>=10'}

  typed-array-buffer@1.0.3:
    resolution: {integrity: sha1-pyOVRQpIaewDP9VJNxtHrzou5TY=, tarball: http://r.npm.sankuai.com/typed-array-buffer/download/typed-array-buffer-1.0.3.tgz}
    engines: {node: '>= 0.4'}

  typed-array-byte-length@1.0.3:
    resolution: {integrity: sha1-hAegT314aE89JSqhoUPSt3tBYM4=, tarball: http://r.npm.sankuai.com/typed-array-byte-length/download/typed-array-byte-length-1.0.3.tgz}
    engines: {node: '>= 0.4'}

  typed-array-byte-offset@1.0.4:
    resolution: {integrity: sha1-rjaYuOyRqKuUUBYQiu8A1b/xI1U=, tarball: http://r.npm.sankuai.com/typed-array-byte-offset/download/typed-array-byte-offset-1.0.4.tgz}
    engines: {node: '>= 0.4'}

  typed-array-length@1.0.7:
    resolution: {integrity: sha1-7k3v+YS2S+HhGLDejJyHfVznPT0=, tarball: http://r.npm.sankuai.com/typed-array-length/download/typed-array-length-1.0.7.tgz}
    engines: {node: '>= 0.4'}

  typescript-eslint@8.18.2:
    resolution: {integrity: sha1-cTNNz4Q63D+7dx3OWt54dqoNYrc=, tarball: http://r.npm.sankuai.com/typescript-eslint/download/typescript-eslint-8.18.2.tgz}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      eslint: ^8.57.0 || ^9.0.0
      typescript: '>=4.8.4 <5.8.0'

  typescript@4.9.5:
    resolution: {integrity: sha1-CVl5+bzA0J2jJNWNA86Pg3TL5lo=, tarball: http://r.npm.sankuai.com/typescript/download/typescript-4.9.5.tgz}
    engines: {node: '>=4.2.0'}
    hasBin: true

  typescript@5.6.3:
    resolution: {integrity: sha1-XzRJ4xydlP67F94DzAgd1W2B21s=, tarball: http://r.npm.sankuai.com/typescript/download/typescript-5.6.3.tgz}
    engines: {node: '>=14.17'}
    hasBin: true

  unbox-primitive@1.1.0:
    resolution: {integrity: sha1-jZ0snt7qhGDH81AzqIhnlEk00eI=, tarball: http://r.npm.sankuai.com/unbox-primitive/download/unbox-primitive-1.1.0.tgz}
    engines: {node: '>= 0.4'}

  undici-types@6.20.0:
    resolution: {integrity: sha1-gXG/IsH1iNFVTVW/IEvGJK84hDM=, tarball: http://r.npm.sankuai.com/undici-types/download/undici-types-6.20.0.tgz}

  union-value@1.0.1:
    resolution: {integrity: sha1-C2/nuDWuzaYcbqTU8CwUIh4QmEc=, tarball: http://r.npm.sankuai.com/union-value/download/union-value-1.0.1.tgz}
    engines: {node: '>=0.10.0'}

  unpipe@1.0.0:
    resolution: {integrity: sha1-sr9O6FFKrmFltIF4KdIbLvSZBOw=, tarball: http://r.npm.sankuai.com/unpipe/download/unpipe-1.0.0.tgz}
    engines: {node: '>= 0.8'}

  unset-value@1.0.0:
    resolution: {integrity: sha1-g3aHP30jNRef+x5vw6jtDfyKtVk=, tarball: http://r.npm.sankuai.com/unset-value/download/unset-value-1.0.0.tgz}
    engines: {node: '>=0.10.0'}

  update-browserslist-db@1.1.1:
    resolution: {integrity: sha1-gIRvuh156CVH+2YfjRQeCUV1X+U=, tarball: http://r.npm.sankuai.com/update-browserslist-db/download/update-browserslist-db-1.1.1.tgz}
    hasBin: true
    peerDependencies:
      browserslist: '>= 4.21.0'

  uri-js@4.4.1:
    resolution: {integrity: sha1-mxpSWVIlhZ5V9mnZKPiMbFfyp34=, tarball: http://r.npm.sankuai.com/uri-js/download/uri-js-4.4.1.tgz}

  urix@0.1.0:
    resolution: {integrity: sha1-2pN/emLiH+wf0Y1Js1wpNQZ6bHI=, tarball: http://r.npm.sankuai.com/urix/download/urix-0.1.0.tgz}
    deprecated: Please see https://github.com/lydell/urix#deprecated

  use@3.1.1:
    resolution: {integrity: sha1-1QyMrHmhn7wg8pEfVuuXP04QBw8=, tarball: http://r.npm.sankuai.com/use/download/use-3.1.1.tgz}
    engines: {node: '>=0.10.0'}

  util-deprecate@1.0.2:
    resolution: {integrity: sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8=, tarball: http://r.npm.sankuai.com/util-deprecate/download/util-deprecate-1.0.2.tgz}

  utils-merge@1.0.1:
    resolution: {integrity: sha1-n5VxD1CiZ5R7LMwSR0HBAoQn5xM=, tarball: http://r.npm.sankuai.com/utils-merge/download/utils-merge-1.0.1.tgz}
    engines: {node: '>= 0.4.0'}

  varint@6.0.0:
    resolution: {integrity: sha1-mIHrDOj+rqZRJDnRnd+Ev1UWYdA=, tarball: http://r.npm.sankuai.com/varint/download/varint-6.0.0.tgz}

  vite-plugin-mock@3.0.2:
    resolution: {integrity: sha1-OCRGZCZh31YjAjlRZwy1cstdaCs=, tarball: http://r.npm.sankuai.com/vite-plugin-mock/download/vite-plugin-mock-3.0.2.tgz}
    engines: {node: '>=16.0.0'}
    peerDependencies:
      esbuild: '>=0.17'
      mockjs: '>=1.1.0'
      vite: '>=4.0.0'

  vite@6.0.6:
    resolution: {integrity: sha1-qFFnT8/1WwwZYvcggjVLiALkhQU=, tarball: http://r.npm.sankuai.com/vite/download/vite-6.0.6.tgz}
    engines: {node: ^18.0.0 || ^20.0.0 || >=22.0.0}
    hasBin: true
    peerDependencies:
      '@types/node': ^18.0.0 || ^20.0.0 || >=22.0.0
      jiti: '>=1.21.0'
      less: '*'
      lightningcss: ^1.21.0
      sass: '*'
      sass-embedded: '*'
      stylus: '*'
      sugarss: '*'
      terser: ^5.16.0
      tsx: ^4.8.1
      yaml: ^2.4.2
    peerDependenciesMeta:
      '@types/node':
        optional: true
      jiti:
        optional: true
      less:
        optional: true
      lightningcss:
        optional: true
      sass:
        optional: true
      sass-embedded:
        optional: true
      stylus:
        optional: true
      sugarss:
        optional: true
      terser:
        optional: true
      tsx:
        optional: true
      yaml:
        optional: true

  vue-eslint-parser@9.4.3:
    resolution: {integrity: sha1-mwSyLHFAHx6Lypvnw+NBakvedqg=, tarball: http://r.npm.sankuai.com/vue-eslint-parser/download/vue-eslint-parser-9.4.3.tgz}
    engines: {node: ^14.17.0 || >=16.0.0}
    peerDependencies:
      eslint: '>=6.0.0'

  vue-router@4.5.0:
    resolution: {integrity: sha1-WPxf43ThC2AY+RAyj3VsPa4IHxQ=, tarball: http://r.npm.sankuai.com/vue-router/download/vue-router-4.5.0.tgz}
    peerDependencies:
      vue: ^3.2.0

  vue@3.5.13:
    resolution: {integrity: sha1-n3YKGpgrCcDASoZ5A/wznJ8p7Ao=, tarball: http://r.npm.sankuai.com/vue/download/vue-3.5.13.tgz}
    peerDependencies:
      typescript: '*'
    peerDependenciesMeta:
      typescript:
        optional: true

  which-boxed-primitive@1.1.1:
    resolution: {integrity: sha1-127Cfff6Fl8Y1YCDdKX+I8KbF24=, tarball: http://r.npm.sankuai.com/which-boxed-primitive/download/which-boxed-primitive-1.1.1.tgz}
    engines: {node: '>= 0.4'}

  which-builtin-type@1.2.1:
    resolution: {integrity: sha1-iRg9obSQerCJprAgKcxdjWV0Jw4=, tarball: http://r.npm.sankuai.com/which-builtin-type/download/which-builtin-type-1.2.1.tgz}
    engines: {node: '>= 0.4'}

  which-collection@1.0.2:
    resolution: {integrity: sha1-Yn73YkOSChB+fOjpYZHevksWwqA=, tarball: http://r.npm.sankuai.com/which-collection/download/which-collection-1.0.2.tgz}
    engines: {node: '>= 0.4'}

  which-typed-array@1.1.18:
    resolution: {integrity: sha1-3yOJ6/P7skanE5DpBzCp7bbOF60=, tarball: http://r.npm.sankuai.com/which-typed-array/download/which-typed-array-1.1.18.tgz}
    engines: {node: '>= 0.4'}

  which@2.0.2:
    resolution: {integrity: sha1-fGqN0KY2oDJ+ELWckobu6T8/UbE=, tarball: http://r.npm.sankuai.com/which/download/which-2.0.2.tgz}
    engines: {node: '>= 8'}
    hasBin: true

  word-wrap@1.2.5:
    resolution: {integrity: sha1-0sRcbdT7zmIaZvE2y+Mor9BBCzQ=, tarball: http://r.npm.sankuai.com/word-wrap/download/word-wrap-1.2.5.tgz}
    engines: {node: '>=0.10.0'}

  wrap-ansi@6.2.0:
    resolution: {integrity: sha1-6Tk7oHEC5skaOyIUePAlfNKFblM=, tarball: http://r.npm.sankuai.com/wrap-ansi/download/wrap-ansi-6.2.0.tgz}
    engines: {node: '>=8'}

  wrap-ansi@7.0.0:
    resolution: {integrity: sha1-Z+FFz/UQpqaYS98RUpEdadLrnkM=, tarball: http://r.npm.sankuai.com/wrap-ansi/download/wrap-ansi-7.0.0.tgz}
    engines: {node: '>=10'}

  wrap-ansi@8.1.0:
    resolution: {integrity: sha1-VtwiNo7lcPrOG0mBmXXZuaXq0hQ=, tarball: http://r.npm.sankuai.com/wrap-ansi/download/wrap-ansi-8.1.0.tgz}
    engines: {node: '>=12'}

  wrap-ansi@9.0.0:
    resolution: {integrity: sha1-Gj3Itw2F7rg5jd+x5KAs0Ybliz4=, tarball: http://r.npm.sankuai.com/wrap-ansi/download/wrap-ansi-9.0.0.tgz}
    engines: {node: '>=18'}

  wrappy@1.0.2:
    resolution: {integrity: sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8=, tarball: http://r.npm.sankuai.com/wrappy/download/wrappy-1.0.2.tgz}

  xml-name-validator@4.0.0:
    resolution: {integrity: sha1-eaAG4uYxSahgDxVDDwpHJdFSSDU=, tarball: http://r.npm.sankuai.com/xml-name-validator/download/xml-name-validator-4.0.0.tgz}
    engines: {node: '>=12'}

  yallist@3.1.1:
    resolution: {integrity: sha1-27fa+b/YusmrRev2ArjLrQ1dCP0=, tarball: http://r.npm.sankuai.com/yallist/download/yallist-3.1.1.tgz}

  yaml@2.6.1:
    resolution: {integrity: sha1-QvKxuokgPzdGCVctU0n7hoZQB3M=, tarball: http://r.npm.sankuai.com/yaml/download/yaml-2.6.1.tgz}
    engines: {node: '>= 14'}
    hasBin: true

  yocto-queue@0.1.0:
    resolution: {integrity: sha1-ApTrPe4FAo0x7hpfosVWpqrxChs=, tarball: http://r.npm.sankuai.com/yocto-queue/download/yocto-queue-0.1.0.tgz}
    engines: {node: '>=10'}

  yoctocolors-cjs@2.1.2:
    resolution: {integrity: sha1-9LkFqECjdQaBOnrKoo/r6XdnokI=, tarball: http://r.npm.sankuai.com/yoctocolors-cjs/download/yoctocolors-cjs-2.1.2.tgz}
    engines: {node: '>=18'}

snapshots:

  '@alloc/quick-lru@5.2.0': {}

  '@ampproject/remapping@2.3.0':
    dependencies:
      '@jridgewell/gen-mapping': 0.3.8
      '@jridgewell/trace-mapping': 0.3.25

  '@ant-design/colors@7.1.0':
    dependencies:
      '@ctrl/tinycolor': 3.6.1

  '@ant-design/cssinjs-utils@1.1.3(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@ant-design/cssinjs': 1.22.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@babel/runtime': 7.26.0
      rc-util: 5.44.3(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@ant-design/cssinjs@1.22.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@babel/runtime': 7.26.0
      '@emotion/hash': 0.8.0
      '@emotion/unitless': 0.7.5
      classnames: 2.5.1
      csstype: 3.1.3
      rc-util: 5.44.3(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
      stylis: 4.3.4

  '@ant-design/fast-color@2.0.6':
    dependencies:
      '@babel/runtime': 7.26.0

  '@ant-design/icons-svg@4.4.2': {}

  '@ant-design/icons@5.5.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@ant-design/colors': 7.1.0
      '@ant-design/icons-svg': 4.4.2
      '@babel/runtime': 7.26.0
      classnames: 2.5.1
      rc-util: 5.44.3(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@ant-design/react-slick@1.1.2(react@18.3.1)':
    dependencies:
      '@babel/runtime': 7.26.0
      classnames: 2.5.1
      json2mq: 0.2.0
      react: 18.3.1
      resize-observer-polyfill: 1.5.1
      throttle-debounce: 5.0.2

  '@babel/code-frame@7.26.2':
    dependencies:
      '@babel/helper-validator-identifier': 7.25.9
      js-tokens: 4.0.0
      picocolors: 1.1.1

  '@babel/compat-data@7.26.3': {}

  '@babel/core@7.26.0':
    dependencies:
      '@ampproject/remapping': 2.3.0
      '@babel/code-frame': 7.26.2
      '@babel/generator': 7.26.3
      '@babel/helper-compilation-targets': 7.25.9
      '@babel/helper-module-transforms': 7.26.0(@babel/core@7.26.0)
      '@babel/helpers': 7.26.0
      '@babel/parser': 7.26.3
      '@babel/template': 7.25.9
      '@babel/traverse': 7.26.4
      '@babel/types': 7.26.3
      convert-source-map: 2.0.0
      debug: 4.4.0
      gensync: 1.0.0-beta.2
      json5: 2.2.3
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color

  '@babel/generator@7.26.3':
    dependencies:
      '@babel/parser': 7.26.3
      '@babel/types': 7.26.3
      '@jridgewell/gen-mapping': 0.3.8
      '@jridgewell/trace-mapping': 0.3.25
      jsesc: 3.1.0

  '@babel/helper-compilation-targets@7.25.9':
    dependencies:
      '@babel/compat-data': 7.26.3
      '@babel/helper-validator-option': 7.25.9
      browserslist: 4.24.3
      lru-cache: 5.1.1
      semver: 6.3.1

  '@babel/helper-module-imports@7.25.9':
    dependencies:
      '@babel/traverse': 7.26.4
      '@babel/types': 7.26.3
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-module-transforms@7.26.0(@babel/core@7.26.0)':
    dependencies:
      '@babel/core': 7.26.0
      '@babel/helper-module-imports': 7.25.9
      '@babel/helper-validator-identifier': 7.25.9
      '@babel/traverse': 7.26.4
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-plugin-utils@7.25.9': {}

  '@babel/helper-string-parser@7.25.9': {}

  '@babel/helper-validator-identifier@7.25.9': {}

  '@babel/helper-validator-option@7.25.9': {}

  '@babel/helpers@7.26.0':
    dependencies:
      '@babel/template': 7.25.9
      '@babel/types': 7.26.3

  '@babel/parser@7.26.3':
    dependencies:
      '@babel/types': 7.26.3

  '@babel/plugin-syntax-jsx@7.25.9(@babel/core@7.26.0)':
    dependencies:
      '@babel/core': 7.26.0
      '@babel/helper-plugin-utils': 7.25.9

  '@babel/plugin-transform-react-jsx-self@7.25.9(@babel/core@7.26.0)':
    dependencies:
      '@babel/core': 7.26.0
      '@babel/helper-plugin-utils': 7.25.9

  '@babel/plugin-transform-react-jsx-source@7.25.9(@babel/core@7.26.0)':
    dependencies:
      '@babel/core': 7.26.0
      '@babel/helper-plugin-utils': 7.25.9

  '@babel/runtime@7.26.0':
    dependencies:
      regenerator-runtime: 0.14.1

  '@babel/template@7.25.9':
    dependencies:
      '@babel/code-frame': 7.26.2
      '@babel/parser': 7.26.3
      '@babel/types': 7.26.3

  '@babel/traverse@7.26.4':
    dependencies:
      '@babel/code-frame': 7.26.2
      '@babel/generator': 7.26.3
      '@babel/parser': 7.26.3
      '@babel/template': 7.25.9
      '@babel/types': 7.26.3
      debug: 4.4.0
      globals: 11.12.0
    transitivePeerDependencies:
      - supports-color

  '@babel/types@7.26.3':
    dependencies:
      '@babel/helper-string-parser': 7.25.9
      '@babel/helper-validator-identifier': 7.25.9

  '@bufbuild/protobuf@2.2.3': {}

  '@ctrl/tinycolor@3.6.1': {}

  '@emotion/hash@0.8.0': {}

  '@emotion/unitless@0.7.5': {}

  '@esbuild/aix-ppc64@0.24.2':
    optional: true

  '@esbuild/android-arm64@0.24.2':
    optional: true

  '@esbuild/android-arm@0.24.2':
    optional: true

  '@esbuild/android-x64@0.24.2':
    optional: true

  '@esbuild/darwin-arm64@0.24.2':
    optional: true

  '@esbuild/darwin-x64@0.24.2':
    optional: true

  '@esbuild/freebsd-arm64@0.24.2':
    optional: true

  '@esbuild/freebsd-x64@0.24.2':
    optional: true

  '@esbuild/linux-arm64@0.24.2':
    optional: true

  '@esbuild/linux-arm@0.24.2':
    optional: true

  '@esbuild/linux-ia32@0.24.2':
    optional: true

  '@esbuild/linux-loong64@0.24.2':
    optional: true

  '@esbuild/linux-mips64el@0.24.2':
    optional: true

  '@esbuild/linux-ppc64@0.24.2':
    optional: true

  '@esbuild/linux-riscv64@0.24.2':
    optional: true

  '@esbuild/linux-s390x@0.24.2':
    optional: true

  '@esbuild/linux-x64@0.24.2':
    optional: true

  '@esbuild/netbsd-arm64@0.24.2':
    optional: true

  '@esbuild/netbsd-x64@0.24.2':
    optional: true

  '@esbuild/openbsd-arm64@0.24.2':
    optional: true

  '@esbuild/openbsd-x64@0.24.2':
    optional: true

  '@esbuild/sunos-x64@0.24.2':
    optional: true

  '@esbuild/win32-arm64@0.24.2':
    optional: true

  '@esbuild/win32-ia32@0.24.2':
    optional: true

  '@esbuild/win32-x64@0.24.2':
    optional: true

  '@eslint-community/eslint-utils@4.4.1(eslint@8.57.1)':
    dependencies:
      eslint: 8.57.1
      eslint-visitor-keys: 3.4.3

  '@eslint-community/eslint-utils@4.4.1(eslint@9.17.0(jiti@1.21.7))':
    dependencies:
      eslint: 9.17.0(jiti@1.21.7)
      eslint-visitor-keys: 3.4.3

  '@eslint-community/regexpp@4.12.1': {}

  '@eslint/config-array@0.19.1':
    dependencies:
      '@eslint/object-schema': 2.1.5
      debug: 4.4.0
      minimatch: 3.1.2
    transitivePeerDependencies:
      - supports-color

  '@eslint/core@0.9.1':
    dependencies:
      '@types/json-schema': 7.0.15

  '@eslint/eslintrc@2.1.4':
    dependencies:
      ajv: 6.12.6
      debug: 4.4.0
      espree: 9.6.1
      globals: 13.24.0
      ignore: 5.3.2
      import-fresh: 3.3.0
      js-yaml: 4.1.0
      minimatch: 3.1.2
      strip-json-comments: 3.1.1
    transitivePeerDependencies:
      - supports-color

  '@eslint/eslintrc@3.2.0':
    dependencies:
      ajv: 6.12.6
      debug: 4.4.0
      espree: 10.3.0
      globals: 14.0.0
      ignore: 5.3.2
      import-fresh: 3.3.0
      js-yaml: 4.1.0
      minimatch: 3.1.2
      strip-json-comments: 3.1.1
    transitivePeerDependencies:
      - supports-color

  '@eslint/js@8.57.1': {}

  '@eslint/js@9.17.0': {}

  '@eslint/object-schema@2.1.5': {}

  '@eslint/plugin-kit@0.2.4':
    dependencies:
      levn: 0.4.1

  '@humanfs/core@0.19.1': {}

  '@humanfs/node@0.16.6':
    dependencies:
      '@humanfs/core': 0.19.1
      '@humanwhocodes/retry': 0.3.1

  '@humanwhocodes/config-array@0.13.0':
    dependencies:
      '@humanwhocodes/object-schema': 2.0.3
      debug: 4.4.0
      minimatch: 3.1.2
    transitivePeerDependencies:
      - supports-color

  '@humanwhocodes/module-importer@1.0.1': {}

  '@humanwhocodes/object-schema@2.0.3': {}

  '@humanwhocodes/retry@0.3.1': {}

  '@humanwhocodes/retry@0.4.1': {}

  '@inquirer/checkbox@4.1.3(@types/node@22.10.2)':
    dependencies:
      '@inquirer/core': 10.1.8(@types/node@22.10.2)
      '@inquirer/figures': 1.0.11
      '@inquirer/type': 3.0.5(@types/node@22.10.2)
      ansi-escapes: 4.3.2
      yoctocolors-cjs: 2.1.2
    optionalDependencies:
      '@types/node': 22.10.2

  '@inquirer/confirm@5.1.7(@types/node@22.10.2)':
    dependencies:
      '@inquirer/core': 10.1.8(@types/node@22.10.2)
      '@inquirer/type': 3.0.5(@types/node@22.10.2)
    optionalDependencies:
      '@types/node': 22.10.2

  '@inquirer/core@10.1.8(@types/node@22.10.2)':
    dependencies:
      '@inquirer/figures': 1.0.11
      '@inquirer/type': 3.0.5(@types/node@22.10.2)
      ansi-escapes: 4.3.2
      cli-width: 4.1.0
      mute-stream: 2.0.0
      signal-exit: 4.1.0
      wrap-ansi: 6.2.0
      yoctocolors-cjs: 2.1.2
    optionalDependencies:
      '@types/node': 22.10.2

  '@inquirer/editor@4.2.8(@types/node@22.10.2)':
    dependencies:
      '@inquirer/core': 10.1.8(@types/node@22.10.2)
      '@inquirer/type': 3.0.5(@types/node@22.10.2)
      external-editor: 3.1.0
    optionalDependencies:
      '@types/node': 22.10.2

  '@inquirer/expand@4.0.10(@types/node@22.10.2)':
    dependencies:
      '@inquirer/core': 10.1.8(@types/node@22.10.2)
      '@inquirer/type': 3.0.5(@types/node@22.10.2)
      yoctocolors-cjs: 2.1.2
    optionalDependencies:
      '@types/node': 22.10.2

  '@inquirer/figures@1.0.11': {}

  '@inquirer/input@4.1.7(@types/node@22.10.2)':
    dependencies:
      '@inquirer/core': 10.1.8(@types/node@22.10.2)
      '@inquirer/type': 3.0.5(@types/node@22.10.2)
    optionalDependencies:
      '@types/node': 22.10.2

  '@inquirer/number@3.0.10(@types/node@22.10.2)':
    dependencies:
      '@inquirer/core': 10.1.8(@types/node@22.10.2)
      '@inquirer/type': 3.0.5(@types/node@22.10.2)
    optionalDependencies:
      '@types/node': 22.10.2

  '@inquirer/password@4.0.10(@types/node@22.10.2)':
    dependencies:
      '@inquirer/core': 10.1.8(@types/node@22.10.2)
      '@inquirer/type': 3.0.5(@types/node@22.10.2)
      ansi-escapes: 4.3.2
    optionalDependencies:
      '@types/node': 22.10.2

  '@inquirer/prompts@7.3.3(@types/node@22.10.2)':
    dependencies:
      '@inquirer/checkbox': 4.1.3(@types/node@22.10.2)
      '@inquirer/confirm': 5.1.7(@types/node@22.10.2)
      '@inquirer/editor': 4.2.8(@types/node@22.10.2)
      '@inquirer/expand': 4.0.10(@types/node@22.10.2)
      '@inquirer/input': 4.1.7(@types/node@22.10.2)
      '@inquirer/number': 3.0.10(@types/node@22.10.2)
      '@inquirer/password': 4.0.10(@types/node@22.10.2)
      '@inquirer/rawlist': 4.0.10(@types/node@22.10.2)
      '@inquirer/search': 3.0.10(@types/node@22.10.2)
      '@inquirer/select': 4.0.10(@types/node@22.10.2)
    optionalDependencies:
      '@types/node': 22.10.2

  '@inquirer/rawlist@4.0.10(@types/node@22.10.2)':
    dependencies:
      '@inquirer/core': 10.1.8(@types/node@22.10.2)
      '@inquirer/type': 3.0.5(@types/node@22.10.2)
      yoctocolors-cjs: 2.1.2
    optionalDependencies:
      '@types/node': 22.10.2

  '@inquirer/search@3.0.10(@types/node@22.10.2)':
    dependencies:
      '@inquirer/core': 10.1.8(@types/node@22.10.2)
      '@inquirer/figures': 1.0.11
      '@inquirer/type': 3.0.5(@types/node@22.10.2)
      yoctocolors-cjs: 2.1.2
    optionalDependencies:
      '@types/node': 22.10.2

  '@inquirer/select@4.0.10(@types/node@22.10.2)':
    dependencies:
      '@inquirer/core': 10.1.8(@types/node@22.10.2)
      '@inquirer/figures': 1.0.11
      '@inquirer/type': 3.0.5(@types/node@22.10.2)
      ansi-escapes: 4.3.2
      yoctocolors-cjs: 2.1.2
    optionalDependencies:
      '@types/node': 22.10.2

  '@inquirer/type@3.0.5(@types/node@22.10.2)':
    optionalDependencies:
      '@types/node': 22.10.2

  '@isaacs/cliui@8.0.2':
    dependencies:
      string-width: 5.1.2
      string-width-cjs: string-width@4.2.3
      strip-ansi: 7.1.0
      strip-ansi-cjs: strip-ansi@6.0.1
      wrap-ansi: 8.1.0
      wrap-ansi-cjs: wrap-ansi@7.0.0

  '@jridgewell/gen-mapping@0.3.8':
    dependencies:
      '@jridgewell/set-array': 1.2.1
      '@jridgewell/sourcemap-codec': 1.5.0
      '@jridgewell/trace-mapping': 0.3.25

  '@jridgewell/resolve-uri@3.1.2': {}

  '@jridgewell/set-array@1.2.1': {}

  '@jridgewell/sourcemap-codec@1.5.0': {}

  '@jridgewell/trace-mapping@0.3.25':
    dependencies:
      '@jridgewell/resolve-uri': 3.1.2
      '@jridgewell/sourcemap-codec': 1.5.0

  '@mrmlnc/readdir-enhanced@2.2.1':
    dependencies:
      call-me-maybe: 1.0.2
      glob-to-regexp: 0.3.0

  '@nodelib/fs.scandir@2.1.5':
    dependencies:
      '@nodelib/fs.stat': 2.0.5
      run-parallel: 1.2.0

  '@nodelib/fs.stat@1.1.3': {}

  '@nodelib/fs.stat@2.0.5': {}

  '@nodelib/fs.walk@1.2.8':
    dependencies:
      '@nodelib/fs.scandir': 2.1.5
      fastq: 1.18.0

  '@pkgjs/parseargs@0.11.0':
    optional: true

  '@rc-component/async-validator@5.0.4':
    dependencies:
      '@babel/runtime': 7.26.0

  '@rc-component/color-picker@2.0.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@ant-design/fast-color': 2.0.6
      '@babel/runtime': 7.26.0
      classnames: 2.5.1
      rc-util: 5.44.3(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@rc-component/context@1.4.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@babel/runtime': 7.26.0
      rc-util: 5.44.3(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@rc-component/mini-decimal@1.1.0':
    dependencies:
      '@babel/runtime': 7.26.0

  '@rc-component/mutate-observer@1.1.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@babel/runtime': 7.26.0
      classnames: 2.5.1
      rc-util: 5.44.3(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@rc-component/portal@1.1.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@babel/runtime': 7.26.0
      classnames: 2.5.1
      rc-util: 5.44.3(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@rc-component/qrcode@1.0.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@babel/runtime': 7.26.0
      classnames: 2.5.1
      rc-util: 5.44.3(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@rc-component/tour@1.15.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@babel/runtime': 7.26.0
      '@rc-component/portal': 1.1.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@rc-component/trigger': 2.2.6(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      classnames: 2.5.1
      rc-util: 5.44.3(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@rc-component/trigger@2.2.6(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@babel/runtime': 7.26.0
      '@rc-component/portal': 1.1.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      classnames: 2.5.1
      rc-motion: 2.9.5(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      rc-resize-observer: 1.4.3(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      rc-util: 5.44.3(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@rollup/plugin-typescript@12.1.2(rollup@4.30.1)(tslib@2.8.1)(typescript@4.9.5)':
    dependencies:
      '@rollup/pluginutils': 5.1.4(rollup@4.30.1)
      resolve: 1.22.10
      typescript: 4.9.5
    optionalDependencies:
      rollup: 4.30.1
      tslib: 2.8.1

  '@rollup/pluginutils@5.1.4(rollup@4.30.1)':
    dependencies:
      '@types/estree': 1.0.6
      estree-walker: 2.0.2
      picomatch: 4.0.2
    optionalDependencies:
      rollup: 4.30.1

  '@rollup/rollup-android-arm-eabi@4.30.1':
    optional: true

  '@rollup/rollup-android-arm64@4.30.1':
    optional: true

  '@rollup/rollup-darwin-arm64@4.30.1':
    optional: true

  '@rollup/rollup-darwin-x64@4.30.1':
    optional: true

  '@rollup/rollup-freebsd-arm64@4.30.1':
    optional: true

  '@rollup/rollup-freebsd-x64@4.30.1':
    optional: true

  '@rollup/rollup-linux-arm-gnueabihf@4.30.1':
    optional: true

  '@rollup/rollup-linux-arm-musleabihf@4.30.1':
    optional: true

  '@rollup/rollup-linux-arm64-gnu@4.30.1':
    optional: true

  '@rollup/rollup-linux-arm64-musl@4.30.1':
    optional: true

  '@rollup/rollup-linux-loongarch64-gnu@4.30.1':
    optional: true

  '@rollup/rollup-linux-powerpc64le-gnu@4.30.1':
    optional: true

  '@rollup/rollup-linux-riscv64-gnu@4.30.1':
    optional: true

  '@rollup/rollup-linux-s390x-gnu@4.30.1':
    optional: true

  '@rollup/rollup-linux-x64-gnu@4.30.1':
    optional: true

  '@rollup/rollup-linux-x64-musl@4.30.1':
    optional: true

  '@rollup/rollup-win32-arm64-msvc@4.30.1':
    optional: true

  '@rollup/rollup-win32-ia32-msvc@4.30.1':
    optional: true

  '@rollup/rollup-win32-x64-msvc@4.30.1':
    optional: true

  '@ss/mtd-adapter-vue3@1.2.19(vue-router@4.5.0(vue@3.5.13(typescript@4.9.5)))(vue@3.5.13(typescript@4.9.5))':
    dependencies:
      tslib: 2.8.1
      vue: 3.5.13(typescript@4.9.5)
      vue-router: 4.5.0(vue@3.5.13(typescript@4.9.5))

  '@ss/mtd-adapter-vue3@1.2.19(vue-router@4.5.0(vue@3.5.13(typescript@5.6.3)))(vue@3.5.13(typescript@5.6.3))':
    dependencies:
      tslib: 2.8.1
      vue: 3.5.13(typescript@5.6.3)
      vue-router: 4.5.0(vue@3.5.13(typescript@5.6.3))

  '@ss/mtd-vue3@1.2.36(@babel/core@7.26.0)(vue-router@4.5.0(vue@3.5.13(typescript@4.9.5)))(vue@3.5.13(typescript@4.9.5))':
    dependencies:
      '@babel/runtime': 7.26.0
      '@ss/mtd-adapter-vue3': 1.2.19(vue-router@4.5.0(vue@3.5.13(typescript@4.9.5)))(vue@3.5.13(typescript@4.9.5))
      '@vue/babel-plugin-jsx': 1.2.5(@babel/core@7.26.0)
      async-validator: 4.2.5
      dayjs: 1.11.13
      js-calendar: 1.2.3
      normalize-wheel: 1.0.1
      popper.js: 1.16.1
      resize-observer-polyfill: 1.5.1
      throttle-debounce: 3.0.1
      vue: 3.5.13(typescript@4.9.5)
    transitivePeerDependencies:
      - '@babel/core'
      - supports-color
      - vue-router

  '@ss/mtd-vue3@1.2.36(@babel/core@7.26.0)(vue-router@4.5.0(vue@3.5.13(typescript@5.6.3)))(vue@3.5.13(typescript@5.6.3))':
    dependencies:
      '@babel/runtime': 7.26.0
      '@ss/mtd-adapter-vue3': 1.2.19(vue-router@4.5.0(vue@3.5.13(typescript@5.6.3)))(vue@3.5.13(typescript@5.6.3))
      '@vue/babel-plugin-jsx': 1.2.5(@babel/core@7.26.0)
      async-validator: 4.2.5
      dayjs: 1.11.13
      js-calendar: 1.2.3
      normalize-wheel: 1.0.1
      popper.js: 1.16.1
      resize-observer-polyfill: 1.5.1
      throttle-debounce: 3.0.1
      vue: 3.5.13(typescript@5.6.3)
    transitivePeerDependencies:
      - '@babel/core'
      - supports-color
      - vue-router

  '@stylistic/eslint-plugin@2.12.1(eslint@8.57.1)(typescript@4.9.5)':
    dependencies:
      '@typescript-eslint/utils': 8.18.2(eslint@8.57.1)(typescript@4.9.5)
      eslint: 8.57.1
      eslint-visitor-keys: 4.2.0
      espree: 10.3.0
      estraverse: 5.3.0
      picomatch: 4.0.2
    transitivePeerDependencies:
      - supports-color
      - typescript

  '@types/babel__core@7.20.5':
    dependencies:
      '@babel/parser': 7.26.3
      '@babel/types': 7.26.3
      '@types/babel__generator': 7.6.8
      '@types/babel__template': 7.4.4
      '@types/babel__traverse': 7.20.6

  '@types/babel__generator@7.6.8':
    dependencies:
      '@babel/types': 7.26.3

  '@types/babel__template@7.4.4':
    dependencies:
      '@babel/parser': 7.26.3
      '@babel/types': 7.26.3

  '@types/babel__traverse@7.20.6':
    dependencies:
      '@babel/types': 7.26.3

  '@types/cookie@0.6.0': {}

  '@types/estree@1.0.6': {}

  '@types/glob@7.2.0':
    dependencies:
      '@types/minimatch': 5.1.2
      '@types/node': 22.10.2

  '@types/json-schema@7.0.15': {}

  '@types/minimatch@5.1.2': {}

  '@types/node@22.10.2':
    dependencies:
      undici-types: 6.20.0

  '@types/prop-types@15.7.14': {}

  '@types/react-dom@18.3.5(@types/react@18.3.18)':
    dependencies:
      '@types/react': 18.3.18

  '@types/react@18.3.18':
    dependencies:
      '@types/prop-types': 15.7.14
      csstype: 3.1.3

  '@typescript-eslint/eslint-plugin@8.18.2(@typescript-eslint/parser@8.18.2(eslint@9.17.0(jiti@1.21.7))(typescript@5.6.3))(eslint@9.17.0(jiti@1.21.7))(typescript@5.6.3)':
    dependencies:
      '@eslint-community/regexpp': 4.12.1
      '@typescript-eslint/parser': 8.18.2(eslint@9.17.0(jiti@1.21.7))(typescript@5.6.3)
      '@typescript-eslint/scope-manager': 8.18.2
      '@typescript-eslint/type-utils': 8.18.2(eslint@9.17.0(jiti@1.21.7))(typescript@5.6.3)
      '@typescript-eslint/utils': 8.18.2(eslint@9.17.0(jiti@1.21.7))(typescript@5.6.3)
      '@typescript-eslint/visitor-keys': 8.18.2
      eslint: 9.17.0(jiti@1.21.7)
      graphemer: 1.4.0
      ignore: 5.3.2
      natural-compare: 1.4.0
      ts-api-utils: 1.4.3(typescript@5.6.3)
      typescript: 5.6.3
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/parser@5.62.0(eslint@8.57.1)(typescript@4.9.5)':
    dependencies:
      '@typescript-eslint/scope-manager': 5.62.0
      '@typescript-eslint/types': 5.62.0
      '@typescript-eslint/typescript-estree': 5.62.0(typescript@4.9.5)
      debug: 4.4.0
      eslint: 8.57.1
    optionalDependencies:
      typescript: 4.9.5
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/parser@8.18.2(eslint@9.17.0(jiti@1.21.7))(typescript@5.6.3)':
    dependencies:
      '@typescript-eslint/scope-manager': 8.18.2
      '@typescript-eslint/types': 8.18.2
      '@typescript-eslint/typescript-estree': 8.18.2(typescript@5.6.3)
      '@typescript-eslint/visitor-keys': 8.18.2
      debug: 4.4.0
      eslint: 9.17.0(jiti@1.21.7)
      typescript: 5.6.3
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/scope-manager@5.62.0':
    dependencies:
      '@typescript-eslint/types': 5.62.0
      '@typescript-eslint/visitor-keys': 5.62.0

  '@typescript-eslint/scope-manager@8.18.2':
    dependencies:
      '@typescript-eslint/types': 8.18.2
      '@typescript-eslint/visitor-keys': 8.18.2

  '@typescript-eslint/type-utils@8.18.2(eslint@9.17.0(jiti@1.21.7))(typescript@5.6.3)':
    dependencies:
      '@typescript-eslint/typescript-estree': 8.18.2(typescript@5.6.3)
      '@typescript-eslint/utils': 8.18.2(eslint@9.17.0(jiti@1.21.7))(typescript@5.6.3)
      debug: 4.4.0
      eslint: 9.17.0(jiti@1.21.7)
      ts-api-utils: 1.4.3(typescript@5.6.3)
      typescript: 5.6.3
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/types@5.62.0': {}

  '@typescript-eslint/types@8.18.2': {}

  '@typescript-eslint/typescript-estree@5.62.0(typescript@4.9.5)':
    dependencies:
      '@typescript-eslint/types': 5.62.0
      '@typescript-eslint/visitor-keys': 5.62.0
      debug: 4.4.0
      globby: 11.1.0
      is-glob: 4.0.3
      semver: 7.6.3
      tsutils: 3.21.0(typescript@4.9.5)
    optionalDependencies:
      typescript: 4.9.5
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/typescript-estree@8.18.2(typescript@4.9.5)':
    dependencies:
      '@typescript-eslint/types': 8.18.2
      '@typescript-eslint/visitor-keys': 8.18.2
      debug: 4.4.0
      fast-glob: 3.3.2
      is-glob: 4.0.3
      minimatch: 9.0.5
      semver: 7.6.3
      ts-api-utils: 1.4.3(typescript@4.9.5)
      typescript: 4.9.5
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/typescript-estree@8.18.2(typescript@5.6.3)':
    dependencies:
      '@typescript-eslint/types': 8.18.2
      '@typescript-eslint/visitor-keys': 8.18.2
      debug: 4.4.0
      fast-glob: 3.3.2
      is-glob: 4.0.3
      minimatch: 9.0.5
      semver: 7.6.3
      ts-api-utils: 1.4.3(typescript@5.6.3)
      typescript: 5.6.3
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/utils@8.18.2(eslint@8.57.1)(typescript@4.9.5)':
    dependencies:
      '@eslint-community/eslint-utils': 4.4.1(eslint@8.57.1)
      '@typescript-eslint/scope-manager': 8.18.2
      '@typescript-eslint/types': 8.18.2
      '@typescript-eslint/typescript-estree': 8.18.2(typescript@4.9.5)
      eslint: 8.57.1
      typescript: 4.9.5
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/utils@8.18.2(eslint@9.17.0(jiti@1.21.7))(typescript@5.6.3)':
    dependencies:
      '@eslint-community/eslint-utils': 4.4.1(eslint@9.17.0(jiti@1.21.7))
      '@typescript-eslint/scope-manager': 8.18.2
      '@typescript-eslint/types': 8.18.2
      '@typescript-eslint/typescript-estree': 8.18.2(typescript@5.6.3)
      eslint: 9.17.0(jiti@1.21.7)
      typescript: 5.6.3
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/visitor-keys@5.62.0':
    dependencies:
      '@typescript-eslint/types': 5.62.0
      eslint-visitor-keys: 3.4.3

  '@typescript-eslint/visitor-keys@8.18.2':
    dependencies:
      '@typescript-eslint/types': 8.18.2
      eslint-visitor-keys: 4.2.0

  '@ungap/structured-clone@1.2.1': {}

  '@vitejs/plugin-react@4.3.4(vite@6.0.6(@types/node@22.10.2)(jiti@1.21.7)(sass-embedded@1.83.0)(yaml@2.6.1))':
    dependencies:
      '@babel/core': 7.26.0
      '@babel/plugin-transform-react-jsx-self': 7.25.9(@babel/core@7.26.0)
      '@babel/plugin-transform-react-jsx-source': 7.25.9(@babel/core@7.26.0)
      '@types/babel__core': 7.20.5
      react-refresh: 0.14.2
      vite: 6.0.6(@types/node@22.10.2)(jiti@1.21.7)(sass-embedded@1.83.0)(yaml@2.6.1)
    transitivePeerDependencies:
      - supports-color

  '@vitejs/plugin-vue@5.2.1(vite@6.0.6(@types/node@22.10.2)(jiti@1.21.7)(sass-embedded@1.83.0)(yaml@2.6.1))(vue@3.5.13(typescript@4.9.5))':
    dependencies:
      vite: 6.0.6(@types/node@22.10.2)(jiti@1.21.7)(sass-embedded@1.83.0)(yaml@2.6.1)
      vue: 3.5.13(typescript@4.9.5)

  '@vue/babel-helper-vue-transform-on@1.2.5': {}

  '@vue/babel-plugin-jsx@1.2.5(@babel/core@7.26.0)':
    dependencies:
      '@babel/helper-module-imports': 7.25.9
      '@babel/helper-plugin-utils': 7.25.9
      '@babel/plugin-syntax-jsx': 7.25.9(@babel/core@7.26.0)
      '@babel/template': 7.25.9
      '@babel/traverse': 7.26.4
      '@babel/types': 7.26.3
      '@vue/babel-helper-vue-transform-on': 1.2.5
      '@vue/babel-plugin-resolve-type': 1.2.5(@babel/core@7.26.0)
      html-tags: 3.3.1
      svg-tags: 1.0.0
    optionalDependencies:
      '@babel/core': 7.26.0
    transitivePeerDependencies:
      - supports-color

  '@vue/babel-plugin-resolve-type@1.2.5(@babel/core@7.26.0)':
    dependencies:
      '@babel/code-frame': 7.26.2
      '@babel/core': 7.26.0
      '@babel/helper-module-imports': 7.25.9
      '@babel/helper-plugin-utils': 7.25.9
      '@babel/parser': 7.26.3
      '@vue/compiler-sfc': 3.5.13
    transitivePeerDependencies:
      - supports-color

  '@vue/compiler-core@3.5.13':
    dependencies:
      '@babel/parser': 7.26.3
      '@vue/shared': 3.5.13
      entities: 4.5.0
      estree-walker: 2.0.2
      source-map-js: 1.2.1

  '@vue/compiler-dom@3.5.13':
    dependencies:
      '@vue/compiler-core': 3.5.13
      '@vue/shared': 3.5.13

  '@vue/compiler-sfc@3.5.13':
    dependencies:
      '@babel/parser': 7.26.3
      '@vue/compiler-core': 3.5.13
      '@vue/compiler-dom': 3.5.13
      '@vue/compiler-ssr': 3.5.13
      '@vue/shared': 3.5.13
      estree-walker: 2.0.2
      magic-string: 0.30.17
      postcss: 8.4.49
      source-map-js: 1.2.1

  '@vue/compiler-ssr@3.5.13':
    dependencies:
      '@vue/compiler-dom': 3.5.13
      '@vue/shared': 3.5.13

  '@vue/devtools-api@6.6.4': {}

  '@vue/reactivity@3.5.13':
    dependencies:
      '@vue/shared': 3.5.13

  '@vue/runtime-core@3.5.13':
    dependencies:
      '@vue/reactivity': 3.5.13
      '@vue/shared': 3.5.13

  '@vue/runtime-dom@3.5.13':
    dependencies:
      '@vue/reactivity': 3.5.13
      '@vue/runtime-core': 3.5.13
      '@vue/shared': 3.5.13
      csstype: 3.1.3

  '@vue/server-renderer@3.5.13(vue@3.5.13(typescript@4.9.5))':
    dependencies:
      '@vue/compiler-ssr': 3.5.13
      '@vue/shared': 3.5.13
      vue: 3.5.13(typescript@4.9.5)

  '@vue/server-renderer@3.5.13(vue@3.5.13(typescript@5.6.3))':
    dependencies:
      '@vue/compiler-ssr': 3.5.13
      '@vue/shared': 3.5.13
      vue: 3.5.13(typescript@5.6.3)

  '@vue/shared@3.5.13': {}

  acorn-jsx@5.3.2(acorn@8.14.0):
    dependencies:
      acorn: 8.14.0

  acorn@8.14.0: {}

  ajv@6.12.6:
    dependencies:
      fast-deep-equal: 3.1.3
      fast-json-stable-stringify: 2.1.0
      json-schema-traverse: 0.4.1
      uri-js: 4.4.1

  ansi-color@0.2.1: {}

  ansi-escapes@4.3.2:
    dependencies:
      type-fest: 0.21.3

  ansi-escapes@7.0.0:
    dependencies:
      environment: 1.1.0

  ansi-regex@5.0.1: {}

  ansi-regex@6.1.0: {}

  ansi-styles@3.2.1:
    dependencies:
      color-convert: 1.9.3

  ansi-styles@4.3.0:
    dependencies:
      color-convert: 2.0.1

  ansi-styles@6.2.1: {}

  antd@5.22.7(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    dependencies:
      '@ant-design/colors': 7.1.0
      '@ant-design/cssinjs': 1.22.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@ant-design/cssinjs-utils': 1.1.3(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@ant-design/icons': 5.5.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@ant-design/react-slick': 1.1.2(react@18.3.1)
      '@babel/runtime': 7.26.0
      '@ctrl/tinycolor': 3.6.1
      '@rc-component/color-picker': 2.0.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@rc-component/mutate-observer': 1.1.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@rc-component/qrcode': 1.0.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@rc-component/tour': 1.15.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@rc-component/trigger': 2.2.6(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      classnames: 2.5.1
      copy-to-clipboard: 3.3.3
      dayjs: 1.11.13
      rc-cascader: 3.30.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      rc-checkbox: 3.3.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      rc-collapse: 3.9.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      rc-dialog: 9.6.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      rc-drawer: 7.2.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      rc-dropdown: 4.2.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      rc-field-form: 2.7.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      rc-image: 7.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      rc-input: 1.6.4(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      rc-input-number: 9.3.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      rc-mentions: 2.17.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      rc-menu: 9.16.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      rc-motion: 2.9.5(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      rc-notification: 5.6.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      rc-pagination: 5.0.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      rc-picker: 4.8.3(dayjs@1.11.13)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      rc-progress: 4.0.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      rc-rate: 2.13.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      rc-resize-observer: 1.4.3(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      rc-segmented: 2.5.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      rc-select: 14.16.4(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      rc-slider: 11.1.7(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      rc-steps: 6.0.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      rc-switch: 4.1.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      rc-table: 7.49.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      rc-tabs: 15.4.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      rc-textarea: 1.8.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      rc-tooltip: 6.2.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      rc-tree: 5.10.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      rc-tree-select: 5.24.5(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      rc-upload: 4.8.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      rc-util: 5.44.3(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
      scroll-into-view-if-needed: 3.1.0
      throttle-debounce: 5.0.2
    transitivePeerDependencies:
      - date-fns
      - luxon
      - moment

  any-promise@1.3.0: {}

  anymatch@3.1.3:
    dependencies:
      normalize-path: 3.0.0
      picomatch: 2.3.1

  arg@5.0.2: {}

  argparse@2.0.1: {}

  arr-diff@4.0.0: {}

  arr-flatten@1.1.0: {}

  arr-union@3.1.0: {}

  array-buffer-byte-length@1.0.2:
    dependencies:
      call-bound: 1.0.3
      is-array-buffer: 3.0.5

  array-includes@3.1.8:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.23.7
      es-object-atoms: 1.0.0
      get-intrinsic: 1.2.6
      is-string: 1.1.1

  array-union@1.0.2:
    dependencies:
      array-uniq: 1.0.3

  array-union@2.1.0: {}

  array-uniq@1.0.3: {}

  array-unique@0.3.2: {}

  array.prototype.findlast@1.2.5:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.23.7
      es-errors: 1.3.0
      es-object-atoms: 1.0.0
      es-shim-unscopables: 1.0.2

  array.prototype.flat@1.3.3:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.23.7
      es-shim-unscopables: 1.0.2

  array.prototype.flatmap@1.3.3:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.23.7
      es-shim-unscopables: 1.0.2

  array.prototype.tosorted@1.1.4:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.23.7
      es-errors: 1.3.0
      es-shim-unscopables: 1.0.2

  arraybuffer.prototype.slice@1.0.4:
    dependencies:
      array-buffer-byte-length: 1.0.2
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.23.7
      es-errors: 1.3.0
      get-intrinsic: 1.2.6
      is-array-buffer: 3.0.5

  arrify@1.0.1: {}

  assign-symbols@1.0.0: {}

  async-validator@4.2.5: {}

  asynckit@0.4.0: {}

  atob@2.1.2: {}

  autoprefixer@10.4.20(postcss@8.4.49):
    dependencies:
      browserslist: 4.24.3
      caniuse-lite: 1.0.30001690
      fraction.js: 4.3.7
      normalize-range: 0.1.2
      picocolors: 1.1.1
      postcss: 8.4.49
      postcss-value-parser: 4.2.0

  available-typed-arrays@1.0.7:
    dependencies:
      possible-typed-array-names: 1.0.0

  axios@1.7.9:
    dependencies:
      follow-redirects: 1.15.9
      form-data: 4.0.1
      proxy-from-env: 1.1.0
    transitivePeerDependencies:
      - debug

  balanced-match@1.0.2: {}

  base@0.11.2:
    dependencies:
      cache-base: 1.0.1
      class-utils: 0.3.6
      component-emitter: 1.3.1
      define-property: 1.0.0
      isobject: 3.0.1
      mixin-deep: 1.3.2
      pascalcase: 0.1.1

  binary-extensions@2.3.0: {}

  boolbase@1.0.0: {}

  brace-expansion@1.1.11:
    dependencies:
      balanced-match: 1.0.2
      concat-map: 0.0.1

  brace-expansion@2.0.1:
    dependencies:
      balanced-match: 1.0.2

  braces@2.3.2:
    dependencies:
      arr-flatten: 1.1.0
      array-unique: 0.3.2
      extend-shallow: 2.0.1
      fill-range: 4.0.0
      isobject: 3.0.1
      repeat-element: 1.1.4
      snapdragon: 0.8.2
      snapdragon-node: 2.1.1
      split-string: 3.1.0
      to-regex: 3.0.2
    transitivePeerDependencies:
      - supports-color

  braces@3.0.3:
    dependencies:
      fill-range: 7.1.1

  browserslist@4.24.3:
    dependencies:
      caniuse-lite: 1.0.30001690
      electron-to-chromium: 1.5.76
      node-releases: 2.0.19
      update-browserslist-db: 1.1.1(browserslist@4.24.3)

  buffer-builder@0.2.0: {}

  bundle-name@4.1.0:
    dependencies:
      run-applescript: 7.0.0

  bundle-require@4.2.1(esbuild@0.24.2):
    dependencies:
      esbuild: 0.24.2
      load-tsconfig: 0.2.5

  cache-base@1.0.1:
    dependencies:
      collection-visit: 1.0.0
      component-emitter: 1.3.1
      get-value: 2.0.6
      has-value: 1.0.0
      isobject: 3.0.1
      set-value: 2.0.1
      to-object-path: 0.3.0
      union-value: 1.0.1
      unset-value: 1.0.0

  call-bind-apply-helpers@1.0.1:
    dependencies:
      es-errors: 1.3.0
      function-bind: 1.1.2

  call-bind@1.0.8:
    dependencies:
      call-bind-apply-helpers: 1.0.1
      es-define-property: 1.0.1
      get-intrinsic: 1.2.6
      set-function-length: 1.2.2

  call-bound@1.0.3:
    dependencies:
      call-bind-apply-helpers: 1.0.1
      get-intrinsic: 1.2.6

  call-me-maybe@1.0.2: {}

  callsites@3.1.0: {}

  camelcase-css@2.0.1: {}

  caniuse-lite@1.0.30001690: {}

  chalk@2.4.2:
    dependencies:
      ansi-styles: 3.2.1
      escape-string-regexp: 1.0.5
      supports-color: 5.5.0

  chalk@4.1.2:
    dependencies:
      ansi-styles: 4.3.0
      supports-color: 7.2.0

  chalk@5.4.1: {}

  chardet@0.7.0: {}

  chokidar@3.6.0:
    dependencies:
      anymatch: 3.1.3
      braces: 3.0.3
      glob-parent: 5.1.2
      is-binary-path: 2.1.0
      is-glob: 4.0.3
      normalize-path: 3.0.0
      readdirp: 3.6.0
    optionalDependencies:
      fsevents: 2.3.3

  class-utils@0.3.6:
    dependencies:
      arr-union: 3.1.0
      define-property: 0.2.5
      isobject: 3.0.1
      static-extend: 0.1.2

  classnames@2.5.1: {}

  cli-cursor@5.0.0:
    dependencies:
      restore-cursor: 5.1.0

  cli-spinners@2.9.2: {}

  cli-truncate@4.0.0:
    dependencies:
      slice-ansi: 5.0.0
      string-width: 7.2.0

  cli-width@4.1.0: {}

  collection-visit@1.0.0:
    dependencies:
      map-visit: 1.0.0
      object-visit: 1.0.1

  color-convert@1.9.3:
    dependencies:
      color-name: 1.1.3

  color-convert@2.0.1:
    dependencies:
      color-name: 1.1.4

  color-name@1.1.3: {}

  color-name@1.1.4: {}

  colorette@2.0.20: {}

  colorjs.io@0.5.2: {}

  combined-stream@1.0.8:
    dependencies:
      delayed-stream: 1.0.0

  commander@12.1.0: {}

  commander@4.1.1: {}

  component-emitter@1.3.1: {}

  compute-scroll-into-view@3.1.0: {}

  concat-map@0.0.1: {}

  connect@3.7.0:
    dependencies:
      debug: 2.6.9
      finalhandler: 1.1.2
      parseurl: 1.3.3
      utils-merge: 1.0.1
    transitivePeerDependencies:
      - supports-color

  convert-source-map@2.0.0: {}

  cookie@1.0.2: {}

  copy-descriptor@0.1.1: {}

  copy-to-clipboard@3.3.3:
    dependencies:
      toggle-selection: 1.0.6

  cp-file@6.2.0:
    dependencies:
      graceful-fs: 4.2.11
      make-dir: 2.1.0
      nested-error-stacks: 2.1.1
      pify: 4.0.1
      safe-buffer: 5.2.1

  cpy@7.3.0:
    dependencies:
      arrify: 1.0.1
      cp-file: 6.2.0
      globby: 9.2.0
      nested-error-stacks: 2.1.1
    transitivePeerDependencies:
      - supports-color

  cross-env@7.0.3:
    dependencies:
      cross-spawn: 7.0.6

  cross-spawn@7.0.6:
    dependencies:
      path-key: 3.1.1
      shebang-command: 2.0.0
      which: 2.0.2

  cssesc@3.0.0: {}

  csstype@3.1.3: {}

  data-view-buffer@1.0.2:
    dependencies:
      call-bound: 1.0.3
      es-errors: 1.3.0
      is-data-view: 1.0.2

  data-view-byte-length@1.0.2:
    dependencies:
      call-bound: 1.0.3
      es-errors: 1.3.0
      is-data-view: 1.0.2

  data-view-byte-offset@1.0.1:
    dependencies:
      call-bound: 1.0.3
      es-errors: 1.3.0
      is-data-view: 1.0.2

  dayjs@1.11.13: {}

  debug@2.6.9:
    dependencies:
      ms: 2.0.0

  debug@4.4.0:
    dependencies:
      ms: 2.1.3

  decode-uri-component@0.2.2: {}

  deep-is@0.1.4: {}

  default-browser-id@5.0.0: {}

  default-browser@5.2.1:
    dependencies:
      bundle-name: 4.1.0
      default-browser-id: 5.0.0

  define-data-property@1.1.4:
    dependencies:
      es-define-property: 1.0.1
      es-errors: 1.3.0
      gopd: 1.2.0

  define-lazy-prop@3.0.0: {}

  define-properties@1.2.1:
    dependencies:
      define-data-property: 1.1.4
      has-property-descriptors: 1.0.2
      object-keys: 1.1.1

  define-property@0.2.5:
    dependencies:
      is-descriptor: 0.1.7

  define-property@1.0.0:
    dependencies:
      is-descriptor: 1.0.3

  define-property@2.0.2:
    dependencies:
      is-descriptor: 1.0.3
      isobject: 3.0.1

  delayed-stream@1.0.0: {}

  didyoumean@1.2.2: {}

  dir-glob@2.2.2:
    dependencies:
      path-type: 3.0.0

  dir-glob@3.0.1:
    dependencies:
      path-type: 4.0.0

  dlv@1.1.3: {}

  doctrine@2.1.0:
    dependencies:
      esutils: 2.0.3

  doctrine@3.0.0:
    dependencies:
      esutils: 2.0.3

  dunder-proto@1.0.1:
    dependencies:
      call-bind-apply-helpers: 1.0.1
      es-errors: 1.3.0
      gopd: 1.2.0

  eastasianwidth@0.2.0: {}

  ee-first@1.1.1: {}

  electron-to-chromium@1.5.76: {}

  emoji-regex@10.4.0: {}

  emoji-regex@8.0.0: {}

  emoji-regex@9.2.2: {}

  encodeurl@1.0.2: {}

  entities@4.5.0: {}

  environment@1.1.0: {}

  es-abstract@1.23.7:
    dependencies:
      array-buffer-byte-length: 1.0.2
      arraybuffer.prototype.slice: 1.0.4
      available-typed-arrays: 1.0.7
      call-bind: 1.0.8
      call-bound: 1.0.3
      data-view-buffer: 1.0.2
      data-view-byte-length: 1.0.2
      data-view-byte-offset: 1.0.1
      es-define-property: 1.0.1
      es-errors: 1.3.0
      es-object-atoms: 1.0.0
      es-set-tostringtag: 2.0.3
      es-to-primitive: 1.3.0
      function.prototype.name: 1.1.8
      get-intrinsic: 1.2.6
      get-symbol-description: 1.1.0
      globalthis: 1.0.4
      gopd: 1.2.0
      has-property-descriptors: 1.0.2
      has-proto: 1.2.0
      has-symbols: 1.1.0
      hasown: 2.0.2
      internal-slot: 1.1.0
      is-array-buffer: 3.0.5
      is-callable: 1.2.7
      is-data-view: 1.0.2
      is-regex: 1.2.1
      is-shared-array-buffer: 1.0.4
      is-string: 1.1.1
      is-typed-array: 1.1.15
      is-weakref: 1.1.0
      math-intrinsics: 1.1.0
      object-inspect: 1.13.3
      object-keys: 1.1.1
      object.assign: 4.1.7
      regexp.prototype.flags: 1.5.3
      safe-array-concat: 1.1.3
      safe-regex-test: 1.1.0
      string.prototype.trim: 1.2.10
      string.prototype.trimend: 1.0.9
      string.prototype.trimstart: 1.0.8
      typed-array-buffer: 1.0.3
      typed-array-byte-length: 1.0.3
      typed-array-byte-offset: 1.0.4
      typed-array-length: 1.0.7
      unbox-primitive: 1.1.0
      which-typed-array: 1.1.18

  es-define-property@1.0.1: {}

  es-errors@1.3.0: {}

  es-iterator-helpers@1.2.1:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.3
      define-properties: 1.2.1
      es-abstract: 1.23.7
      es-errors: 1.3.0
      es-set-tostringtag: 2.0.3
      function-bind: 1.1.2
      get-intrinsic: 1.2.6
      globalthis: 1.0.4
      gopd: 1.2.0
      has-property-descriptors: 1.0.2
      has-proto: 1.2.0
      has-symbols: 1.1.0
      internal-slot: 1.1.0
      iterator.prototype: 1.1.4
      safe-array-concat: 1.1.3

  es-module-lexer@1.6.0: {}

  es-object-atoms@1.0.0:
    dependencies:
      es-errors: 1.3.0

  es-set-tostringtag@2.0.3:
    dependencies:
      get-intrinsic: 1.2.6
      has-tostringtag: 1.0.2
      hasown: 2.0.2

  es-shim-unscopables@1.0.2:
    dependencies:
      hasown: 2.0.2

  es-to-primitive@1.3.0:
    dependencies:
      is-callable: 1.2.7
      is-date-object: 1.1.0
      is-symbol: 1.1.1

  esbuild@0.24.2:
    optionalDependencies:
      '@esbuild/aix-ppc64': 0.24.2
      '@esbuild/android-arm': 0.24.2
      '@esbuild/android-arm64': 0.24.2
      '@esbuild/android-x64': 0.24.2
      '@esbuild/darwin-arm64': 0.24.2
      '@esbuild/darwin-x64': 0.24.2
      '@esbuild/freebsd-arm64': 0.24.2
      '@esbuild/freebsd-x64': 0.24.2
      '@esbuild/linux-arm': 0.24.2
      '@esbuild/linux-arm64': 0.24.2
      '@esbuild/linux-ia32': 0.24.2
      '@esbuild/linux-loong64': 0.24.2
      '@esbuild/linux-mips64el': 0.24.2
      '@esbuild/linux-ppc64': 0.24.2
      '@esbuild/linux-riscv64': 0.24.2
      '@esbuild/linux-s390x': 0.24.2
      '@esbuild/linux-x64': 0.24.2
      '@esbuild/netbsd-arm64': 0.24.2
      '@esbuild/netbsd-x64': 0.24.2
      '@esbuild/openbsd-arm64': 0.24.2
      '@esbuild/openbsd-x64': 0.24.2
      '@esbuild/sunos-x64': 0.24.2
      '@esbuild/win32-arm64': 0.24.2
      '@esbuild/win32-ia32': 0.24.2
      '@esbuild/win32-x64': 0.24.2

  escalade@3.2.0: {}

  escape-html@1.0.3: {}

  escape-string-regexp@1.0.5: {}

  escape-string-regexp@4.0.0: {}

  eslint-plugin-react-hooks@5.1.0(eslint@8.57.1):
    dependencies:
      eslint: 8.57.1

  eslint-plugin-react-hooks@5.1.0(eslint@9.17.0(jiti@1.21.7)):
    dependencies:
      eslint: 9.17.0(jiti@1.21.7)

  eslint-plugin-react-refresh@0.4.16(eslint@8.57.1):
    dependencies:
      eslint: 8.57.1

  eslint-plugin-react-refresh@0.4.16(eslint@9.17.0(jiti@1.21.7)):
    dependencies:
      eslint: 9.17.0(jiti@1.21.7)

  eslint-plugin-react@7.37.3(eslint@8.57.1):
    dependencies:
      array-includes: 3.1.8
      array.prototype.findlast: 1.2.5
      array.prototype.flatmap: 1.3.3
      array.prototype.tosorted: 1.1.4
      doctrine: 2.1.0
      es-iterator-helpers: 1.2.1
      eslint: 8.57.1
      estraverse: 5.3.0
      hasown: 2.0.2
      jsx-ast-utils: 3.3.5
      minimatch: 3.1.2
      object.entries: 1.1.8
      object.fromentries: 2.0.8
      object.values: 1.2.1
      prop-types: 15.8.1
      resolve: 2.0.0-next.5
      semver: 6.3.1
      string.prototype.matchall: 4.0.12
      string.prototype.repeat: 1.0.0

  eslint-plugin-vue@9.33.0(eslint@8.57.1):
    dependencies:
      '@eslint-community/eslint-utils': 4.4.1(eslint@8.57.1)
      eslint: 8.57.1
      globals: 13.24.0
      natural-compare: 1.4.0
      nth-check: 2.1.1
      postcss-selector-parser: 6.1.2
      semver: 7.6.3
      vue-eslint-parser: 9.4.3(eslint@8.57.1)
      xml-name-validator: 4.0.0
    transitivePeerDependencies:
      - supports-color

  eslint-scope@7.2.2:
    dependencies:
      esrecurse: 4.3.0
      estraverse: 5.3.0

  eslint-scope@8.2.0:
    dependencies:
      esrecurse: 4.3.0
      estraverse: 5.3.0

  eslint-visitor-keys@3.4.3: {}

  eslint-visitor-keys@4.2.0: {}

  eslint@8.57.1:
    dependencies:
      '@eslint-community/eslint-utils': 4.4.1(eslint@8.57.1)
      '@eslint-community/regexpp': 4.12.1
      '@eslint/eslintrc': 2.1.4
      '@eslint/js': 8.57.1
      '@humanwhocodes/config-array': 0.13.0
      '@humanwhocodes/module-importer': 1.0.1
      '@nodelib/fs.walk': 1.2.8
      '@ungap/structured-clone': 1.2.1
      ajv: 6.12.6
      chalk: 4.1.2
      cross-spawn: 7.0.6
      debug: 4.4.0
      doctrine: 3.0.0
      escape-string-regexp: 4.0.0
      eslint-scope: 7.2.2
      eslint-visitor-keys: 3.4.3
      espree: 9.6.1
      esquery: 1.6.0
      esutils: 2.0.3
      fast-deep-equal: 3.1.3
      file-entry-cache: 6.0.1
      find-up: 5.0.0
      glob-parent: 6.0.2
      globals: 13.24.0
      graphemer: 1.4.0
      ignore: 5.3.2
      imurmurhash: 0.1.4
      is-glob: 4.0.3
      is-path-inside: 3.0.3
      js-yaml: 4.1.0
      json-stable-stringify-without-jsonify: 1.0.1
      levn: 0.4.1
      lodash.merge: 4.6.2
      minimatch: 3.1.2
      natural-compare: 1.4.0
      optionator: 0.9.4
      strip-ansi: 6.0.1
      text-table: 0.2.0
    transitivePeerDependencies:
      - supports-color

  eslint@9.17.0(jiti@1.21.7):
    dependencies:
      '@eslint-community/eslint-utils': 4.4.1(eslint@9.17.0(jiti@1.21.7))
      '@eslint-community/regexpp': 4.12.1
      '@eslint/config-array': 0.19.1
      '@eslint/core': 0.9.1
      '@eslint/eslintrc': 3.2.0
      '@eslint/js': 9.17.0
      '@eslint/plugin-kit': 0.2.4
      '@humanfs/node': 0.16.6
      '@humanwhocodes/module-importer': 1.0.1
      '@humanwhocodes/retry': 0.4.1
      '@types/estree': 1.0.6
      '@types/json-schema': 7.0.15
      ajv: 6.12.6
      chalk: 4.1.2
      cross-spawn: 7.0.6
      debug: 4.4.0
      escape-string-regexp: 4.0.0
      eslint-scope: 8.2.0
      eslint-visitor-keys: 4.2.0
      espree: 10.3.0
      esquery: 1.6.0
      esutils: 2.0.3
      fast-deep-equal: 3.1.3
      file-entry-cache: 8.0.0
      find-up: 5.0.0
      glob-parent: 6.0.2
      ignore: 5.3.2
      imurmurhash: 0.1.4
      is-glob: 4.0.3
      json-stable-stringify-without-jsonify: 1.0.1
      lodash.merge: 4.6.2
      minimatch: 3.1.2
      natural-compare: 1.4.0
      optionator: 0.9.4
    optionalDependencies:
      jiti: 1.21.7
    transitivePeerDependencies:
      - supports-color

  espree@10.3.0:
    dependencies:
      acorn: 8.14.0
      acorn-jsx: 5.3.2(acorn@8.14.0)
      eslint-visitor-keys: 4.2.0

  espree@9.6.1:
    dependencies:
      acorn: 8.14.0
      acorn-jsx: 5.3.2(acorn@8.14.0)
      eslint-visitor-keys: 3.4.3

  esquery@1.6.0:
    dependencies:
      estraverse: 5.3.0

  esrecurse@4.3.0:
    dependencies:
      estraverse: 5.3.0

  estraverse@5.3.0: {}

  estree-walker@2.0.2: {}

  esutils@2.0.3: {}

  eventemitter3@5.0.1: {}

  execa@8.0.1:
    dependencies:
      cross-spawn: 7.0.6
      get-stream: 8.0.1
      human-signals: 5.0.0
      is-stream: 3.0.0
      merge-stream: 2.0.0
      npm-run-path: 5.3.0
      onetime: 6.0.0
      signal-exit: 4.1.0
      strip-final-newline: 3.0.0

  expand-brackets@2.1.4:
    dependencies:
      debug: 2.6.9
      define-property: 0.2.5
      extend-shallow: 2.0.1
      posix-character-classes: 0.1.1
      regex-not: 1.0.2
      snapdragon: 0.8.2
      to-regex: 3.0.2
    transitivePeerDependencies:
      - supports-color

  extend-shallow@2.0.1:
    dependencies:
      is-extendable: 0.1.1

  extend-shallow@3.0.2:
    dependencies:
      assign-symbols: 1.0.0
      is-extendable: 1.0.1

  external-editor@3.1.0:
    dependencies:
      chardet: 0.7.0
      iconv-lite: 0.4.24
      tmp: 0.0.33

  extglob@2.0.4:
    dependencies:
      array-unique: 0.3.2
      define-property: 1.0.0
      expand-brackets: 2.1.4
      extend-shallow: 2.0.1
      fragment-cache: 0.2.1
      regex-not: 1.0.2
      snapdragon: 0.8.2
      to-regex: 3.0.2
    transitivePeerDependencies:
      - supports-color

  fast-deep-equal@3.1.3: {}

  fast-glob@2.2.7:
    dependencies:
      '@mrmlnc/readdir-enhanced': 2.2.1
      '@nodelib/fs.stat': 1.1.3
      glob-parent: 3.1.0
      is-glob: 4.0.3
      merge2: 1.4.1
      micromatch: 3.1.10
    transitivePeerDependencies:
      - supports-color

  fast-glob@3.3.2:
    dependencies:
      '@nodelib/fs.stat': 2.0.5
      '@nodelib/fs.walk': 1.2.8
      glob-parent: 5.1.2
      merge2: 1.4.1
      micromatch: 4.0.8

  fast-json-stable-stringify@2.1.0: {}

  fast-levenshtein@2.0.6: {}

  fastq@1.18.0:
    dependencies:
      reusify: 1.0.4

  file-entry-cache@6.0.1:
    dependencies:
      flat-cache: 3.2.0

  file-entry-cache@8.0.0:
    dependencies:
      flat-cache: 4.0.1

  fill-range@4.0.0:
    dependencies:
      extend-shallow: 2.0.1
      is-number: 3.0.0
      repeat-string: 1.6.1
      to-regex-range: 2.1.1

  fill-range@7.1.1:
    dependencies:
      to-regex-range: 5.0.1

  finalhandler@1.1.2:
    dependencies:
      debug: 2.6.9
      encodeurl: 1.0.2
      escape-html: 1.0.3
      on-finished: 2.3.0
      parseurl: 1.3.3
      statuses: 1.5.0
      unpipe: 1.0.0
    transitivePeerDependencies:
      - supports-color

  find-up-simple@1.0.0: {}

  find-up@5.0.0:
    dependencies:
      locate-path: 6.0.0
      path-exists: 4.0.0

  flat-cache@3.2.0:
    dependencies:
      flatted: 3.3.2
      keyv: 4.5.4
      rimraf: 3.0.2

  flat-cache@4.0.1:
    dependencies:
      flatted: 3.3.2
      keyv: 4.5.4

  flatted@3.3.2: {}

  follow-redirects@1.15.9: {}

  for-each@0.3.3:
    dependencies:
      is-callable: 1.2.7

  for-in@1.0.2: {}

  foreground-child@3.3.0:
    dependencies:
      cross-spawn: 7.0.6
      signal-exit: 4.1.0

  form-data@4.0.1:
    dependencies:
      asynckit: 0.4.0
      combined-stream: 1.0.8
      mime-types: 2.1.35

  fraction.js@4.3.7: {}

  fragment-cache@0.2.1:
    dependencies:
      map-cache: 0.2.2

  fs.realpath@1.0.0: {}

  fsevents@2.3.3:
    optional: true

  function-bind@1.1.2: {}

  function.prototype.name@1.1.8:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.3
      define-properties: 1.2.1
      functions-have-names: 1.2.3
      hasown: 2.0.2
      is-callable: 1.2.7

  functions-have-names@1.2.3: {}

  gensync@1.0.0-beta.2: {}

  get-east-asian-width@1.3.0: {}

  get-intrinsic@1.2.6:
    dependencies:
      call-bind-apply-helpers: 1.0.1
      dunder-proto: 1.0.1
      es-define-property: 1.0.1
      es-errors: 1.3.0
      es-object-atoms: 1.0.0
      function-bind: 1.1.2
      gopd: 1.2.0
      has-symbols: 1.1.0
      hasown: 2.0.2
      math-intrinsics: 1.1.0

  get-stream@8.0.1: {}

  get-symbol-description@1.1.0:
    dependencies:
      call-bound: 1.0.3
      es-errors: 1.3.0
      get-intrinsic: 1.2.6

  get-tsconfig@4.8.1:
    dependencies:
      resolve-pkg-maps: 1.0.0

  get-value@2.0.6: {}

  glob-parent@3.1.0:
    dependencies:
      is-glob: 3.1.0
      path-dirname: 1.0.2

  glob-parent@5.1.2:
    dependencies:
      is-glob: 4.0.3

  glob-parent@6.0.2:
    dependencies:
      is-glob: 4.0.3

  glob-to-regexp@0.3.0: {}

  glob@10.4.5:
    dependencies:
      foreground-child: 3.3.0
      jackspeak: 3.4.3
      minimatch: 9.0.5
      minipass: 7.1.2
      package-json-from-dist: 1.0.1
      path-scurry: 1.11.1

  glob@11.0.0:
    dependencies:
      foreground-child: 3.3.0
      jackspeak: 4.0.2
      minimatch: 10.0.1
      minipass: 7.1.2
      package-json-from-dist: 1.0.1
      path-scurry: 2.0.0

  glob@7.2.3:
    dependencies:
      fs.realpath: 1.0.0
      inflight: 1.0.6
      inherits: 2.0.4
      minimatch: 3.1.2
      once: 1.4.0
      path-is-absolute: 1.0.1

  globals@11.12.0: {}

  globals@13.24.0:
    dependencies:
      type-fest: 0.20.2

  globals@14.0.0: {}

  globals@15.14.0: {}

  globalthis@1.0.4:
    dependencies:
      define-properties: 1.2.1
      gopd: 1.2.0

  globby@11.1.0:
    dependencies:
      array-union: 2.1.0
      dir-glob: 3.0.1
      fast-glob: 3.3.2
      ignore: 5.3.2
      merge2: 1.4.1
      slash: 3.0.0

  globby@9.2.0:
    dependencies:
      '@types/glob': 7.2.0
      array-union: 1.0.2
      dir-glob: 2.2.2
      fast-glob: 2.2.7
      glob: 7.2.3
      ignore: 4.0.6
      pify: 4.0.1
      slash: 2.0.0
    transitivePeerDependencies:
      - supports-color

  gopd@1.2.0: {}

  graceful-fs@4.2.11: {}

  graphemer@1.4.0: {}

  has-bigints@1.1.0: {}

  has-flag@3.0.0: {}

  has-flag@4.0.0: {}

  has-property-descriptors@1.0.2:
    dependencies:
      es-define-property: 1.0.1

  has-proto@1.2.0:
    dependencies:
      dunder-proto: 1.0.1

  has-symbols@1.1.0: {}

  has-tostringtag@1.0.2:
    dependencies:
      has-symbols: 1.1.0

  has-value@0.3.1:
    dependencies:
      get-value: 2.0.6
      has-values: 0.1.4
      isobject: 2.1.0

  has-value@1.0.0:
    dependencies:
      get-value: 2.0.6
      has-values: 1.0.0
      isobject: 3.0.1

  has-values@0.1.4: {}

  has-values@1.0.0:
    dependencies:
      is-number: 3.0.0
      kind-of: 4.0.0

  hasown@2.0.2:
    dependencies:
      function-bind: 1.1.2

  html-tags@3.3.1: {}

  human-signals@5.0.0: {}

  iconv-lite@0.4.24:
    dependencies:
      safer-buffer: 2.1.2

  ignore@4.0.6: {}

  ignore@5.3.2: {}

  immutable@5.0.3: {}

  import-fresh@3.3.0:
    dependencies:
      parent-module: 1.0.1
      resolve-from: 4.0.0

  imurmurhash@0.1.4: {}

  inflight@1.0.6:
    dependencies:
      once: 1.4.0
      wrappy: 1.0.2

  inherits@2.0.4: {}

  inquirer@12.4.3(@types/node@22.10.2):
    dependencies:
      '@inquirer/core': 10.1.8(@types/node@22.10.2)
      '@inquirer/prompts': 7.3.3(@types/node@22.10.2)
      '@inquirer/type': 3.0.5(@types/node@22.10.2)
      ansi-escapes: 4.3.2
      mute-stream: 2.0.0
      run-async: 3.0.0
      rxjs: 7.8.2
    optionalDependencies:
      '@types/node': 22.10.2

  internal-slot@1.1.0:
    dependencies:
      es-errors: 1.3.0
      hasown: 2.0.2
      side-channel: 1.1.0

  is-accessor-descriptor@1.0.1:
    dependencies:
      hasown: 2.0.2

  is-array-buffer@3.0.5:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.3
      get-intrinsic: 1.2.6

  is-async-function@2.0.0:
    dependencies:
      has-tostringtag: 1.0.2

  is-bigint@1.1.0:
    dependencies:
      has-bigints: 1.1.0

  is-binary-path@2.1.0:
    dependencies:
      binary-extensions: 2.3.0

  is-boolean-object@1.2.1:
    dependencies:
      call-bound: 1.0.3
      has-tostringtag: 1.0.2

  is-buffer@1.1.6: {}

  is-callable@1.2.7: {}

  is-core-module@2.16.1:
    dependencies:
      hasown: 2.0.2

  is-data-descriptor@1.0.1:
    dependencies:
      hasown: 2.0.2

  is-data-view@1.0.2:
    dependencies:
      call-bound: 1.0.3
      get-intrinsic: 1.2.6
      is-typed-array: 1.1.15

  is-date-object@1.1.0:
    dependencies:
      call-bound: 1.0.3
      has-tostringtag: 1.0.2

  is-descriptor@0.1.7:
    dependencies:
      is-accessor-descriptor: 1.0.1
      is-data-descriptor: 1.0.1

  is-descriptor@1.0.3:
    dependencies:
      is-accessor-descriptor: 1.0.1
      is-data-descriptor: 1.0.1

  is-docker@3.0.0: {}

  is-extendable@0.1.1: {}

  is-extendable@1.0.1:
    dependencies:
      is-plain-object: 2.0.4

  is-extglob@2.1.1: {}

  is-finalizationregistry@1.1.1:
    dependencies:
      call-bound: 1.0.3

  is-fullwidth-code-point@3.0.0: {}

  is-fullwidth-code-point@4.0.0: {}

  is-fullwidth-code-point@5.0.0:
    dependencies:
      get-east-asian-width: 1.3.0

  is-generator-function@1.0.10:
    dependencies:
      has-tostringtag: 1.0.2

  is-glob@3.1.0:
    dependencies:
      is-extglob: 2.1.1

  is-glob@4.0.3:
    dependencies:
      is-extglob: 2.1.1

  is-inside-container@1.0.0:
    dependencies:
      is-docker: 3.0.0

  is-interactive@2.0.0: {}

  is-map@2.0.3: {}

  is-number-object@1.1.1:
    dependencies:
      call-bound: 1.0.3
      has-tostringtag: 1.0.2

  is-number@3.0.0:
    dependencies:
      kind-of: 3.2.2

  is-number@7.0.0: {}

  is-path-inside@3.0.3: {}

  is-plain-object@2.0.4:
    dependencies:
      isobject: 3.0.1

  is-regex@1.2.1:
    dependencies:
      call-bound: 1.0.3
      gopd: 1.2.0
      has-tostringtag: 1.0.2
      hasown: 2.0.2

  is-set@2.0.3: {}

  is-shared-array-buffer@1.0.4:
    dependencies:
      call-bound: 1.0.3

  is-stream@3.0.0: {}

  is-string@1.1.1:
    dependencies:
      call-bound: 1.0.3
      has-tostringtag: 1.0.2

  is-symbol@1.1.1:
    dependencies:
      call-bound: 1.0.3
      has-symbols: 1.1.0
      safe-regex-test: 1.1.0

  is-typed-array@1.1.15:
    dependencies:
      which-typed-array: 1.1.18

  is-unicode-supported@1.3.0: {}

  is-unicode-supported@2.1.0: {}

  is-weakmap@2.0.2: {}

  is-weakref@1.1.0:
    dependencies:
      call-bound: 1.0.3

  is-weakset@2.0.4:
    dependencies:
      call-bound: 1.0.3
      get-intrinsic: 1.2.6

  is-windows@1.0.2: {}

  is-wsl@3.1.0:
    dependencies:
      is-inside-container: 1.0.0

  isarray@1.0.0: {}

  isarray@2.0.5: {}

  isexe@2.0.0: {}

  isobject@2.1.0:
    dependencies:
      isarray: 1.0.0

  isobject@3.0.1: {}

  iterator.prototype@1.1.4:
    dependencies:
      define-data-property: 1.1.4
      es-object-atoms: 1.0.0
      get-intrinsic: 1.2.6
      has-symbols: 1.1.0
      reflect.getprototypeof: 1.0.9
      set-function-name: 2.0.2

  jackspeak@3.4.3:
    dependencies:
      '@isaacs/cliui': 8.0.2
    optionalDependencies:
      '@pkgjs/parseargs': 0.11.0

  jackspeak@4.0.2:
    dependencies:
      '@isaacs/cliui': 8.0.2

  jiti@1.21.7: {}

  js-calendar@1.2.3: {}

  js-tokens@4.0.0: {}

  js-yaml@4.1.0:
    dependencies:
      argparse: 2.0.1

  jsesc@3.1.0: {}

  json-buffer@3.0.1: {}

  json-schema-traverse@0.4.1: {}

  json-stable-stringify-without-jsonify@1.0.1: {}

  json2mq@0.2.0:
    dependencies:
      string-convert: 0.2.1

  json5@2.2.3: {}

  jsx-ast-utils@3.3.5:
    dependencies:
      array-includes: 3.1.8
      array.prototype.flat: 1.3.3
      object.assign: 4.1.7
      object.values: 1.2.1

  keyv@4.5.4:
    dependencies:
      json-buffer: 3.0.1

  kind-of@3.2.2:
    dependencies:
      is-buffer: 1.1.6

  kind-of@4.0.0:
    dependencies:
      is-buffer: 1.1.6

  kind-of@6.0.3: {}

  levn@0.4.1:
    dependencies:
      prelude-ls: 1.2.1
      type-check: 0.4.0

  lilconfig@3.1.3: {}

  lines-and-columns@1.2.4: {}

  lint-staged@15.3.0:
    dependencies:
      chalk: 5.4.1
      commander: 12.1.0
      debug: 4.4.0
      execa: 8.0.1
      lilconfig: 3.1.3
      listr2: 8.2.5
      micromatch: 4.0.8
      pidtree: 0.6.0
      string-argv: 0.3.2
      yaml: 2.6.1
    transitivePeerDependencies:
      - supports-color

  listr2@8.2.5:
    dependencies:
      cli-truncate: 4.0.0
      colorette: 2.0.20
      eventemitter3: 5.0.1
      log-update: 6.1.0
      rfdc: 1.4.1
      wrap-ansi: 9.0.0

  load-tsconfig@0.2.5: {}

  locate-path@6.0.0:
    dependencies:
      p-locate: 5.0.0

  lodash.isobject@3.0.2: {}

  lodash.merge@4.6.2: {}

  lodash@4.17.21: {}

  log-symbols@6.0.0:
    dependencies:
      chalk: 5.4.1
      is-unicode-supported: 1.3.0

  log-update@6.1.0:
    dependencies:
      ansi-escapes: 7.0.0
      cli-cursor: 5.0.0
      slice-ansi: 7.1.0
      strip-ansi: 7.1.0
      wrap-ansi: 9.0.0

  loose-envify@1.4.0:
    dependencies:
      js-tokens: 4.0.0

  lru-cache@10.4.3: {}

  lru-cache@11.0.2: {}

  lru-cache@5.1.1:
    dependencies:
      yallist: 3.1.1

  magic-string@0.30.17:
    dependencies:
      '@jridgewell/sourcemap-codec': 1.5.0

  make-dir@2.1.0:
    dependencies:
      pify: 4.0.1
      semver: 5.7.2

  map-cache@0.2.2: {}

  map-visit@1.0.0:
    dependencies:
      object-visit: 1.0.1

  math-intrinsics@1.1.0: {}

  merge-stream@2.0.0: {}

  merge2@1.4.1: {}

  micromatch@3.1.10:
    dependencies:
      arr-diff: 4.0.0
      array-unique: 0.3.2
      braces: 2.3.2
      define-property: 2.0.2
      extend-shallow: 3.0.2
      extglob: 2.0.4
      fragment-cache: 0.2.1
      kind-of: 6.0.3
      nanomatch: 1.2.13
      object.pick: 1.3.0
      regex-not: 1.0.2
      snapdragon: 0.8.2
      to-regex: 3.0.2
    transitivePeerDependencies:
      - supports-color

  micromatch@4.0.8:
    dependencies:
      braces: 3.0.3
      picomatch: 2.3.1

  mime-db@1.52.0: {}

  mime-types@2.1.35:
    dependencies:
      mime-db: 1.52.0

  mimic-fn@4.0.0: {}

  mimic-function@5.0.1: {}

  minimatch@10.0.1:
    dependencies:
      brace-expansion: 2.0.1

  minimatch@3.1.2:
    dependencies:
      brace-expansion: 1.1.11

  minimatch@9.0.5:
    dependencies:
      brace-expansion: 2.0.1

  minimist@1.2.8: {}

  minipass@7.1.2: {}

  mixin-deep@1.3.2:
    dependencies:
      for-in: 1.0.2
      is-extendable: 1.0.1

  mkdirp@0.5.6:
    dependencies:
      minimist: 1.2.8

  mockjs@1.1.0:
    dependencies:
      commander: 12.1.0

  ms@2.0.0: {}

  ms@2.1.3: {}

  mute-stream@2.0.0: {}

  mz@2.7.0:
    dependencies:
      any-promise: 1.3.0
      object-assign: 4.1.1
      thenify-all: 1.6.0

  nanoid@3.3.8: {}

  nanomatch@1.2.13:
    dependencies:
      arr-diff: 4.0.0
      array-unique: 0.3.2
      define-property: 2.0.2
      extend-shallow: 3.0.2
      fragment-cache: 0.2.1
      is-windows: 1.0.2
      kind-of: 6.0.3
      object.pick: 1.3.0
      regex-not: 1.0.2
      snapdragon: 0.8.2
      to-regex: 3.0.2
    transitivePeerDependencies:
      - supports-color

  natural-compare@1.4.0: {}

  nested-error-stacks@2.1.1: {}

  node-releases@2.0.19: {}

  normalize-path@3.0.0: {}

  normalize-range@0.1.2: {}

  normalize-wheel@1.0.1: {}

  npm-run-path@5.3.0:
    dependencies:
      path-key: 4.0.0

  nth-check@2.1.1:
    dependencies:
      boolbase: 1.0.0

  object-assign@4.1.1: {}

  object-copy@0.1.0:
    dependencies:
      copy-descriptor: 0.1.1
      define-property: 0.2.5
      kind-of: 3.2.2

  object-hash@3.0.0: {}

  object-inspect@1.13.3: {}

  object-keys@1.1.1: {}

  object-visit@1.0.1:
    dependencies:
      isobject: 3.0.1

  object.assign@4.1.7:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.3
      define-properties: 1.2.1
      es-object-atoms: 1.0.0
      has-symbols: 1.1.0
      object-keys: 1.1.1

  object.entries@1.1.8:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-object-atoms: 1.0.0

  object.fromentries@2.0.8:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.23.7
      es-object-atoms: 1.0.0

  object.pick@1.3.0:
    dependencies:
      isobject: 3.0.1

  object.values@1.2.1:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.3
      define-properties: 1.2.1
      es-object-atoms: 1.0.0

  on-finished@2.3.0:
    dependencies:
      ee-first: 1.1.1

  once@1.4.0:
    dependencies:
      wrappy: 1.0.2

  onetime@6.0.0:
    dependencies:
      mimic-fn: 4.0.0

  onetime@7.0.0:
    dependencies:
      mimic-function: 5.0.1

  open@10.1.0:
    dependencies:
      default-browser: 5.2.1
      define-lazy-prop: 3.0.0
      is-inside-container: 1.0.0
      is-wsl: 3.1.0

  optionator@0.9.4:
    dependencies:
      deep-is: 0.1.4
      fast-levenshtein: 2.0.6
      levn: 0.4.1
      prelude-ls: 1.2.1
      type-check: 0.4.0
      word-wrap: 1.2.5

  ora@8.1.1:
    dependencies:
      chalk: 5.4.1
      cli-cursor: 5.0.0
      cli-spinners: 2.9.2
      is-interactive: 2.0.0
      is-unicode-supported: 2.1.0
      log-symbols: 6.0.0
      stdin-discarder: 0.2.2
      string-width: 7.2.0
      strip-ansi: 7.1.0

  os-tmpdir@1.0.2: {}

  p-limit@3.1.0:
    dependencies:
      yocto-queue: 0.1.0

  p-locate@5.0.0:
    dependencies:
      p-limit: 3.1.0

  package-json-from-dist@1.0.1: {}

  parent-module@1.0.1:
    dependencies:
      callsites: 3.1.0

  parseurl@1.3.3: {}

  pascalcase@0.1.1: {}

  path-dirname@1.0.2: {}

  path-exists@4.0.0: {}

  path-is-absolute@1.0.1: {}

  path-key@3.1.1: {}

  path-key@4.0.0: {}

  path-parse@1.0.7: {}

  path-scurry@1.11.1:
    dependencies:
      lru-cache: 10.4.3
      minipass: 7.1.2

  path-scurry@2.0.0:
    dependencies:
      lru-cache: 11.0.2
      minipass: 7.1.2

  path-to-regexp@6.3.0: {}

  path-type@3.0.0:
    dependencies:
      pify: 3.0.0

  path-type@4.0.0: {}

  picocolors@1.1.1: {}

  picomatch@2.3.1: {}

  picomatch@4.0.2: {}

  pidtree@0.6.0: {}

  pify@2.3.0: {}

  pify@3.0.0: {}

  pify@4.0.1: {}

  pirates@4.0.6: {}

  pkg-dir@8.0.0:
    dependencies:
      find-up-simple: 1.0.0

  popper.js@1.16.1: {}

  posix-character-classes@0.1.1: {}

  possible-typed-array-names@1.0.0: {}

  postcss-import@15.1.0(postcss@8.4.49):
    dependencies:
      postcss: 8.4.49
      postcss-value-parser: 4.2.0
      read-cache: 1.0.0
      resolve: 1.22.10

  postcss-js@4.0.1(postcss@8.4.49):
    dependencies:
      camelcase-css: 2.0.1
      postcss: 8.4.49

  postcss-load-config@4.0.2(postcss@8.4.49):
    dependencies:
      lilconfig: 3.1.3
      yaml: 2.6.1
    optionalDependencies:
      postcss: 8.4.49

  postcss-nested@6.2.0(postcss@8.4.49):
    dependencies:
      postcss: 8.4.49
      postcss-selector-parser: 6.1.2

  postcss-selector-parser@6.1.2:
    dependencies:
      cssesc: 3.0.0
      util-deprecate: 1.0.2

  postcss-value-parser@4.2.0: {}

  postcss@8.4.49:
    dependencies:
      nanoid: 3.3.8
      picocolors: 1.1.1
      source-map-js: 1.2.1

  prelude-ls@1.2.1: {}

  prettier@3.4.2: {}

  prop-types@15.8.1:
    dependencies:
      loose-envify: 1.4.0
      object-assign: 4.1.1
      react-is: 16.13.1

  proxy-from-env@1.1.0: {}

  punycode@2.3.1: {}

  queue-microtask@1.2.3: {}

  rc-cascader@3.30.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    dependencies:
      '@babel/runtime': 7.26.0
      classnames: 2.5.1
      rc-select: 14.16.4(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      rc-tree: 5.10.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      rc-util: 5.44.3(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  rc-checkbox@3.3.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    dependencies:
      '@babel/runtime': 7.26.0
      classnames: 2.5.1
      rc-util: 5.44.3(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  rc-collapse@3.9.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    dependencies:
      '@babel/runtime': 7.26.0
      classnames: 2.5.1
      rc-motion: 2.9.5(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      rc-util: 5.44.3(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  rc-dialog@9.6.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    dependencies:
      '@babel/runtime': 7.26.0
      '@rc-component/portal': 1.1.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      classnames: 2.5.1
      rc-motion: 2.9.5(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      rc-util: 5.44.3(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  rc-drawer@7.2.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    dependencies:
      '@babel/runtime': 7.26.0
      '@rc-component/portal': 1.1.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      classnames: 2.5.1
      rc-motion: 2.9.5(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      rc-util: 5.44.3(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  rc-dropdown@4.2.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    dependencies:
      '@babel/runtime': 7.26.0
      '@rc-component/trigger': 2.2.6(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      classnames: 2.5.1
      rc-util: 5.44.3(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  rc-field-form@2.7.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    dependencies:
      '@babel/runtime': 7.26.0
      '@rc-component/async-validator': 5.0.4
      rc-util: 5.44.3(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  rc-image@7.11.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    dependencies:
      '@babel/runtime': 7.26.0
      '@rc-component/portal': 1.1.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      classnames: 2.5.1
      rc-dialog: 9.6.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      rc-motion: 2.9.5(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      rc-util: 5.44.3(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  rc-input-number@9.3.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    dependencies:
      '@babel/runtime': 7.26.0
      '@rc-component/mini-decimal': 1.1.0
      classnames: 2.5.1
      rc-input: 1.6.4(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      rc-util: 5.44.3(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  rc-input@1.6.4(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    dependencies:
      '@babel/runtime': 7.26.0
      classnames: 2.5.1
      rc-util: 5.44.3(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  rc-mentions@2.17.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    dependencies:
      '@babel/runtime': 7.26.0
      '@rc-component/trigger': 2.2.6(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      classnames: 2.5.1
      rc-input: 1.6.4(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      rc-menu: 9.16.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      rc-textarea: 1.8.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      rc-util: 5.44.3(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  rc-menu@9.16.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    dependencies:
      '@babel/runtime': 7.26.0
      '@rc-component/trigger': 2.2.6(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      classnames: 2.5.1
      rc-motion: 2.9.5(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      rc-overflow: 1.3.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      rc-util: 5.44.3(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  rc-motion@2.9.5(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    dependencies:
      '@babel/runtime': 7.26.0
      classnames: 2.5.1
      rc-util: 5.44.3(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  rc-notification@5.6.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    dependencies:
      '@babel/runtime': 7.26.0
      classnames: 2.5.1
      rc-motion: 2.9.5(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      rc-util: 5.44.3(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  rc-overflow@1.3.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    dependencies:
      '@babel/runtime': 7.26.0
      classnames: 2.5.1
      rc-resize-observer: 1.4.3(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      rc-util: 5.44.3(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  rc-pagination@5.0.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    dependencies:
      '@babel/runtime': 7.26.0
      classnames: 2.5.1
      rc-util: 5.44.3(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  rc-picker@4.8.3(dayjs@1.11.13)(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    dependencies:
      '@babel/runtime': 7.26.0
      '@rc-component/trigger': 2.2.6(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      classnames: 2.5.1
      rc-overflow: 1.3.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      rc-resize-observer: 1.4.3(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      rc-util: 5.44.3(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    optionalDependencies:
      dayjs: 1.11.13

  rc-progress@4.0.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    dependencies:
      '@babel/runtime': 7.26.0
      classnames: 2.5.1
      rc-util: 5.44.3(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  rc-rate@2.13.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    dependencies:
      '@babel/runtime': 7.26.0
      classnames: 2.5.1
      rc-util: 5.44.3(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  rc-resize-observer@1.4.3(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    dependencies:
      '@babel/runtime': 7.26.0
      classnames: 2.5.1
      rc-util: 5.44.3(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
      resize-observer-polyfill: 1.5.1

  rc-segmented@2.5.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    dependencies:
      '@babel/runtime': 7.26.0
      classnames: 2.5.1
      rc-motion: 2.9.5(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      rc-util: 5.44.3(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  rc-select@14.16.4(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    dependencies:
      '@babel/runtime': 7.26.0
      '@rc-component/trigger': 2.2.6(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      classnames: 2.5.1
      rc-motion: 2.9.5(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      rc-overflow: 1.3.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      rc-util: 5.44.3(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      rc-virtual-list: 3.16.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  rc-slider@11.1.7(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    dependencies:
      '@babel/runtime': 7.26.0
      classnames: 2.5.1
      rc-util: 5.44.3(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  rc-steps@6.0.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    dependencies:
      '@babel/runtime': 7.26.0
      classnames: 2.5.1
      rc-util: 5.44.3(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  rc-switch@4.1.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    dependencies:
      '@babel/runtime': 7.26.0
      classnames: 2.5.1
      rc-util: 5.44.3(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  rc-table@7.49.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    dependencies:
      '@babel/runtime': 7.26.0
      '@rc-component/context': 1.4.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      classnames: 2.5.1
      rc-resize-observer: 1.4.3(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      rc-util: 5.44.3(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      rc-virtual-list: 3.16.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  rc-tabs@15.4.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    dependencies:
      '@babel/runtime': 7.26.0
      classnames: 2.5.1
      rc-dropdown: 4.2.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      rc-menu: 9.16.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      rc-motion: 2.9.5(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      rc-resize-observer: 1.4.3(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      rc-util: 5.44.3(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  rc-textarea@1.8.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    dependencies:
      '@babel/runtime': 7.26.0
      classnames: 2.5.1
      rc-input: 1.6.4(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      rc-resize-observer: 1.4.3(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      rc-util: 5.44.3(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  rc-tooltip@6.2.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    dependencies:
      '@babel/runtime': 7.26.0
      '@rc-component/trigger': 2.2.6(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      classnames: 2.5.1
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  rc-tree-select@5.24.5(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    dependencies:
      '@babel/runtime': 7.26.0
      classnames: 2.5.1
      rc-select: 14.16.4(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      rc-tree: 5.10.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      rc-util: 5.44.3(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  rc-tree@5.10.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    dependencies:
      '@babel/runtime': 7.26.0
      classnames: 2.5.1
      rc-motion: 2.9.5(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      rc-util: 5.44.3(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      rc-virtual-list: 3.16.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  rc-upload@4.8.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    dependencies:
      '@babel/runtime': 7.26.0
      classnames: 2.5.1
      rc-util: 5.44.3(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  rc-util@5.44.3(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    dependencies:
      '@babel/runtime': 7.26.0
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
      react-is: 18.3.1

  rc-virtual-list@3.16.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    dependencies:
      '@babel/runtime': 7.26.0
      classnames: 2.5.1
      rc-resize-observer: 1.4.3(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      rc-util: 5.44.3(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  react-dom@18.3.1(react@18.3.1):
    dependencies:
      loose-envify: 1.4.0
      react: 18.3.1
      scheduler: 0.23.2

  react-is@16.13.1: {}

  react-is@18.3.1: {}

  react-refresh@0.14.2: {}

  react-router@7.1.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    dependencies:
      '@types/cookie': 0.6.0
      cookie: 1.0.2
      react: 18.3.1
      set-cookie-parser: 2.7.1
      turbo-stream: 2.4.0
    optionalDependencies:
      react-dom: 18.3.1(react@18.3.1)

  react@18.3.1:
    dependencies:
      loose-envify: 1.4.0

  read-cache@1.0.0:
    dependencies:
      pify: 2.3.0

  readdirp@3.6.0:
    dependencies:
      picomatch: 2.3.1

  reflect.getprototypeof@1.0.9:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      dunder-proto: 1.0.1
      es-abstract: 1.23.7
      es-errors: 1.3.0
      get-intrinsic: 1.2.6
      gopd: 1.2.0
      which-builtin-type: 1.2.1

  regenerator-runtime@0.14.1: {}

  regex-not@1.0.2:
    dependencies:
      extend-shallow: 3.0.2
      safe-regex: 1.1.0

  regexp.prototype.flags@1.5.3:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-errors: 1.3.0
      set-function-name: 2.0.2

  repeat-element@1.1.4: {}

  repeat-string@1.6.1: {}

  resize-observer-polyfill@1.5.1: {}

  resolve-from@4.0.0: {}

  resolve-pkg-maps@1.0.0: {}

  resolve-url@0.2.1: {}

  resolve@1.22.10:
    dependencies:
      is-core-module: 2.16.1
      path-parse: 1.0.7
      supports-preserve-symlinks-flag: 1.0.0

  resolve@2.0.0-next.5:
    dependencies:
      is-core-module: 2.16.1
      path-parse: 1.0.7
      supports-preserve-symlinks-flag: 1.0.0

  restore-cursor@5.1.0:
    dependencies:
      onetime: 7.0.0
      signal-exit: 4.1.0

  ret@0.1.15: {}

  reusify@1.0.4: {}

  rfdc@1.4.1: {}

  rimraf@3.0.2:
    dependencies:
      glob: 7.2.3

  rollup-plugin-cpy@2.0.1(rollup@4.30.1):
    dependencies:
      chalk: 2.4.2
      cpy: 7.3.0
      lodash.isobject: 3.0.2
      mkdirp: 0.5.6
      rollup: 4.30.1
    transitivePeerDependencies:
      - supports-color

  rollup-plugin-esbuild@6.1.1(esbuild@0.24.2)(rollup@4.30.1):
    dependencies:
      '@rollup/pluginutils': 5.1.4(rollup@4.30.1)
      debug: 4.4.0
      es-module-lexer: 1.6.0
      esbuild: 0.24.2
      get-tsconfig: 4.8.1
      rollup: 4.30.1
    transitivePeerDependencies:
      - supports-color

  rollup@4.30.1:
    dependencies:
      '@types/estree': 1.0.6
    optionalDependencies:
      '@rollup/rollup-android-arm-eabi': 4.30.1
      '@rollup/rollup-android-arm64': 4.30.1
      '@rollup/rollup-darwin-arm64': 4.30.1
      '@rollup/rollup-darwin-x64': 4.30.1
      '@rollup/rollup-freebsd-arm64': 4.30.1
      '@rollup/rollup-freebsd-x64': 4.30.1
      '@rollup/rollup-linux-arm-gnueabihf': 4.30.1
      '@rollup/rollup-linux-arm-musleabihf': 4.30.1
      '@rollup/rollup-linux-arm64-gnu': 4.30.1
      '@rollup/rollup-linux-arm64-musl': 4.30.1
      '@rollup/rollup-linux-loongarch64-gnu': 4.30.1
      '@rollup/rollup-linux-powerpc64le-gnu': 4.30.1
      '@rollup/rollup-linux-riscv64-gnu': 4.30.1
      '@rollup/rollup-linux-s390x-gnu': 4.30.1
      '@rollup/rollup-linux-x64-gnu': 4.30.1
      '@rollup/rollup-linux-x64-musl': 4.30.1
      '@rollup/rollup-win32-arm64-msvc': 4.30.1
      '@rollup/rollup-win32-ia32-msvc': 4.30.1
      '@rollup/rollup-win32-x64-msvc': 4.30.1
      fsevents: 2.3.3

  run-applescript@7.0.0: {}

  run-async@3.0.0: {}

  run-parallel@1.2.0:
    dependencies:
      queue-microtask: 1.2.3

  rxjs@7.8.1:
    dependencies:
      tslib: 2.8.1

  rxjs@7.8.2:
    dependencies:
      tslib: 2.8.1

  safe-array-concat@1.1.3:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.3
      get-intrinsic: 1.2.6
      has-symbols: 1.1.0
      isarray: 2.0.5

  safe-buffer@5.2.1: {}

  safe-regex-test@1.1.0:
    dependencies:
      call-bound: 1.0.3
      es-errors: 1.3.0
      is-regex: 1.2.1

  safe-regex@1.1.0:
    dependencies:
      ret: 0.1.15

  safer-buffer@2.1.2: {}

  sass-embedded-android-arm64@1.83.0:
    optional: true

  sass-embedded-android-arm@1.83.0:
    optional: true

  sass-embedded-android-ia32@1.83.0:
    optional: true

  sass-embedded-android-riscv64@1.83.0:
    optional: true

  sass-embedded-android-x64@1.83.0:
    optional: true

  sass-embedded-darwin-arm64@1.83.0:
    optional: true

  sass-embedded-darwin-x64@1.83.0:
    optional: true

  sass-embedded-linux-arm64@1.83.0:
    optional: true

  sass-embedded-linux-arm@1.83.0:
    optional: true

  sass-embedded-linux-ia32@1.83.0:
    optional: true

  sass-embedded-linux-musl-arm64@1.83.0:
    optional: true

  sass-embedded-linux-musl-arm@1.83.0:
    optional: true

  sass-embedded-linux-musl-ia32@1.83.0:
    optional: true

  sass-embedded-linux-musl-riscv64@1.83.0:
    optional: true

  sass-embedded-linux-musl-x64@1.83.0:
    optional: true

  sass-embedded-linux-riscv64@1.83.0:
    optional: true

  sass-embedded-linux-x64@1.83.0:
    optional: true

  sass-embedded-win32-arm64@1.83.0:
    optional: true

  sass-embedded-win32-ia32@1.83.0:
    optional: true

  sass-embedded-win32-x64@1.83.0:
    optional: true

  sass-embedded@1.83.0:
    dependencies:
      '@bufbuild/protobuf': 2.2.3
      buffer-builder: 0.2.0
      colorjs.io: 0.5.2
      immutable: 5.0.3
      rxjs: 7.8.1
      supports-color: 8.1.1
      sync-child-process: 1.0.2
      varint: 6.0.0
    optionalDependencies:
      sass-embedded-android-arm: 1.83.0
      sass-embedded-android-arm64: 1.83.0
      sass-embedded-android-ia32: 1.83.0
      sass-embedded-android-riscv64: 1.83.0
      sass-embedded-android-x64: 1.83.0
      sass-embedded-darwin-arm64: 1.83.0
      sass-embedded-darwin-x64: 1.83.0
      sass-embedded-linux-arm: 1.83.0
      sass-embedded-linux-arm64: 1.83.0
      sass-embedded-linux-ia32: 1.83.0
      sass-embedded-linux-musl-arm: 1.83.0
      sass-embedded-linux-musl-arm64: 1.83.0
      sass-embedded-linux-musl-ia32: 1.83.0
      sass-embedded-linux-musl-riscv64: 1.83.0
      sass-embedded-linux-musl-x64: 1.83.0
      sass-embedded-linux-riscv64: 1.83.0
      sass-embedded-linux-x64: 1.83.0
      sass-embedded-win32-arm64: 1.83.0
      sass-embedded-win32-ia32: 1.83.0
      sass-embedded-win32-x64: 1.83.0

  scheduler@0.23.2:
    dependencies:
      loose-envify: 1.4.0

  scroll-into-view-if-needed@3.1.0:
    dependencies:
      compute-scroll-into-view: 3.1.0

  semver@5.7.2: {}

  semver@6.3.1: {}

  semver@7.6.3: {}

  set-cookie-parser@2.7.1: {}

  set-function-length@1.2.2:
    dependencies:
      define-data-property: 1.1.4
      es-errors: 1.3.0
      function-bind: 1.1.2
      get-intrinsic: 1.2.6
      gopd: 1.2.0
      has-property-descriptors: 1.0.2

  set-function-name@2.0.2:
    dependencies:
      define-data-property: 1.1.4
      es-errors: 1.3.0
      functions-have-names: 1.2.3
      has-property-descriptors: 1.0.2

  set-value@2.0.1:
    dependencies:
      extend-shallow: 2.0.1
      is-extendable: 0.1.1
      is-plain-object: 2.0.4
      split-string: 3.1.0

  shebang-command@2.0.0:
    dependencies:
      shebang-regex: 3.0.0

  shebang-regex@3.0.0: {}

  side-channel-list@1.0.0:
    dependencies:
      es-errors: 1.3.0
      object-inspect: 1.13.3

  side-channel-map@1.0.1:
    dependencies:
      call-bound: 1.0.3
      es-errors: 1.3.0
      get-intrinsic: 1.2.6
      object-inspect: 1.13.3

  side-channel-weakmap@1.0.2:
    dependencies:
      call-bound: 1.0.3
      es-errors: 1.3.0
      get-intrinsic: 1.2.6
      object-inspect: 1.13.3
      side-channel-map: 1.0.1

  side-channel@1.1.0:
    dependencies:
      es-errors: 1.3.0
      object-inspect: 1.13.3
      side-channel-list: 1.0.0
      side-channel-map: 1.0.1
      side-channel-weakmap: 1.0.2

  signal-exit@4.1.0: {}

  simple-git-hooks@2.11.1: {}

  slash@2.0.0: {}

  slash@3.0.0: {}

  slice-ansi@5.0.0:
    dependencies:
      ansi-styles: 6.2.1
      is-fullwidth-code-point: 4.0.0

  slice-ansi@7.1.0:
    dependencies:
      ansi-styles: 6.2.1
      is-fullwidth-code-point: 5.0.0

  snapdragon-node@2.1.1:
    dependencies:
      define-property: 1.0.0
      isobject: 3.0.1
      snapdragon-util: 3.0.1

  snapdragon-util@3.0.1:
    dependencies:
      kind-of: 3.2.2

  snapdragon@0.8.2:
    dependencies:
      base: 0.11.2
      debug: 2.6.9
      define-property: 0.2.5
      extend-shallow: 2.0.1
      map-cache: 0.2.2
      source-map: 0.5.7
      source-map-resolve: 0.5.3
      use: 3.1.1
    transitivePeerDependencies:
      - supports-color

  source-map-js@1.2.1: {}

  source-map-resolve@0.5.3:
    dependencies:
      atob: 2.1.2
      decode-uri-component: 0.2.2
      resolve-url: 0.2.1
      source-map-url: 0.4.1
      urix: 0.1.0

  source-map-url@0.4.1: {}

  source-map@0.5.7: {}

  split-string@3.1.0:
    dependencies:
      extend-shallow: 3.0.2

  static-extend@0.1.2:
    dependencies:
      define-property: 0.2.5
      object-copy: 0.1.0

  statuses@1.5.0: {}

  stdin-discarder@0.2.2: {}

  string-argv@0.3.2: {}

  string-convert@0.2.1: {}

  string-width@4.2.3:
    dependencies:
      emoji-regex: 8.0.0
      is-fullwidth-code-point: 3.0.0
      strip-ansi: 6.0.1

  string-width@5.1.2:
    dependencies:
      eastasianwidth: 0.2.0
      emoji-regex: 9.2.2
      strip-ansi: 7.1.0

  string-width@7.2.0:
    dependencies:
      emoji-regex: 10.4.0
      get-east-asian-width: 1.3.0
      strip-ansi: 7.1.0

  string.prototype.matchall@4.0.12:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.3
      define-properties: 1.2.1
      es-abstract: 1.23.7
      es-errors: 1.3.0
      es-object-atoms: 1.0.0
      get-intrinsic: 1.2.6
      gopd: 1.2.0
      has-symbols: 1.1.0
      internal-slot: 1.1.0
      regexp.prototype.flags: 1.5.3
      set-function-name: 2.0.2
      side-channel: 1.1.0

  string.prototype.repeat@1.0.0:
    dependencies:
      define-properties: 1.2.1
      es-abstract: 1.23.7

  string.prototype.trim@1.2.10:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.3
      define-data-property: 1.1.4
      define-properties: 1.2.1
      es-abstract: 1.23.7
      es-object-atoms: 1.0.0
      has-property-descriptors: 1.0.2

  string.prototype.trimend@1.0.9:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.3
      define-properties: 1.2.1
      es-object-atoms: 1.0.0

  string.prototype.trimstart@1.0.8:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-object-atoms: 1.0.0

  strip-ansi@6.0.1:
    dependencies:
      ansi-regex: 5.0.1

  strip-ansi@7.1.0:
    dependencies:
      ansi-regex: 6.1.0

  strip-final-newline@3.0.0: {}

  strip-json-comments@3.1.1: {}

  stylis@4.3.4: {}

  sucrase@3.35.0:
    dependencies:
      '@jridgewell/gen-mapping': 0.3.8
      commander: 4.1.1
      glob: 10.4.5
      lines-and-columns: 1.2.4
      mz: 2.7.0
      pirates: 4.0.6
      ts-interface-checker: 0.1.13

  supports-color@5.5.0:
    dependencies:
      has-flag: 3.0.0

  supports-color@7.2.0:
    dependencies:
      has-flag: 4.0.0

  supports-color@8.1.1:
    dependencies:
      has-flag: 4.0.0

  supports-preserve-symlinks-flag@1.0.0: {}

  svg-tags@1.0.0: {}

  sync-child-process@1.0.2:
    dependencies:
      sync-message-port: 1.1.3

  sync-message-port@1.1.3: {}

  tailwindcss@3.4.17:
    dependencies:
      '@alloc/quick-lru': 5.2.0
      arg: 5.0.2
      chokidar: 3.6.0
      didyoumean: 1.2.2
      dlv: 1.1.3
      fast-glob: 3.3.2
      glob-parent: 6.0.2
      is-glob: 4.0.3
      jiti: 1.21.7
      lilconfig: 3.1.3
      micromatch: 4.0.8
      normalize-path: 3.0.0
      object-hash: 3.0.0
      picocolors: 1.1.1
      postcss: 8.4.49
      postcss-import: 15.1.0(postcss@8.4.49)
      postcss-js: 4.0.1(postcss@8.4.49)
      postcss-load-config: 4.0.2(postcss@8.4.49)
      postcss-nested: 6.2.0(postcss@8.4.49)
      postcss-selector-parser: 6.1.2
      resolve: 1.22.10
      sucrase: 3.35.0
    transitivePeerDependencies:
      - ts-node

  text-table@0.2.0: {}

  thenify-all@1.6.0:
    dependencies:
      thenify: 3.3.1

  thenify@3.3.1:
    dependencies:
      any-promise: 1.3.0

  throttle-debounce@3.0.1: {}

  throttle-debounce@5.0.2: {}

  tmp@0.0.33:
    dependencies:
      os-tmpdir: 1.0.2

  to-object-path@0.3.0:
    dependencies:
      kind-of: 3.2.2

  to-regex-range@2.1.1:
    dependencies:
      is-number: 3.0.0
      repeat-string: 1.6.1

  to-regex-range@5.0.1:
    dependencies:
      is-number: 7.0.0

  to-regex@3.0.2:
    dependencies:
      define-property: 2.0.2
      extend-shallow: 3.0.2
      regex-not: 1.0.2
      safe-regex: 1.1.0

  toggle-selection@1.0.6: {}

  ts-api-utils@1.4.3(typescript@4.9.5):
    dependencies:
      typescript: 4.9.5

  ts-api-utils@1.4.3(typescript@5.6.3):
    dependencies:
      typescript: 5.6.3

  ts-interface-checker@0.1.13: {}

  tslib@1.14.1: {}

  tslib@2.8.1: {}

  tsutils@3.21.0(typescript@4.9.5):
    dependencies:
      tslib: 1.14.1
      typescript: 4.9.5

  turbo-stream@2.4.0: {}

  type-check@0.4.0:
    dependencies:
      prelude-ls: 1.2.1

  type-fest@0.20.2: {}

  type-fest@0.21.3: {}

  typed-array-buffer@1.0.3:
    dependencies:
      call-bound: 1.0.3
      es-errors: 1.3.0
      is-typed-array: 1.1.15

  typed-array-byte-length@1.0.3:
    dependencies:
      call-bind: 1.0.8
      for-each: 0.3.3
      gopd: 1.2.0
      has-proto: 1.2.0
      is-typed-array: 1.1.15

  typed-array-byte-offset@1.0.4:
    dependencies:
      available-typed-arrays: 1.0.7
      call-bind: 1.0.8
      for-each: 0.3.3
      gopd: 1.2.0
      has-proto: 1.2.0
      is-typed-array: 1.1.15
      reflect.getprototypeof: 1.0.9

  typed-array-length@1.0.7:
    dependencies:
      call-bind: 1.0.8
      for-each: 0.3.3
      gopd: 1.2.0
      is-typed-array: 1.1.15
      possible-typed-array-names: 1.0.0
      reflect.getprototypeof: 1.0.9

  typescript-eslint@8.18.2(eslint@9.17.0(jiti@1.21.7))(typescript@5.6.3):
    dependencies:
      '@typescript-eslint/eslint-plugin': 8.18.2(@typescript-eslint/parser@8.18.2(eslint@9.17.0(jiti@1.21.7))(typescript@5.6.3))(eslint@9.17.0(jiti@1.21.7))(typescript@5.6.3)
      '@typescript-eslint/parser': 8.18.2(eslint@9.17.0(jiti@1.21.7))(typescript@5.6.3)
      '@typescript-eslint/utils': 8.18.2(eslint@9.17.0(jiti@1.21.7))(typescript@5.6.3)
      eslint: 9.17.0(jiti@1.21.7)
      typescript: 5.6.3
    transitivePeerDependencies:
      - supports-color

  typescript@4.9.5: {}

  typescript@5.6.3: {}

  unbox-primitive@1.1.0:
    dependencies:
      call-bound: 1.0.3
      has-bigints: 1.1.0
      has-symbols: 1.1.0
      which-boxed-primitive: 1.1.1

  undici-types@6.20.0: {}

  union-value@1.0.1:
    dependencies:
      arr-union: 3.1.0
      get-value: 2.0.6
      is-extendable: 0.1.1
      set-value: 2.0.1

  unpipe@1.0.0: {}

  unset-value@1.0.0:
    dependencies:
      has-value: 0.3.1
      isobject: 3.0.1

  update-browserslist-db@1.1.1(browserslist@4.24.3):
    dependencies:
      browserslist: 4.24.3
      escalade: 3.2.0
      picocolors: 1.1.1

  uri-js@4.4.1:
    dependencies:
      punycode: 2.3.1

  urix@0.1.0: {}

  use@3.1.1: {}

  util-deprecate@1.0.2: {}

  utils-merge@1.0.1: {}

  varint@6.0.0: {}

  vite-plugin-mock@3.0.2(esbuild@0.24.2)(mockjs@1.1.0)(vite@6.0.6(@types/node@22.10.2)(jiti@1.21.7)(sass-embedded@1.83.0)(yaml@2.6.1)):
    dependencies:
      bundle-require: 4.2.1(esbuild@0.24.2)
      chokidar: 3.6.0
      connect: 3.7.0
      debug: 4.4.0
      esbuild: 0.24.2
      fast-glob: 3.3.2
      mockjs: 1.1.0
      path-to-regexp: 6.3.0
      picocolors: 1.1.1
      vite: 6.0.6(@types/node@22.10.2)(jiti@1.21.7)(sass-embedded@1.83.0)(yaml@2.6.1)
    transitivePeerDependencies:
      - supports-color

  vite@6.0.6(@types/node@22.10.2)(jiti@1.21.7)(sass-embedded@1.83.0)(yaml@2.6.1):
    dependencies:
      esbuild: 0.24.2
      postcss: 8.4.49
      rollup: 4.30.1
    optionalDependencies:
      '@types/node': 22.10.2
      fsevents: 2.3.3
      jiti: 1.21.7
      sass-embedded: 1.83.0
      yaml: 2.6.1

  vue-eslint-parser@9.4.3(eslint@8.57.1):
    dependencies:
      debug: 4.4.0
      eslint: 8.57.1
      eslint-scope: 7.2.2
      eslint-visitor-keys: 3.4.3
      espree: 9.6.1
      esquery: 1.6.0
      lodash: 4.17.21
      semver: 7.6.3
    transitivePeerDependencies:
      - supports-color

  vue-router@4.5.0(vue@3.5.13(typescript@4.9.5)):
    dependencies:
      '@vue/devtools-api': 6.6.4
      vue: 3.5.13(typescript@4.9.5)

  vue-router@4.5.0(vue@3.5.13(typescript@5.6.3)):
    dependencies:
      '@vue/devtools-api': 6.6.4
      vue: 3.5.13(typescript@5.6.3)

  vue@3.5.13(typescript@4.9.5):
    dependencies:
      '@vue/compiler-dom': 3.5.13
      '@vue/compiler-sfc': 3.5.13
      '@vue/runtime-dom': 3.5.13
      '@vue/server-renderer': 3.5.13(vue@3.5.13(typescript@4.9.5))
      '@vue/shared': 3.5.13
    optionalDependencies:
      typescript: 4.9.5

  vue@3.5.13(typescript@5.6.3):
    dependencies:
      '@vue/compiler-dom': 3.5.13
      '@vue/compiler-sfc': 3.5.13
      '@vue/runtime-dom': 3.5.13
      '@vue/server-renderer': 3.5.13(vue@3.5.13(typescript@5.6.3))
      '@vue/shared': 3.5.13
    optionalDependencies:
      typescript: 5.6.3

  which-boxed-primitive@1.1.1:
    dependencies:
      is-bigint: 1.1.0
      is-boolean-object: 1.2.1
      is-number-object: 1.1.1
      is-string: 1.1.1
      is-symbol: 1.1.1

  which-builtin-type@1.2.1:
    dependencies:
      call-bound: 1.0.3
      function.prototype.name: 1.1.8
      has-tostringtag: 1.0.2
      is-async-function: 2.0.0
      is-date-object: 1.1.0
      is-finalizationregistry: 1.1.1
      is-generator-function: 1.0.10
      is-regex: 1.2.1
      is-weakref: 1.1.0
      isarray: 2.0.5
      which-boxed-primitive: 1.1.1
      which-collection: 1.0.2
      which-typed-array: 1.1.18

  which-collection@1.0.2:
    dependencies:
      is-map: 2.0.3
      is-set: 2.0.3
      is-weakmap: 2.0.2
      is-weakset: 2.0.4

  which-typed-array@1.1.18:
    dependencies:
      available-typed-arrays: 1.0.7
      call-bind: 1.0.8
      call-bound: 1.0.3
      for-each: 0.3.3
      gopd: 1.2.0
      has-tostringtag: 1.0.2

  which@2.0.2:
    dependencies:
      isexe: 2.0.0

  word-wrap@1.2.5: {}

  wrap-ansi@6.2.0:
    dependencies:
      ansi-styles: 4.3.0
      string-width: 4.2.3
      strip-ansi: 6.0.1

  wrap-ansi@7.0.0:
    dependencies:
      ansi-styles: 4.3.0
      string-width: 4.2.3
      strip-ansi: 6.0.1

  wrap-ansi@8.1.0:
    dependencies:
      ansi-styles: 6.2.1
      string-width: 5.1.2
      strip-ansi: 7.1.0

  wrap-ansi@9.0.0:
    dependencies:
      ansi-styles: 6.2.1
      string-width: 7.2.0
      strip-ansi: 7.1.0

  wrappy@1.0.2: {}

  xml-name-validator@4.0.0: {}

  yallist@3.1.1: {}

  yaml@2.6.1: {}

  yocto-queue@0.1.0: {}

  yoctocolors-cjs@2.1.2: {}
