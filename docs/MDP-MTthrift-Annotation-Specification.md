# MDP-MTthrift注解形式规范

## 前言

MTthrift注解功能总共有2个大的里程碑版本。请至少保证MTthrift和swift-codec的版本超过里程碑1的版本（MTthrift >= 2.2.1.1 && swift-codec >= 0.17.4-mt）。更早版本不再维护，本文档的示例在低于里程碑1的版本中可能无法运行。

| 里程碑版本 | 优化内容 |
| --- | --- |
| MTthrift >= [com.meituan.service.mobile:mtthrift:2.2.1.1](http://maven.sankuai.com/artifact/com.meituan.service.mobile/mtthrift/2.2.1.1) && swift-codec = [com.facebook.swift:swift-codec:0.17.4-mt](http://maven.sankuai.com/artifact/com.facebook.swift/swift-codec/0.17.4-mt) | 需要客户端服务端都升级<br>1. 可以省略@ThriftMethod 和 @ThriftStruct 注解<br>2. @ThriftField 支持配置在field上（之前版本是配置在get、set方法上）<br>3. 支持同时定义多个泛型。e.g. 同时定义List<String>和List<Integer>，之前版本如果注解定义中出现了List<String>，就不可以再定义List<Integer><br>4. 客户端的Integer等类型传null时，服务端接收到的也是null（不是默认行为，需配置ThriftServerPublisher#enableAnnotationDefaultVal=false）。之前版本服务端收到的是默认值0。 |
| 暂未发布正式版。预计在2025年6月份发布正式版<br>MTthrift版本：`com.meituan.service.mobile mtthrift 2.12.0-RC1`<br>swift-codec 版本：[com.facebook.swift:swift-codec:0.23.1-mt-RC6](http://maven.sankuai.com/artifact/com.facebook.swift/swift-codec/0.23.1-mt-RC6)<br>swift-annotations版本：[com.facebook.swift:swift-annotations:0.23.1-mt-RC6](http://maven.sankuai.com/artifact/com.facebook.swift/swift-annotations/0.23.1-mt-RC6) | 需要客户端服务端都升级<br>1. 支持Date等常用类型，可以精简Bean的定义代码。<br>2. 在RPC框架层做字段null值校验，可以精简业务层的校验代码。<br>3. 支持循环引用，避免客户端多次冗余请求获取完成信息。<br>4. 启动优化，类加载耗时减半。 |

## 1. MTthrift注解开发介绍

### 1.1 相对于IDL开发，使用注解有哪些好处？

1. 不需要学习IDL的定义语法。
2. 开发流程更便捷：省去了定义IDL、编译IDL的过程，直接定义Java文件，符合开发者的开发习惯。
3. 注解的定义代码可读性更好。

### 1.2 什么情况下不要用注解，需要用IDL？

如果项目有跨编程语言需要（或者未来可能跨语言调用），那么不要用注解。IDL支持跨语言，注解不支持。

> **注意事项：**
> - **使用注解开发方式时，应避免IDL和注解混用**（比如：Demo中TestService类用注解标示，其成员TestException类用IDL生成，未用注解标示），否则会导致无法初始化、异常解析错误等问题
> - **如果业务之前使用IDL方式定义接口，想更新为thrift注解的方式，这时服务端需要和调用端同步升级，保持一致。不支持一方使用IDL，一方使用注解。**

## 2. QuickStart Demo

### 2.1 定义接口

1. 类上添加@ThriftService
2. 方法上添加@ThriftMethod
3. 方法定义一定要抛出TException（TException是框架异常，业务异常请参考[怎样定义业务异常](#33-怎样定义业务异常)）

备注：虽然高版本支持不写@ThriftService、@ThriftMethod，但是建议填写，显示声明。

\`\`\`java
package com.meituan.service.mobile.mtthrift.annotation.demo;

import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import org.apache.thrift.TException;

@ThriftService
public interface TestService {
    @ThriftMethod
    public TestResponse method1(TestRequest testRequest) throws TException;

    @ThriftMethod
    public Long method2(int i) throws TException;

    @ThriftMethod(exception = {@ThriftException(type = TestException.class, id = 1)})
    public String method3() throws TestException, TException;
}
\`\`\`

### 2.2 定义参数Pojo类

1. 类上添加@ThriftStruct
2. 字段上一定要添加@ThriftField，并指定value值。同一个类(包括父类)中不同字段的value一定要不同。

备注：虽然高版本支持不写@ThriftStruct，但是建议填写，显示声明。

\`\`\`java
package com.meituan.service.mobile.mtthrift.annotation.demo;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

// 定义结构体
@ThriftStruct
@Getter
@Setter
@ToString
@EqualsAndHashCode
public class TestRequest {
    @ThriftField(value = 1)
    private Integer userid;

    @ThriftField(value = 2)
    private String message;

    @ThriftField(value = 3)
    private Integer seqid;
}
\`\`\`

### 2.3 启动服务端/客户端

和IDL的方式一样。此处不再赘述，可参考[服务端发布、客户端发布](https://km.sankuai.com/page/28257152)。

如果之前有用过IDL语法，可以参考下表理解注解的关键字：

| IDL文件内容 | 注解 |
| --- | --- |
| service | @ThriftService |
| struct | @ThriftStruct |
| struct 属性 | @ThriftField |
| enum | @ThriftEnumValue |

## 3. 更多用法Demo

### 3.1 @ThriftStruct类怎样使用有参的构造函数

在添加的构造函数中添加@ThriftConstructor注解：

\`\`\`java
//如果有非无参构造函数，需要在构造函数上加上@ThriftConstructor注解，
//一个类只能有一个带有@ThriftConstructor注解的构造函数
@ThriftConstructor
public TestResponse(Integer userid, String message, Integer seqid) {
    this.userid = userid;
    this.message = message;
    this.seqid = seqid;
}
\`\`\`

### 3.2 是否支持多个构造函数

不支持。只能有1个带有@ThriftConstructor注解的构造函数。

### 3.3 怎样定义业务异常

1. 方法上要声明@ThriftException
2. 业务异常类要extends AbstractThriftException

\`\`\`java
@ThriftMethod(exception = {@ThriftException(type = TestException.class, id = 1)})
public String method3() throws TestException, TException;
\`\`\`

\`\`\`java
// TestException.java
package com.meituan.service.mobile.mtthrift.annotation.demo;

import com.facebook.swift.codec.ThriftStruct;

//自定义异常
// 无message
@ThriftStruct
public class TestException extends AbstractThriftException {
}

// 有message
@ThriftStruct
public class TestException extends AbstractThriftException {
    private String message;

    @ThriftConstructor
    public TestException(String message) {
        this.message = message;
    }

    /** 重要，必须重写getMessage()，否则异常的message信息为空 */
    @ThriftField(1)
    public String getMessage() {
        return message;
    }

    @ThriftField
    public void setMessage(String message) {
        this.message = message;
    }
}
\`\`\`

### 3.4 怎样定义枚举Enum

2个要点：
1. 类上使用@ThriftEnum
2. 设置一个@ThriftEnumValue的int字段（网络传输的时候，这个值是唯一标识）

备注：@ThriftEnumValue可以不加，但是会埋下隐患：
- 当Enum值的代码定义顺序发生改变时，会导致RPC调用失败。

\`\`\`java
// 推荐方式，使用@ThriftEnumValue
@ThriftEnum
public enum ThriftAnnotatedEnum {
    FIRST_VALUE("fist", 0),
    SECOND_VALUE("second", 1);

    private String description;
    private int intValue;//直接在枚举类定义整数类型的成员变量用于标识，int类型这里不能为负数

    ThriftAnnotatedEnum(String description, int intValue) {
        this.description = description;
        this.intValue = intValue;
    }

    //此函数的返回值不能是负数，负数将导致对端收到的值为null
    @ThriftEnumValue
    public int getIntValue() {
        return intValue;
    }

    //有些业务可能不想定义intValue字段，也可以通过下面方法实现getIntValue()
    //这个用法代码比较简洁:没有intValue字段。但是hashCode()有可能存在冲突
    //@ThriftEnumValue
    //public int getIntValue(){
    //  // & Integer.MAX_VALUE为了保证返回值>=0
    //  return type.hashCode() & Integer.MAX_VALUE;
    //}
}
\`\`\`

## 4. 注意事项

和IDL方式一致，不支持特性部分的说明。

注解方式的其它约束：

1. 属性ID可以顺序追加，不可更改已有属性ID和数据类型
2. 所有接口必须抛出TException
3. 自定义异常必须继承自AbstractThriftException，同时重写toString方法（AbstractThriftException的toString方法不够明确）
4. Thrift注解代码和IDL生成的目标代码不能相互引用
5. 所有的POJO类属性强烈建议使用包装数据类型，如果用基础类型的话无法使用IDL方式里的isSet来判断某个属性是否被赋值过
6. RPC方法的返回值必须使用包装数据类型
7. 如果是包装类型（Integer、Long等）：
   - 参数是包装数据类型：同时又传null的话，服务端收到时会分配对应的基础类型的默认值（参考Defaults#defaultValue）。比如bizline如果传null，服务端将收到0。
   - 对象内部的字段是包装数据类型：客户端传null值，若定义字段时有默认值，服务端会收到该值，否则收到null值
8. 如果枚举类型中有int属性，该属性的值不能为负数
9. 一个类只能有一个带有@ThriftConstructor注解的构造函数

## 5. FAQ

> **注解常见问题排查指南：** [https://km.sankuai.com/page/181724065](https://km.sankuai.com/page/181724065)

### 5.1 注解支持哪些类型

| 类型 | 支持的类型 |
| --- | --- |
| 集合 | List、Set、Map |
| 基本类型 | byte、boolean、short、int、long、float、double、java.nio.ByteBuffer |
| 自定义结构体 | @ThriftStruct 标记的Pojo类 |
| 拓展类型 | 文章开头里程碑2版本才支持 java.util.Date、BigDecimal、BigInteger |

注解支持的类型和IDL方式是一样的。

### 5.2 Thrift注解是否支持object类型

不支持。参数、类的field为Object都不支持。

### 5.3 Thrift注解支持Date/BigDecimal/BigInteger类型吗

高版本(文章开头的里程碑2)中支持。低版本不支持，需要通过Long、String类型存储。

### 5.4 Thrift注解是否支持List<?>等类似的通配符泛型

- ✅ 如果<>中最终序列化的类，可以支持。比如List<String>
- ❌ 如果<>中最终被序列化的类，则不支持。比如List<?>, List<? extends Object>, List<T>。

> 💡为什么不支持List<?>这种泛型定义？
> 
> List<?>这种定义会导致无法反序列化。传输的数据中并没有序列化数据的类型，对端需要通过方法定义本身进行反序列化。如果方法定义的时候类型不明确，那么就无法反序列化。比如，如果参数类型是List<?>，客户端传输了List<Person>之后，服务端从数据本身并无法判断数据要反序列化为Person类型，只能从方法定义中寻找线索。方法定义如果是List<?>，那么服务端就无法知道数据是Person类型。

### 5.5 是否支持ArrayList, HashMap外的其他List/Map

支持。如Collections.singletonList()：java.util.Collections.SingletonList#SingletonList，new ImmutableMap.Builder().build()：com.google.common.collect.RegularImmutableMap

### 5.6 注解定义接口, Java服务和其它语言开发的服务如何对接?

注解是Java实现的，其他语言无法使用，需要使用IDL。

### 5.7 IDL和注解是否能混用？

可能存在不兼容情况，建议统一使用IDL或者注解方式。Java技术栈建议统一到注解方式。

### 5.8 @ThriftStruct注解的类是否支持继承?

1. 支持继承。
2. 但是声明时类型必须明确：
   - 实际参数类型必须是具体类型，不能是父类型
   - 对象实际赋值的field类型必须是具体类型，不能是父类型

### 5.9 @ThriftStruct注解的类是否支持overload

不支持。

### 5.10 接口声明中的方法、参数是否支持多态

不支持多态，参数、返回值等必须使用接口里声明的具体类型。e.g. public Person getPerson() 方法不能返回Student(Person的子类)。

### 5.11 是否支持多个构造函数

不支持。

### 5.12 使用注解后, MTthrift客户端和服务端的启动方式和之前一样吗?

一样。已经对thrift代码类型做了自动判断, MTthrift使用方式没有变化。

### 5.13 @ThriftMethod的value字段有什么用

1. 为方法提供一个别名，已变通地实现重载。java支持overload，但是thrift不支持；当2个方法的名字相同的时候，可以将其中1个方法使用value字段起一个别名。
2. e.g. 如果类中有methodA(int)、mehtodA(String), thrift是不支持的。但是可以给methodA(int)加一个注解@ThriftMethod(value="anotherMethodA"})，thrift内部会使用anotherMethodA(int)和mehtodA(String)。

### 5.14 @ThriftMethod的exception字段有什么用

@ThriftException中有一个id字段用于标记不同的异常，thrift在序列化的时候会用到id字段。

### 5.15 怎样设置某些字段是必填？ThriftField.Requiredness.REQUIRED不生效？

高版本（文章开头的里程碑2）支持。低版本不支持。

\`\`\`java
// 设置requiredness = REQUIRED
@ThriftField(value = 2, requiredness = ThriftField.Requiredness.REQUIRED)
String name;
\`\`\`

### 5.16 兼容性

注解的兼容性大致和IDL对齐。

- thrift传输的是字段@ThriftField的ID，不会传输字段名；因此修改字段名不会影响兼容性，但不能修改字段ID。这里包含结构里的字段，异常的字段，服务方法声明里参数和异常的字段。
- 服务名不会传输，但方法名会传输；因此可以修改服务名，但不能修改方法名，但不建议修改。
- 包名不会传输。

以下情况是兼容的：
- 新增加optional字段（默认是optional）
- 不改字段ID的情况下修改字段名
- 新增加方法
- 给方法增加可选参数、调整参数顺序
- 修改包名
- 修改服务名，不建议这么做

以下情况是不兼容的：
- 修改字段ID，即使不改字段名
- 修改方法名称
- 删除方法参数、修改参数id
- 删除方法
- 新增加、删除带ThriftField.Requiredness.REQUIRED的字段

### 5.17 ThriftField注解属性可以是private的吗？

可以是private的，正常情况下就是private的。

### 5.18 @ThriftStruct可以是内部类吗？

可以。

\`\`\`java
package com.sankuai.lzw.thrift.test.annotation;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import lombok.Data;

@ThriftStruct
public class MwsResult {
    @ThriftField(1)
    Integer code;

    @ThriftField(value = 1)
    public Integer getCode() {
        return code;
    }

    @ThriftField
    public void setCode(Integer code) {
        this.code = code;
    }

    @ThriftStruct
    public static class Version {
        @ThriftField(1)
        Integer version;
    }
}
\`\`\`

### 5.19 Integer等基本类型，设置为null，对端收到的是defaultValue？

高版本（文章开头的里程碑版本1）可以通过配置解决。ThriftServerPublisher#enableAnnotationDefaultVal=false可以关闭。

### 5.20 是否支持循环引用？

高版本（文章的里程碑版本2）支持。低版本不支持。

低版本怎么办？低版本支持不了循环引用，只能改造下类定义破除循环引用。比如原先存在循环引用A->B->A，可以将B->A的引用，改为B引用A.id。

| 改造后 | 改造前 |
| --- | --- |
| \`\`\`java
class Person{
    long id;
    long sonId;
    String name;
}
\`\`\` | \`\`\`java
class Person{
    long id;
    Person son;
    String name;
}
\`\`\` |

## 6. 最佳实践

1. 始终使用显式注解（@ThriftService、@ThriftStruct、@ThriftMethod），即使高版本支持省略
2. 所有POJO类属性使用包装数据类型（Integer而非int）
3. 枚举类型始终使用@ThriftEnumValue注解
4. 自定义异常必须继承AbstractThriftException并重写toString方法
5. 避免IDL和注解混用
6. 升级到注解方式时，确保客户端和服务端同步升级
7. 使用最新版本的MTthrift和swift-codec以获得更好的功能支持

## 7. 参考资料

- [Thrift注解常见问题排查指南](https://km.sankuai.com/page/181724065)
- [Thrift注解开发优化深度剖析](https://km.sankuai.com/collabpage/1594031130)
- [MTthrift服务端发布、客户端发布](https://km.sankuai.com/page/28257152)