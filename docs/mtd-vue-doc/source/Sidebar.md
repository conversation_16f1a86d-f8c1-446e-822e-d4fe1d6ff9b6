## 源码解析

### 文件依赖

- base/menu-item.tsx
- base/submenu.tsx
- demo.md
- demo/sidebar-interactive.vue
- doc/base.vue
- doc/collapse-button-position.vue
- doc/custom.vue
- doc/demo/icon-slot.vue
- doc/demo/item-slot.vue
- doc/demo/submenu-slot.vue
- doc/demo/title-slot.vue
- doc/fold.vue
- doc/search.vue
- doc/theme.vue
- index.md
- index.ts
- props.ts
- sidebar.tsx
- types.ts
- useSideBar.ts

### 源码内容

#### base/menu-item.tsx

```tsx
import {
  defineComponent,
  PropType,
  computed,
  getScopedSlotsInRender,
  vSlots,
  useResetAttrs,
} from '@ss/mtd-adapter'
import { SidebarData } from '../types'
import MtdMenuItem from '@components/menu-item'
import MtdIcon from '@components/icon'
export default defineComponent({
  name: 'SidebarMenuItem',
  components: {
    MtdMenuItem,
    MtdIcon,
  },
  props: {
    /**
     * @param {Object} item
     * @param {Router} route 路由跳转信息，详细请查看 route-link to 属性
     * @param {string} href 同 a 标签 href 属性
     */
    item: {
      type: Object as PropType<SidebarData>,
      required: true,
    },
    itemKey: {
      type: String,
      required: true,
    },
    tooltipProps: {
      type: Object,
    },
  },
  emits: [],
  setup(props) {
    const menuItemAttrs = computed(() => {
      const item = props.item
      const itemKey = props.itemKey

      const attrs = useResetAttrs({
        name: item[itemKey],
        href: item.href,
        route: item.route,
        replace: item.replace,
        target: item.target,
        rel: item.rel,
        disabled: item.disabled,
        'active-class': item['active-class'],
        'exact-active-class': item['exact-active-class'],
        exact: item.exact,
      })
      return attrs.value
    })

    const computedCollection = {
      menuItemAttrs,
    }


    return {
      ...computedCollection,
    }

  },
  render() {

    const {
      menuItemAttrs, tooltipProps, item,
    } = this

    const slots = {
      icon: (getScopedSlotsInRender(this, 'icon') || item.icon)
        ? (() => getScopedSlotsInRender(this, 'icon')?.({ item }) || <MtdIcon name={item.icon} />)
        : undefined, 
      activeIcon: (getScopedSlotsInRender(this, 'activeIcon') || item.activeIcon) 
        ? (() =>  getScopedSlotsInRender(this, 'activeIcon')?.({ item }) || <MtdIcon name={item.activeIcon} />)
        : undefined,
      default: () => (getScopedSlotsInRender(this, 'default')?.({ item }) || item.title),
    }

    return <mtd-menu-item
      {...menuItemAttrs}
      tooltip-props={tooltipProps}
      tooltip={item.tooltip}
      {...vSlots(slots)}
      v-slots={slots}
    >
    </mtd-menu-item>
  },
})

```

#### base/submenu.tsx

```tsx
import {
  defineComponent,
  getScopedSlotsInRender,
  getAllScopedSlots,
  vSlots,
  vueInstance,
} from '@ss/mtd-adapter'
import MenuItem from './menu-item'
import MtdSubmenu from '@components/submenu'
import MtdIcon from '@components/icon'

export default defineComponent({
  name: 'SidebarSubmenu',
  components: {
    MenuItem,
    MtdSubmenu,
    MtdIcon,
  },
  inheritAttrs: true,
  props: {
    item: {
      type: Object,
      required: true,
    },
    itemKey: {
      type: String,
      required: true,
    },
    tooltipProps: {
      type: Object,
    },
  },

  render() {
    const {
      tooltipProps, item, itemKey,
    } = this

    const slots = {
      icon: () => (getScopedSlotsInRender(this, 'icon')?.({ item }) || (item.icon ? <MtdIcon name={item.icon} /> : undefined)),
      title: () => getScopedSlotsInRender(this, 'title')?.({ item }) || item.title,
      activeIcon: () => (getScopedSlotsInRender(this, 'activeIcon')?.({ item }) || (item.activeIcon ? <MtdIcon name={item.activeIcon} /> : undefined)),
    }

    const childSlots = {
      title: getScopedSlotsInRender(this, 'title'),
      item: getScopedSlotsInRender(this, 'item'),
      default: getScopedSlotsInRender(this, 'item'),
      icon: getScopedSlotsInRender(this, 'itemIcon'),
      activeIcon: getScopedSlotsInRender(this, 'itemActiveIcon'),
    }

    return <mtd-submenu
      /* v-bind="$attrs" */
      name={item[itemKey]}
      tooltip={item.tooltip}
      tooltip-props={tooltipProps}
      {...vSlots(slots)}
      v-slots={slots}
    >
      {
        // 遍历item.children数组，为每个子元素创建一个组件
        item.children.map((child: Record<string, any>) => {
          let Component: any = MenuItem

          // 如果有children属性，继续当作SideBarSubmenu处理
          if (child.children) {
            Component = vueInstance()?.$options
            return <Component
              item={child}
              key={child[itemKey]}
              item-key={itemKey}
              tooltip-props={tooltipProps}
              {...vSlots(getAllScopedSlots(this))}
              v-slots={getAllScopedSlots(this)}
            >
            </Component>
          }

          // 返回创建的组件
          return <Component
            item={child}
            key={child[itemKey]}
            item-key={itemKey}
            tooltip-props={tooltipProps}
            {...vSlots(childSlots)}
            v-slots={childSlots}
          >
          </Component>
        })
      }
    </mtd-submenu>
  },
})

```

#### demo.md

```markdown
# 补充demo

## item-slot
:::include(src="./doc/demo/item-slot.vue")
:::

## submenu-slot
:::include(src="./doc/demo/submenu-slot.vue")
:::

## title-slot
:::include(src="./doc/demo/title-slot.vue")
:::

## icon-slot
:::include(src="./doc/demo/icon-slot.vue")
:::
```

#### demo/sidebar-interactive.vue

```vue

<template>
  <div>
    <mtd-doc-usage name="sidebar" :apiJson="apiJson" :usageJson="usageJson" @propChange="propChange">
      <template #sidebar="{ configProps }">
        <div class="slot-container">
          <mtd-sidebar
            :data="data"
            :titleImage="titleImage"
            v-bind="configProps"
            @collapse-change="collapseChange"
            v-model="activeKey"
          ></mtd-sidebar>
        </div>
      </template>
    </mtd-doc-usage>
  </div>
</template>
<script>
import apiJson from '../api.json'


export default {
  data() {
    return {
      apiJson: apiJson,
      collapse: false,
      data: [{
        icon: 'home-o',
        title: '一级菜单',
        tooltip: '一级菜单',
        id: '1',
        children: [{
          title: '二级菜单',
          id: '1-1',
        }, {
          title: '二级菜单',
          id: '1-2',
          route: '/components/sidebar/1-2',
        }, {
          title: '二级菜单',
          id: '1-3',
          route: '/components/sidebar/1-3',
        }],
      }, {
        icon: 'cart-o',
        title: '一级菜单',
        id: '2',
        children: [{
          title: '二级菜单',
          id: '2-1',
          route: '/components/sidebar/2-1',
        }, {
          title: '二级菜单',
          id: '2-2',
          route: '/components/sidebar/2-2',
        }, {
          title: '二级菜单',
          id: '2-3',
          route: '/components/sidebar/2-3',
        }],
      }, {
        icon: 'time-o',
        title: '一级菜单',
        id: '3',
        route: '/components/sidebar/3',
      }, {
        icon: 'calendar-o',
        title: '一级菜单',
        id: '4',
        route: '/components/sidebar/4',
      }],
      usageJson: {
        componentName: "sidebar",
        desc: "侧导航",
        codeTemplate: "<mtd-sidebar v-bind='configProps'></mtd-sidebar>"
      },
      titleImage: require('../logo-expand.png'),
      theme: 'light',
      activeKey: '1-1',
      expandType: 'partial'
    }
  },
  watch: {
    collapse() {
      this.setTitleImage()
    },
    theme() {
      this.setTitleImage()
    },
    expandType() {
      this.setTitleImage()
    }
  },
  methods: {
    propChange(propertyName, val) {
      this[propertyName] = val
      console.log('prop changed: , propertyName: ' + propertyName + ', val: ' + val)
    },
    collapseChange(val) {
      this.collapse = val
    },
    setTitleImage() {
      switch (this.expandType) {
        case 'none': {
          this.titleImage = require('../logo.png')
          break
        }
        case 'fully':
        case 'partial': {
          if (this.collapse) {
            this.titleImage = require('../logo.png')
          } else {
            if (this.theme === 'light') {
              this.titleImage = require('../logo-expand.png')
            } else {
              this.titleImage = require('../logo-expand-dark.png')
            }
          }
        }
      }
    }
  }
}
</script>
<style lang="scss" scoped>
  .slot-container {
    height: 300px;
  }
</style>
  
```

#### doc/base.vue

```vue
<template>
  <div class="demo-sidebar-container demo-full-width">
    <div class="demo-sidebar-aside">
      <mtd-sidebar
        :data="data"
        :model-value="activeKey"
        title="导航标题"
        @menu-item-click="handleItemClick"
        @menu-expand-change="handleExpandChange" />
    </div>
    <div class="demo-sidebar-main">
      <router-view />
    </div>
  </div>
</template>

<script>
export default {
  data () {
    return {
      'data': [{
        icon: 'home-o',
        title: '一级菜单',
        tooltip: '一级菜单',
        id: '1',
        children: [{
          title: '二级菜单',
          id: '1-1',
          route: '/components/sidebar/1-1',
        }, {
          title: '二级菜单',
          id: '1-2',
          route: '/components/sidebar/1-2',
        }, {
          title: '二级菜单',
          id: '1-3',
          route: '/components/sidebar/1-3',
        }],
      }, {
        icon: 'cart-o',
        title: '一级菜单',
        id: '2',
        children: [{
          title: '二级菜单',
          id: '2-1',
          route: '/components/sidebar/2-1',
        }, {
          title: '二级菜单',
          id: '2-2',
          route: '/components/sidebar/2-2',
        }, {
          title: '二级菜单',
          id: '2-3',
          route: '/components/sidebar/2-3',
        }],
      }, {
        icon: 'time-o',
        title: '一级菜单',
        id: '3',
        route: '/components/sidebar/3',
      }, {
        icon: 'calendar-o',
        title: '一级菜单',
        id: '4',
        route: '/components/sidebar/4',
      }],
    }
  },
  computed: {
    activeKey () {
      return this.$route.params.name
    },
  },
  methods: {
    handleItemClick ({key, item}) {
      console.log('click', key, item)
    },
    handleExpandChange ({expandKeys, key, item, expanded}) {
      console.log('expand', expandKeys, key, item, expanded)
    },
  },
}
</script>
<style lang='scss' scoped>
  .demo-sidebar-container {
    display: flex;
    height: 600px;
  }

  .demo-sidebar-aside {
    flex: 0 0 auto;
    position: relative;
    z-index: 1;
  }

  .demo-sidebar-main {
    min-width: 0;
    flex: 1 1 auto;
    background-color: #f7f8fc;
    padding: 20px;
  }
</style>

```

#### doc/collapse-button-position.vue

```vue
<template>
  <div class="demo-wrapper">
    <mtd-tabs v-model="activeTab" size="small" type="text" class="demo-tabs-content">
      <mtd-tab-pane label="底部按钮" value="bottom">
        <div class="demo-sidebar-container demo-full-width">
          <div class="demo-sidebar-aside">
            <mtd-sidebar 
              :data="data"
              :titleImage="titleImage"
              v-model="activeKey"
              collapse-button-position="bottom"
              expandType="partial"
              @collapse-change="handleCollapseChange"
              @menu-expand-change="handleExpandChange"
              @menu-item-click="handleItemClick">
            </mtd-sidebar>
          </div>
        </div>
        <div class="demo-sidebar-main">
          <router-view />
        </div>
      </mtd-tab-pane>
      <mtd-tab-pane label="顶部按钮" value="top">
        <div class="demo-sidebar-container demo-full-width">
          <div class="demo-sidebar-aside">
            <mtd-sidebar 
              :data="data"
              :titleImage="titleImage0"
              v-model="activeKey"
              collapse-button-position="top"
              expandType="partial"
              @collapse-change="handleCollapseChange0"
              @menu-expand-change="handleExpandChange"
              @menu-item-click="handleItemClick">
            </mtd-sidebar>
          </div>
        </div>
        <div class="demo-sidebar-main">
          <router-view />
        </div>
      </mtd-tab-pane>
    </mtd-tabs>
  </div>
</template>

<script>
export default {
  data () {
    return {
      data: [{
        icon: 'mtdicon mtdicon-home-o',
        title: '一级菜单 1',
        id: '1',
        children: [{
          title: '二级菜单 1-1',
          id: '1-1',
          route: '/components/sidebar/1-1',
        }, {
          title: '二级菜单 1-2',
          id: '1-2',
          route: '/components/sidebar/1-2',
        }, {
          title: '二级菜单 1-3',
          id: '1-3',
          route: '/components/sidebar/1-3',
        }],
      }, {
        icon: 'mtdicon mtdicon-cart-o',
        title: '一级菜单 2',
        id: '2',
        children: [{
          title: '二级菜单 2-1',
          id: '2-1',
          route: '/components/sidebar/2-1',
        }, {
          title: '二级菜单 2-2',
          id: '2-2',
          route: '/components/sidebar/2-2',
        }, {
          title: '二级菜单 2-3',
          id: '2-3',
          route: '/components/sidebar/2-3',
        }],
      }, {
        icon: 'mtdicon mtdicon-time-o',
        title: '一级菜单 3',
        id: '3',
        route: '/components/sidebar/3',
      }, {
        icon: 'mtdicon mtdicon-calendar-o',
        title: '一级菜单 4',
        id: '4',
        route: '/components/sidebar/4',
      }],
      val: '',
      activeTab: 'bottom', 
      loading: false,
      collapse: false,
      collapse0: false,
      activeKey: '1-1',
      titleImage0: require('../logo-expand.png'),
      titleImage: require('../logo-expand.png'),
    }
  },
  watch: {
    '$route': {
      handler() {
        this.activeKey = this.$route.params.name
      },
      immediate: true
    },
    collapse() {
      if (this.collapse) {
        this.titleImage = require('../logo.png')
      } else {
        this.titleImage = require('../logo-expand.png')
      }
    },
    collapse0() {
      if (this.collapse0) {
        this.titleImage0 = require('../logo.png')
      } else {
        this.titleImage0 = require('../logo-expand.png')
      }
    }
  },
  methods: {
    handleItemClick ({key, item}) {
      console.log('click', key, item)
    },
    handleCollapseChange(val) {
      this.collapse = val
      console.log('CollapseChange', val)
    },
    handleCollapseChange0(val) {
      this.collapse0 = val
      console.log('CollapseChange0', val)
    },
    handleExpandChange(val) {
      console.log('handleExpandChange', val)
    }
  },
}
</script>
<style lang='scss' scoped>
  .demo-sidebar-container {
    display: flex;
    height: 600px;
  }

  .demo-sidebar-aside {
    flex: 0 0 auto;
    position: relative;
    z-index: 1;
  }

  .demo-sidebar-main {
      min-width: 0;
      flex: 1 1 auto;
      background-color: #f7f8fc;
      padding: 20px;
    }

  .demo-wrapper {
    width: 100%;

    ::v-deep .mtd-tab-pane {
      padding: 20px 0;
      display: flex;
      justify-content: space-around;

      .title {
        margin-bottom: 16px;
      }
    }

    ::v-deep .mtd-tabs-nav {
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      border-bottom: 1px solid #edf0f7;
      padding: 1.5px 16px;
    }

    .close {
      position: fixed;
      top: 24px;
      right: 24px;
      width: 32px;
      height: 32px;
      border-radius: 50%;
      background: rgba(0, 0, 0, 0.5);
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 16px;
      color: #fff;
      cursor: pointer;
      z-index: 99999;

      &:hover {
        background-color: rgba(17, 25, 37, 0.3);
      }
    }
  }
</style>

```

#### doc/custom.vue

```vue
<template>
  <div class="demo-sidebar-container demo-full-width">
    <div class="demo-sidebar-aside">
      <mtd-sidebar 
        :data="data"
        :model-value="activeKey"
        title="导航标题"
        @menu-item-click="handleItemClick"
        @menu-expand-change="handleExpandChange">
        <template #submenu="scope">
          <div>
            {{ scope &&scope.item.title }}（{{ scope && scope.item.children.length }} 项）
          </div>
        </template>
        <template #item="scope">
          <div>
            自定义 {{ scope.item.title }}
          </div>
        </template>
      </mtd-sidebar>
    </div>
    <div class="demo-sidebar-main">
      <router-view />
    </div>
  </div>
</template>

<script>
export default {
  data () {
    return {
      'data': [{
        icon: 'mtdicon mtdicon-home-o',
        title: '一级菜单',
        id: '1',
        children: [{
          title: '二级菜单',
          id: '1-1',
          route: '/components/sidebar/1-1',
        }, {
          title: '二级菜单',
          id: '1-2',
          route: '/components/sidebar/1-2',
        }, {
          title: '二级菜单',
          id: '1-3',
          route: '/components/sidebar/1-3',
        }],
      }, {
        icon: 'mtdicon mtdicon-cart-o',
        title: '一级菜单',
        id: '2',
        children: [{
          title: '二级菜单',
          id: '2-1',
          route: '/components/sidebar/2-1',
        }, {
          title: '二级菜单',
          id: '2-2',
          route: '/components/sidebar/2-2',
        }, {
          title: '二级菜单',
          id: '2-3',
          route: '/components/sidebar/2-3',
        }],
      }, {
        icon: 'mtdicon mtdicon-time-o',
        title: '一级菜单',
        id: '3',
        route: '/components/sidebar/3',
      }, {
        icon: 'mtdicon mtdicon-calendar-o',
        title: '一级菜单',
        id: '4',
        route: '/components/sidebar/4',
      }],
    }
  },
  computed: {
    activeKey () {
      return this.$route.params.name
    },
  },
  methods: {
    handleItemClick ({key, item}) {
      console.log('click', key, item)
    },
    handleExpandChange ({expandKeys, key, item, expanded}) {
      console.log('expand', expandKeys, key, item, expanded)
    },
  },
}
</script>
<style lang='scss' scoped>
  .demo-sidebar-container {
    display: flex;
    height: 600px;
  }

  .demo-sidebar-aside {
    flex: 0 0 auto;
    position: relative;
    z-index: 1;
  }

  .demo-sidebar-main {
    min-width: 0;
    flex: 1 1 auto;
    background-color: #f7f8fc;
    padding: 20px;
  }
</style>

```

#### doc/demo/icon-slot.vue

```vue
<template>
  <div class="demo-sidebar-container demo-full-width">
    <div class="demo-sidebar-aside">
      <mtd-sidebar
        :data="data"
        :model-value="activeKey"
        title="导航标题"
        @menu-item-click="handleItemClick"
        @menu-expand-change="handleExpandChange">
        <template #submenu-icon="{ item }">
          <i class="mtdicon mtdicon-customer-o"></i>
        </template>
        <template #item-icon="{ item }">
          <i v-if="item.id === '1-1'" class="mtdicon mtdicon-sad"></i>
          <i v-else class="mtdicon mtdicon-bell-o"></i>
        </template>
        <template #submenu-active-icon="{ item }">
          <i class="mtdicon mtdicon-calculator-o"></i>
        </template>
        <template #item-active-icon="{ item }">
          <i class="mtdicon mtdicon-cart-o"></i>
        </template>
      </mtd-sidebar>
    </div>
    <div class="demo-sidebar-main">
      <router-view />
    </div>
  </div>
</template>

<script>
export default {
  data () {
    return {
      'data': [{
        icon: 'home-o',
        title: '一级菜单',
        tooltip: '一级菜单',
        id: '1',
        children: [{
          title: '二级菜单',
          id: '1-1',
        }, {
          title: '二级菜单',
          id: '1-2',
          children: [{
            title: '三级菜单',
            id: '1-2-1',
          }]
        }, {
          title: '二级菜单',
          id: '1-3',
        }],
      }, {
        icon: 'cart-o',
        title: '一级菜单',
        id: '2',
        children: [{
          title: '二级菜单',
          id: '2-1',
        }, {
          title: '二级菜单',
          id: '2-2',
        }, {
          title: '二级菜单',
          id: '2-3',
        }],
      }, {
        icon: 'time-o',
        title: '一级菜单',
        id: '3',
      }, {
        icon: 'calendar-o',
        title: '一级菜单',
        id: '4',
      }],
    }
  },
  computed: {
    activeKey () {
      return this.$route.params.name
    },
  },
  methods: {
    handleItemClick ({key, item}) {
      console.log('click', key, item)
    },
    handleExpandChange ({expandKeys, key, item, expanded}) {
      console.log('expand', expandKeys, key, item, expanded)
    },
  },
}
</script>
<style lang='scss' scoped>
  .demo-sidebar-container {
    display: flex;
    height: 600px;
  }

  .demo-sidebar-aside {
    flex: 0 0 auto;
    position: relative;
    z-index: 1;
  }

  .demo-sidebar-main {
    min-width: 0;
    flex: 1 1 auto;
    background-color: #f7f8fc;
    padding: 20px;
  }
</style>

```

#### doc/demo/item-slot.vue

```vue
<template>
  <div class="demo-sidebar-container demo-full-width">
    <div class="demo-sidebar-aside">
      <mtd-sidebar
        :data="data"
        :model-value="activeKey"
        title="导航标题"
        @menu-item-click="handleItemClick"
        @menu-expand-change="handleExpandChange">
        <template #item="{ item }">
          <div>自定义item - {{ item.title }}</div>
        </template>
      </mtd-sidebar>
    </div>
    <div class="demo-sidebar-main">
      <router-view />
    </div>
  </div>
</template>

<script>
export default {
  data () {
    return {
      'data': [{
        icon: 'home-o',
        title: '一级菜单',
        tooltip: '一级菜单',
        id: '1',
        children: [{
          title: '二级菜单',
          id: '1-1',
          route: '/components/sidebar/1-1',
        }, {
          title: '二级菜单',
          id: '1-2',
          route: '/components/sidebar/1-2',
        }, {
          title: '二级菜单',
          id: '1-3',
          route: '/components/sidebar/1-3',
        }],
      }, {
        icon: 'cart-o',
        title: '一级菜单',
        id: '2',
        children: [{
          title: '二级菜单',
          id: '2-1',
          route: '/components/sidebar/2-1',
        }, {
          title: '二级菜单',
          id: '2-2',
          route: '/components/sidebar/2-2',
        }, {
          title: '二级菜单',
          id: '2-3',
          route: '/components/sidebar/2-3',
        }],
      }, {
        icon: 'time-o',
        title: '一级菜单',
        id: '3',
        route: '/components/sidebar/3',
      }, {
        icon: 'calendar-o',
        title: '一级菜单',
        id: '4',
        route: '/components/sidebar/4',
      }],
    }
  },
  computed: {
    activeKey () {
      return this.$route.params.name
    },
  },
  methods: {
    handleItemClick ({key, item}) {
      console.log('click', key, item)
    },
    handleExpandChange ({expandKeys, key, item, expanded}) {
      console.log('expand', expandKeys, key, item, expanded)
    },
  },
}
</script>
<style lang='scss' scoped>
  .demo-sidebar-container {
    display: flex;
    height: 600px;
  }

  .demo-sidebar-aside {
    flex: 0 0 auto;
    position: relative;
    z-index: 1;
  }

  .demo-sidebar-main {
    min-width: 0;
    flex: 1 1 auto;
    background-color: #f7f8fc;
    padding: 20px;
  }
</style>

```

#### doc/demo/submenu-slot.vue

```vue
<template>
  <div class="demo-sidebar-container demo-full-width">
    <div class="demo-sidebar-aside">
      <mtd-sidebar
        :data="data"
        :model-value="activeKey"
        title="导航标题"
        @menu-item-click="handleItemClick"
        @menu-expand-change="handleExpandChange">
        <template #submenu="{ item }">
          <div>submenu - {{ item.title }}</div>
        </template>
      </mtd-sidebar>
    </div>
    <div class="demo-sidebar-main">
      <router-view />
    </div>
  </div>
</template>

<script>
export default {
  data () {
    return {
      'data': [{
        icon: 'home-o',
        title: '一级菜单',
        tooltip: '一级菜单',
        id: '1',
        children: [{
          title: '二级菜单',
          id: '1-1',
          route: '/components/sidebar/1-1',
          children: [{
            icon: 'home-o',
            title: '三级菜单',
            id: '1-1-1',
          }]
        }, {
          title: '二级菜单',
          id: '1-2',
          route: '/components/sidebar/1-2',
        }, {
          title: '二级菜单',
          id: '1-3',
          route: '/components/sidebar/1-3',
        }],
      }, {
        icon: 'cart-o',
        title: '一级菜单',
        id: '2',
        children: [{
          title: '二级菜单',
          id: '2-1',
          route: '/components/sidebar/2-1',
        }, {
          title: '二级菜单',
          id: '2-2',
          route: '/components/sidebar/2-2',
        }, {
          title: '二级菜单',
          id: '2-3',
          route: '/components/sidebar/2-3',
        }],
      }, {
        icon: 'time-o',
        title: '一级菜单',
        id: '3',
        route: '/components/sidebar/3',
      }, {
        icon: 'calendar-o',
        title: '一级菜单',
        id: '4',
        route: '/components/sidebar/4',
      }],
    }
  },
  computed: {
    activeKey () {
      return this.$route.params.name
    },
  },
  methods: {
    handleItemClick ({key, item}) {
      console.log('click', key, item)
    },
    handleExpandChange ({expandKeys, key, item, expanded}) {
      console.log('expand', expandKeys, key, item, expanded)
    },
  },
}
</script>
<style lang='scss' scoped>
  .demo-sidebar-container {
    display: flex;
    height: 600px;
  }

  .demo-sidebar-aside {
    flex: 0 0 auto;
    position: relative;
    z-index: 1;
  }

  .demo-sidebar-main {
    min-width: 0;
    flex: 1 1 auto;
    background-color: #f7f8fc;
    padding: 20px;
  }
</style>

```

#### doc/demo/title-slot.vue

```vue
<template>
  <div class="demo-sidebar-container demo-full-width">
    <div class="demo-sidebar-aside">
      <mtd-sidebar
        :data="data"
        :model-value="activeKey"
        title="导航标题"
        @menu-item-click="handleItemClick"
        @menu-expand-change="handleExpandChange">
        <template #title>
          <div>自定义title插槽</div>
        </template>
      </mtd-sidebar>
    </div>
    <div class="demo-sidebar-main">
      <router-view />
    </div>
  </div>
</template>

<script>
export default {
  data () {
    return {
      'data': [{
        icon: 'home-o',
        title: '一级菜单',
        tooltip: '一级菜单',
        id: '1',
        children: [{
          title: '二级菜单',
          id: '1-1',
          route: '/components/sidebar/1-1',
        }, {
          title: '二级菜单',
          id: '1-2',
          route: '/components/sidebar/1-2',
        }, {
          title: '二级菜单',
          id: '1-3',
          route: '/components/sidebar/1-3',
        }],
      }, {
        icon: 'cart-o',
        title: '一级菜单',
        id: '2',
        children: [{
          title: '二级菜单',
          id: '2-1',
          route: '/components/sidebar/2-1',
        }, {
          title: '二级菜单',
          id: '2-2',
          route: '/components/sidebar/2-2',
        }, {
          title: '二级菜单',
          id: '2-3',
          route: '/components/sidebar/2-3',
        }],
      }, {
        icon: 'time-o',
        title: '一级菜单',
        id: '3',
        route: '/components/sidebar/3',
      }, {
        icon: 'calendar-o',
        title: '一级菜单',
        id: '4',
        route: '/components/sidebar/4',
      }],
    }
  },
  computed: {
    activeKey () {
      return this.$route.params.name
    },
  },
  methods: {
    handleItemClick ({key, item}) {
      console.log('click', key, item)
    },
    handleExpandChange ({expandKeys, key, item, expanded}) {
      console.log('expand', expandKeys, key, item, expanded)
    },
  },
}
</script>
<style lang='scss' scoped>
  .demo-sidebar-container {
    display: flex;
    height: 600px;
  }

  .demo-sidebar-aside {
    flex: 0 0 auto;
    position: relative;
    z-index: 1;
  }

  .demo-sidebar-main {
    min-width: 0;
    flex: 1 1 auto;
    background-color: #f7f8fc;
    padding: 20px;
  }
</style>

```

#### doc/fold.vue

```vue
<template>
  <div class="demo-wrapper">
    <mtd-tabs v-model="activeTab" size="small" type="text" class="demo-tabs-content">
      <mtd-tab-pane label="全展开收起" value="fully">
        <div class="demo-sidebar-container demo-full-width">
          <div class="demo-sidebar-aside">
            <mtd-sidebar 
              :data="data"
              :titleImage="titleImage0"
              v-model="activeKey"
              expandType="fully"
              @collapse-change="handleCollapseChange"
              @menu-expand-change="handleExpandChange"
              @menu-item-click="handleItemClick">
            </mtd-sidebar>
          </div>
        </div>
        <div class="demo-sidebar-main">
          <router-view />
        </div>
      </mtd-tab-pane>
      <mtd-tab-pane label="半展开收起" value="partial">
        <div class="demo-sidebar-container demo-full-width">
          <div class="demo-sidebar-aside">
            <mtd-sidebar 
              :data="data"
              :titleImage="titleImage"
              v-model="activeKey"
              expandType="partial"
              @collapse-change="handleCollapseChange"
              @menu-expand-change="handleExpandChange"
              @menu-item-click="handleItemClick">
            </mtd-sidebar>
          </div>
        </div>
        <div class="demo-sidebar-main">
          <router-view />
        </div>
      </mtd-tab-pane>
      <mtd-tab-pane label="不展开收起" value="none">
        <div class="demo-sidebar-container demo-full-width">
          <div class="demo-sidebar-aside">
            <mtd-sidebar 
              :data="data2"
              :titleImage="titleImage2"
              v-model="activeKey"
              expandType="none"
              @collapse-change="handleCollapseChange"
              @menu-expand-change="handleExpandChange"
              @menu-item-click="handleItemClick">
            </mtd-sidebar>
          </div>
        </div>
        <div class="demo-sidebar-main">
          <router-view />
        </div>
      </mtd-tab-pane>
    </mtd-tabs>
  </div>
</template>

<script>
export default {
  data () {
    return {
      data: [{
        icon: 'mtdicon mtdicon-home-o',
        title: '一级菜单 1',
        id: '1',
        children: [{
          title: '二级菜单 1-1',
          id: '1-1',
          route: '/components/sidebar/1-1',
        }, {
          title: '二级菜单 1-2',
          id: '1-2',
          route: '/components/sidebar/1-2',
        }, {
          title: '二级菜单 1-3',
          id: '1-3',
          route: '/components/sidebar/1-3',
        }],
      }, {
        icon: 'mtdicon mtdicon-cart-o',
        title: '一级菜单 2',
        id: '2',
        children: [{
          title: '二级菜单 2-1',
          id: '2-1',
          route: '/components/sidebar/2-1',
        }, {
          title: '二级菜单 2-2',
          id: '2-2',
          route: '/components/sidebar/2-2',
        }, {
          title: '二级菜单 2-3',
          id: '2-3',
          route: '/components/sidebar/2-3',
        }],
      }, {
        icon: 'mtdicon mtdicon-time-o',
        title: '一级菜单 3',
        id: '3',
        route: '/components/sidebar/3',
      }, {
        icon: 'mtdicon mtdicon-calendar-o',
        title: '一级菜单 4',
        id: '4',
        route: '/components/sidebar/4',
      }],
      data2: [{
        icon: 'mtdicon mtdicon-home-o',
        title: '菜单1',
        id: '1',
        children: [{
          title: '二级菜单 1-1',
          id: '1-1',
          route: '/components/sidebar/1-1',
        }, {
          title: '二级菜单 1-2',
          id: '1-2',
          route: '/components/sidebar/1-2',
        }, {
          title: '二级菜单 1-3',
          id: '1-3',
          route: '/components/sidebar/1-3',
        }],
      }, {
        icon: 'mtdicon mtdicon-cart-o',
        title: '菜单2',
        id: '2',
        children: [{
          title: '二级菜单 2-1',
          id: '2-1',
          route: '/components/sidebar/2-1',
        }, {
          title: '二级菜单 2-2',
          id: '2-2',
          route: '/components/sidebar/2-2',
        }, {
          title: '二级菜单 2-3',
          id: '2-3',
          route: '/components/sidebar/2-3',
        }],
      }, {
        icon: 'mtdicon mtdicon-time-o',
        title: '菜单3',
        id: '3',
        route: '/components/sidebar/3',
      }, {
        icon: 'mtdicon mtdicon-calendar-o',
        title: '菜单4',
        id: '4',
        route: '/components/sidebar/4',
      }],
      val: '',
      activeTab: 'fully', //
      loading: false,
      collapse: false,
      activeKey: '1-1',
      titleImage0: require('../logo-expand.png'),
      titleImage: require('../logo-expand.png'),
      titleImage2: require('../logo.png')
    }
  },
  watch: {
    '$route': {
      handler() {
        this.activeKey = this.$route.params.name
      },
      immediate: true
    },
    collapse() {
      if (this.collapse) {
        this.titleImage = require('../logo.png')
      } else {
        this.titleImage = require('../logo-expand.png')
      }
    }
  },
  methods: {
    handleItemClick ({key, item}) {
      console.log('click', key, item)
    },
    handleCollapseChange(val) {
      this.collapse = val
      console.log('CollapseChange', val)
    },
    handleExpandChange(val) {
      console.log('handleExpandChange', val)
    }
  },
}
</script>
<style lang='scss' scoped>
  .demo-sidebar-container {
    display: flex;
    height: 600px;
  }

  .demo-sidebar-aside {
    flex: 0 0 auto;
    position: relative;
    z-index: 1;
  }

  .demo-sidebar-main {
      min-width: 0;
      flex: 1 1 auto;
      background-color: #f7f8fc;
      padding: 20px;
    }

  .demo-wrapper {
    width: 100%;

    ::v-deep .mtd-tab-pane {
      padding: 20px 0;
      display: flex;
      justify-content: space-around;

      .title {
        margin-bottom: 16px;
      }
    }

    ::v-deep .mtd-tabs-nav {
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      border-bottom: 1px solid #edf0f7;
      padding: 1.5px 16px;
    }

    .close {
      position: fixed;
      top: 24px;
      right: 24px;
      width: 32px;
      height: 32px;
      border-radius: 50%;
      background: rgba(0, 0, 0, 0.5);
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 16px;
      color: #fff;
      cursor: pointer;
      z-index: 99999;

      &:hover {
        background-color: rgba(17, 25, 37, 0.3);
      }
    }
  }
</style>

```

#### doc/search.vue

```vue
<template>
  <div class="demo-sidebar-container demo-full-width">
    <div class="demo-sidebar-aside">
      <mtd-sidebar 
        :data="data"
        :expand-keys="expandKeys"
        :titleImage="titleImage"
        default-active-key="1-1"
        v-model="activeKey"
        @collapse-change="handleCollapseChange"
        @menu-item-click="handleItemClick"
        expandType="fully"
        @menu-expand-change="handleExpandChange">
        <template #search>
          <div :style="collapse ? 'display: none;' : 'padding: 0 8px;'">
            <mtd-input-search
              style="height: 32px;"
              placeholder="输入关键字"
              v-model="val"
              :loading="loading"
              @search="handleSearch"/>
          </div>
        </template>
      </mtd-sidebar>
    </div>
    <div class="demo-sidebar-main">
      <router-view />
    </div>
  </div>
</template>

<script>
export default {
  data () {
    return {
      data: [{
        icon: 'mtdicon mtdicon-home-o',
        title: '一级菜单 1',
        id: '1',
        children: [{
          title: '二级菜单 1-1',
          id: '1-1',
          route: '/components/sidebar/1-1',
        }, {
          title: '二级菜单 1-2',
          id: '1-2',
          route: '/components/sidebar/1-2',
        }, {
          title: '二级菜单 1-3',
          id: '1-3',
          route: '/components/sidebar/1-3',
        }],
      }, {
        icon: 'mtdicon mtdicon-cart-o',
        title: '一级菜单 2',
        id: '2',
        children: [{
          title: '二级菜单 2-1',
          id: '2-1',
          route: '/components/sidebar/2-1',
        }, {
          title: '二级菜单 2-2',
          id: '2-2',
          route: '/components/sidebar/2-2',
        }, {
          title: '二级菜单 2-3',
          id: '2-3',
          route: '/components/sidebar/2-3',
        }],
      }, {
        icon: 'mtdicon mtdicon-time-o',
        title: '一级菜单 3',
        id: '3',
        route: '/components/sidebar/3',
      }, {
        icon: 'mtdicon mtdicon-calendar-o',
        title: '一级菜单 4',
        id: '4',
        route: '/components/sidebar/4',
      }],
      val: '',
      loading: false,
      expandKeys: ['1', '1-2'],
      activeKey: '1-1',
      collapse: false,
      titleImage: require('../logo-expand.png')
    }
  },
  watch: {
    '$route': {
      handler() {
        this.activeKey = this.$route.params.name
      },
      immediate: true
    },
    collapse() {
      if (this.collapse) {
        this.titleImage = require('../logo.png')
      } else {
        this.titleImage = require('../logo-expand.png')
      }
    }
  },
  methods: {
    handleItemClick ({key, item}) {
      console.log('click', key, item)
    },
    handleExpandChange ({expandKeys, key, item, expanded}) {
      this.expandKeys = [...expandKeys]
    },
    handleSearch(val) {
      this.loading = true

      const expandKeysArr = []
      let activeKey = ''
      function findExpandKeys(data) {
        const ret = data.map(item => {
          if (item.title === val) {
            activeKey = item.id
          }
          if (item.title.includes(val)) {
            expandKeysArr.push(item.id)
            return item
          }
          if (item.children && findExpandKeys(item.children).length > 0) {
            expandKeysArr.push(item.id)
            return item
          }
          return null
        }).filter(v => v !== null)

        return ret
      }
      findExpandKeys(this.data)

      setTimeout(() => {
        console.log('搜索完成 val:', val, expandKeysArr)
        this.loading = false
        this.expandKeys = expandKeysArr;
        if (activeKey) {
          this.activeKey = activeKey
        }
      }, 1000)
    },
    handleCollapseChange(val) {
      this.collapse = val
    } 
  },
}
</script>
<style lang='scss' scoped>
  .demo-sidebar-container {
    display: flex;
    height: 600px;
  }

  .demo-sidebar-aside {
    flex: 0 0 auto;
    position: relative;
    z-index: 1;
  }

  .demo-sidebar-main {
    min-width: 0;
    flex: 1 1 auto;
    background-color: #f7f8fc;
    padding: 20px;
  }
</style>

```

#### doc/theme.vue

```vue
<template>
  <div class="demo-wrapper">
    <mtd-tabs v-model="activeTab" size="small" type="text" class="demo-tabs-content">
      <mtd-tab-pane label="浅色" value="light">
        <div class="demo-sidebar-container demo-full-width">
          <div class="demo-sidebar-aside">
            <mtd-sidebar 
              :data="data"
              v-model="activeKey"
              :item-style="(item) => {
                if (item.level === 1) {
                  return {
                    paddingLeft: '40px'
                  }
                }
                return {}
              }"
              theme="light"
              :titleImage="titleImage"
              @collapse-change="handleCollapseChange"
              @menu-item-click="handleItemClick"
              @menu-expand-change="handleExpandChange" />
          </div>
        </div>
        <div class="demo-sidebar-main">
          <router-view />
        </div>
      </mtd-tab-pane>
      <mtd-tab-pane label="深色" value="dark">
        <div class="demo-sidebar-container demo-full-width">
          <div class="demo-sidebar-aside">
            <mtd-sidebar 
              :data="data"
              v-model="activeKey"
              theme="dark"
              :titleImage="titleImage2"
              @collapse-change="handleCollapseChange2"
              @menu-item-click="handleItemClick"
              @menu-expand-change="handleExpandChange" />
          </div>
        </div>
        <div class="demo-sidebar-main">
          <router-view />
        </div>
      </mtd-tab-pane>
    </mtd-tabs>
  
  </div>
</template>

<script>
export default {
  data () {
    return {
      'data': [{
        icon: 'mtdicon mtdicon-home-o',
        title: '一级菜单',
        id: '1',
        children: [{
          title: '二级菜单',
          id: '1-1',
          route: '/components/sidebar/1-1',
        }, {
          title: '二级菜单',
          id: '1-2',
          route: '/components/sidebar/1-2',
        }, {
          title: '二级菜单',
          id: '1-3',
          route: '/components/sidebar/1-3',
        }],
      }, {
        icon: 'mtdicon mtdicon-cart-o',
        title: '一级菜单',
        id: '2',
        children: [{
          title: '二级菜单',
          id: '2-1',
          route: '/components/sidebar/2-1',
        }, {
          title: '二级菜单',
          id: '2-2',
          route: '/components/sidebar/2-2',
        }, {
          title: '二级菜单',
          id: '2-3',
          route: '/components/sidebar/2-3',
        }],
      }, {
        icon: 'mtdicon mtdicon-time-o',
        title: '一级菜单',
        id: '3',
        route: '/components/sidebar/3',
      }, {
        icon: 'mtdicon mtdicon-cloud-o',
        title: '一级菜单',
        id: '4',
        route: '/components/sidebar/4',
      }],
      activeTab: 'light',
      activeKey: '2-1',
      collapse: false,
      collapse2: false,
      titleImage: require('../logo-expand.png'),
      titleImage2: require('../logo-expand-dark.png'),
    }
  },
  watch: {
    collapse() {
      if (this.collapse) {
          this.titleImage = require('../logo.png')
          this.titleImage2 = require('../logo.png')
      } else {
          this.titleImage = require('../logo-expand.png')
          this.titleImage2 = require('../logo-expand-dark.png')
      }
    },
    collapse2() {
      if (this.collapse2) {
          this.titleImage2 = require('../logo.png')
      } else {
          this.titleImage2 = require('../logo-expand-dark.png')
      }
    },
    activeTab(newVal) {
      // debugger
      console.log(this.activeTab, newVal)
      if (newVal === 'light') {
        if (this.collapse) {
          this.titleImage = require('../logo.png')
        } else {
          this.titleImage = require('../logo-expand.png')
        }
      } else {
        if (this.collapse2) {
          this.titleImage2 = require('../logo.png')
        } else {
          this.titleImage2 = require('../logo-expand-dark.png')
        }
      }
    }
  },
  computed: {
    activeKey () {
      return this.$route.params.name
    },
  },
  methods: {
    handleItemClick ({key, item}) {
      console.log('click', key, item)
    },
    handleExpandChange ({expandKeys, key, item, expanded}) {
      console.log('expand', expandKeys, key, item, expanded)
    },
    handleCollapseChange(val) {
      this.collapse = val
    },
    handleCollapseChange2(val) {
      this.collapse2 = val
    } 
  },
}
</script>
<style lang='scss' scoped>
  .demo-sidebar-container {
    display: flex;
    height: 600px;
  }

  .demo-sidebar-aside {
    flex: 0 0 auto;
    position: relative;
    z-index: 1;
  }

  .demo-sidebar-main {
    min-width: 0;
    flex: 1 1 auto;
    background-color: #f7f8fc;
    padding: 20px;
  }
.demo-wrapper {
  width: 100%;

  ::v-deep .mtd-tab-pane {
    padding: 20px 0;
    display: flex;
    justify-content: space-around;

    .title {
      margin-bottom: 16px;
    }
  }

  ::v-deep .mtd-tabs-nav {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    border-bottom: 1px solid #edf0f7;
    padding: 1.5px 16px;
  }

  .close {
    position: fixed;
    top: 24px;
    right: 24px;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    color: #fff;
    cursor: pointer;
    z-index: 99999;

    &:hover {
      background-color: rgba(17, 25, 37, 0.3);
    }
  }
}

</style>

```

#### index.md

```markdown
# 侧导航 / Sidebar
侧导航将网站整体的信息架构分组归类并以菜单的形式展示给用户，方便用户查找所要获取的信息。

## 互动演示
:::include(src="./demo/sidebar-interactive.vue")
:::

## 代码示例
### 主题
侧导航提供浅色、深色两种主题。
:::include(src="./doc/theme.vue")
:::

### 内嵌搜索框
:::include(src="./doc/search.vue")
:::

### 展开收起
:::include(src="./doc/fold.vue")
:::

### 半展开收起按钮位置
:::include(src="./doc/collapse-button-position.vue")
:::



## API
<api-doc name="Sidebar" :doc="require('./api.json')"></api-doc>
### data 字段说明
| 参数 | 说明 | 类型 | 可选值 | 默认值 |
|--- |--- |--- |--- |--- |
| id | 菜单的唯一标识，可以使用 Sidebar 组件的 item-key 属性进行更改 | string | - | - |
| icon | 菜单的图标 | string | - | - |
| title | 菜单的标题 | string | - | - |
| tooltip | tooltip 显示的内容 | string | - | 收起状态下默认显示 title 字段 |
| disabled | 是否禁用 | boolean | - | false |
| route | 配置改属性后使用 router-link 组件进行渲染，该属性可选值同 router-link 组件 to 属性，详细请参考 [router-link](https://router.vuejs.org/zh/api/#to) | string / object | - | - |
| replace | 仅当配置 route 属性生效，同 router-link replace 属性 | boolean | - | false |
| exact | 仅当配置 route 属性生效，同 router-link exact 属性 | boolean | - | - |
| href | 同 a 标签 href 属性 | string | - | - |
| target | 同 a 标签 target 属性 | string | - | - |
| rel | 同 a 标签 rel 属性 | string | - | - |
| children | 子菜单项 | Array | - | - |

```

#### index.ts

```typescript
import Sidebar from './sidebar'
import { withInstall } from '@ss/mtd-adapter'
export default withInstall(Sidebar)

```

#### props.ts

```typescript
import { ExtractPropTypes, PropType } from '@ss/mtd-adapter'
import { SidebarData } from './types'
import { CSSPropertyType } from '@components/types'
import { MenuItem } from '@components/menu-item/types'

export interface VirtualOptions {
  rowHeight?: number,
  rowSplit?: number[], 
}

const sildebarProps = () => ({
  // 主题
  theme: {
    type: String,
    default: 'light',
  },
  collapse: Boolean,
  // 激活菜单名称
  modelValue: [Number, String],
  // 默认展开的菜单集合
  expandKeys: {
    type: Array,
    default: () => {
      return []
    },
  },
  // 是否开启手风琴模式
  accordion: Boolean,
  data: {
    type: Array as PropType<SidebarData[]>,
    required: true,
  },
  itemKey: {
    type: String,
    default: 'id',
  },
  itemStyle: {
    type: [Object, Function] as PropType<CSSPropertyType | ((item: MenuItem) => CSSPropertyType)>,
    default: () => ({}),
  },
  titleImage: { // 标题 logo
    type: String,
  },
  title: {
    type: String,
    default: '',
  },
  tooltipProps: {
    type: Object,
  },

  defaultActiveKey: [String, Number],
  defaultExpandKeys: Array,
  expandType: {
    type: String,
    default: 'partial', // 全展开、半展开、不展开
    validator(value: string) {
      return ['fully', 'partial', 'none'].includes(value)
    },
  },
  collapseButtonPosition: {
    type: String,
    default: 'bottom', // 默认放在底部
    validator(value: string) {
      return ['top', 'bottom'].includes(value)
    },
  },
  hiddenHeader: {
    type: Boolean,
    default: false,
  },
})

export type SidebarProps = Partial<ExtractPropTypes<ReturnType<typeof sildebarProps>>>;

export default sildebarProps

```

#### sidebar.tsx

```tsx
import {
  defineComponent,
  computed,
  Fragment, classNames, styles,
  getSlotsInRender,
  getScopedSlotsInRender,
  vSlots,
  useResetAttrs,
} from '@ss/mtd-adapter'
import useConfig from '@hooks/config'
import MtdMenu from '@components/menu'
import MtdTooltip from '@components/tooltip'
import Submenu from './base/submenu'
import MenuItem from './base/menu-item'
import useSidebar from './useSideBar'
import { SidebarData } from './types'
import sideBarProps from './props'
import MtdIcon from '@components/icon'

export default defineComponent({
  name: 'MtdSidebar',
  components: {
    MtdMenu,
    Submenu,
    MenuItem,
    MtdIcon,
    MtdTooltip,
  },
  inheritAttrs: false,
  model: {
    prop: 'modelValue',
    event: 'update:modelValue',
  },
  props: sideBarProps(),
  emits: [
    'update:collapse',
    'collapse-change',
    'update:modelValue',
    'update:expandKeys',
    'menu-item-click',
    'menu-expand-change',
  ],
  setup(props, ctx) {
    const { emit } = ctx
    const config = useConfig()
    const prefix = computed(() => config.getPrefixCls('sidebar'))
    const prefixMTD = computed(() => config.getPrefix())

    const useSidebarHook = useSidebar(props, ctx as any)

    const menuAttrs = useResetAttrs(ctx.attrs)

    // @Function
    function toggle() {
      const flag = !useSidebarHook.m_collapse.value
      useSidebarHook.updateCollapse(flag)

      emit('collapse-change', flag)
    }
    function getItem(key: string) {
      let result: any = null
      const { itemKey } = props
      function flat(item: SidebarData) {
        if (item[itemKey] === key) {
          result = item
          return true
        } else if (item.children) {
          item.children.some(flat)
        }
      }
      props.data!.some(flat)
      return result
    }
    function handleMenuItemActive(key: string) {
      useSidebarHook.updateActiveKey(key)
      const item = getItem(key)
      emit('menu-item-click', { key, item })
    }
    function handleExpandChange(
      keys: string[],
      extra: { name: string; expanded: boolean },
    ) {
      useSidebarHook.updateExpandKeys(keys)
      const { name, expanded } = extra
      const item = getItem(extra.name)
      emit('menu-expand-change', {
        item,
        expandKeys: keys,
        key: name,
        expanded,
      })
    }

    const functionCollection = {
      toggle, getItem, handleMenuItemActive, handleExpandChange,
    }


    const m_expandNavigationText = computed(() => config.t('el.sidebar.expandNavigation'))
    const m_collapseNavigationText = computed(() => config.t('el.sidebar.collapseNavigation'))

    return {
      prefix, menuAttrs, prefixMTD,
      m_expandNavigationText,
      m_collapseNavigationText,
      ...useSidebarHook,
      ...functionCollection,
    }
  },
  render() {
    const {
      prefix, prefixMTD, m_collapse, m_value, m_expandKeys, theme, title, accordion, tooltipProps, itemKey,
      data, menuAttrs, titleImage, expandType, collapseButtonPosition, hiddenHeader, m_expandNavigationText, m_collapseNavigationText, itemStyle,
    } = this

    const menuListeners = {
      ['expand-change']: this.handleExpandChange,
      ['update:modelValue']: this.handleMenuItemActive,
    }

    const submenuSlots = {
      title: getScopedSlotsInRender(this, 'submenu'),
      item: getScopedSlotsInRender(this, 'item'),
      icon: getScopedSlotsInRender(this, 'submenu-icon'),
      activeIcon: getScopedSlotsInRender(this, 'submenu-active-icon'),
      itemIcon: getScopedSlotsInRender(this, 'item-icon'),
      itemActiveIcon: getScopedSlotsInRender(this, 'item-active-icon'),
    }
    
    const menuItemSlots = {
      default: getScopedSlotsInRender(this, 'item'),
      icon: getScopedSlotsInRender(this, 'item-icon'),
      activeIcon: getScopedSlotsInRender(this, 'item-active-icon'),
    }

    const isTitleExist = getSlotsInRender(this, 'title') || title
    const isFullyCollapse = expandType === 'fully'
    const isPartialCollapse = expandType === 'partial'
    const isNoneCollapse = expandType === 'none'
    const that = this


    function renderSideBarHeader() {
      if (hiddenHeader && collapseButtonPosition === 'bottom') return null
      let titleElement: any | null = null // JSX.Element 会报错，先用一下any
      let collapseButtonElement: any | null = null // JSX.Element 会报错，先用一下any

      const showTitle = (collapseButtonPosition === 'top' && !m_collapse) || collapseButtonPosition === 'bottom' // 按钮在顶部且展开 | 按钮在底部时才展示title
      /* 
       * 【header内容】展示： 
       * 1. 这里是为了兼容老逻辑，有title的时候优先使用title, 然后再降级使用图片 
      */
      if (isTitleExist && showTitle) {
        titleElement = <span class={`${prefix}-header-title`}> {getSlotsInRender(that, 'title') || title}</span>
      } else if (titleImage && showTitle) {
        titleElement = <img src={titleImage} class={`${prefix}-header-image`}/>
      }

      /*
        *【展开/隐藏】按钮： 
        *  1. 全展开菜单显示全展开按钮
        *  2. 半展开 + collapseButtonPosition 为 top 的时候，显示【展开｜隐藏】按钮
      */
      if (isFullyCollapse) {
        collapseButtonElement = (
          <mtd-tooltip content={m_collapse ? m_expandNavigationText : m_collapseNavigationText} size="small" placement="right">
            <span class={`${prefix}-header-fully-icon`} onClick={that.toggle}>
              <mtd-icon name="left-thick"/>
            </span>
          </mtd-tooltip>
        )
      } else if (isPartialCollapse && collapseButtonPosition === 'top') {
        collapseButtonElement = (<mtd-tooltip content={m_collapse ? m_expandNavigationText : m_collapseNavigationText} size="small" placement="right">
          <span class={`${prefix}-header-icon`} onClick={that.toggle}>
            <mtd-icon name={m_collapse ? 'expand' : 'collapse'} class={`${prefixMTD}-menu-title-backward`} />
          </span>
        </mtd-tooltip>)
      }

      return (
        <div class={`${prefix}-header`}>
          {titleElement}
          {collapseButtonElement}
        </div>
      )
    }

    function renderSideBarFooter() {
      if (isPartialCollapse && collapseButtonPosition === 'bottom') {
        return (
          <div class={`${prefix}-footer`}>
            <mtd-tooltip content={m_collapse ? m_expandNavigationText : m_collapseNavigationText} size="small" placement="right">
              <span class={`${prefix}-footer-icon`} onClick={that.toggle}>
                <mtd-icon name={m_collapse ? 'expand' : 'collapse'} class={`${prefixMTD}-menu-title-backward`} />
              </span>
            </mtd-tooltip>

          </div>
        )
      }
      return null
    }

    return <div
      class={classNames(this, {
        [prefix]: true,
        [prefix + '-collapse']: m_collapse,
        [prefix + '-collapse-fully']: isFullyCollapse,
        [prefix + '-collapse-none']: isNoneCollapse,
        [`${prefix}-${theme}`]: theme,
        [prefix + '-hidden-header']: hiddenHeader && collapseButtonPosition === 'bottom',
      })}
      style={styles(this)}
    >
      {/* 头部 */}
      { renderSideBarHeader() }
      
      {/* 搜索框 */}
      {getSlotsInRender(this, 'search') && <div class={`${prefix}-search`}>{getSlotsInRender(this, 'search')}</div>}

      {/* 主题 */}
      <div class={`${prefix}-menus`}>
        {getSlotsInRender(this, 'menu') ||
          <mtd-menu
            class={`${prefix}-menus-inner`}
            {...menuAttrs}
            theme={theme}
            modelValue={m_value}
            default-expanded-names={m_expandKeys}
            collapse={m_collapse}
            accordion={accordion}
            on={menuListeners}
            tooltip-props={tooltipProps}
            item-key={itemKey}
            expandType={expandType}
            item-style={itemStyle}
          >
            {data?.map(item => <Fragment>
              {item.children
                ? <submenu
                  item-key={itemKey}
                  item={item}
                  key={item[itemKey]}
                  tooltip-props={tooltipProps}
                  {...vSlots(submenuSlots)}
                  v-slots={submenuSlots}
                >
                </submenu>
                : <menu-item
                  item={item}
                  key={item[itemKey]}
                  item-key={itemKey}
                  tooltip-props={tooltipProps}
                  {...vSlots(menuItemSlots)}
                  v-slots={menuItemSlots}
                >
                </menu-item>
              }
            </Fragment>)}
          </mtd-menu>
        }
      </div>

      {/* 底部 */}
      { renderSideBarFooter() }
    </div >
  },
})

```

#### types.ts

```typescript
import { CPI } from '@components/types/component'

export interface SidebarData {
  [key: string]: any;
  title: string;
  tooltip?: string;
  disabled: boolean;
  route?: string | object;
  replace: boolean;
  exact: boolean;
  href?: string;
  blank?: string;
  rel?: string;
  children?: SidebarData[];
}

export interface ISidebar extends CPI {
  theme: string;
  collapse: boolean;
  activeKey: number | string;
  expandKeys: string[] | number[];
  accordion: boolean;
  itemKey: string;
  title: string;
  tooltipProps?: object;

  data: SidebarData;
}

declare const Sidebar: ISidebar
export default Sidebar

```

#### useSideBar.ts

```typescript
import { computed, reactive, SetupContext, hasProp, vueInstance } from '@ss/mtd-adapter'
import { SidebarProps } from './props'

export default function useSidebar(props: SidebarProps, ctx: SetupContext) {
  const ins = vueInstance()

  const state = reactive({
    collapse: false,
    value: props.defaultActiveKey || '',
    expandKeys: props.defaultExpandKeys || [],
  })

  const isCollapseControlled = computed(() => hasProp(ins, 'collapse'))
  const m_collapse = computed(() => isCollapseControlled.value
    ? props.collapse
    : state.collapse,
  )
  function updateCollapse(v: boolean) {
    state.collapse = v
    ctx.emit('update:collapse', v)
  }

  const isActiveControlled = computed(() => hasProp(ins, 'modelValue'))
  const m_value = computed(() => isActiveControlled.value
    ? props.modelValue
    : state.value,
  )
  function updateActiveKey(v: string) {
    state.value = v
    ctx.emit('update:modelValue', v)
  }

  const isExpandControlled = computed(() => hasProp(ins, 'expandKeys'))
  const m_expandKeys = computed(() => isExpandControlled.value
    ? props.expandKeys
    : state.expandKeys,
  )
  function updateExpandKeys(v: string[]) {
    state.expandKeys = v
    ctx.emit('update:expandKeys', v)
  }

  return {
    m_collapse, m_value, m_expandKeys, isActiveControlled,
    updateCollapse, updateActiveKey, updateExpandKeys,
  }
}

```

