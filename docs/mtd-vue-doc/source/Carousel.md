## 源码解析

### 文件依赖

- carousel.tsx
- demo.md
- demo/carousel-interactive.vue
- demo/usage.json
- doc/arrow.vue
- doc/base.vue
- doc/demo/applyProps.vue
- doc/demo/autoplay.vue
- doc/demo/initial-index.vue
- doc/demo/methods.vue
- doc/indecator-style.vue
- doc/indecator.vue
- doc/interval.vue
- doc/loop.vue
- doc/other.vue
- doc/speed.vue
- doc/type-card.vue
- doc/type-fade.vue
- doc/type-horizontal.vue
- index.md
- index.ts
- types.ts

### 源码内容

#### carousel.tsx

```tsx
import {
  defineComponent,
  ref,
  computed,
  onMounted,
  onUpdated,
  onUnmounted,
  nextTick,
  getChildInsList,
  getChildVNodeList,
  applyProps2,
  VNode,
  getElementFromComponent,
  PropType,
  hasProp,
} from '@ss/mtd-adapter'
import { addResizeListener, removeResizeListener } from '@utils/resize-event'
import useConfig from '@hooks/config'
import vueInstance from '@hooks/instance'
import { CPI } from '@components/types/component'

const ScaleValue = 0.8

export default defineComponent({
  name: 'MtdCarousel',
  props: {
    initialIndex: {
      type: Number,
      default: 0,
    },
    autoplay: {
      type: Boolean,
      default: true,
    },
    indicatorPosition: {
      type: String,
      default: 'inside',
    },
    indicatorType: {
      type: String,
      default: 'line',
    },
    speed: {
      type: Number,
      default: 600,
    },
    interval: {
      type: Number,
      default: 3000,
    },
    loop: {
      type: Boolean,
      default: true,
    },
    arrow: {
      type: String,
      default: 'always',
    },
    type: {
      type: String,
      default: 'normal',
    },
    height: {
      type: Number,
    },
    showButton: {
      type: Boolean,
      default: true,
    },

    // 非公开API
    customChildNames: {
      type: Array as PropType<string[]>,
      default: () => ['MtdCarouselItem'],
    },
  },
  emits: ['change'],
  setup(props, ctx) {
    const config = useConfig()
    const prefix = computed(() => config.getPrefixCls('carousel'))
    const componentsPrefixName = computed(() => config.getComponentsPrefixName('CarouselItem'))
    const iconPrefix = config.getIconCls
    const ins = vueInstance()

    const width = ref<number>(0)
    const activeIndex = ref<number>(props.initialIndex || 0)
    const isTransition = ref<boolean>(false)

    const carouselInstanceItems = ref<CPI[]>([])

    const wrapperRef = ref<HTMLElement | null>(null)
    const itemListRef = ref<HTMLElement | null>(null)

    const m_height = ref<number | undefined>(props.height)
    // 是否展示下一张的按钮
    const isShowNextBtn = ref<boolean>(true)

    // @Computed
    const isNormalType = computed(() => {
      return props.type === 'normal' || props.type === 'vertical'
    })
    const len = computed(() => {
      return carouselInstanceItems.value.length
    })
    const m_activeDot = computed(() => {
      let activeDot
      if (activeIndex.value === len.value) {
        activeDot = 0
      } else if (activeIndex.value === -1) {
        activeDot = len.value - 1
      } else {
        activeDot = activeIndex.value
      }

      return activeDot
    })


    // @Created
    const isMouseover = ref<boolean>(false) // 控制autoplay和next函数
    const timer = ref<any>(null) // autoplay的时间锚

    onMounted(() => {
      const wrapper = wrapperRef.value
      // 获取容器宽度，容器为100%,拿到真实宽度后渲染children的宽度
      width.value = wrapper ? wrapper.offsetWidth : 0


      // const maxChildHeight = Math.max(...carouselInstanceItems.map((item:any) => getElementFromComponent(item)?.offsetHeight || 0))

      // translated(activeIndex.value + 1)
      nextTick(() => {
        isTransition.value = true // tofix: https://tt.sankuai.com/ticket/detail?id=40600798
      })
      // 当页面宽度改变时，更新width属性
      addResizeListener(ins.$el, handleWindowResized)
    })

    onUpdated(() => {
      if (m_height.value === undefined) {
        const carouselInstanceItemsArr = getCarouselItemInstanceList()
        const maxChildHeight = Math.max(...carouselInstanceItemsArr.map((item: any) => getElementFromComponent(item)?.offsetHeight || 0))
        m_height.value = maxChildHeight
      }

      calcCarousel()
    })

    onUnmounted(() => {
      removeResizeListener(ins.$el, handleWindowResized)
    })

    // @Methods
    const handlePrevBtnClick = () => {
      isMouseover.value = false
      prev()
    }

    const handleNextBtnClick = () => {
      isMouseover.value = false
      next()
    }

    const handleDotClick = (index: number) => {
      isMouseover.value = false
      setActiveItem(index)
    }

    const handleWindowResized = () => {
      const wrapper = wrapperRef.value
      width.value = wrapper ? wrapper.offsetWidth : 0
      setActiveItem(activeIndex.value)
    }

    const handleMouseEnter = () => {
      if (!props.autoplay) {
        return
      }

      isMouseover.value = true
      autoplayHandler()
    }

    const handleMouseLeave = () => {
      if (!props.autoplay) {
        return
      }

      isMouseover.value = false
      autoplayHandler()
    }

    const setActiveItem = (index: number, type?: 'next' | 'prev') => {
      const prevIndex = activeIndex.value
      if (activeIndex.value === index) {
        return
      }

      if (isNormalType.value) {
        const prevDir = props.type === 'normal' ? 'left' : 'top'
        const nextDir = props.type === 'normal' ? 'right' : 'bottom'

        let curActiveItem_position = ''
        let nextActiveItem_position = ''

        if (type) {
          curActiveItem_position = type === 'next' ? prevDir : nextDir
          nextActiveItem_position = type === 'next' ? nextDir : prevDir
        } else {
          curActiveItem_position = activeIndex.value > index ? nextDir : prevDir
          nextActiveItem_position = activeIndex.value > index ? prevDir : nextDir
        }

        const curActiveItem = carouselInstanceItems.value[activeIndex.value]
        const nextActiveItem = carouselInstanceItems.value[index]

        curActiveItem?.setAnimation(curActiveItem_position, 'out') // 往 哪个方向 出去
        nextActiveItem?.setAnimation(nextActiveItem_position, 'enter') // 从哪个方向 进来
      }

      activeIndex.value = index
      ctx.emit('change', prevIndex, index)
    }

    const autoplayHandler = () => {
      if (!isMouseover.value) {
        const nextIndex = (activeIndex.value + 1) % len.value
        // 修复loop属性不生效，自动播放到最后一张时如果loop属性为false则表示不自动循环，就清空定时器退出自动播放,隐藏next按钮
        if (nextIndex === 0 && !props.loop) {
          clearCarouselTimeout()
          isShowNextBtn.value = false
          return
        }

        isShowNextBtn.value = true
        timer.value = setTimeout(() => {
          next()
          autoplayHandler()
        }, props.interval)
      } else {
        clearCarouselTimeout()
      }
    }

    const clearCarouselTimeout = () => {
      timer.value && clearTimeout(timer.value)
      timer.value = null
    }

    const next = () => {
      const nextIndex = (activeIndex.value + 1) % len.value
      setActiveItem(nextIndex, 'next')
    }

    const prev = () => {
      const prevIndex = activeIndex.value - 1 < 0 ? len.value - 1 : (activeIndex.value - 1) % len.value
      setActiveItem(prevIndex, 'prev')
    }

    const calcCarousel = () => {
      const newCarouselInstanceItems = getCarouselItemInstanceList()

      if (
        carouselInstanceItems.value.length !== newCarouselInstanceItems.length
      ) {
        carouselInstanceItems.value = newCarouselInstanceItems
        const curActiveIndex = props.initialIndex || 0
        activeIndex.value = curActiveIndex
        setActiveItem(curActiveIndex)
        if (props.autoplay) {
          clearCarouselTimeout()
          autoplayHandler()
        }
      }
    }

    // 获取 item ins
    const getCarouselItemInstanceList = () => {
      const childNames = hasProp(ins, 'customChildNames') ? props.customChildNames : [componentsPrefixName.value]
      return getChildInsList(ins, childNames)
    }

    // 获取 item vnode
    const getCarouselItemVNodeList = () => {
      const childNames = hasProp(ins, 'customChildNames') ? props.customChildNames : [componentsPrefixName.value]
      return getChildVNodeList(ins, childNames)
    }

    function getCardType(index: number) {
      if (index === (activeIndex.value + 1) % len.value) {
        return 'next'
      } else if (index === ((activeIndex.value - 1) % len.value >= 0
        ? (activeIndex.value - 1) % len.value
        : len.value + (activeIndex.value - 1))) {
        return 'prev'
      }
    }

    return {
      width,
      len,
      activeIndex,
      isTransition,
      prefix,
      wrapperRef,
      itemListRef,
      m_activeDot,
      isNormalType,
      m_height,
      isShowNextBtn,
      iconPrefix,
      getCarouselItemVNodeList,
      handleDotClick,
      handleMouseEnter,
      handleMouseLeave,
      handlePrevBtnClick,
      handleNextBtnClick,
      prev,
      next,
      setActiveItem,
      getCardType,
    }
  },
  render() {
    const {
      width,
      height,
      m_height,
      len,
      activeIndex,
      isTransition,
      prefix,
      speed,
      arrow,
      indicatorPosition,
      m_activeDot,
      indicatorType,
      type,
      showButton,
      isShowNextBtn,
      getCarouselItemVNodeList,
      handleMouseEnter,
      handleMouseLeave,
      handlePrevBtnClick,
      handleNextBtnClick,
      handleDotClick,
    } = this
    const children = getCarouselItemVNodeList()
    const items = children.map((i: VNode, index: number) =>
      applyProps2(i, {
        index,
        width: type === 'card' ? width * ScaleValue : width,
        height,
        activeIndex,
        mask: type === 'card',
        speed,
        cardType: this.getCardType(index),
      }),
    )

    let liList: number[] = []
    const itemListStyle: any = {}

    if (items.length > 0) {
      liList = Array.from(new Array(len), (val, index) => index)

      itemListStyle.width = '100%'

      if (isTransition) {
        itemListStyle.transition = `transform ${speed / 1000}s ease-in-out`
      }

      // itemListStyle.overflow = `hidden`
      itemListStyle.position = 'relative'
      itemListStyle['transform-style'] = 'preserver-3d'
      itemListStyle.height = `${m_height || 0}px`
    }

    return (
      <div
        class={[prefix, `${prefix}-${type}`]}
        ref={'wrapperRef'}
        onMouseenter={handleMouseEnter}
        onMouseleave={handleMouseLeave}
      >
        <div class={`${prefix}-itemList-outer`}>
          {
            <div
              class={`${prefix}-itemList`}
              style={{ ...itemListStyle, 'transform-style': 'preserve-3d' }}
              ref={'itemListRef'}
            >
              {width ? items : null}
            </div>
          }

          {/* 左右按钮 */}
          {
            (showButton && items.length > 1)
            && <div
              class={[`${prefix}-btn`, `${prefix}-prev`, `${prefix}-${arrow}`]}
              onClick={handlePrevBtnClick}
            >
              <i class={this.iconPrefix('left-thick')} style={
                'transform: translateX(-1px)'
              } />
            </div>
          }
          {
            (isShowNextBtn && showButton && items.length > 1)
            && <div
              class={[`${prefix}-btn`, `${prefix}-next`, `${prefix}-${arrow}`]}
              onClick={handleNextBtnClick}
            >
              <i class={this.iconPrefix('right-thick')} style={
                'transform: translateX(1px)'
              } />
            </div>
          }
        </div>


        {/* 指示器 */}
        {
          indicatorPosition !== 'none'
          && <ul class={{
            [`${prefix}-indicator`]: true,
            [`${prefix}-inside`]: indicatorPosition.indexOf('inside') >= 0,
            [`${prefix}-${indicatorPosition}`]: true,
          }}>
            {liList.map((item) => {
              return (
                <span
                  class={[
                    `${prefix}-dot`,
                    {
                      [`${prefix}-active-dot`]: m_activeDot === item,
                      [`${prefix}-dot-${indicatorType}`]: true,
                    },
                  ]}
                  onClick={() => handleDotClick(item)}
                />
              )
            })}
          </ul>
        }
      </div >
    )
  },
})

```

#### demo.md

```markdown
# 轮播 / Carousel

## 补充Demo
### initial-index
:::include(src="./doc/demo/initial-index.vue")
:::

### autoplay
:::include(src="./doc/demo/autoplay.vue")
:::

### methods
:::include(src="./doc/demo/methods.vue")
:::

### applyProps
applyProps在vue3打包后有问题，补充Demo
:::include(src="./doc/demo/applyProps.vue")
:::
```

#### demo/carousel-interactive.vue

```vue
<template>
  <mtd-doc-usage name='Carousel' :apiJson="apiJson" :usageJson="usage">
    <template #Carousel="{ configProps }">
      <mtd-carousel v-bind="configProps">
        <mtd-carousel-item>
          <div :style="itemStyle(0)">0</div>
        </mtd-carousel-item>
        <mtd-carousel-item>
          <div :style="itemStyle(1)">1</div>
        </mtd-carousel-item>
        <mtd-carousel-item>
          <div :style="itemStyle(2)">2</div>
        </mtd-carousel-item>
        <mtd-carousel-item>
          <div :style="itemStyle(3)">3</div>
        </mtd-carousel-item>
      </mtd-carousel>
    </template>
  </mtd-doc-usage>
</template>
<script>
const usage = require('./usage.json')
const apiJson = require('../api.json')
export default {
  data() {
    return {
      usage,
      apiJson,
      bgc: {
        0: '#8FBDD3',
        1: '#E4D1B9',
        2: '#BE8C63',
        3: '#8879B0',
      },
    };
  },
  methods: {
    itemStyle(num) {
      return {
        height: '240px',
        lineHeight: '240px',
        textAlign: 'center',
        color: 'white',
        backgroundColor: this.bgc[num],
        fontSize: '30px',
        fontWeight: '900',
        //opacity: 0.5,
      }
    },
  },
};
</script>
  
```

#### demo/usage.json

```json
{
    "componentName": "Carousel",
    "desc": "常规型",
    "codeTemplate": "<mtd-carousel v-bind='configProps'></mtd-carousel>"
}
```

#### doc/arrow.vue

```vue
<template>
  <div class="carousel-demo-content">
    <mtd-tabs v-model="arrow" type="text" size="small">
      <mtd-tab-pane label="常显按钮" value="always" />
      <mtd-tab-pane label="悬浮显示按钮" value="hover" />
      <mtd-tab-pane label="不显示按钮" value="never" />
    </mtd-tabs>
    <mtd-carousel :arrow="arrow">
      <mtd-carousel-item>
        <div :style="itemStyle">0</div>
      </mtd-carousel-item>
      <mtd-carousel-item>
        <div :style="itemStyle">1</div>
      </mtd-carousel-item>
      <mtd-carousel-item>
        <div :style="itemStyle">2</div>
      </mtd-carousel-item>
      <mtd-carousel-item>
        <div :style="itemStyle">3</div>
      </mtd-carousel-item>
    </mtd-carousel>
  </div>
</template>
<script>
export default {
  data() {
    return {
      itemStyle: {
        height: '240px',
        lineHeight: '240px',
        textAlign: 'center',
        color: 'white',
        backgroundColor: '#5D6EAC',
        fontSize: '30px',
        fontWeight: '900',
      },
      arrow: 'always'
    };
  },
};
</script>
<style scoped>
.carousel-demo-content {
  margin-top: 30px;
  width: 100%;
}

::v-deep .carousel-demo-content .mtd-tabs-nav {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  border-bottom: 1px solid #edf0f7;
  padding: 1.5px 16px;
}
</style>

```

#### doc/base.vue

```vue
<template>
  <div style="width:100%">
    <mtd-carousel arrow="always">
      <mtd-carousel-item>
        <div :style="itemStyle(0)">0</div>
      </mtd-carousel-item>
      <mtd-carousel-item>
        <div :style="itemStyle(1)">1</div>
      </mtd-carousel-item>
      <mtd-carousel-item>
        <div :style="itemStyle(2)">2</div>
      </mtd-carousel-item>
      <mtd-carousel-item>
        <div :style="itemStyle(3)">3</div>
      </mtd-carousel-item>
    </mtd-carousel>
    <br><br><br>
  </div>
</template>
<script>
export default {
  data() {
    return {
      bgc: {
        0: '#8FBDD3',
        1: '#E4D1B9',
        2: '#BE8C63',
        3: '#8879B0',
      },
    }
  },
  methods: {
    itemStyle(num) {
      return {
        height: '240px',
        lineHeight: '240px',
        textAlign: 'center',
        color: 'white',
        backgroundColor: this.bgc[num],
        fontSize: '30px',
        fontWeight: '900',
        //opacity: 0.5,
      }
    },
  },
}
</script>

```

#### doc/demo/applyProps.vue

```vue
<template>
  <div style="height:200px; width: 200px">
    <mtd-carousel v-if="imageList.length > 1" :autoplay="false" arrow="always" @change="change">
      <mtd-carousel-item style="height:200px; width: 200px" v-for="(image, index) in imageList" :key="image">
        <div class="carousel-item">
          <img :src="image" :data-original="image" @click.stop="onOpenView(index)" />
        </div>
      </mtd-carousel-item>
    </mtd-carousel>
  </div>
</template>
<script>
export default {
  data() {
    return {
      imageList: ['http://p0.meituan.net/wmcomment/d9e6f3115bef02c4ff136086b062381e16050.jpg.webp','http://p0.meituan.net/wmcomment/564e6e3e894c19e4da4982de97c5ffc737457.jpg.webp'],
    }
  },
  methods: {
    onOpenView(index) {
      console.log(index);
    },
    change(index) {
      console.log('index')
    }
  },
}
</script>
<style>
  .carousel-item {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 200px;
    height: 200px;
  }
</style>

```

#### doc/demo/autoplay.vue

```vue
<template>
  <div style="width:100%">
    <mtd-carousel arrow="always" :autoplay="autoplay">
      <mtd-carousel-item>
        <div :style="itemStyle(0)">0</div>
      </mtd-carousel-item>
      <mtd-carousel-item>
        <div :style="itemStyle(1)">1</div>
      </mtd-carousel-item>
      <mtd-carousel-item>
        <div :style="itemStyle(2)">2</div>
      </mtd-carousel-item>
      <mtd-carousel-item>
        <div :style="itemStyle(3)">3</div>
      </mtd-carousel-item>
    </mtd-carousel>
    <br><br><br>
  </div>
</template>
<script>
export default {
  data() {
    return {
      bgc: {
        0: '#8FBDD3',
        1: '#E4D1B9',
        2: '#BE8C63',
        3: '#8879B0',
      },
      autoplay: false,
    }
  },
  methods: {
    itemStyle(num) {

      return {
        height: '240px',
        lineHeight: '240px',
        textAlign: 'center',
        color: 'white',
        backgroundColor: this.bgc[num],
        fontSize: '30px',
        fontWeight: '900',
        //opacity: 0.5,
      }
    },
  },
}
</script>

```

#### doc/demo/initial-index.vue

```vue
<template>
  <div style="width:100%">
    <mtd-carousel arrow="always" :initial-index="index">
      <mtd-carousel-item>
        <div :style="itemStyle(0)">0</div>
      </mtd-carousel-item>
      <mtd-carousel-item>
        <div :style="itemStyle(1)">1</div>
      </mtd-carousel-item>
      <mtd-carousel-item>
        <div :style="itemStyle(2)">2</div>
      </mtd-carousel-item>
      <mtd-carousel-item>
        <div :style="itemStyle(3)">3</div>
      </mtd-carousel-item>
    </mtd-carousel>
    <br><br><br>
  </div>
</template>
<script>
export default {
  data() {
    return {
      bgc: {
        0: '#8FBDD3',
        1: '#E4D1B9',
        2: '#BE8C63',
        3: '#8879B0',
      },
      index: 3,
    }
  },
  methods: {
    itemStyle(num) {

      return {
        height: '240px',
        lineHeight: '240px',
        textAlign: 'center',
        color: 'white',
        backgroundColor: this.bgc[num],
        fontSize: '30px',
        fontWeight: '900',
        //opacity: 0.5,
      }
    },
  },
}
</script>

```

#### doc/demo/methods.vue

```vue
<template>
  <div style="width:100%">
    <div style="margin-bottom: 10px;">
      <mtd-button type="primary" @click="handlePrev" style="margin-right: 5px;">上一张</mtd-button>
      <mtd-button type="primary" @click="handleNext" style="margin-right: 5px;">下一张</mtd-button>
      <mtd-button type="primary" @click="setActiveItem">跳转至第二张</mtd-button>
    </div>
    <mtd-carousel arrow="always" :autoplay="false" ref="currentCarousel">
      <mtd-carousel-item>
        <div :style="itemStyle(0)">0</div>
      </mtd-carousel-item>
      <mtd-carousel-item>
        <div :style="itemStyle(1)">1</div>
      </mtd-carousel-item>
      <mtd-carousel-item>
        <div :style="itemStyle(2)">2</div>
      </mtd-carousel-item>
      <mtd-carousel-item>
        <div :style="itemStyle(3)">3</div>
      </mtd-carousel-item>
    </mtd-carousel>
    <br><br><br>
  </div>
</template>
<script>
export default {
  data() {
    return {
      bgc: {
        0: '#8FBDD3',
        1: '#E4D1B9',
        2: '#BE8C63',
        3: '#8879B0',
      },
    }
  },
  methods: {
    itemStyle(num) {

      return {
        height: '240px',
        lineHeight: '240px',
        textAlign: 'center',
        color: 'white',
        backgroundColor: this.bgc[num],
        fontSize: '30px',
        fontWeight: '900',
        //opacity: 0.5,
      }
    },
    handlePrev() {
      this.$refs.currentCarousel.prev()
    },
    handleNext() {
      this.$refs.currentCarousel.next()

    },
    setActiveItem() {
      this.$refs.currentCarousel.setActiveItem(2)
    }
  },
}
</script>

```

#### doc/indecator-style.vue

```vue
<template>
  <div class="carousel-demo-content">
    <mtd-tabs v-model="type" type="text" size="small">
      <mtd-tab-pane label="段点" value="line">
      </mtd-tab-pane>
      <mtd-tab-pane label="圆点" value="circle">
      </mtd-tab-pane>
    </mtd-tabs>
    <mtd-carousel arrow="always" @change="change" :indicator-type="type">
      <mtd-carousel-item>
        <div :style="itemStyle(0)">0</div>
      </mtd-carousel-item>
      <mtd-carousel-item>
        <div :style="itemStyle(1)">1</div>
      </mtd-carousel-item>
      <mtd-carousel-item>
        <div :style="itemStyle(2)">2</div>
      </mtd-carousel-item>
      <mtd-carousel-item>
        <div :style="itemStyle(3)">3</div>
      </mtd-carousel-item>
      <div :style="itemStyle">4(无法被识别)</div>
    </mtd-carousel>
  </div>
</template>
<script>
export default {
  data() {
    return {
      bgc: {
        0: '#8FBDD3',
        1: '#E4D1B9',
        2: '#BE8C63',
        3: '#8879B0',
      },
      position: 'outside',
      type: 'line',
    };
  },
  computed: {
    showButton() {
      return this.position !== 'inside-left' && this.position !== 'inside-right'
    }
  },
  methods: {
    change(prev, next) {
      console.info(prev, next);
    },
    itemStyle(num) {
      return {
        height: '240px',
        lineHeight: '240px',
        textAlign: 'center',
        color: 'white',
        backgroundColor: this.bgc[num],
        fontSize: '30px',
        fontWeight: '900',
        //opacity: 0.5,
      }
    }
  },
};
</script>
<style scoped>
.carousel-demo-content {
  margin-top: 30px;
  width: 100%;
}

::v-deep .carousel-demo-content .mtd-tabs-nav {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  border-bottom: 1px solid #edf0f7;
  padding: 1.5px 16px;
}
</style>

```

#### doc/indecator.vue

```vue
<template>
  <div class="carousel-demo-content">
    <mtd-tabs v-model="position" type="text" size="small">
      <mtd-tab-pane label="内部" value="inside">
      </mtd-tab-pane>
      <mtd-tab-pane label="外部" value="outside">
      </mtd-tab-pane>
    </mtd-tabs>

    <mtd-radio-group v-model="insidePosition" v-if="isShowPosition" style="margin-bottom:20px ;" type="slider">
      <mtd-radio-button value="inside">底部</mtd-radio-button>
      <mtd-radio-button value="inside-top">顶部</mtd-radio-button>
      <mtd-radio-button value="inside-left">左侧</mtd-radio-button>
      <mtd-radio-button value="inside-right">右侧</mtd-radio-button>
      <mtd-radio-button value="custom">自定义</mtd-radio-button>
    </mtd-radio-group>
    <mtd-carousel arrow="always" :class="`carousel-demo-content-${insidePosition}`" @change="change"
      :indicator-position="isShowPosition ? insidePosition === 'custom' ? 'inside' : insidePosition : position">
      <mtd-carousel-item>
        <div :style="itemStyle(0)">0</div>
      </mtd-carousel-item>
      <mtd-carousel-item>
        <div :style="itemStyle(1)">1</div>
      </mtd-carousel-item>
      <mtd-carousel-item>
        <div :style="itemStyle(2)">2</div>
      </mtd-carousel-item>
      <mtd-carousel-item>
        <div :style="itemStyle(3)">3</div>
      </mtd-carousel-item>
      <div :style="itemStyle">4(无法被识别)</div>
    </mtd-carousel>
  </div>
</template>
<script>
export default {
  data() {
    return {
      bgc: {
        0: '#8FBDD3',
        1: '#E4D1B9',
        2: '#BE8C63',
        3: '#8879B0',
      },
      position: 'inside',
      insidePosition: 'inside',
      type: 'line',
    };
  },
  computed: {
    isShowPosition() {
      return this.position === 'inside'
    }
  },
  methods: {
    change(prev, next) {
      console.info(prev, next);
    },
    itemStyle(num) {
      return {
        height: '240px',
        lineHeight: '240px',
        textAlign: 'center',
        color: 'white',
        backgroundColor: this.bgc[num],
        fontSize: '30px',
        fontWeight: '900',
        //opacity: 0.5,
      }
    }
  },
};
</script>
<style scoped>
.carousel-demo-content {
  margin-top: 30px;
  width: 100%;
}

::v-deep .carousel-demo-content .mtd-tabs-nav {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  border-bottom: 1px solid #edf0f7;
  padding: 1.5px 16px;
}

::v-deep .carousel-demo-content-custom .mtd-carousel-indicator {
  top: 70%;
}
</style>
```

#### doc/interval.vue

```vue
<template>
  <mtd-carousel arrow="always" :interval="5000">
    <mtd-carousel-item><div :style="itemStyle">0</div></mtd-carousel-item>
    <mtd-carousel-item><div :style="itemStyle">1</div></mtd-carousel-item>
    <mtd-carousel-item><div :style="itemStyle">2</div></mtd-carousel-item>
    <mtd-carousel-item><div :style="itemStyle">3</div></mtd-carousel-item>
  </mtd-carousel>
</template>
<script>
export default {
  data() {
    return {
      itemStyle: {
        height: '240px',
        lineHeight: '240px',
        textAlign: 'center',
        color: 'white',
        backgroundColor: '#5D6EAC',
        fontSize: '30px',
        fontWeight: '900',
      },
    };
  },
};
</script>

```

#### doc/loop.vue

```vue
<template>
  <div class="carousel-demo-content">
    <mtd-tabs v-model="autoplay" type="text" size="small">
      <mtd-tab-pane label="自动切换" :value="1" />
      <mtd-tab-pane label="不自动切换" :value="0" />
    </mtd-tabs>
    <div v-if="autoplay" style="height: 240px;" key="123">
      <mtd-carousel arrow="always" :autoplay="Boolean(autoplay)">
        <mtd-carousel-item>
          <div :style="itemStyle">0</div>
        </mtd-carousel-item>
        <mtd-carousel-item>
          <div :style="itemStyle">1</div>
        </mtd-carousel-item>
        <mtd-carousel-item>
          <div :style="itemStyle">2</div>
        </mtd-carousel-item>
        <mtd-carousel-item>
          <div :style="itemStyle">3</div>
        </mtd-carousel-item>
      </mtd-carousel>
    </div>
    <div v-if="!autoplay" style="height: 240px;" key="321">
      <mtd-carousel arrow="always" :autoplay="Boolean(autoplay)">
        <mtd-carousel-item>
          <div :style="itemStyle">0</div>
        </mtd-carousel-item>
        <mtd-carousel-item>
          <div :style="itemStyle">1</div>
        </mtd-carousel-item>
        <mtd-carousel-item>
          <div :style="itemStyle">2</div>
        </mtd-carousel-item>
        <mtd-carousel-item>
          <div :style="itemStyle">3</div>
        </mtd-carousel-item>
      </mtd-carousel>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      itemStyle: {
        height: '240px',
        lineHeight: '240px',
        textAlign: 'center',
        color: 'white',
        backgroundColor: '#5D6EAC',
        fontSize: '30px',
        fontWeight: '900',
      },
      autoplay: 1,
    };
  },
};
</script>
<style scoped>
.carousel-demo-content {
  margin-top: 30px;
  width: 100%;
}

::v-deep .carousel-demo-content .mtd-tabs-nav {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  border-bottom: 1px solid #edf0f7;
  padding: 1.5px 16px;
}
</style>
```

#### doc/other.vue

```vue
<template>
  <mtd-carousel arrow="always" indicator-position="outside">
    <mtd-carousel-item><div :style="itemStyle">0</div></mtd-carousel-item>
    <mtd-carousel-item><div :style="itemStyle">1</div></mtd-carousel-item>
    <mtd-carousel-item><div :style="itemStyle">2</div></mtd-carousel-item>
    <mtd-carousel-item><div :style="itemStyle">3</div></mtd-carousel-item>
  </mtd-carousel>
</template>

<script>
export default {
  data() {
    return {
      itemStyle: {
        height: '240px',
        lineHeight: '240px',
        textAlign: 'center',
        color: 'white',
        backgroundColor: '#5D6EAC',
        fontSize: '30px',
        fontWeight: '900',
      },
    };
  },
};
</script>

```

#### doc/speed.vue

```vue
<template>
  <mtd-carousel :speed="200">
    <mtd-carousel-item><div :style="itemStyle">0</div></mtd-carousel-item>
    <mtd-carousel-item><div :style="itemStyle">1</div></mtd-carousel-item>
    <mtd-carousel-item><div :style="itemStyle">2</div></mtd-carousel-item>
    <mtd-carousel-item><div :style="itemStyle">3</div></mtd-carousel-item>
  </mtd-carousel>
</template>
<script>
export default {
  data() {
    return {
      itemStyle: {
        height: '240px',
        lineHeight: '240px',
        textAlign: 'center',
        color: 'white',
        backgroundColor: '#5D6EAC',
        fontSize: '30px',
        fontWeight: '900',
      },
    };
  },
};
</script>

```

#### doc/type-card.vue

```vue
<template>
  <div style="width:100%">
    <mtd-carousel arrow="always" type="card" indicator-position="outside">
      <mtd-carousel-item>
        <div :style="itemStyle(0)">0</div>
      </mtd-carousel-item>
      <mtd-carousel-item>
        <div :style="itemStyle(1)">1</div>
      </mtd-carousel-item>
      <mtd-carousel-item>
        <div :style="itemStyle(2)">2</div>
      </mtd-carousel-item>
      <mtd-carousel-item>
        <div :style="itemStyle(3)">3</div>
      </mtd-carousel-item>
    </mtd-carousel>
    <br><br><br>
  </div>
</template>
<script>
export default {
  data() {
    return {
      bgc: {
        0: '#8FBDD3',
        1: '#E4D1B9',
        2: '#BE8C63',
        3: '#8879B0',
      },
    };
  },
  methods: {
    itemStyle(num) {
      return {
        height: '240px',
        lineHeight: '240px',
        textAlign: 'center',
        color: 'white',
        backgroundColor: this.bgc[num],
        fontSize: '30px',
        fontWeight: '900',
        //opacity: 0.5,
      }
    }
  }
};
</script>

```

#### doc/type-fade.vue

```vue
<template>
  <div style="width:100%">
    <mtd-carousel arrow="always"  type="fade">
      <mtd-carousel-item><div :style="itemStyle(0)">0</div></mtd-carousel-item>
      <mtd-carousel-item><div :style="itemStyle(1)">1</div></mtd-carousel-item>
      <mtd-carousel-item><div :style="itemStyle(2)">2</div></mtd-carousel-item>
      <mtd-carousel-item><div :style="itemStyle(3)">3</div></mtd-carousel-item>
    </mtd-carousel>
    <br><br><br>
  </div>
</template>
<script>
export default {
  data() {
    return {
      bgc:{
        0: '#8FBDD3',
        1: '#E4D1B9',
        2: '#BE8C63',
        3: '#8879B0',
      },
    };
  },
  methods: {
    itemStyle(num) {
      return {
        height: '240px',
        lineHeight: '240px',
        textAlign: 'center',
        color: 'white',
        backgroundColor: this.bgc[num],
        fontSize: '30px',
        fontWeight: '900',
        //opacity: 0.5,
      }
    }
  }
};
</script>

```

#### doc/type-horizontal.vue

```vue
<template>
  <div style="width:100%">
    <mtd-carousel arrow="never"  type="vertical" indicator-position="inside-left">
      <mtd-carousel-item><div :style="itemStyle(0)">0</div></mtd-carousel-item>
      <mtd-carousel-item><div :style="itemStyle(1)">1</div></mtd-carousel-item>
      <mtd-carousel-item><div :style="itemStyle(2)">2</div></mtd-carousel-item>
      <mtd-carousel-item><div :style="itemStyle(3)">3</div></mtd-carousel-item>
    </mtd-carousel>
    <br><br><br>
  </div>
</template>
<script>
export default {
  data() {
    return {
      bgc:{
        0: '#8FBDD3',
        1: '#E4D1B9',
        2: '#BE8C63',
        3: '#8879B0',
      },
    };
  },
  methods: {
    itemStyle(num) {
      return {
        height: '240px',
        lineHeight: '240px',
        textAlign: 'center',
        color: 'white',
        backgroundColor: this.bgc[num],
        fontSize: '30px',
        fontWeight: '900',
        //opacity: 0.5,
      }
    }
  }
};
</script>

```

#### index.md

```markdown
# 轮播 / Carousel
轮播是在一定区域内，显示多个平级的图片或卡片，提升用户在有限空间里的阅读内容。

## 互动演示
:::include(src="./demo/carousel-interactive.vue")
:::

## 代码示例
### 基础用法
:::include(src="./doc/base.vue")
:::

### 卡片型
:::include(src="./doc/type-card.vue")
:::

### 指示器样式
:::include(src="./doc/indecator-style.vue")
:::

### 指示器位置
轮播指示器支持配置在上下左右（内置/外置）共8种位置，也可在此基础上偏移来自定义位置。卡片型的锚点仅支持外置下部，以下 demo 不做呈现。
:::include(src="./doc/indecator.vue")
:::


### 垂直切换
:::include(src="./doc/type-horizontal.vue")
:::


### 切换按钮显示
不自动播放时，推荐配置常显按钮；当自动播放时，推荐配置鼠标悬浮时显示按钮；在某些面积较小的次要场景支持不显示按钮
:::include(src="./doc/arrow.vue")
:::


### 渐变切换
通过配置 type 为 fade 可以渐变的形式进行轮播切换。
:::include(src="./doc/type-fade.vue")
:::


### 自动切换
推荐使用自动切换，在步骤指引等某些特殊场景下不自动切换。通过配置 speed 和 interval 参数可自定义自动轮播的切换速度和页面停留时长。
:::include(src="./doc/loop.vue")
:::

### speed控制动画速度
基本使用，通过配置props。
:::include(src="./doc/speed.vue")
:::


### interval控制当前页停留时长
基本使用，通过配置props。
:::include(src="./doc/interval.vue")
:::

## API
<api-doc name="Carousel" :doc="require('./api.json')"></api-doc>

```

#### index.ts

```typescript
import Carousel from './carousel'
import { withInstall } from '@ss/mtd-adapter'
export default withInstall(Carousel)

```

#### types.ts

```typescript
import Carousel from './index'

export type Carousel = InstanceType<typeof Carousel>
```

