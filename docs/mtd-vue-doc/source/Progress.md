## 源码解析

### 文件依赖

- circle.tsx
- demo/progress-interactive.vue
- doc/base.vue
- doc/circle.vue
- doc/color.vue
- doc/custom.vue
- doc/pie.vue
- doc/size.vue
- doc/status.vue
- doc/step.vue
- doc/steps.vue
- index.md
- index.ts
- line.tsx
- pie.tsx
- progress.tsx
- step.tsx
- types.ts

### 源码内容

#### circle.tsx

```tsx
import {
  computed,
  defineComponent,
} from '@ss/mtd-adapter'
import useConfig from '@hooks/config'

export default defineComponent({
  name: 'MtdCircleProgress',
  props: {
    color: {
      type: String,
      default: 'currentColor',
    },
    strokeWidth: {
      type: Number,
      default: 6,
    },
    strokeLinecap: {
      type: String,
    },
    showInfo: {
      type: Boolean,
    },
    width: {
      type: Number,
      default: 80,
    },
    hasIcon: {
      type: Boolean,
      default: false,
    },
    percentage: {
      type: Number,
      default: 0,
      required: true,
      validator: (val: number): boolean => val >= 0 && val <= 100,
    },
  },
  setup(props) {
    const config = useConfig()
    const prefix = computed(() => config.getPrefixCls('progress'))
    const cx = computed(() => props.width / 2)
    const cr = computed(() => cx.value - props.strokeWidth / 2)
    const strokeDasharray = computed(() => {
      const percent = props.percentage / 100
      const perimeter = Math.PI * 2 * cr.value
      return `${perimeter * percent} ${perimeter * (1 - percent)}`
    })
    const viewbox = computed(() => `0 0 ${props.width} ${props.width}`)
    const transform = computed(() => `matrix(0,-1,1,0,0,${props.width})`)
    return {
      prefix,
      cx,
      cr,
      strokeDasharray,
      viewbox,
      transform,
    }
  },
  render() {
    const {
      width, cx, cr, strokeDasharray, viewbox, transform, strokeWidth,
      prefix, color, strokeLinecap, percentage, showInfo, hasIcon,
    } = this
    return <div
      class={`${prefix}-circle-wrapper`}
      style={{
        height: `${width}px`,
        fontSize: `${width / 5}px`,
      }}
    >
      <svg width={width} height={width} viewBox={viewbox}>
        <circle
          cx={cx}
          cy={cx}
          r={cr}
          stroke-width={strokeWidth}
          stroke={color}
          class={prefix + '-circle-outer'}
          fill={'none'}
          stroke-linecap={strokeLinecap as any}
        />
        <circle
          cx={cx}
          cy={cx}
          r={cr}
          stroke-width={strokeWidth}
          stroke={color}
          fill={'none'}
          transform={transform}
          stroke-dasharray={strokeDasharray}
          stroke-linecap={strokeLinecap as any}
        />
      </svg>
      {showInfo && (!hasIcon) && <span class={`${prefix}-circle-text`}>
        {percentage}%
      </span>}
    </div>
  },
})

```

#### demo/progress-interactive.vue

```vue

<template>
  <div>
    <mtd-doc-usage name="progress" :apiJson="apiJson" :usageJson="usageJson" @propChange="propChange">
      <template #progress="{ configProps }">
        <div class="slot-container">
          <mtd-progress v-bind="configProps" style="width: 220px;"></mtd-progress>
        </div>
      </template>
    </mtd-doc-usage>
  </div>
</template>
<script>
import apiJson from '../api.json'

export default {
  data() {
    return {
      apiJson: apiJson,
      usageJson: {
        componentName: "progress",
        desc: "进度条",
        codeTemplate: "<mtd-progress v-bind='configProps'></mtd-progress>"
      }
    }
  },
  methods: {
    propChange(propertyName, val) {
      console.log('prop changed: , propertyName: ' + propertyName + ', val: ' + val)
    }
  }
}
</script>
<style lang="scss" scoped>
  .slot-container {
    width: 300px;
  }
</style>
  
```

#### doc/base.vue

```vue
<template>
  <div class="demo-box" style="width: 440px;">
    <div style="display: flex; justify-content: space-between;">
      <div style="width: 260px; margin-right: 100px;">
        <p>条形</p>
        <mtd-progress style="width: 220px;" :percentage="value" class="progress-base" :status="status" />
      </div>
      <div>
        <p>环形</p>
        <mtd-progress type="circle" :percentage="value" class="progress-base" :status="status" />
      </div>
    </div>
    <mtd-button @click="handleClick">预览进度</mtd-button>
  </div>
</template>

<script>
export default {
  data () {
    return {
      value: 10
    };
  },
  methods: {
    handleClick () {
      this.value = 0;
      const f1 = () => {
        setTimeout(() => {
          if (this.value < 100) {
            this.value = this.value + 5;
            f1();
          } else {
            this.value = 100;
          }
        }, 100);
      };
      f1();
    },
  },
  computed: {
    status() {
      return this.value === 100 ? 'success' : undefined;
    }
  }
};
</script>
<style lang="scss">
  .demo-box{
    width: 440px ;
    display: inline-block;
    margin-bottom: 40px;
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  .demo-progress-small {
    width: 250px;
  }
</style>

```

#### doc/circle.vue

```vue
<template>
  <div>
    <div class="demo-circle">
      <mtd-progress :percentage="72" type="circle" :width="32" :stroke-width="3" :show-info="false" />
      <div>72%</div>
    </div>
    <div class="demo-circle">
      <mtd-progress :percentage="100" type="circle" :width="32" :stroke-width="3" status="success" :show-info="true" />
      <div>完成</div>
    </div>
    <div class="demo-circle">
      <mtd-progress :percentage="72" type="circle" :width="32" :stroke-width="3" status="error" :show-info="true" />
      <div>失败</div>
    </div>
  </div>
</template>
<style lang="scss">
  .demo-circle {
    display: inline-block;
    width: 50px;
    font-size: 12px;
    text-align: center;
    color:rgba(0,0,0,0.9);
  }
</style>

```

#### doc/color.vue

```vue
<template>
  <div class="demo-box" style="width: 440px;">
    <div style="display: flex; justify-content: space-between;">
      <div style="width: 260px;">
        <p>条形</p>
        <mtd-progress color="red" style="width: 200px" :percentage="value" class="progress-base" :status="status" />
      </div>
      <div>
        <p>环形</p>
        <mtd-progress color="red" type="circle" :percentage="value" class="progress-base" :status="status" />
      </div>
    </div>
    <mtd-button @click="handleClick">预览进度</mtd-button>
  </div>
</template>

<script>
export default {
  data () {
    return {
      value: 10
    };
  },
  methods: {
    handleClick () {
      this.value = 0;
      const f1 = () => {
        setTimeout(() => {
          if (this.value < 100) {
            this.value = this.value + 5;
            f1();
          } else {
            this.value = 100;
          }
        }, 100);
      };
      f1();
    },
  },
  computed: {
    status() {
      return this.value === 100 ? 'success' : undefined;
    }
  }
};
</script>
<style lang="scss">
  .demo-box{
    width: 440px ;
    margin-left: 20px;
    display: inline-block;
    margin-bottom: 40px;
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  .demo-progress-small {
    width: 250px;
  }
</style>

```

#### doc/custom.vue

```vue
<template>
  <div class="demo-full-width">
    <mtd-progress :percentage="value" class="progress-base" />
    <mtd-button @click="handleClick">预览效果</mtd-button>
  </div>
</template>

<script>
export default {
  data () {
    return {
      value: 10,
    };
  },
  methods: {
    handleClick () {
      this.value = 0;
      const f1 = () => {
        setTimeout(() => {
          if (this.value < 100) {
            this.value = this.value + 1;
            f1();
          }
        }, 10);
      };
      f1();
    },
  },
};
</script>
<style lang="scss">
  .demo-box{
    width: 350px;
    margin-left: 20px;
    display: inline-block;
    margin-bottom: 40px;
  }
  .demo-progress-small {
    width: 250px;
  }
</style>

```

#### doc/pie.vue

```vue
<template>
  <div style="display: flex;">
    <div class="demo-pie">
      <div style="margin-bottom: 8px;">默认</div>
      <mtd-progress :percentage="72" type="pie" :width="16" />
    </div>
    <div class="demo-pie">
      <div style="margin-bottom: 8px;">完成</div>
      <mtd-progress :percentage="value1" :status="status1" type="pie" :width="16" />
      <br />
      <mtd-button style="margin-top: 24px;" @click="handleClick1">预览效果</mtd-button>
    </div>
    <div class="demo-pie">
      <div style="margin-bottom: 8px;">失败</div>
      <mtd-progress :percentage="value2" :status="status2" type="pie" :width="16" />
      <br />
      <mtd-button style="margin-top: 24px;" @click="handleClick2">预览效果</mtd-button>
    </div>
  </div>
</template>
<script>
export default {
  data () {
    return {
      avtiveTab: 'round',
      value1: 80,
      value2: 70,
      status1: 'success',
      status2: 'error',
    };
  },
  methods: {
    handleClick1 () {
      this.value1 = 0;
      this.status1 = '';
      const f1 = () => {
        setTimeout(() => {
          if (this.value1 < 100) {
            this.value1 = this.value1 + 1;
            f1();
          } else {
            this.status1 = 'success';
          }
        }, 20);
      };
      f1();
    },
    handleClick2 () {
      this.value2 = 0;
      this.status2 = '';
      const f1 = () => {
        setTimeout(() => {
          if (this.value2 < 50) {
            this.value2 = this.value2 + 1;
            f1();
          } else {
            this.status2 = 'error';
          }
        }, 20);
      };
      f1();
    },
  },
};
</script>
<style lang="scss">
  .demo-pie {
    display: inline-block;
    width: 80px;
    font-size: 12px;
    margin: 0 40px; 
    text-align: center;
    color:rgba(0,0,0,0.9);
  }
</style>

```

#### doc/size.vue

```vue
<template>
  <div class="demo-box source" style="width: 100%; margin-left: 12px; overflow: hidden;">
    <mtd-tabs v-model="avtiveTab" size="small" type="text">
      <mtd-tab-pane label="条形" value="round">
        <div class="demo-progress-show-row" style="margin: 100px 0;" v-if="avtiveTab === 'round'">
          <mtd-progress :percentage="65" :stroke-width="4" size="small" class="demo-progress-small" />
          <mtd-progress :percentage="65" class="demo-progress-small" />
          <mtd-progress :percentage="65" :stroke-width="8" size="large" class="demo-progress-small" />
        </div>
      </mtd-tab-pane>
      <mtd-tab-pane label="环形" value="circle">
        <div class="demo-progress-show-row" style="margin: 100px 0;" v-if="avtiveTab === 'circle'">
          <mtd-progress :percentage="65" type="circle" size="small" :stroke-width="3" />
          <mtd-progress :percentage="65" type="circle" :stroke-width="4" />
          <mtd-progress :percentage="65" type="circle" size="large" :stroke-width="6" />
        </div>
      </mtd-tab-pane>
    </mtd-tabs>
  </div>
</template>

<script>
export default {
  name: 'Size',
  data() {
    return {
      avtiveTab: 'round',
      tabTypes: ['round', 'circle']
    }
  },
}

</script>

<style scoped lang="scss">
::v-deep {
  .demo-source {
    padding: 0 0 40px 0;
  }
  .mtd-tabs-nav {
    width: 100%;
    box-sizing: border-box;    
    border-bottom: 1px solid #edf0f7;
    padding: 1.5px 16px;
  }
}
  .demo-box{
    display: inline-block;
  }
  .demo-progress-width {
    width: 250px;
  }
  .demo-progress-show-row{
    text-align: left;
    display: flex;
    align-items: center;
    justify-content: space-around;

    margin-bottom: 16px;
  }
</style>

```

#### doc/status.vue

```vue
<template>
  <div class="demo-full-width" style="overflow: hidden;">
    <mtd-tabs type="text" v-model="avtiveTab" size="small">
      <mtd-tab-pane label="条形" value="round">
        <div style="display: flex; justify-content: space-around; margin: 100px 0;">
          <div style="width: 260px;">
            <mtd-progress :percentage="value" :status="status" />
          </div>
          <div style="width: 260px; text-align: left;">
            <mtd-progress :percentage="value1" :status="status1" />
            <mtd-button style="margin-top: 24px;" @click="handleClick1">预览效果</mtd-button>
          </div>
          <br>
          <div style="width: 260px; text-align: left;">
            <mtd-progress :percentage="value2" :status="status2" />
            <mtd-button style="margin-top: 24px;" @click="handleClick2">预览效果</mtd-button>
          </div>
        </div>
      </mtd-tab-pane>
      <mtd-tab-pane label="环形" value="circle">
        <div style="display: flex; justify-content: center; margin: 100px 0;">
          <div style="width: 180px;">
            <mtd-progress type="circle" :percentage="value" :status="status" />
          </div>
          <div style="width: 180px; text-align: left;">
            <mtd-progress type="circle" :percentage="value1" :status="status1" />
            <br />
            <mtd-button style="margin-top: 24px;" @click="handleClick1">预览效果</mtd-button>
          </div>
          <br>
          <div style="width: 180px; text-align: left;">
            <mtd-progress type="circle" :percentage="value2" :status="status2" />
            <br />
            <mtd-button style="margin-top: 24px;" @click="handleClick2">预览效果</mtd-button>
          </div>
        </div>
      </mtd-tab-pane>
    </mtd-tabs>
  </div>    

    
</template>

<script>
export default {
  data () {
    return {
      avtiveTab: 'round',
      value: 30,
      value1: 100,
      value2: 70,
      status: '',
      status1: 'success',
      status2: 'error',
    };
  },
  methods: {
    handleClick1 () {
      this.value1 = 0;
      this.status1 = '';
      const f1 = () => {
        setTimeout(() => {
          if (this.value1 < 100) {
            this.value1 = this.value1 + 1;
            f1();
          } else {
            this.status1 = 'success';
          }
        }, 20);
      };
      f1();
    },
    handleClick2 () {
      this.value2 = 0;
      this.status2 = '';
      const f1 = () => {
        setTimeout(() => {
          if (this.value2 < 50) {
            this.value2 = this.value2 + 1;
            f1();
          } else {
            this.status2 = 'error';
          }
        }, 20);
      };
      f1();
    },
  },
};
</script>
<style lang="scss" scoped>

::v-deep {
  .demo-source {
    padding: 0 0 40px 0;
  }
  .mtd-tabs-nav {
    width: 100%;
    box-sizing: border-box;    
    border-bottom: 1px solid #edf0f7;
    padding: 1.5px 16px;
  }
}

</style>

```

#### doc/step.vue

```vue
<template>
    <div class="demo-full-width">
      <div class="demo-step-progress-box">
        <mtd-progress :percentage="value1" :show-rate="false"/>
        <mtd-progress :percentage="value2" :show-rate="false"/>
        <mtd-progress :percentage="value3" :show-rate="false"/>
        <mtd-progress :percentage="value4" :show-rate="false"/>
        {{`${parseInt(value)}%`}}
      </div>
      <br/><br/>
      <mtd-button @click="handleClick1">预览效果</mtd-button>
    </div>
</template>

<script>
export default {
  data () {
    return {
      value: 0,
      value1: 0,
      value2: 0,
      value3: 0,
      value4: 0,
    };
  },
  watch: {
    value(v){
      this.value1 = this.createInterval(v,0)
      this.value2 = this.createInterval(v,100)
      this.value3 = this.createInterval(v,200)
      this.value4 = this.createInterval(v,300)
    }
  },
  methods: {
    handleClick1 () {
      this.value = 0;
      const f1 = () => {
        setTimeout(() => {
          if (this.value < 100) {
            this.value = this.value + 1;
            f1();
          }
        }, 100);
      };
      f1();
    },
    createInterval (v,num) {
      const val = v * 4 - num 
      return val >= 0 ? (val > 100  ? 100 : val) : 0 
    }
  },
};
</script>
<style lang="scss">
  .demo-box{
    width: 600px;
    margin-left: 20px;
    display: inline-block;
    margin-bottom: 40px;
  }

  .demo-step-progress-box{
    display: flex;
    justify-content: center;
    .mtd-progress{
      width: 20%;
      margin-right: 8px;
    }
  }
</style>
```

#### doc/steps.vue

```vue
<template>
  <div class="demo-box" style="width: 440px;">
    <div style="display: flex; justify-content: space-between; margin: 84px 0;">
      <div style="width: 260px;">
        <mtd-progress 
          type="steps"
          size="small"
          :steps="5"
          :percentage="value"
          class="progress-base"
          :status="status"
        />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data () {
    return {
      value: 61
    };
  },
  methods: {
    handleClick () {
      this.value = 0;
      const f1 = () => {
        setTimeout(() => {
          if (this.value < 100) {
            this.value = this.value + 5;
            f1();
          } else {
            this.value = 100;
          }
        }, 100);
      };
      f1();
    },
  },
  computed: {
    status() {
      return this.value === 100 ? 'success' : undefined;
    }
  }
};
</script>
<style lang="scss">
  .demo-box{
    width: 440px ;
    margin-left: 20px;
    display: inline-block;
    margin-bottom: 40px;
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  .demo-progress-small {
    width: 250px;
  }
</style>

```

#### index.md

```markdown
# 进度条 / Progress

进度条用于任务运行时间较长时，展示当前进度和状态。

## 互动演示
:::include(src="./demo/progress-interactive.vue")
:::

## 代码演示
### 基础类型
最基础、常见的是条形进度条，环形进度条适用于横向空间受限、需要更强的视觉效果的情况。
:::include(src="./doc/base.vue")
:::

### 尺寸
进度条支持小、中、大三种尺寸。
:::include(src="./doc/size.vue")
:::

### 状态
进度条状态有默认、完成、失败三种。
:::include(src="./doc/status.vue")
:::

### 微型进度条
适用于空间局限、且仍需要展示进度的情况，Hover可显示进度。
:::include(src="./doc/pie.vue")
:::

### 步骤进度条
适用于分阶段运行、且需要展示此区分的情况。
:::include(src="./doc/steps.vue")
:::


### 自定义颜色
:::include(src="./doc/color.vue")
:::



## API
<api-doc name="Progress" :doc="require('./api.json')"></api-doc>

```

#### index.ts

```typescript
import Progress from './progress'
import { withInstall } from '@ss/mtd-adapter'
export default withInstall(Progress)

```

#### line.tsx

```tsx
import {
  computed,
  defineComponent,
} from '@ss/mtd-adapter'
import useConfig from '@hooks/config'

export default defineComponent({
  name: 'MtdLineProgress',
  inheritAttrs: true,
  props: {
    color: {
      type: String,
    },
    strokeWidth: {
      type: Number,
      default: 6,
    },
    percentage: {
      type: Number,
      default: 0,
      required: true,
      validator: (val: number): boolean => val >= 0 && val <= 100,
    },
    strokeLinecap: {
      type: String,
    },
  },
  setup() {
    const config = useConfig()
    const prefix = computed(() => config.getPrefixCls('progress'))
    return {
      prefix,
    }
  },
  render() {
    const {
      prefix, strokeWidth, percentage, color, strokeLinecap,
    } = this
    return <div
      class={[`${prefix}-bar`, `${prefix}-stroke-linecap-${strokeLinecap}`]}
      style={{ height: `${strokeWidth}px` }}
    >
      <div class={`${prefix}-bar-outer`}>
        <div
          class={`${prefix}-bar-inner`}
          style={{
            width: `${percentage}%`,
            backgroundColor: color,
          }}
        />
      </div>
    </div >
  },
})

```

#### pie.tsx

```tsx
import {
  computed,
  defineComponent,
  PropType,
} from '@ss/mtd-adapter'
import useConfig from '@hooks/config'
import { ProgressStatus } from './types'
import MtdIcon from '@components/icon'
import MtdTooltip from '@components/tooltip'

const colorMap = {
  default: {
    bgc: '#f0f6ff',
    color: '#166FF7',
  },
  success: {
    bgc: '#00ba73',
    color: '#edfaf4',
  },
  error: {
    bgc: '#fff2f0',
    color: '#f5483b',
  },
}


export default defineComponent({
  name: 'MtdPieProgress',
  components: {
    MtdIcon,
    MtdTooltip,
  },
  props: {
    color: {
      type: String,
    },
    status: {
      type: String as PropType<ProgressStatus>,
    },
    width: {
      type: Number,
      default: 26,
    },
    percentage: {
      type: Number,
      default: 0,
      required: true,
      validator: (val: number): boolean => val >= 0 && val <= 100,
    },
  },
  setup(props) {
    const config = useConfig()
    const prefix = computed(() => config.getPrefixCls('progress'))

    const pieStyle = computed(() => {
      const colorObj = props.status ? colorMap[props.status] : colorMap['default']
      const colorDeg = Math.floor(props.percentage * 360 / 100)
      return {
        ['background-image']: `conic-gradient(
          ${colorObj.color} ${colorDeg}deg, 
          ${colorObj.bgc} ${colorDeg}deg
        )`,
        width: props.width + 'px',
        height: props.width + 'px',
      }
    })
    return {
      prefix,
      pieStyle,
    }
  },
  render() {
    const {
      prefix, status, percentage, pieStyle, width,
    } = this
    return <mtd-tooltip content={percentage + '%'} placement="top">
      {status === 'success'
        ? <mtd-icon
          name={'check'}
          class={`${prefix}-icon`}
          style={{
            ['font-size']: width + 'px',
            ['font-weight']: 1000,
          }}
        />
        : <div
          class={{
            [prefix + '-pie']: true,
            [prefix + '-' + status]: status,
          }}
          style={pieStyle}
        />
      }
    </mtd-tooltip>
  },
})


```

#### progress.tsx

```tsx
import {
  computed,
  defineComponent,
  PropType,
  classNames, styles,
  useResetAttrs,
  getSlotsInRender,
} from '@ss/mtd-adapter'
import useConfig from '@hooks/config'

import MtdLine from './line'
import MtdCircle from './circle'
import MtdPie from './pie'
import MtdStep from './step'

import MtdIcon from '@components/icon'

import { ProgressType, ProgressStatus, ProgressSize, IconFontSizeEnum, FontSizeEnum, WidthSizeEnum, StrokeWidthSizeEnum  } from './types'

export default defineComponent({
  name: 'MtdProgress',
  components: {
    MtdLine,
    MtdCircle,
    MtdPie,
    MtdIcon,
    MtdStep,
  },
  inheritAttrs: false,
  props: {
    type: {
      type: String as PropType<ProgressType>,
      default: 'line',
      validator: (val: string): boolean => ['line', 'circle', 'pie', 'steps'].indexOf(val) > -1,
    },
    size: {
      type: String as PropType<ProgressSize>,
      default: 'normal',
      validator: (val: string): boolean => ['small', 'normal', 'large'].indexOf(val) > -1,
    },
    steps: {
      type: Number,
      default: 4,
    },
    status: {
      type: String as PropType<ProgressStatus>,
    },
    percentage: {
      type: Number,
      default: 0,
      validator: (val: number) => val >= 0 && val <= 100,
    },
    showInfo: {
      type: Boolean,
      default: true,
    },
    strokeLinecap: {
      type: String,
      default: 'round',
    },
    strokeWidth: {
      type: Number,
    },
    width: {
      type: Number,
    },
    color: {
      type: String,
    },
  },
  emits:[],
  slots: ['percentage'],
  setup(props, { attrs }) {
    const config = useConfig()
    const prefix = computed(() => config.getPrefixCls('progress'))
    const tag = computed(() => {
      switch (props.type) {
        case 'circle':
          return MtdCircle
        case 'pie':
          return MtdPie
        case 'steps':
          return MtdStep
        default:
          return MtdLine
      }
    })
    const icon = computed(() => {
      if (props.type === 'line') {
        if (props.status === 'success') {
          return 'success-circle'
        } else if (props.status === 'error') {
          return 'error-circle'
        }
      } else if (props.type === 'circle') {
        if (props.status === 'success') {
          return 'check'
        } else if (props.status === 'error') {
          return 'close'
        }
      }
      return undefined
    })



    const m_fontSize = computed(() => {

      switch (props.size) {
        case 'small': return FontSizeEnum.SMALL
        case 'large': return FontSizeEnum.LARGE
        case 'normal': {
          if (props.strokeWidth) { // 这里主要是做好向前兼容（默认设置了StrokeWidth的情况与线上保持一致）
            return props.type === 'line' ? (Math.min(props.strokeWidth * 2 + 2, 16)) + 'px' : '1em'
          }
          return FontSizeEnum.NORMAL
        }
        default: {
          return FontSizeEnum.NORMAL
        } 
      }
    })


    const m_width = computed(() => {
      if (props.width) return props.width

      switch (props.size) {
        case 'small': return WidthSizeEnum.SMALL
        case 'large': return WidthSizeEnum.LARGE
        default: {
          return WidthSizeEnum.NORMAL
        } 
      }
    })

    const iconFontSize = computed(() => {
      if (props.type === 'circle') return m_width.value / 2
      switch (props.size) {
        case 'small': return IconFontSizeEnum.SMALL
        case 'large': return IconFontSizeEnum.LARGE
        default: {
          return IconFontSizeEnum.NORMAL
        } 
      }
    })

    const m_strokeWidth = computed(() => {
      if (props.strokeWidth) {
        return props.strokeWidth
      }

      switch (props.size) {
        case 'small': {
          if (props.type === 'line' || props.type === 'steps') return StrokeWidthSizeEnum.SMALL_LINE
          else return StrokeWidthSizeEnum.SMALL_CIRCLE
        }
        case 'large': {
          if (props.type === 'line' || props.type === 'steps') return StrokeWidthSizeEnum.LARGE_LINE
          else return StrokeWidthSizeEnum.LARGE_CIRCLE
        }
        default: {
          if (props.type === 'line' || props.type === 'steps') return StrokeWidthSizeEnum.NORMAL_LINE
          else return StrokeWidthSizeEnum.NORMAL_CIRCLE
        }
      }
    })

    const resetAttrs = useResetAttrs(attrs)

    return {
      prefix,
      tag,
      icon,
      resetAttrs,
      iconFontSize,
      m_fontSize,
      m_width,
      m_strokeWidth,
    }
  },
  render() {
    const {
      prefix, status, type, tag, percentage, icon, resetAttrs, showInfo, m_fontSize,
      strokeLinecap, iconFontSize, width, color, steps, size, m_strokeWidth, m_width,
    } = this
    const Component = tag as any


    const getStyle = () => {
      const style: { [key: string]: string | number } = {}

      if (color) {
        style.color = color
      }

      if (['line', 'steps'].includes(type)) {
        style.height = `${iconFontSize}px`
      }

      return style
    }

    return <div
      class={classNames(this, {
        [prefix]: true,
        [`${prefix}-${status}`]: status,
        [`${prefix}-${type}`]: type,
        [`${prefix}-hide-rate`]: !showInfo,
        [`${prefix}-size-${size}`]: size,
      })}
      style={styles(this, getStyle())}
    >
      <Component
        {...resetAttrs}
        strokeLinecap={strokeLinecap}
        strokeWidth={m_strokeWidth}
        percentage={percentage}
        status={status}
        showInfo={!getSlotsInRender(this, 'percentage') && showInfo} // 历史实现比较蠢，组件内部和外部有俩percentage，新增了一个showInfo变量用来控制组件内部的percentage展示，这里外部有percentage内部的就不用再显示了
        steps={steps}
        color={color}
        width={m_width}
        hasIcon={Boolean(icon)}
      />
      {(showInfo && !(type === 'pie'))
        ? <div class={`${prefix}-percentage`}>
          {getSlotsInRender(this, 'percentage') || (icon
            ? (<mtd-icon
              name={icon}
              class={`${prefix}-icon`}
              style={{
                ['font-size']: iconFontSize + 'px',
              }}
            />)
            : (<span
              class={`${prefix}-text`}
              style={`font-size: ${m_fontSize}px`}
            >
              {['line', 'steps'].includes(type) ? `${percentage}%` : ''}
            </span>))
          }
        </div>
        : ''
      }
    </div>
  },
})


```

#### step.tsx

```tsx
import {
  VNode,
  computed,
  defineComponent,
} from '@ss/mtd-adapter'
import useConfig from '@hooks/config'

export default defineComponent({
  name: 'MtdStepProgress',
  inheritAttrs: true,
  props: {
    steps: {
      type: Number,
      default: 0,
    },
    color: {
      type: String,
    },
    strokeWidth: {
      type: Number,
      default: 6,
    },
    percentage: {
      type: Number,
      default: 0,
      required: true,
      validator: (val: number): boolean => val >= 0 && val <= 100,
    },
    strokeLinecap: {
      type: String,
    },
  },
  setup(props) {
    const config = useConfig()
    const prefix = computed(() => config.getPrefixCls('progress'))

    const currentStep = computed(() => Math.ceil(props.steps * props.percentage * 0.01))

    return {
      prefix,
      currentStep,
    }
  },
  render() {
    const {
      prefix, strokeWidth, percentage, color, strokeLinecap, currentStep, steps,
    } = this


    const renderSteps = () => {
      const children: VNode[] = []

      for (let i = 0; i < steps; i++) {
        const isFillBar = currentStep > 0 && i < currentStep
        children.push(
          <div
            class={[`${prefix}-steps-bar-item`, `${prefix}-steps-stroke-linecap-${strokeLinecap}`, isFillBar ? `${prefix}-steps-bar-item-active` : '' ]}
            style={{
              backgroundColor: (isFillBar && color) ? color : undefined,
              opacity: (i + 1) / steps,
            }}
          />
        )
      }

      return (
        <div class={`${prefix}-steps-bar-outer`}>
          {children}
        </div>
      )
    }

    return <div
      class={[`${prefix}-steps-container`]}
      style={{ height: `${strokeWidth}px` }}
    >
      {renderSteps()}
    </div >
  },
})

```

#### types.ts

```typescript
export type ProgressType = 'line' | 'circle' | 'pie' | 'steps';

export type ProgressStatus = 'success' | 'error';
export type ProgressSize = 'small' | 'normal' | 'large';


export enum FontSizeEnum {
  SMALL = 12,
  NORMAL = 14,
  LARGE = 16,
}

export enum IconFontSizeEnum {
  SMALL = 16,
  NORMAL = 20,
  LARGE = 24,
}

export enum WidthSizeEnum {
  SMALL = 48,
  NORMAL = 80,
  LARGE = 116,
}

export enum StrokeWidthSizeEnum {
  SMALL_LINE = 4,
  SMALL_CIRCLE = 3,
  NORMAL_LINE = 6,
  NORMAL_CIRCLE = 5,
  LARGE_LINE = 8,
  LARGE_CIRCLE = 7,
}
```

