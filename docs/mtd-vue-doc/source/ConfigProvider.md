## 源码解析

### 文件依赖

- config-provider.tsx
- config.ts
- doc/base.vue
- doc/i18n.vue
- hooks.ts
- index.md
- index.ts
- tests/config-provider.spec.jsx

### 源码内容

#### config-provider.tsx

```tsx
import {
  defineComponent,
  PropType,
  getSlotsInRender,
  computed,
} from '@ss/mtd-adapter'

import {
  useConfigProvider,
} from './hooks'

// skip undefined prop merged and return new object.
function combine(option1: any, option2: any) { // eslint-disable-line
  const option = { ...option1 }
  Object.keys(option2).forEach((key) => {
    const value = option2[key]
    if (value !== undefined) {
      option[key] = value
    }
  })
  return option
}

export default defineComponent({
  name: 'MtdConfigProvider',
  props: {
    prefixCls: String,
    iconPrefixCls: String,
    locale: Object,
    tag: {
      type: String,
      default: 'div',
    },
    getPopupContainer: Function as PropType<() => HTMLElement>,
    componentsPrefixCls: String,
  },
  setup(props) {
    const config = useConfigProvider(props)
    const prefix = computed(() => config.getPrefixCls('config-provider'))
    return { prefix }
  },
  render() {
    const { tag, prefix } = this

    return (
      <div is={tag} class={prefix}>
        {getSlotsInRender(this)}
      </div>
    )
  },
})

```

#### config.ts

```typescript
import zhCN from '@components/locale/lang/zh-cn'
type ComponentPublicInstance = any; // eslint-disable-line
export const CONFIG_PROVIDER = 'MTD_NEXT_Config'

let zIndex = 2000
const stack: ComponentPublicInstance[] = [] // 实例栈

export function getPopupContainer(): HTMLElement {
  return document.body
}
const DEFAULT_CONFIG = {
  prefixCls: 'mtd',
  iconPrefixCls: 'mtdicon',

  locale: zhCN,

  componentsPrefixCls: 'Mtd',

  getPopupContainer,

  getNextZIndex() {
    return zIndex++
  },
  addPopup(instance: ComponentPublicInstance) {
    if (stack.indexOf(instance) === -1) {
      stack.push(instance)
    }
  },
  removePopup(instance: ComponentPublicInstance) {
    const index = stack.lastIndexOf(instance)
    if (index > -1) {
      stack.splice(index, 1)
    }
  },
  getLastPopup() {
    if (stack.length > 0) {
      return stack[stack.length - 1]
    }
  },
}

export type Config = typeof DEFAULT_CONFIG

export function getConfig() {
  return DEFAULT_CONFIG
}

export function getPrefix() {
  return DEFAULT_CONFIG.prefixCls
}

export function getIconPrefix() {
  return DEFAULT_CONFIG.iconPrefixCls
}

export function getLocale() {
  return DEFAULT_CONFIG.locale
}

export function getPrefixCls(suffixCls: string, customizePrefixCls?: string) {
  return customizePrefixCls || `${DEFAULT_CONFIG.prefixCls}-${suffixCls}`
}

export function getIconCls(suffixCls: string, customizePrefixCls?: string) {
  const { iconPrefixCls } = DEFAULT_CONFIG
  return suffixCls?.includes(`${iconPrefixCls}-`)
    ? suffixCls
    : customizePrefixCls || `${iconPrefixCls} ${iconPrefixCls}-${suffixCls || ''}`
}

export function getComponentsPrefix() {
  return DEFAULT_CONFIG.componentsPrefixCls
}

export function getComponentsPrefixName(name: string) {
  return `${DEFAULT_CONFIG.componentsPrefixCls}${name}`
}

export function config(options: any) { // eslint-disable-line
  return Object.assign(DEFAULT_CONFIG, options)
}

```

#### doc/base.vue

```vue
<template>
  <mtd-config-provider prefix-cls="hello">
    <mtd-button class="world">你好</mtd-button>
  </mtd-config-provider>
</template>

```

#### doc/i18n.vue

```vue
<template>
  <mtd-config-provider :locale="lang[localeName]">
    <div class="demo-props-option">
      <span>请选择语言：</span>
      <mtd-select v-model="localeName" @change="handleSelect">
        <mtd-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
      </mtd-select>
    </div>
    <div :key="localeName">
      <div class="demo-item">
        <div>
          <p>精确到天</p>
          <mtd-date-picker type="daterange" v-model="values1" :placeholder="['开始时间', '结束时间']" style="width: 240px;" />
        </div><br>
        <div>
          <p>精确到时间</p>
          <mtd-date-picker type="datetimerange" v-model="values2" :placeholder="['开始时间', '结束时间']" style="width: 360px;" />
        </div><br>
        <div>
          <p>精确到月</p>
          <mtd-date-picker type="monthrange" v-model="values3" :placeholder="['开始时间', '结束时间']" style="width: 240px;" />
        </div><br>
        <div>
          <p>精确到周</p>
          <mtd-date-picker type="weekrange" v-model="values4" :placeholder="['开始时间', '结束时间']" style="width: 240px;" />
        </div><br>
        <div>
          <p>精确到季度</p>
          <mtd-date-picker type="quarterrange" v-model="values5" :placeholder="['开始时间', '结束时间']" style="width: 240px;"
            format="yyyy年第Q季度" />
        </div><br>
        <div>
          <p>精确到半年</p>
          <mtd-date-picker type="halfyearrange" v-model="values6" :placeholder="['开始时间', '结束时间']" style="width: 240px;"
            format="yyyy年G" />
        </div><br>
        <div>
          <p>精确到年</p>
          <mtd-date-picker type="yearrange" v-model="values7" :placeholder="['开始时间', '结束时间']" style="width: 240px;" />
        </div><br>
        <div>
          <p>选择周</p>
          <mtd-date-picker type="week" v-model="values8" placeholder="选择时间" style="width: 240px;" />
        </div><br>
      </div>
      <div class="demo-item">
        <p>表格</p>
        <mtd-table :data="[]">
          <mtd-table-column prop="date" label="日期" width="180" />
          <mtd-table-column prop="name" label="姓名" width="180" />
          <mtd-table-column prop="address" label="地址" />
        </mtd-table>
      </div>
      <div class="demo-item">
        <p>分页</p>
        <mtd-pagination size="small" :total="600" show-quick-jumper show-size-changer show-total
          :current-page.sync="currentPageMore" v-model:current-page="currentPageMore" :page-size.sync="currentPageSize"
          v-model:page-size="currentPageSize" />
      </div>
      <div class="demo-item">
        <mtd-loading />
      </div>
    </div>
  </mtd-config-provider>
</template>
<script>
import zhCN from '@components/locale/lang/zh-cn'
import zhTW from '@components/locale/lang/zh-tw'
import ja from '@components/locale/lang/ja'
import ko from '@components/locale/lang/ko'
import th from '@components/locale/lang/th'
import en from '@components/locale/lang/en'

export default {
  data() {
    return {
      values1: [],
      values2: [],
      values3: [],
      values4: [],
      values5: [],
      values6: [],
      values7: [],
      values8: '',
      currentPageMore: 1,
      currentPageSize: 10,
      options: [
        {
          value: 'zhCN',
          label: "简体中文",
        },
        {
          value: 'zhTW',
          label: "繁体中文",
        },
        {
          value: 'en',
          label: "英语",
        },
        {
          value: 'ja',
          label: "日文",
        },
        {
          value: 'ko',
          label: "韩文",
        },
        {
          value: 'th',
          label: "泰文",
        },

      ],
      value1: '',
      localeName: localStorage.lang || 'zhCN',
      lang: {
        zhCN,
        zhTW,
        ja,
        ko,
        th,
        en,
      },
    };
  },
  methods: {
    handleSelect(localeName) {
      localStorage.lang = localeName
    },
  },
};
</script>

```

#### hooks.ts

```typescript
import { useLocale } from '@components/locale'
import {
  getPrefix,
  getPrefixCls,
  getIconCls,
  getIconPrefix,
  getLocale,
  getPopupContainer,
  getConfig,
  Config,
  getComponentsPrefix,
  getComponentsPrefixName,
} from './config'
import {
  provide,
  inject,
  computed,
  ComputedRef,
} from '@ss/mtd-adapter'
import { Language } from '@components/locale/types'

const CONFIG_PROVIDER = 'MTD_NEXT_Config'

// skip undefined prop merged and return new object.
function combine(option1: any, option2: any) { // eslint-disable-line
  const option = { ...option1 }
  Object.keys(option2).forEach((key) => {
    const value = option2[key]
    if (value !== undefined) {
      option[key] = value
    }
  })
  return option
}

interface ConfigContextOptions {
  prefixCls?: string
  iconPrefixCls?: string
  getPopupContainer?: () => HTMLElement
}

export interface ConfigContext {
  options: ComputedRef<ConfigContextOptions>
  getPrefix(): string
  getPrefixCls(suffixCls: string): string
  getIconCls(suffixCls?: string): string
  getIconPrefix(): string
  getPopupContainer(): HTMLElement
  getLocale(): Language
  t(path: string, option?: Record<string, any>): string
  getComponentsPrefix(): string
  getComponentsPrefixName(suffixCls: string): string
}

export function useConfig(): ConfigContext {
  const defaultOptions = computed(() => {
    return getConfig()
  })

  const { t } = useLocale(getLocale())
  const config = inject<ConfigContext>(CONFIG_PROVIDER, {
    options: defaultOptions,
    getPrefix,
    getPrefixCls,
    getIconCls,
    getPopupContainer,
    getIconPrefix,
    getLocale,
    t,
    getComponentsPrefix,
    getComponentsPrefixName,
  })

  return config
}

export function useConfigProvider(opt: ConfigContextOptions) {
  const parentConfig = useConfig()

  const options = computed<Config>(() => {
    return combine(parentConfig.options.value, opt)
  })

  const prefix = computed<string>(() => {
    return options.value.prefixCls
  })

  const iconPrefix = computed<string>(() => {
    return options.value.iconPrefixCls
  })

  const locale = computed<Language>(() => {
    return options.value.locale
  })

  const componentsPrefix = computed<string>(() => {
    return options.value.componentsPrefixCls
  })

  const getPrefixCls = (suffixCls: string) => {
    return `${prefix.value}-${suffixCls}`
  }
  const getPrefix = () => {
    return prefix.value
  }

  const getLocale = () => {
    return locale.value
  }

  const getComponentsPrefix = () => {
    return componentsPrefix.value
  }

  const getComponentsPrefixName = (name: string) => {
    return `${componentsPrefix.value}${name}`
  }

  const t = (path: string, options?: Record<string, any>): string => {
    const { t: translate } = useLocale(getLocale())
    return translate(path, options)
  }

  const getIconCls = (suffixCls?: string) => {
    return suffixCls ? `${iconPrefix.value} ${iconPrefix.value}-${suffixCls}` : iconPrefix.value
  }

  const context: ConfigContext = {
    options: options,
    getPrefix,
    getPrefixCls,
    getIconCls,
    getIconPrefix,
    getLocale,
    getPopupContainer: options.value.getPopupContainer,
    t,
    getComponentsPrefix,
    getComponentsPrefixName,
  }

  provide<ConfigContext>(CONFIG_PROVIDER, context)
  return context
}

export default useConfig

```

#### index.md

```markdown
# 全局化配置 / ConfigProvider

<div class="doc-warning">
受 Vue 组件要求必须有唯一根节点限制，以组件的方式使用时会创建一个 <code>div</code> 节点，在 Vue3.x 版本将会移除该节点。
</div>

:::include(src="./doc/base.vue")
:::

## 使用
### 组件
`ConfigProvider` 使用 `Vue` 提供的 [provider/inject](https://cn.vuejs.org/v2/api/#provide-inject)，只需要在应用中包裹即可生效。
```html
<template>
    <mtd-config-provider prefix-cls="xx">
      <App />
    </mtd-config-provider>
</template>
```
<br>

### 全局方法
你可以通过 `全局方法 config` 来直接调用组件。
**注： 方法参数不同于组件属性，需要使用驼峰式写法，如：`prefixCls`**
```js
import  MTUI from '@ss/mtd-vue2';
MTUI.config({ prefixCls: 'xx' });
```
组件实例上的方法如：`this.$mtd.message`、`this.$mtd.notify`、`this.$mtd.confirm` 则必须通过上述方式来全局设置。

<br>

### 完整示例
针对 `prefixCls`、 `iconPrefixCls` 等配置项，需要结合 `scss` 来配合使用，示例如下：

如果你是vite 项目，针对$icon-font-path的配置会有所不同，[请看这里](https://github.com/vitejs/vite/issues/382)

**index.js** 文件
```js
// 也可采用组件形式
import MTUI from '@ss/mtd-vue2';
import './index.scss'; // 引入样式，无需在引入 mtd 的 css 文件
MTUI.config({ prefixCls: 'xx', iconPrefixCls: 'xxicon' });
```
**index.scss** 文件
```scss
$prefix: 'mtdu'; // 修改统一样式前缀
$icon-prefix: 'mtduicon';
$icon-font-family: 'mtduicon'; // 修改图标的 font-family 属性名
$icon-font-path: '~@ss/mtd-vue2/src/theme-chalk/fonts'; // ⚠️⚠️⚠️必填必填必填必填必填必填必填！！！！！！！！！！！！！！！！
@import '@ss/mtd-vue2/src/theme-chalk/index.scss'
```

### 国际化

:::include(src="./doc/i18n.vue")
:::


## API
<api-doc :doc="require('./api.json')"></api-doc>

```

#### index.ts

```typescript
import ConfigProvider from './config-provider'
import { withInstall } from '@ss/mtd-adapter'
import useConfig, { ConfigContext as Context } from './hooks'
import { config } from './config'

export type ConfigContext = Context

export { useConfig, config }
export default withInstall(ConfigProvider)

```

#### tests/config-provider.spec.jsx

```jsx
import { mount } from '@tests/utils';
import Button from '@components/button'
import Icon from '@components/icon'
import { getConfig } from '../config'
import ConfigProvider, { useConfig } from '../index';


describe('ConfigProvider', function () {
  it('未使用 ConfigProvider 时采用默认值', function () {
    const wrapper = mount({
      render () {
        return <Button ref="button">xxx</Button>
      }
    })
    const button = wrapper.findComponent(Button)
    expect(button.exists()).toBe(true)
    expect(button.vm.prefix).toBe(`${getConfig().prefixCls}-btn`)
  })

  it('使用 ConfigProvider 配置', function () {
    const wrapper = mount({
      render () {
        return (
          <ConfigProvider prefixCls="xxx">
            <Button ref="button">xxx</Button>
          </ConfigProvider>
        )
      }
    })
    const button = wrapper.findComponent(Button)
    expect(button.exists()).toBe(true)
    expect(button.vm.prefix).toBe('xxx-btn')
  })

  it('嵌套 ConfigProvider 时，应当使用最接近的选项', function () {
    const wrapper = mount({
      render () {
        return (
          <ConfigProvider prefixCls="xxx">
            <ConfigProvider prefixCls="yyy">
              <Button ref="button">xxx</Button>
            </ConfigProvider>
          </ConfigProvider>
        )
      }
    })
    const button = wrapper.findComponent(Button)
    expect(button.exists()).toBe(true)
    expect(button.vm.prefix).toBe('yyy-btn')
  })

  it('嵌套 ConfigProvider 时，应当合并配置', function () {
    const wrapper = mount({
      setup () {
        const config = useConfig();
        return config
      },
      render () {
        return (
          <ConfigProvider prefixCls="xxx" icon-prefix-cls="xxx-icon">
            <ConfigProvider prefixCls="yyy">
              <Button ref="button">xxx</Button>
              <Icon name="test" />
            </ConfigProvider>
          </ConfigProvider>
        )
      }
    })
    const button = wrapper.findComponent(Button)
    expect(button.classes()).toContain('yyy-btn')

    const icon = wrapper.findComponent(Icon)
    expect(icon.classes()).toContain('xxx-icon-test')
  })
})

```

