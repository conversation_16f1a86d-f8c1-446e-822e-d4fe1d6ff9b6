## 源码解析

### 文件依赖

- container.tsx
- doc/base.vue
- index.md
- index.ts

### 源码内容

#### container.tsx

```tsx
import {
  defineComponent,
  computed,
  getSlotsInRender,
  getComponentName,
} from '@ss/mtd-adapter'

import { VNode } from 'vue'

import useConfig from '@hooks/config'
export default defineComponent({
  name: 'MtdContainer',
  inheritAttrs: true,
  props: {
    direction: String,
  },
  emits: [],
  setup(props) {
    const config = useConfig()
    const prefix = computed(() => config.getPrefixCls('container'))

    function getIsVertical(children: VNode[]) {
      if (props.direction === 'vertical') {
        return true
      } else if (props.direction === 'horizontal') {
        return false
      }
      return children.some((vnode) => {
        const tag = getComponentName(vnode as any)
        return tag === 'MtdHeader' || tag === 'MtdFooter'
      })
    }
    return {
      prefix, getIsVertical,
    }
  },
  render() {
    const { prefix } = this
    const children = getSlotsInRender(this) || []
    const isVertical = this.getIsVertical(children as any[])
    return <section class={[prefix, { [`${prefix}-vertical`]: isVertical }]}>
      {children}
    </section>
  },
})

```

#### doc/base.vue

```vue
<template>
  <div class="demo-full-width">
    <mtd-container direction="vertical">
      <mtd-header>Header</mtd-header>
      <mtd-main>Main</mtd-main>
    </mtd-container>
    <br />
    <mtd-container>
      <mtd-header height="100px" style="line-height: 100px;">Header</mtd-header>
      <mtd-main>Main</mtd-main>
      <mtd-footer height="100px" style="line-height: 100px;">Footer</mtd-footer>
    </mtd-container>
    <br />
    <mtd-container direction="horizontal">
      <mtd-aside width="200px">Aside</mtd-aside>
      <mtd-main>Main</mtd-main>
    </mtd-container>
    <br />
    <mtd-container>
      <mtd-header>Header</mtd-header>
      <mtd-container>
        <mtd-aside width="200px">Aside</mtd-aside>
        <mtd-main>Main</mtd-main>
      </mtd-container>
    </mtd-container>
    <br />
    <mtd-container>
      <mtd-header>Header</mtd-header>
      <mtd-container>
        <mtd-aside width="200px">Aside</mtd-aside>
        <mtd-container>
          <mtd-main>Main</mtd-main>
          <mtd-footer>Footer</mtd-footer>
        </mtd-container>
      </mtd-container>
    </mtd-container>
    <br />
    <mtd-container>
      <mtd-aside width="200px">Aside</mtd-aside>
      <mtd-container>
        <mtd-header>Header</mtd-header>
        <mtd-main>Main</mtd-main>
      </mtd-container>
    </mtd-container>
    <br />
    <mtd-container>
      <mtd-aside width="200px">Aside</mtd-aside>
      <mtd-container>
        <mtd-header>Header</mtd-header>
        <mtd-main>Main</mtd-main>
        <mtd-footer>Footer</mtd-footer>
      </mtd-container>
    </mtd-container>
  </div>
</template>
<style scoped>
.mtd-header,
.mtd-footer {
  background-color: #b3c0d1;
  color: #333;
  text-align: center;
  line-height: 60px;
}

.mtd-aside {
  background-color: #d3dce6;
  color: #333;
  text-align: center;
  line-height: 200px;
}

.mtd-main {
  background-color: #e9eef3;
  color: #333;
  text-align: center;
  line-height: 160px;
}

.mtd-container:nth-child(5) .mtd-aside,
.mtd-container:nth-child(6) .mtd-aside {
  line-height: 260px;
}

.mtd-container:nth-child(7) .mtd-aside {
  line-height: 320px;
}
</style>

```

#### index.md

```markdown
# 布局容器 / Layout

`<mtd-container>`：外层容器。当子元素中包含 `<mtd-header>` 或 `<mtd-footer>` 时，全部子元素会垂直上下排列，否则会水平左右排列。

`<mtd-header>`：顶栏容器。

`<mtd-aside>`：侧边栏容器。

`<mtd-main>`：主要区域容器。

`<mtd-footer>`：底栏容器。
## 常见页面布局
:::include(src="./doc/base.vue")
:::

## API
<api-doc name="Container" :doc="require('./api.json')"></api-doc>
<api-doc name="Header" :doc="require('./../header/api.json')"></api-doc>
<api-doc name="Aside" :doc="require('./../aside/api.json')"></api-doc>
<api-doc name="Main" :doc="require('./../main/api.json')"></api-doc>
<api-doc name="Footer" :doc="require('./../footer/api.json')"></api-doc>

```

#### index.ts

```typescript
import Container from './container'
import { withInstall } from '@ss/mtd-adapter'
export default withInstall(Container)

```

