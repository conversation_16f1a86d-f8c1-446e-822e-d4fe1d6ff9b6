## 源码解析

### 文件依赖

- demo/radio-interactive.vue
- doc/base.vue
- doc/change-card.vue
- doc/radio-button-size.vue
- doc/radio-button-status.vue
- doc/radio-button.vue
- doc/size1.vue
- doc/status.vue
- doc/text.vue
- index.md
- index.ts
- props.ts
- radio.tsx
- tests/__snapshots__/radio.spec.jsx.snap
- tests/radio.spec.jsx
- types.ts

### 源码内容

#### demo/radio-interactive.vue

```vue

<template>
  <div>
    <mtd-doc-usage name="radio" :apiJson="apiJson" :usageJson="usageJson" @propChange="propChange">
      <template #radio="{ configProps }">
        <div class="slot-container">
          <mtd-radio v-bind="configProps">北京</mtd-radio>
        </div>
      </template>
    </mtd-doc-usage>
  </div>
</template>
<script>
import apiJson from '../api.json'

export default {
  data() {
    return {
      value: 'beijing',
      apiJson: apiJson,
      usageJson: {
        componentName: "radio",
        desc: "单选框",
        codeTemplate: "<mtd-radio v-bind='configProps'></mtd-radio>"
      }
    }
  },
  methods: {
    propChange(propertyName, val) {
      console.log('prop changed: , propertyName: ' + propertyName + ', val: ' + val)
    }
  }
}
</script>
<style lang="scss" scoped>
  
</style>
  
```

#### doc/base.vue

```vue
<template>
  <div class="demo-flex">
    <mtd-tabs v-model="activeTab" type="text" size="small">
      <mtd-tab-pane label="横向" value="row">
        <div style="margin: 80px 0;">
          <mtd-radio-group v-model="value1" @change="t" size="normal">
            <mtd-radio value="beijing">北京</mtd-radio>
            <mtd-radio value="shanghai">上海</mtd-radio>
          </mtd-radio-group>
        </div>
      </mtd-tab-pane>
      <div style="margin: 80px 0;">
      <mtd-tab-pane label="纵向" value="column">
        <mtd-radio-group v-model="value2" size="normal" class="demo-radio-group-vertical">
          <mtd-radio value="beijing">北京</mtd-radio>
          <mtd-radio value="shanghai">上海</mtd-radio>
        </mtd-radio-group>
      </mtd-tab-pane>
      </div>
    </mtd-tabs>
  </div>
</template>
<script>
export default {
  data() {
    return {
      activeTab: "row",
      value1: "beijing",
      value2: "beijing"
    };
  },
  methods: {
    t(v) {
      console.log("change", v, this.value);
    },
  },
};
</script>
<style lang="scss" scoped>
.demo-radio-group-vertical{
  display: inline-flex;
  flex-direction: column;
  
  .mtd-radio-normal{
    margin-bottom: 8px;
  }
}


::v-deep {
  .mtd-tabs-nav {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    border-bottom: 1px solid #edf0f7;
    padding: 1.5px 16px;
  }
}
</style>

```

#### doc/change-card.vue

```vue
<template>
  <div class="example-list-item">
    <mtd-radio-group v-model="value4">
      <mtd-radio-button value="beijing">北京</mtd-radio-button>
      <mtd-radio-button value="shanghai">上海</mtd-radio-button>
      <mtd-radio-button value="shenzhen">深圳</mtd-radio-button>
    </mtd-radio-group>
  </div>
</template>

<script>
export default {
  data() {
    return {
      value4: 'beijing',
    };
  },
};
</script>

```

#### doc/radio-button-size.vue

```vue
<template>
  <div class="demo-flex">
    <mtd-tabs v-model="activeTab" type="text" size="small">
      <mtd-tab-pane label="线型" value="line">
        <div style="display: flex;justify-content: space-around; margin: 80px 0;">
          <div style="margin: 0 36px;">
            <mtd-radio-group v-model="value1" type="line" size="small">
              <mtd-radio-button value="beijing" name="beijing">北京</mtd-radio-button>
              <mtd-radio-button value="shanghai">上海</mtd-radio-button>
              <mtd-radio-button value="guangzhou">广州</mtd-radio-button>
              <mtd-radio-button value="shenzhen">深圳</mtd-radio-button>
            </mtd-radio-group>
          </div>
          <div style="margin: 0 36px;">
            <mtd-radio-group v-model="value1" type="line" size="normal">
              <mtd-radio-button value="beijing">北京</mtd-radio-button>
              <mtd-radio-button value="shanghai">上海</mtd-radio-button>
              <mtd-radio-button value="guangzhou">广州</mtd-radio-button>
              <mtd-radio-button value="shenzhen">深圳</mtd-radio-button>
            </mtd-radio-group>
          </div>
          <div style="margin: 0 36px;">
            <mtd-radio-group v-model="value1" type="line" size="large">
              <mtd-radio-button value="beijing">北京</mtd-radio-button>
              <mtd-radio-button value="shanghai">上海</mtd-radio-button>
              <mtd-radio-button value="guangzhou">广州</mtd-radio-button>
              <mtd-radio-button value="shenzhen">深圳</mtd-radio-button>
            </mtd-radio-group>
          </div>
        </div>
      </mtd-tab-pane>
      <mtd-tab-pane label="面型" value="fill">
        <div style="display: flex;justify-content: space-around; margin: 80px 0;">
          <div style="margin: 0 36px;">
            <mtd-radio-group v-model="value1" type="fill" size="small">
              <mtd-radio-button value="beijing">北京</mtd-radio-button>
              <mtd-radio-button value="shanghai">上海</mtd-radio-button>
              <mtd-radio-button value="guangzhou">广州</mtd-radio-button>
              <mtd-radio-button value="shenzhen">深圳</mtd-radio-button>
            </mtd-radio-group>
          </div>
          <div style="margin: 0 36px;">
            <mtd-radio-group v-model="value1" type="fill" size="normal">
              <mtd-radio-button value="beijing">北京</mtd-radio-button>
              <mtd-radio-button value="shanghai">上海</mtd-radio-button>
              <mtd-radio-button value="guangzhou">广州</mtd-radio-button>
              <mtd-radio-button value="shenzhen">深圳</mtd-radio-button>
            </mtd-radio-group>
          </div>
          <div style="margin: 0 36px;">
            <mtd-radio-group v-model="value1" type="fill" size="large">
              <mtd-radio-button value="beijing">北京</mtd-radio-button>
              <mtd-radio-button value="shanghai">上海</mtd-radio-button>
              <mtd-radio-button value="guangzhou">广州</mtd-radio-button>
              <mtd-radio-button value="shenzhen">深圳</mtd-radio-button>
            </mtd-radio-group>
          </div>
        </div>
      </mtd-tab-pane>
      <mtd-tab-pane label="滑块" value="slider">
        <div style="display: flex;justify-content: space-around; margin: 80px 0;">
          <div style="margin: 0 36px;">
            <mtd-radio-group v-model="value1" type="slider" size="small">
              <mtd-radio-button value="beijing">北京</mtd-radio-button>
              <mtd-radio-button value="shanghai">上海</mtd-radio-button>
              <mtd-radio-button value="guangzhou">广州</mtd-radio-button>
              <mtd-radio-button value="shenzhen">深圳</mtd-radio-button>
            </mtd-radio-group>
          </div>
          <div style="margin: 0 36px;">
            <mtd-radio-group v-model="value1" type="slider" size="normal">
              <mtd-radio-button value="beijing">北京</mtd-radio-button>
              <mtd-radio-button value="shanghai">上海</mtd-radio-button>
              <mtd-radio-button value="guangzhou">广州</mtd-radio-button>
              <mtd-radio-button value="shenzhen">深圳</mtd-radio-button>
            </mtd-radio-group>
          </div>
          <div style="margin: 0 36px;">
            <mtd-radio-group v-model="value1" type="slider" size="large">
              <mtd-radio-button value="beijing">北京</mtd-radio-button>
              <mtd-radio-button value="shanghai">上海</mtd-radio-button>
              <mtd-radio-button value="guangzhou">广州</mtd-radio-button>
              <mtd-radio-button value="shenzhen">深圳</mtd-radio-button>
            </mtd-radio-group>
          </div>
        </div>
      </mtd-tab-pane>
    </mtd-tabs>
  </div>
</template>
<script>
export default {
  data() {
    return {
      activeTab: "line",
      value1: "beijing"
    };
  },
  methods: {
    t(v) {
      console.log("change", v, this.value);
    },
  },
};
</script>
<style lang="scss" scoped>
.demo-radio-group-vertical{
  display: inline-flex;
  flex-direction: column;
  
  .mtd-radio-normal{
    margin-bottom: 8px;
  }
}


::v-deep {
  .mtd-tabs-nav {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    border-bottom: 1px solid #edf0f7;
    padding: 1.5px 16px;
  }
}
</style>

```

#### doc/radio-button-status.vue

```vue
<template>
  <div class="demo-flex">
    <mtd-tabs v-model="activeTab" type="text" size="small">
      <mtd-tab-pane label="线型" value="line">
        <div style="display: flex;justify-content: space-around; margin: 80px 0;">
          <div style="margin: 0 36px;">
            <p style="text-align: left;">默认</p>
            <mtd-radio-group v-model="value1" type="line" @input="handleInputChange">
              <mtd-radio-button value="beijing">北京</mtd-radio-button>
              <mtd-radio-button value="shanghai">上海</mtd-radio-button>
              <mtd-radio-button value="guangzhou">广州</mtd-radio-button>
              <mtd-radio-button value="shenzhen">深圳</mtd-radio-button>
            </mtd-radio-group>
          </div>
          <div style="margin: 0 36px;">
            <p style="text-align: left;">部分禁用</p>
            <mtd-radio-group v-model="value1" type="line">
              <mtd-radio-button value="beijing">北京</mtd-radio-button>
              <mtd-radio-button value="shanghai">上海</mtd-radio-button>
              <mtd-radio-button value="guangzhou">广州</mtd-radio-button>
              <mtd-radio-button value="shenzhen" disabled>深圳</mtd-radio-button>
            </mtd-radio-group>
          </div>
          <div style="margin: 0 36px;">
            <p style="text-align: left;">全部禁用</p>
            <mtd-radio-group v-model="value1" type="line" disabled>
              <mtd-radio-button value="beijing">北京</mtd-radio-button>
              <mtd-radio-button value="shanghai">上海</mtd-radio-button>
              <mtd-radio-button value="guangzhou">广州</mtd-radio-button>
              <mtd-radio-button value="shenzhen">深圳</mtd-radio-button>
            </mtd-radio-group>
          </div>
        </div>
      </mtd-tab-pane>
      <mtd-tab-pane label="面型" value="fill">
        <div style="display: flex;justify-content: space-around; margin: 80px 0;">
          <div style="margin: 0 36px;">
            <p style="text-align: left;">默认</p>
            <mtd-radio-group v-model="value1" type="fill">
              <mtd-radio-button value="beijing">北京</mtd-radio-button>
              <mtd-radio-button value="shanghai">上海</mtd-radio-button>
              <mtd-radio-button value="guangzhou">广州</mtd-radio-button>
              <mtd-radio-button value="shenzhen">深圳</mtd-radio-button>
            </mtd-radio-group>
          </div>
          <div style="margin: 0 36px;">
            <p style="text-align: left;">部分禁用</p>
            <mtd-radio-group v-model="value1" type="fill">
              <mtd-radio-button value="beijing">北京</mtd-radio-button>
              <mtd-radio-button value="shanghai">上海</mtd-radio-button>
              <mtd-radio-button value="guangzhou">广州</mtd-radio-button>
              <mtd-radio-button value="shenzhen" disabled>深圳</mtd-radio-button>
            </mtd-radio-group>
          </div>
          <div style="margin: 0 36px;">
            <p style="text-align: left;">全部禁用</p>
            <mtd-radio-group v-model="value1" type="fill" disabled>
              <mtd-radio-button value="beijing">北京</mtd-radio-button>
              <mtd-radio-button value="shanghai">上海</mtd-radio-button>
              <mtd-radio-button value="guangzhou">广州</mtd-radio-button>
              <mtd-radio-button value="shenzhen">深圳</mtd-radio-button>
            </mtd-radio-group>
          </div>
        </div>
      </mtd-tab-pane>
      <mtd-tab-pane label="滑块" value="slider">
        <div style="display: flex;justify-content: space-around; margin: 80px 0;">
          <div style="margin: 0 36px;">
            <p style="text-align: left;">默认</p>
            <mtd-radio-group v-model="value1" type="slider">
              <mtd-radio-button value="beijing">北京</mtd-radio-button>
              <mtd-radio-button value="shanghai">上海</mtd-radio-button>
              <mtd-radio-button value="guangzhou">广州</mtd-radio-button>
              <mtd-radio-button value="shenzhen">深圳</mtd-radio-button>
            </mtd-radio-group>
          </div>
          <div style="margin: 0 36px;">
            <p style="text-align: left;">部分禁用</p>
            <mtd-radio-group v-model="value1" type="slider">
              <mtd-radio-button value="beijing">北京</mtd-radio-button>
              <mtd-radio-button value="shanghai">上海</mtd-radio-button>
              <mtd-radio-button value="guangzhou">广州</mtd-radio-button>
              <mtd-radio-button value="shenzhen" disabled>深圳</mtd-radio-button>
            </mtd-radio-group>
          </div>
          <div style="margin: 0 36px;">
            <p style="text-align: left;">全部禁用</p>
            <mtd-radio-group v-model="value1" type="slider" disabled>
              <mtd-radio-button value="beijing">北京</mtd-radio-button>
              <mtd-radio-button value="shanghai">上海</mtd-radio-button>
              <mtd-radio-button value="guangzhou">广州</mtd-radio-button>
              <mtd-radio-button value="shenzhen">深圳</mtd-radio-button>
            </mtd-radio-group>
          </div>
        </div>
      </mtd-tab-pane>
    </mtd-tabs>
  </div>
</template>
<script>
export default {
  data() {
    return {
      activeTab: "line",
      value1: "beijing"
    };
  },
  methods: {
    handleInputChange(v) {
      console.log("input");
    }
  },
};
</script>
<style lang="scss" scoped>
.demo-radio-group-vertical{
  display: inline-flex;
  flex-direction: column;
  
  .mtd-radio-normal{
    margin-bottom: 8px;
  }
}


::v-deep {
  .mtd-tabs-nav {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    border-bottom: 1px solid #edf0f7;
    padding: 1.5px 16px;
  }
}
</style>

```

#### doc/radio-button.vue

```vue
<template>
  <div class="demo-full-width" style="display: flex;justify-content: space-around;">
    <div>
      <p style="text-align: left;">线型</p>
      <mtd-radio-group v-model="value1" type="line" :size="size">
        <mtd-radio-button value="beijing">北京</mtd-radio-button>
        <mtd-radio-button value="shanghai">上海</mtd-radio-button>
        <mtd-radio-button value="guangzhou">广州</mtd-radio-button>
        <mtd-radio-button value="shenzhen">深圳</mtd-radio-button>
      </mtd-radio-group>
    </div>
    <div>
      <p style="text-align: left;">面型</p>
      <mtd-radio-group v-model="value2" type="fill" :size="size">
        <mtd-radio-button value="beijing">北京</mtd-radio-button>
        <mtd-radio-button value="shanghai">上海</mtd-radio-button>
        <mtd-radio-button value="guangzhou">广州</mtd-radio-button>
        <mtd-radio-button value="shenzhen">深圳</mtd-radio-button>
      </mtd-radio-group>
    </div>
    <div>
      <p style="text-align: left;">滑块型</p>
      <mtd-radio-group v-model="value3" type="slider" :size="size">
        <mtd-radio-button value="beijing">北京</mtd-radio-button>
        <mtd-radio-button value="shanghai">上海</mtd-radio-button>
        <mtd-radio-button value="guangzhou">广州</mtd-radio-button>
        <mtd-radio-button value="shenzhen">深圳</mtd-radio-button>
      </mtd-radio-group>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      value1: 'beijing',
      value2: 'beijing',
      value3: 'beijing',
      size: '',
    };
  },
};
</script>

```

#### doc/size1.vue

```vue
<template>
  <div class="demo-flex">

    <div style="text-align:left; margin: 100px 0;">
      <mtd-radio-group v-model="value" size="small" @change="radioChange">
        <mtd-radio value="beijing" @change="radioChange">北京</mtd-radio>
        <mtd-radio value="shanghai">上海</mtd-radio>
      </mtd-radio-group>
    </div>

    <div style="text-align:left; margin: 100px 0;">
      <mtd-radio-group v-model="value" size="normal">
        <mtd-radio value="beijing">北京</mtd-radio>
        <mtd-radio value="shanghai">上海</mtd-radio>
      </mtd-radio-group>
    </div>

    <div style="text-align:left; margin: 100px 0;">
      <mtd-radio-group v-model="value" size="large">
        <mtd-radio value="beijing">北京</mtd-radio>
        <mtd-radio value="shanghai">上海</mtd-radio>
      </mtd-radio-group>
    </div>
   
  </div>
</template>
<script>
export default {
  data() {
    return {
      value: "beijing",
    };
  },
  methods: {
    t(v) {
      console.log("change", v, this.value);
    },
    radioChange(v) {
      console.log("radioChange", v);
    }
  },
};
</script>
<style lang="scss">
.demo-radio-group-vertical{
  display: inline-flex;
  flex-direction: column;
  
  .mtd-radio{
    margin-bottom: 8px;
  }
}
</style>

```

#### doc/status.vue

```vue
<template>
  <div style="display: flex; text-align: left;">
    <div style="margin: 0 48px;">
      <div style="margin-bottom: 12px;color: rgba(17, 25, 37, 0.65);">未选</div>
      <mtd-radio>北京</mtd-radio>
    </div>
    <div style="margin: 0 48px;">
      <div style="margin-bottom: 12px;color: rgba(17, 25, 37, 0.65);">已选</div>
      <mtd-radio checked>北京</mtd-radio>
    </div>
    <div style="margin: 0 48px;">
      <div style="margin-bottom: 12px;color: rgba(17, 25, 37, 0.65);">未选禁用</div>
      <mtd-radio disabled>北京</mtd-radio>
    </div>
    <div style="margin: 0 48px;">
      <div style="margin-bottom: 12px;color: rgba(17, 25, 37, 0.65);">已选禁用</div>
      <mtd-radio disabled checked>北京</mtd-radio>
    </div>
  </div>
</template>

```

#### doc/text.vue

```vue
<template>
  <div>
    <span class="demo-tags">标签:</span>
    <mtd-radio-group
      v-model="value5"
      size="small"
      class="demo-mtd-radio-only-text"
    >
      <mtd-radio value="beijing">北京</mtd-radio>
      <mtd-radio value="shanghai">上海</mtd-radio>
      <mtd-radio value="guangzhou">广州</mtd-radio>
      <mtd-radio value="shenzhen">深圳</mtd-radio>
      <mtd-radio value="zhuhai">珠海</mtd-radio>
    </mtd-radio-group>
  </div>
</template>

<script>
export default {
  data() {
    return {
      value5: 'beijing',
    };
  },
};
</script>
<style>
.demo-mtd-radio-only-text .mtd-radio-inner {
  display: none;
}
</style>

```

#### index.md

```markdown
<style lang="scss">
  .demo-radio-box {
    display: flex;
    justify-content: space-around;
  }
  .mtd-radio-group {
    line-height: auto;
  }
  .demo-mtd-radio-only-text .mtd-radio-group {
    margin-right: 20px;
    vertical-align: baseline;
  }
  .demo-mtd-radio-only-text .mtd-radio {
    vertical-align: baseline;
    margin-right: 10px;
  }
  .demo-mtd-radio-only-text .mtd-radio-checked,
  .demo-mtd-radio-only-text .mtd-radio:hover {
    color: #4477F0;
  }
  .demo-tags {
    display: inline-block;
    vertical-align: middle;
  }
  .demo-mtd-radio-card {
    display: inline-block;
    margin: 0 25px;
    padding: 12px 16px;
    border: 1px solid #D3D8E4;
    border-radius: 4px;
    cursor:pointer;
  }
  .demo-mtd-radio-card i {
    color: #B5BBD1;
  }
  .demo-mtd-radio-card-title {
    color: #464646;
  }
  .demo-mtd-radio-card-desc {
    color: #ADADAD;
    font-size: 12px;
  }
  .demo-mtd-radio-only-text .mtd-radio,
  .demo-mtd-radio-only-text .mtd-radio:hover,
  .demo-mtd-radio-card,
  .demo-mtd-radio-card i,
  .demo-mtd-radio-card .demo-mtd-radio-card-title,
  .demo-mtd-radio-card:hover,
  .demo-mtd-radio-card:hover i,
  .demo-mtd-radio-card:hover .demo-mtd-radio-card-title {
    transition: all .3s ease-in-out;
  }
</style>

# 单选框 / Radio

单选框是只能选择其中一项的选择框。

## 互动演示
:::include(src="./demo/radio-interactive.vue")
:::

## 代码示例
### 尺寸
单选框支持小、中、大三种尺寸。
:::include(src="./doc/size1.vue")
:::

### 状态
单选框状态有默认状态、悬停状态、激活（点击）状态、禁用状态。
:::include(src="./doc/status.vue")
:::

### 基础单选框组
基础单选框组支持横向和纵向两种布局方式。
:::include(src="./doc/base.vue")
:::


### 选项卡型单选框组
:::include(src="./doc/radio-button.vue")
:::


### 选项卡型尺寸
选项卡型单选框支持小、中、大三种尺寸。
:::include(src="./doc/radio-button-size.vue")
:::

### 选项卡型状态
选项卡型单选框状态有默认状态、悬停状态、激活（点击）状态、部分禁用和全部禁用状态。
:::include(src="./doc/radio-button-status.vue")
:::


<!-- ### 选项卡形式

在类目少、名称短时，需要突出信息时使用。
:::include(src="./doc/change-card.vue")
::: -->

<!-- > 选项卡形式可用作标签页切换，请参考 [标签页/Tabs <i class="mtdicon mtdicon-link-o"></i>](/components/tabs) -->

<!-- ### 单文字

多用于商品展示页品类的筛选。

:::include(src="./doc/text.vue")
::: -->

## API

<api-doc name="Radio" :doc="require('./api.json')"></api-doc>
<api-doc name="RadioButton" :doc="require('../radio-button/api.json')"></api-doc>
<api-doc name="RadioGroup" :doc="require('../radio-group/api.json')"></api-doc>

```

#### index.ts

```typescript
import Radio from './radio'

import { withInstall } from '@ss/mtd-adapter'
export default withInstall(Radio)

```

#### props.ts

```typescript
import { ExtractPropTypes, PropType } from '@ss/mtd-adapter'
import { RadioSizeType } from './types'


export const radioProps = () => ({
  value: {
    type: [String, Number, Boolean, Function, Object, Array],
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  name: String,
  size: {
    type: String as PropType<RadioSizeType>,
    validator: (value: string): boolean => {
      return ['small','large'].indexOf(value) > -1
    },
  },
  label: [String, Number],
  checked: {
    type: Boolean,
    default: undefined,
  },
})

export type RadioProps = Partial<ExtractPropTypes<ReturnType<typeof radioProps>>>

export default radioProps
```

#### radio.tsx

```tsx
import {
  defineComponent,
  computed,
  getSlotsInRender,
} from '@ss/mtd-adapter'
import useConfig from '@hooks/config'
import { useRadioGroup } from '@components/radio-group/useRadioGroup'
import props from './props'

export default defineComponent({
  name: 'MtdRadio',
  inheritAttrs: true,
  model: {
    prop: 'value',
    event: 'update:value',
  },
  props: props(),
  emits: ['input', 'change', 'click'],
  setup(props, ctx) {
    const { emit } = ctx
    const config = useConfig()
    const prefix = computed(() => config.getPrefixCls('radio'))

    const contextProps = useRadioGroup(props, ctx as any)
    const {
      m_checked, m_disabled, m_size, setChecked,
    } = contextProps

    const wrapperCls = computed(() => {
      return [
        prefix.value,
        {
          [`${prefix.value}-checked`]: m_checked.value,
          [`${prefix.value}-disabled`]: m_disabled.value,
          [`${prefix.value}-${m_size.value}`]: m_size.value,
        },
      ]
    })

    const handleClick = ($event: Event) => {
      $event.preventDefault()
      if (m_disabled.value) { return }
      if (!m_checked.value) {
        setChecked(true)
      }
      emit('click', $event)
    }

    return {
      prefix,
      wrapperCls,
      handleClick,
      ...contextProps,
    }
  },
  render() {
    const { prefix, wrapperCls, handleClick, m_checked, m_name } = this
    const { label } = this.$props

    return <label class={wrapperCls} onClick={handleClick}
    ><input
        class={`${prefix}-input`}
        type="radio"
        checked={m_checked}
        name={m_name}
        style="display: none"
      /><span class={`${prefix}-inner`} /><span class={`${prefix}-text`}>
        {getSlotsInRender(this) || label}
      </span>
    </label>
  },
})

```

#### tests/__snapshots__/radio.spec.jsx.snap

```plaintext
// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Radio renders ./src/radio/doc/base.vue correctly 1`] = `<div class="mtd-radio-group"><label class="mtd-radio mtd-radio-checked mtd-radio-normal"><input type="radio" class="mtd-radio-input" style="display: none;"><span class="mtd-radio-inner"></span><span class="mtd-radio-text">北京</span></label> <label class="mtd-radio mtd-radio-normal"><input type="radio" class="mtd-radio-input" style="display: none;"><span class="mtd-radio-inner"></span><span class="mtd-radio-text">上海</span></label></div>`;

exports[`Radio renders ./src/radio/doc/change-card.vue correctly 1`] = `
<div class="example-list-item">
  <div class="mtd-radio-group"><label class="mtd-radio-button mtd-radio-button-checked"><input type="radio" class="mtd-radio-button-input" style="display: none;"><span class="mtd-radio-button-inner">北京</span></label> <label class="mtd-radio-button"><input type="radio" class="mtd-radio-button-input" style="display: none;"><span class="mtd-radio-button-inner">上海</span></label> <label class="mtd-radio-button"><input type="radio" class="mtd-radio-button-input" style="display: none;"><span class="mtd-radio-button-inner">深圳</span></label></div>
</div>
`;

exports[`Radio renders ./src/radio/doc/size.vue correctly 1`] = `
<div class="demo-radio-box source">
  <div class="mtd-radio-group"><label class="mtd-radio mtd-radio-checked mtd-radio-normal"><input type="radio" class="mtd-radio-input" style="display: none;"><span class="mtd-radio-inner"></span><span class="mtd-radio-text">北京</span></label> <label class="mtd-radio mtd-radio-normal"><input type="radio" class="mtd-radio-input" style="display: none;"><span class="mtd-radio-inner"></span><span class="mtd-radio-text">上海</span></label></div>
  <div class="mtd-radio-group"><label class="mtd-radio mtd-radio-checked mtd-radio-normal"><input type="radio" class="mtd-radio-input" style="display: none;"><span class="mtd-radio-inner"></span><span class="mtd-radio-text">北京</span></label> <label class="mtd-radio mtd-radio-normal"><input type="radio" class="mtd-radio-input" style="display: none;"><span class="mtd-radio-inner"></span><span class="mtd-radio-text">上海</span></label></div>
</div>
`;

exports[`Radio renders ./src/radio/doc/status.vue correctly 1`] = `<div><label class="mtd-radio mtd-radio-normal"><input type="radio" class="mtd-radio-input" style="display: none;"><span class="mtd-radio-inner"></span><span class="mtd-radio-text">未选中态</span></label> <label class="mtd-radio mtd-radio-normal" checked="checked"><input type="radio" class="mtd-radio-input" style="display: none;"><span class="mtd-radio-inner"></span><span class="mtd-radio-text">已选中态</span></label> <label class="mtd-radio mtd-radio-disabled mtd-radio-normal"><input type="radio" class="mtd-radio-input" style="display: none;"><span class="mtd-radio-inner"></span><span class="mtd-radio-text">未选中禁用态</span></label> <label class="mtd-radio mtd-radio-disabled mtd-radio-normal" checked="checked"><input type="radio" class="mtd-radio-input" style="display: none;"><span class="mtd-radio-inner"></span><span class="mtd-radio-text">已选中禁用态</span></label></div>`;

exports[`Radio renders ./src/radio/doc/text.vue correctly 1`] = `
<div><span class="demo-tags">标签:</span>
  <div class="demo-mtd-radio-only-text mtd-radio-group"><label class="mtd-radio mtd-radio-checked mtd-radio-normal"><input type="radio" class="mtd-radio-input" style="display: none;"><span class="mtd-radio-inner"></span><span class="mtd-radio-text">北京</span></label> <label class="mtd-radio mtd-radio-normal"><input type="radio" class="mtd-radio-input" style="display: none;"><span class="mtd-radio-inner"></span><span class="mtd-radio-text">上海</span></label> <label class="mtd-radio mtd-radio-normal"><input type="radio" class="mtd-radio-input" style="display: none;"><span class="mtd-radio-inner"></span><span class="mtd-radio-text">广州</span></label> <label class="mtd-radio mtd-radio-normal"><input type="radio" class="mtd-radio-input" style="display: none;"><span class="mtd-radio-inner"></span><span class="mtd-radio-text">深圳</span></label> <label class="mtd-radio mtd-radio-normal"><input type="radio" class="mtd-radio-input" style="display: none;"><span class="mtd-radio-inner"></span><span class="mtd-radio-text">珠海</span></label></div>
</div>
`;

```

#### tests/radio.spec.jsx

```jsx
import Radio from '@components/radio'
import {
  createDemoTest,
  mount,
  triggerClick,
  wait,
  waitImmediate,
} from '@tests/utils';

describe('Radio', function () {
  createDemoTest('radio')
})

```

#### types.ts

```typescript
import Radio from './index'

export type Radio = InstanceType<typeof Radio>
export type RadioSizeType = 'small' | 'large'
```

