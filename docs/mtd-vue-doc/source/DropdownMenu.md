## 源码解析

### 文件依赖

- cascader-menus.tsx
- demo.md
- demo/dropdown-menu-interactive.vue
- demo/usage.json
- doc/base-drag.vue
- doc/base.vue
- doc/classify.vue
- doc/demo/item-disabled.vue
- doc/demo/item-slot.vue
- doc/demo/model-value.vue
- doc/hover.vue
- doc/icon-drag.vue
- doc/long-text.vue
- doc/multi-level.vue
- doc/position.vue
- doc/size.vue
- doc/split-line.vue
- dropdown-menu.tsx
- index.md
- index.ts

### 源码内容

#### cascader-menus.tsx

```tsx
import {
  defineComponent,
  computed,
  ref, classNames, styles,
  getScopedSlotsInRender,
  getSlotsInRender,
  vSlots,
} from '@ss/mtd-adapter'
import useConfig from '@hooks/config'
import MtdCascaderMenus from '@components/cascader-menus'
import { Node } from '@components/cascader/types'
import useDropdownProvide from '@components/dropdown/useProvide'

export default defineComponent({
  name: 'MtdDropdownMenuCascader',
  components: {
    MtdCascaderMenus,
  },
  inheritAttrs: false,
  props: {
    data: Array,
    trigger: {
      type: String, // hover / click
      default: 'hover',
    },
  },
  emits: [
    'select',
    'expanded-change',
  ],
  setup(props, { emit }) {
    const config = useConfig()
    const prefix = computed(() => config.getPrefixCls('cascader-menu'))

    const expandedValue = ref<any[]>([])
    const { injectDropdown } = useDropdownProvide()
    const dropdown = injectDropdown()

    function handleExpandedChange(values: any[], nodes: Node[]) {
      expandedValue.value = values
      emit('expanded-change', values, nodes)
    }

    function handleSelect(values: any[], nodes: Node[]) {
      emit('select', values, nodes)
      dropdown && dropdown.emitter.emit('itemClick')
    }
    return {
      prefix, expandedValue,
      handleExpandedChange, handleSelect,
    }
  },
  render() {
    const { trigger, expandedValue, data } = this
    const $render = getScopedSlotsInRender(this)
    const cascaderMenusScopedSlots = {
      default: $render
        ? (props: { node: Node, data: any }) => $render({ node: props.node, data: props.node.data })
        : (props: { node: Node, data: any }) => <span>{props.node.label}</span>,
      ['addendum-header']: getSlotsInRender(this, 'addendum-header') ? () => getSlotsInRender(this, 'addendum-header') : undefined,
      ['addendum-footer']: getSlotsInRender(this, 'addendum-footer') ? () => getSlotsInRender(this, 'addendum-footer') : undefined,
    }

    return <mtd-cascader-menus
      class={classNames(this)}
      style={styles(this)}
      options={data}
      expanded-value={expandedValue}
      expand-trigger={trigger}
      onExpanded-change={this.handleExpandedChange}
      onSelect={this.handleSelect}
      v-slots={cascaderMenusScopedSlots}
      {...vSlots(cascaderMenusScopedSlots)}
    />
  },
})

```

#### demo.md

```markdown
# 补充 Demo

## 受控
:::include(src="./doc/demo/model-value.vue")
:::


## 下拉选项禁用
:::include(src="./doc/demo/item-disabled.vue")
:::

## 下拉选项插槽
:::include(src="./doc/demo/item-slot.vue")
:::

```

#### demo/dropdown-menu-interactive.vue

```vue
<template>
    <mtd-doc-usage name='Dropdown ' :apiJson="apiJson" :usageJson="usage">
        <template #MtdDropdown="{ configProps }">
            <mtd-dropdown v-bind="configProps">
                <mtd-button type="text-primary" :disabled="configProps.disabled">
                    <span style="margin-right: 4px;">点击触发</span>
                    <i class="mtdicon mtdicon-down-thick" />
                </mtd-button>
                <template #dropdown>
                    <mtd-dropdown-menu>
                        <mtd-dropdown-menu-item>计算</mtd-dropdown-menu-item>
                        <mtd-dropdown-menu-item>网络</mtd-dropdown-menu-item>
                        <mtd-dropdown-menu-item>存储</mtd-dropdown-menu-item>
                        <mtd-dropdown-menu-item>数据库</mtd-dropdown-menu-item>
                    </mtd-dropdown-menu>
                </template>
            </mtd-dropdown>
        </template>
    </mtd-doc-usage>
</template>
<script>
const usage = require('./usage.json')
const apiJson = require('../../dropdown/api.json')
export default {
    data() {
        return {
            usage,
            apiJson
        };
    },
};
</script>
```

#### demo/usage.json

```json
{
    "componentName": "MtdDropdown",
    "desc": "下拉菜单",
    "codeTemplate": "<mtd-dropdown><mtd-dropdown-menu  v-bind='configProps'></mtd-dropdown-menu> </mtd-dropdown>"
}
```

#### doc/base-drag.vue

```vue
<template>
  <div>
    <mtd-dropdown trigger="hover">
      <mtd-button type="text-primary">
        <span style="margin-right: 4px;">产品服务</span>
        <i class="mtdicon mtdicon-down-thick" />
      </mtd-button>
      <template #dropdown>
        <mtd-dropdown-menu>
          <mtd-dropdown-menu-item>计算</mtd-dropdown-menu-item>
          <mtd-dropdown-menu-item>网络</mtd-dropdown-menu-item>
          <mtd-dropdown-menu-item>存储</mtd-dropdown-menu-item>
          <mtd-dropdown-menu-item>数据库</mtd-dropdown-menu-item>
        </mtd-dropdown-menu>
      </template>
    </mtd-dropdown>
  </div>
</template>

```

#### doc/base.vue

```vue
<template>
  <div style="padding: 100px 0">
    <mtd-dropdown show-arrow>
      <mtd-button type="text-primary" class="dropdown-menu-demo-button">
        <span style="margin-right: 4px;">带箭头的下拉菜单</span>
        <i class="mtdicon mtdicon-down-thick" />
      </mtd-button>
      <template #dropdown>
        <mtd-dropdown-menu class="mtd-popper">
          <mtd-dropdown-menu-item>文件大小</mtd-dropdown-menu-item>
          <mtd-dropdown-menu-item selected>文件说明</mtd-dropdown-menu-item>
          <mtd-dropdown-menu-item>文件类型</mtd-dropdown-menu-item>
        </mtd-dropdown-menu>
      </template>
    </mtd-dropdown>
  </div>
</template>

```

#### doc/classify.vue

```vue
<template>
  <div style="padding: 100px 0;">
    <mtd-dropdown trigger="hover">
      <mtd-button type="text-primary">
        <span style="margin-right: 4px;">分组下拉菜单</span>
        <i class="mtdicon mtdicon-down-thick" />
      </mtd-button>
      <template #dropdown>
        <mtd-dropdown-menu style="width: 100px;">
          <mtd-dropdown-menu-item-group header="水果">
            <mtd-dropdown-menu-item> 苹果 </mtd-dropdown-menu-item>
            <mtd-dropdown-menu-item> 香蕉 </mtd-dropdown-menu-item>
          </mtd-dropdown-menu-item-group>

          <mtd-dropdown-menu-item-group header="蔬菜">
            <mtd-dropdown-menu-item> 土豆 </mtd-dropdown-menu-item>
            <mtd-dropdown-menu-item> 辣椒 </mtd-dropdown-menu-item>
            <mtd-dropdown-menu-item> 白菜 </mtd-dropdown-menu-item>
          </mtd-dropdown-menu-item-group>
        </mtd-dropdown-menu>
      </template>
    </mtd-dropdown>
  </div>
</template>
<style lang="scss">
.demo-right-text {
  float: right;
  font-size: 12px;
  color: #ADADAD;
}
</style>

```

#### doc/demo/item-disabled.vue

```vue
<template>
  <div style="padding: 100px 0">
    <div style="margin-bottom: 10px;">
      <mtd-switch v-model="disabled">控制item是否禁用</mtd-switch>
    </div>

    <mtd-dropdown>
      <mtd-button type="text-primary" class="dropdown-menu-demo-button">
        <span style="margin-right: 4px;">下拉菜单</span>
        <i class="mtdicon mtdicon-down-thick" />
      </mtd-button>
      <template #dropdown>
        <mtd-dropdown-menu class="mtd-popper">
          <mtd-dropdown-menu-item :disabled="disabled">文件大小</mtd-dropdown-menu-item>
          <mtd-dropdown-menu-item selected>文件说明</mtd-dropdown-menu-item>
          <mtd-dropdown-menu-item>文件类型</mtd-dropdown-menu-item>
        </mtd-dropdown-menu>
      </template>
    </mtd-dropdown>
  </div>
</template>
<script>
export default {
  data() {
    return {
      disabled: false
    }
  }
}
</script>

```

#### doc/demo/item-slot.vue

```vue
<template>
  <div style="padding: 100px 0">
    <mtd-dropdown>
      <mtd-button type="text-primary" class="dropdown-menu-demo-button">
        <span style="margin-right: 4px;">下拉菜单</span>
        <i class="mtdicon mtdicon-down-thick" />
      </mtd-button>
      <template #dropdown>
        <mtd-dropdown-menu class="mtd-popper">
          <mtd-dropdown-menu-item>
            <template #icon>
              <mtd-icon name="calculator-o" />
            </template>
            计算
            <template #description>
              <span class="mtd-dropdown-menu-item-right-description">计算补充说明</span>
            </template>
          </mtd-dropdown-menu-item>
          <mtd-dropdown-menu-item>
            <template #description>
              <span class="mtd-dropdown-menu-item-right-description">网络补充说明</span>
            </template>
            网络
          </mtd-dropdown-menu-item>
          <mtd-dropdown-menu-item>存储</mtd-dropdown-menu-item>
        </mtd-dropdown-menu>
      </template>
    </mtd-dropdown>
  </div>
</template>

```

#### doc/demo/model-value.vue

```vue
<template>
  <div style="padding: 100px 0">
    <div>
      <mtd-button @click="handeVis">控制下拉菜单</mtd-button>
    </div>
    
    <mtd-dropdown :model-value="visible" @update:modelValue="handeVis">
      <mtd-button type="text-primary" class="dropdown-menu-demo-button">
        <span style="margin-right: 4px;">下拉菜单</span>
        <i class="mtdicon mtdicon-down-thick" />
      </mtd-button>
      <template #dropdown>
        <mtd-dropdown-menu class="mtd-popper">
          <mtd-dropdown-menu-item>文件大小</mtd-dropdown-menu-item>
          <mtd-dropdown-menu-item selected>文件说明</mtd-dropdown-menu-item>
          <mtd-dropdown-menu-item>文件类型</mtd-dropdown-menu-item>
        </mtd-dropdown-menu>
      </template>
    </mtd-dropdown>
  </div>
</template>
<script>
export default {
  data() {
    return {
      visible: false
    }
  },
  methods: {
    handeVis() {
      this.visible = !this.visible
    }
  }
}
</script>

```

#### doc/hover.vue

```vue
<template>
  <div class="dropdown-menu-demo">
    <mtd-dropdown>
      <mtd-button type="text-primary">
        <span style="margin-right: 4px;">点击触发</span>
        <i class="mtdicon mtdicon-down-thick" style="font-size: 16px;" />
      </mtd-button>
      <template #dropdown>
        <mtd-dropdown-menu>
          <mtd-dropdown-menu-item>计算</mtd-dropdown-menu-item>
          <mtd-dropdown-menu-item>网络</mtd-dropdown-menu-item>
          <mtd-dropdown-menu-item>存储</mtd-dropdown-menu-item>
          <mtd-dropdown-menu-item>数据库</mtd-dropdown-menu-item>
        </mtd-dropdown-menu>
      </template>
    </mtd-dropdown>

    <mtd-dropdown trigger="hover">
      <mtd-button type="text-primary" style="margin-right: 40px;">
        <span style="margin-right: 4px;">悬停触发</span>
        <i class="mtdicon mtdicon-down-thick" style="font-size: 16px;" />
      </mtd-button>
      <template #dropdown>
        <mtd-dropdown-menu>
          <mtd-dropdown-menu-item>计算</mtd-dropdown-menu-item>
          <mtd-dropdown-menu-item>网络</mtd-dropdown-menu-item>
          <mtd-dropdown-menu-item>存储</mtd-dropdown-menu-item>
          <mtd-dropdown-menu-item>数据库</mtd-dropdown-menu-item>
        </mtd-dropdown-menu>
      </template>
    </mtd-dropdown>

  </div>
</template>
<style scoped>
.dropdown-menu-demo {
  display: flex;
  justify-content: space-between;
  width: 400px;
  padding: 100px 0;
}
</style>
```

#### doc/icon-drag.vue

```vue
<template>
  <div class="dropdown-menu-demo">
    <mtd-dropdown>
      <mtd-button type="text-primary">
        <span style="margin-right: 4px;">基础下拉</span>
        <i class="mtdicon mtdicon-down-thick" />
      </mtd-button>
      <template #dropdown>
        <mtd-dropdown-menu>
          <mtd-dropdown-menu-item>计算</mtd-dropdown-menu-item>
          <mtd-dropdown-menu-item>网络</mtd-dropdown-menu-item>
          <mtd-dropdown-menu-item>存储</mtd-dropdown-menu-item>
          <mtd-dropdown-menu-item>数据库</mtd-dropdown-menu-item>
        </mtd-dropdown-menu>
      </template>
    </mtd-dropdown>
    <mtd-dropdown>
      <mtd-button type="text-primary">
        <span style="margin-right: 4px;">带图标</span>
        <i class="mtdicon mtdicon-down-thick" />
      </mtd-button>
      <template #dropdown>
        <mtd-dropdown-menu slot="dropdown">
          <mtd-dropdown-menu-item icon="calculator-o">
            计算
          </mtd-dropdown-menu-item>
          <mtd-dropdown-menu-item icon="paste-code-o">
            网络
          </mtd-dropdown-menu-item>
          <mtd-dropdown-menu-item icon="save-o">
            存储
          </mtd-dropdown-menu-item>
          <mtd-dropdown-menu-item icon="database-o">
            数据库
          </mtd-dropdown-menu-item>
        </mtd-dropdown-menu>
      </template>
    </mtd-dropdown>
    <mtd-dropdown>
      <mtd-button type="text-primary">
        <span style="margin-right: 4px;">带图标和辅助说明</span>
        <i class="mtdicon mtdicon-down-thick" />
      </mtd-button>
      <template #dropdown>
        <mtd-dropdown-menu slot="dropdown">
          <mtd-dropdown-menu-item icon="calculator-o" description="我是计算的说明" class="demo-dropdown-menu-item">
            计算
          </mtd-dropdown-menu-item>
          <mtd-dropdown-menu-item icon="paste-code-o" description="我是网络的说明" class="demo-dropdown-menu-item">
            网络
          </mtd-dropdown-menu-item>
          <mtd-dropdown-menu-item icon="save-o" description="我是存储的说明" class="demo-dropdown-menu-item">
            存储
          </mtd-dropdown-menu-item>
          <mtd-dropdown-menu-item icon="database-o" description="我是数据库的说明" class="demo-dropdown-menu-item">
            数据库
          </mtd-dropdown-menu-item>
        </mtd-dropdown-menu>
      </template>
    </mtd-dropdown>
  </div>
</template>
<style lang='scss' scoped>
.dropdown-menu-demo {
  display: flex;
  justify-content: space-between;
  width: 500px;
  padding: 100px 0;
}

.demo-dropdown-menu-item {
  width: 200px;

  .mtdicon {
    color: rgba(0, 0, 0, 0.90);
    margin-right: 8px;
    font-size: 16px;
    vertical-align: -20%;
  }

  .demo-right-text {
    float: right;
    font-size: 12px;
    color: #ADADAD;
  }

}
</style>

```

#### doc/long-text.vue

```vue
<template>
  <div style="padding: 100px 0;">
    <mtd-dropdown popper-class="long-text-demo">
      <mtd-button type="text-primary">
        <span style="margin-right: 4px;">长文本下拉菜单</span>
        <i class="mtdicon mtdicon-down-thick" />
      </mtd-button>
      <template #dropdown>
        <mtd-dropdown-menu>
          <mtd-dropdown-menu-item>
            <mtd-tooltip content="昨夜斗回北，今朝岁起东。" placement="top">
              <div class="verse">昨夜斗回北，今朝岁起东。</div>
            </mtd-tooltip>
          </mtd-dropdown-menu-item>

          <mtd-dropdown-menu-item>
            <mtd-tooltip content="我年已强仕，无禄尚忧农。" placement="top">
              <div class="verse">我年已强仕，无禄尚忧农。</div>
            </mtd-tooltip>
          </mtd-dropdown-menu-item>

          <mtd-dropdown-menu-item>
            <mtd-tooltip content="桑野就耕父，荷锄随牧童。" placement="top">
              <div class="verse">桑野就耕父，荷锄随牧童。</div>
            </mtd-tooltip>
          </mtd-dropdown-menu-item>

          <mtd-dropdown-menu-item>
            <mtd-tooltip content="田家占气候，共说此年丰。" placement="top">
              <div class="verse">田家占气候，共说此年丰。</div>
            </mtd-tooltip>
          </mtd-dropdown-menu-item>
        </mtd-dropdown-menu>
      </template>
    </mtd-dropdown>
  </div>
</template>

<style>
.long-text-demo .verse {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 150px;
}
</style>

```

#### doc/multi-level.vue

```vue
<template>
  <div style="padding: 100px 0;">
    <mtd-dropdown>
      <mtd-button type="text-primary">
        <span style="margin-right: 4px;">菜单类型</span>
        <i class="mtdicon mtdicon-down-thick" />
      </mtd-button>
      <template #dropdown>
        <mtd-dropdown-menu>
          <mtd-dropdown-menu-item icon="folder-fill">菜单1</mtd-dropdown-menu-item>
          <mtd-dropdown-submenu icon="folder-fill" title="菜单2">
            <mtd-dropdown-menu-item icon="folder-fill">菜单2-1</mtd-dropdown-menu-item>
            <mtd-dropdown-submenu title="">
              <template #title>
                <span>菜单2-2</span>
              </template>
              <template #icon>
                <mtd-icon name="folder-fill" />
              </template>
              <mtd-dropdown-menu-item icon="folder-fill">菜单2-2-1</mtd-dropdown-menu-item>
              <mtd-dropdown-menu-item icon="folder-fill">菜单2-2-2</mtd-dropdown-menu-item>
              <mtd-dropdown-menu-item icon="folder-fill">菜单2-2-3</mtd-dropdown-menu-item>
            </mtd-dropdown-submenu>
            <mtd-dropdown-menu-item icon="folder-fill">菜单2-3</mtd-dropdown-menu-item>
          </mtd-dropdown-submenu>
          <mtd-dropdown-menu-item icon="folder-fill">菜单3</mtd-dropdown-menu-item>
        </mtd-dropdown-menu>
      </template>
    </mtd-dropdown>
  </div>
</template>

```

#### doc/position.vue

```vue
<template>
  <div class="dropdown-menu-demo">
    <mtd-dropdown v-for="item in postionList" :placement="item.value" :key="item.value">
      <mtd-button type="text-primary" class="dropdown-menu-demo-button">
        <span style="margin-right: 4px;">{{ item.label }}</span>
        <i class="mtdicon mtdicon-down-thick" />
      </mtd-button>
      <template #dropdown>
        <mtd-dropdown-menu>
          <mtd-dropdown-menu-item>计算</mtd-dropdown-menu-item>
          <mtd-dropdown-menu-item>网络</mtd-dropdown-menu-item>
          <mtd-dropdown-menu-item>存储</mtd-dropdown-menu-item>
          <mtd-dropdown-menu-item>数据库</mtd-dropdown-menu-item>
        </mtd-dropdown-menu>
      </template>
    </mtd-dropdown>
  </div>
</template>

<script>
export default {
  data() {
    return {
      position: 'bottom-start',
      postionList: [
        {
          label: '向上左对齐',
          value: 'top-start'
        },
        {
          label: '向上中对齐',
          value: 'top'
        },
        {
          label: '向上右对齐',
          value: 'top-end'
        },
        {
          label: '向下左对齐',
          value: 'bottom-start'
        },
        {
          label: '向下中对齐',
          value: 'bottom'
        },
        {
          label: '向下右对齐',
          value: 'bottom-end'
        }]
    }
  },
}
</script>
<style scoped lang="scss">
.dropdown-menu-demo {
  display: flex;
  flex-wrap: wrap;
  width: 350px;
  justify-content: space-between;
  padding: 100px 0;

  &-button {
    margin-top: 48px;
  }
}
</style>

```

#### doc/size.vue

```vue
<template>
  <div style="padding: 100px 0;">
    <div class="demo-dropdown-menu-group" style="margin-right: 60px;">
      <mtd-dropdown-menu size="small" class="mtd-popper">
        <mtd-dropdown-menu-item icon="folder-fill">文件大小</mtd-dropdown-menu-item>
        <mtd-dropdown-menu-item icon="folder-fill">文件说明</mtd-dropdown-menu-item>
        <mtd-dropdown-menu-item icon="folder-fill">文件说明</mtd-dropdown-menu-item>
      </mtd-dropdown-menu>
    </div>
    <div class="demo-dropdown-menu-group">
      <mtd-dropdown-menu class="mtd-popper" style="margin-right: 60px;">
        <mtd-dropdown-menu-item icon="folder-fill">文件大小</mtd-dropdown-menu-item>
        <mtd-dropdown-menu-item icon="folder-fill">文件说明</mtd-dropdown-menu-item>
        <mtd-dropdown-menu-item icon="folder-fill">文件说明</mtd-dropdown-menu-item>
      </mtd-dropdown-menu>
    </div>
    <div class="demo-dropdown-menu-group">
      <mtd-dropdown-menu size="large" class="mtd-popper">
        <mtd-dropdown-menu-item icon="folder-fill">文件大小</mtd-dropdown-menu-item>
        <mtd-dropdown-menu-item icon="folder-fill">文件说明</mtd-dropdown-menu-item>
        <mtd-dropdown-menu-item icon="folder-fill">文件说明</mtd-dropdown-menu-item>
      </mtd-dropdown-menu>
    </div>
  </div>
</template>

<style lang='scss'>
.demo-dropdown-menu-group {
  display: inline-block;
  vertical-align: top;
  text-align: left;
}
</style>

```

#### doc/split-line.vue

```vue
<template>
    <div style="padding: 100px 0;">
        <mtd-dropdown>
            <mtd-button type="text-primary">
                <span style="margin-right: 4px;">带分割线下拉菜单</span>
                <i class="mtdicon mtdicon-down-thick" />
            </mtd-button>
            <template #dropdown>
                <mtd-dropdown-menu style="width: 100px;">
                    <mtd-dropdown-menu-item> 苹果 </mtd-dropdown-menu-item>
                    <mtd-dropdown-menu-item> 香蕉 </mtd-dropdown-menu-item>
                    <mtd-dropdown-menu-item> 橘子 </mtd-dropdown-menu-item>
                    <mtd-dropdown-menu-item style="margin: 4px 0;">
                        <div class="split-line"></div>
                    </mtd-dropdown-menu-item>
                    <mtd-dropdown-menu-item disabled> 辣椒 </mtd-dropdown-menu-item>
                </mtd-dropdown-menu>
            </template>
        </mtd-dropdown>
    </div>
</template>
<style lang="scss" scoped>
.demo-right-text {
    float: right;
    font-size: 12px;
    color: #ADADAD;
}

.split-line {
    height: 1px;
    background-color: rgba(17, 25, 37, 0.1);
    position: absolute;
    left: 0;
    width: 100%;
}
</style>

```

#### dropdown-menu.tsx

```tsx
import {
  defineComponent,
  computed,
  getSlotsInRender, classNames, styles,
} from '@ss/mtd-adapter'
import useConfig from '@hooks/config'
import MtdCascaderMenus from './cascader-menus'
import { Node } from '@components/cascader/types'
import useProvider from '@components/dropdown/useProvide'


export default defineComponent({
  name: 'MtdDropdownMenu',
  components: {
    MtdCascaderMenus,
  },
  inheritAttrs: false,
  props: {
    size: {
      type: String,
      required: false,
    },
    model: {
      type: String,
      default: 'normal',
    },
    data: Array,
    expandTrigger: String,
    value: [String, Number, Object],
  },
  emits: [
    'select',
  ],
  setup(props, { emit }) {
    const config = useConfig()
    const prefix = computed(() => config.getPrefixCls('dropdown-menu'))

    const { provideDropdown, injectDropdown } = useProvider()

    provideDropdown({
      props,
      ...injectDropdown() as any,
    })

    function handleSelect(values: any[], nodes: Node[]) {
      emit('select', values, nodes)
    }

    return {
      prefix, handleSelect,
    }
  },
  render() {
    const { prefix, size, model, data, expandTrigger } = this
    const renderFun = () => {
      switch (model) {
        case 'cascader':
          return <mtd-cascader-menus
            class={classNames(this, `${prefix}-cascader`)}
            style={styles(this)}
            data={data}
            trigger={expandTrigger}
            onSelect={this.handleSelect}
          />
        default:
          return <ul
            class={classNames(this, {
              [prefix]: true,
              [`${prefix}-${size}`]: size,
            })}
            style={styles(this)}
          >
            {getSlotsInRender(this)}
          </ul>
      }
    }

    return renderFun()
  },
})

```

#### index.md

```markdown
<style lang='scss'>
.demo-dropdown-menu{
  .mtd-dropdown-menu{
    width: 180px;
    display: inline-block;
  }
  .demo-dropdown-menu-group{
    display: inline-block;
    vertical-align: top;
    text-align: left;
  }
}
</style>

# 下拉菜单 / Dropdown
下拉菜单是向下弹出的列表，用于承载过多的操作集合，主要是通过下拉拓展的面板形式，收纳更多的操作元素（由按钮、操作图标或其他控件触发）。
## 互动演示
:::include(src="./demo/dropdown-menu-interactive.vue")
:::
## 代码示例
### 尺寸
下拉菜单支持小、中、大三种尺寸。
:::include(src="./doc/size.vue")
:::

### 触发方式
:::include(src="./doc/hover.vue")
:::

### 位置
:::include(src="./doc/position.vue")
:::

### 带箭头
:::include(src="./doc/base.vue")
:::
 
### 下拉选项元素
下拉选项元素包括文本、图标、辅助说明，其中图标和辅助说明为可选项，可根据实际业务情况选用。
:::include(src="./doc/icon-drag.vue")
:::

### 分组下拉菜单
:::include(src="./doc/classify.vue")
:::

### 带分割线
:::include(src="./doc/split-line.vue")
:::

### 多层级下拉菜单
:::include(src="./doc/multi-level.vue")
:::

### 长文本 
:::include(src="./doc/long-text.vue")
:::

## API

<api-doc name="Dropdown" :doc="require('../dropdown/api.json')"></api-doc>
<api-doc name="DropdownMenu" :doc="require('./api.json')"></api-doc>
<api-doc name="DropdownSubmenu" :doc="require('../dropdown-submenu/api.json')"></api-doc>
<api-doc name="DropdownMenuItem" :doc="require('../dropdown-menu-item/api.json')"></api-doc>

```

#### index.ts

```typescript
import DropdownMenu from './dropdown-menu'
import { withInstall } from '@ss/mtd-adapter'

export default withInstall(DropdownMenu)

```

