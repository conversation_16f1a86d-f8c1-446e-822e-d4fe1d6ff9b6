## 源码解析

### 文件依赖

- demo.md
- demo/skeleton-interactive.vue
- demo/usage.json
- doc/gradually.vue
- doc/skeleton-active.vue
- doc/skeleton.vue
- index.md
- index.ts
- skeleton.tsx

### 源码内容

#### demo.md

```markdown
# 骨架屏 / Skeleton
在数据加载的区域，显示大致的数据结构。

## 暂无补充Demo
```

#### demo/skeleton-interactive.vue

```vue
<template>
  <mtd-doc-usage name='Skeleton' :apiJson="apiJson" :usageJson="usage">
    <template #Skeleton="{ configProps }">
      <div class="user-weibo">
        <mtd-skeleton type="avatar" :size="36" v-bind="configProps" />
        <div class="user-content">
          <mtd-skeleton :size="16" :line-num="1" style="width: 117px;" v-bind="configProps" />
          <mtd-skeleton :size="12" :line-num="1" style="width: 190px;" v-bind="configProps" />
          <br>
          <div style="display:flex">
            <mtd-skeleton :size="110" type="picture" style="margin-right: 8px;" v-bind="configProps" />
            <mtd-skeleton :size="110" type="picture" style="margin-right: 8px;" v-bind="configProps" />
            <mtd-skeleton :size="110" type="picture" v-bind="configProps" />
          </div>
        </div>
      </div>
    </template>
  </mtd-doc-usage>
</template>
<script>
const usage = require('./usage.json')
const apiJson = require('../api.json')

export default {
  data() {
    return {
      usage,
      apiJson,
      value: ''
    };
  },
};
</script>
<style scoped>
.user-weibo {
  padding: 12px;
  display: flex;
  width: 100%;
  height: 200px;
}

.user-content {
  margin-left: 8px;
  width: 100%;
  display: flex;
  flex-direction: column;
}
</style>
  
```

#### demo/usage.json

```json
{
    "componentName": "Skeleton",
    "desc": "骨架屏",
    "codeTemplate": "<mtd-skeleton v-bind='configProps'></mtd-skeleton>"
}
```

#### doc/gradually.vue

```vue
<template>
  <div class="demo-skeleton-wrapper">
    <div v-if="isShow">
      <mtd-skeleton type="avatar" :loading="loading1" style="margin-bottom: 12px;">
        <div style=" width: 36px;  height: 36px;  background: #a36a00;  border-radius: 50%;" />
      </mtd-skeleton>
      <mtd-skeleton :loading="loading2">
        <div>
          <p>骨架屏组件真的很好用很棒的</p>
          <p>骨架屏组件真的很好用很棒的</p>
          <p>骨架屏组件真的很好用很棒的</p>
        </div>
      </mtd-skeleton>
      <mtd-skeleton type="picture" :loading="loading3">
        <div style=" width: 190px;  height: 110px;  background: #4D97FF;  border-radius: 6px;" />
      </mtd-skeleton>
    </div>
    <mtd-button @click="open" style="margin-top:12px; width: 80px;">逐渐加载</mtd-button>
  </div>
</template>

<script>
export default {
  data() {
    return {
      loading1: true,
      loading2: true,
      loading3: true,
      isShow: false,
    };
  },
  methods: {
    open() {
      this.isShow = true
      this.loading1 = true
      this.loading2 = true
      this.loading3 = true
      setTimeout(() => {
        this.loading1 = false
      }, 2000);
      setTimeout(() => {
        this.loading2 = false
      }, 4000);
      setTimeout(() => {
        this.loading3 = false
      }, 6000);
    }
  }
};
</script>

<style scoped lang="scss">
.demo-skeleton-wrapper {
  display: flex;
  flex-wrap: wrap;
  flex-direction: column;
  align-items: center;
}

.demo-skeleton-wrapper>div {
  width: 200px;

  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-direction: column;
}
</style>

```

#### doc/skeleton-active.vue

```vue
<template>
  <div class="demo-skeleton-wrapper">
    <div style="width:500px;">
      <div style="text-align: left; margin-bottom: 24px;">
        <mtd-switch v-model="active" />&nbsp;动画
      </div>
      <div class="user-weibo">
        <mtd-skeleton type="avatar" :size="40" :active="active" />
        <div class="user-content">
          <mtd-skeleton :size="16" :line-num="1" :active="active" />
          <mtd-skeleton :size="10" :line-num="1" :active="active" />
          <br>
          <div style="display:flex">
            <mtd-skeleton :size="14" :line-num="4" :active="active" style="margin-right: 12px" />
            <mtd-skeleton type="picture" :active="active" />
          </div>
        </div>
      </div>

    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      active: true,
    };
  },
};

</script>

<style scoped>
.demo-skeleton-wrapper {
  display: flex;
  justify-content: space-around;
  flex-wrap: wrap;
}

.demo-skeleton-wrapper>div {
  width: 250px;

  display: flex;
  justify-content: space-between;
  /* align-items: center; */
  flex-direction: column;
}

.user-weibo {
  display: flex;
  width: 100%;
  height: 200px;
}

.user-content {
  margin-left: 8px;
  width: 100%;
  display: flex;
  flex-direction: column;
}
</style>

```

#### doc/skeleton.vue

```vue
<template>
  <div class="demo-skeleton-wrapper">
    <div style=" margin-right: 128px;">
      <span style="margin-bottom: 49px;">头像</span>
      <mtd-skeleton type="avatar" />
    </div>
    <div style="width: 190px;  margin-right: 128px;">
      <span style="margin-bottom: 28px;">文字</span>
      <mtd-skeleton :active="false" />
    </div>
    <div style="width: 190px;">
      <span style="margin-bottom: 12px;">图片</span>
      <mtd-skeleton type="picture" />
    </div>
  </div>
</template>

<style scoped>
.demo-skeleton-wrapper {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
}

.demo-skeleton-wrapper>div {
  display: flex;
  align-items: center;
  flex-direction: column;
}
</style>

```

#### index.md

```markdown
# 骨架屏 / Skeleton
在数据加载的区域，显示大致的数据结构。

## 互动演示
:::include(src="./demo/skeleton-interactive.vue")
:::
## 代码示例
### 基础类型
:::include(src="./doc/skeleton.vue")
:::

### 动画
:::include(src="./doc/skeleton-active.vue")
:::

### 逐渐加载
:::include(src="./doc/gradually.vue")
:::

## API
<api-doc name="Skeleton" :doc="require('./api.json')"></api-doc>


```

#### index.ts

```typescript
import Skeleton from './skeleton'
import { withInstall } from '@ss/mtd-adapter'
export default withInstall(Skeleton)

```

#### skeleton.tsx

```tsx
import {
  defineComponent,
  PropType,
  computed,
  hasProp,
  getSlotsInRender, vueInstance,
} from '@ss/mtd-adapter'
import useConfig from '@hooks/config'


type SkeletonType = 'avatar' | 'paragraph' | 'picture'

export default defineComponent({
  name: 'MtdSkeleton',
  inheritAttrs: true,
  props: {
    type: {
      type: String as PropType<SkeletonType>,
      default: 'paragraph',
    },
    active: {
      type: Boolean,
      default: true,
    },
    loading: {
      type: Boolean,
      default: true,
    },
    lineNum: {
      type: Number,
      default: 3,
    },
    size: {
      type: Number,
      default: 16,
    },
  },
  emits: [],
  setup(props) {
    const config = useConfig()
    const prefix = computed(() => config.getPrefixCls('skeleton'))
    const ins = vueInstance()

    const m_size = computed(() => {
      return hasProp(ins, 'size')
        ? props.size
        : props.type === 'avatar' ? 36 : 16
    })

    function renderSkeleton() {
      const className = [
        `${prefix.value}-${props.type}`,
        `${prefix.value}-content`,
        props.active ? `${prefix.value}-active` : '',
      ]
      if (props.type === 'paragraph') {
        return new Array(props.lineNum).fill(0).map((item, index) => <div
          class={className}
          style={{
            marginTop: `${index === 0 ? m_size.value / 2 : 0}px`,
            marginBottom: `${m_size.value}px`,
            height: `${m_size.value}px`,
          }}
        />)
      } else {
        const style: any = {}
        if (props.type === 'avatar') {
          style.height = `${m_size.value}px`
          style.width = `${m_size.value}px`
        }
        return <div class={className} style={style} />
      }
    }
    return {
      prefix, renderSkeleton,
    }
  },
  render() {
    const {
      prefix, type, loading,
    } = this

    return <div class={prefix} style={type === 'paragraph' ? 'width: 100%' : ''}>
      {loading
        ? this.renderSkeleton()
        : getSlotsInRender(this)
      }
    </div>
  },
})


```

