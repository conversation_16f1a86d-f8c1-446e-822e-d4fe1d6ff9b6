## 源码解析

### 文件依赖

- demo.md
- demo/steps-interactive.vue
- doc/base.vue
- doc/demo/label-align.vue
- doc/demo/steps-status.vue
- doc/demo/usage.json
- doc/desc.vue
- doc/direction.vue
- doc/doc-scope.vue
- doc/length-wise-direction.vue
- doc/navigation.vue
- doc/picture.vue
- doc/position.vue
- doc/progress.vue
- doc/scroll.vue
- doc/size.vue
- doc/space.vue
- doc/status.vue
- index.md
- index.ts
- steps.tsx
- types.ts
- useProvide.ts
- useSteps.ts

### 源码内容

#### demo.md

```markdown
# 步骤条 / Steps

## 补充 Demo

### steps-status
:::include(src="./doc/demo/steps-status.vue")
:::

### label-align
:::include(src="./doc/demo/label-align.vue")
:::
```

#### demo/steps-interactive.vue

```vue
<template>
  <div>
    <mtd-doc-usage name="steps" :apiJson="apiJson" :usageJson="usageJson" @propChange="propChange">
      <template slot="steps" slot-scope="{ configProps }">
        <div class="slot-container"> 
          <mtd-steps v-bind="configProps" :active="1">
            <mtd-step title="已完成" description="辅助信息"/>
            <mtd-step title="进行中" description="辅助信息"/>
            <mtd-step title="未开始" description="辅助信息"/>
          </mtd-steps>
        </div>
      </template>
      <template #steps="{ configProps }">
        <div class="slot-container">
          <mtd-steps v-bind="configProps" :active="1">
            <mtd-step title="已完成" description="辅助信息"/>
            <mtd-step title="进行中" description="辅助信息"/>
            <mtd-step title="未开始" description="辅助信息"/>
          </mtd-steps>
        </div>
      </template>
    </mtd-doc-usage>
  </div>
</template>
<script>
import apiJson from '../api.json'
import usageJson from '../doc/demo/usage.json'

export default {
  data() {
    return {
      apiJson: apiJson,
      usageJson: {
        componentName: "steps",
        desc: "步骤条",
        codeTemplate: "<mtd-steps v-bind='configProps'></mtd-steps>"
      }
    }
  },
  methods:{
    propChange(propertyName, val) {
      console.log('prop changed: , propertyName: ' + propertyName + ', val: ' + val)
    },
  },
}
</script>
```

#### doc/base.vue

```vue
<template>
  <div class="demo-full-width">
    <mtd-steps v-model="active" v-model:active="active">
      <mtd-step :title="title(0)" description="辅助信息" />
      <mtd-step :title="title(1)" description="辅助信息" />
      <mtd-step :title="title(2)" description="辅助信息" />
    </mtd-steps>
    <div class="stepTextarea">{{ text[active] }}</div>
    <div style="margin-top: 24px;">
      <mtd-button type="panel" @click="prev" :disabled="Prevdisabled">上一步</mtd-button>
      <mtd-button type="primary" style="margin-left: 24px;" @click="next" :disabled="Nextdisabled">下一步</mtd-button>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      active: 1,
      text: ['步骤 1 内容区', '步骤 2 内容区', '步骤 3 内容区'],
      Nextdisabled: false,
      Prevdisabled: false
    }
  },
  computed: {
    title() {
      return function (num) {
        if (this.active === num) {
          return '进行中'
        } else if (this.active > num) {
          return '已完成'
        } else {
          return '未开始'
        }
      }
    },
  },
  watch: {
    active(newVal) {
      this.Prevdisabled = newVal === 0;
      this.Nextdisabled = newVal === 2;
    }
  },
  methods: {
    next() {
      if (this.active < 2) {
        this.active++;
        this.Nextdisabled = this.active === 2;
        this.Prevdisabled = false;
      }
    },
    prev() {
      if (this.active > 0) {
        this.active--;
        this.Prevdisabled = this.active === 0;
        this.Nextdisabled = false
      }
    }
  },
}
</script>
<style lang="scss" scoped>
.demo-full-width {
  display: flex;
  flex-direction: column;
  align-items: center;

  .mtd-steps {
    width: 100%;
  }

  .stepTextarea {
    width: 622px;
    background: rgba(17, 25, 37, 0.03);
    height: 240px;
    text-align: center;
    line-height: 240px;
    margin-top: 24px;
  }
}
</style>

```

#### doc/demo/label-align.vue

```vue
<template>
  <div class="steps-demo-content">
    <mtd-button style="margin-bottom: 12px;" @click="change">change</mtd-button>
    <mtd-steps direction="horizontal" :label-align="labelAlign">
      <mtd-step title="已完成" description="辅助信息" />
      <mtd-step title="进行中" description="辅助信息" />
      <mtd-step title="未开始" description="辅助信息" />
    </mtd-steps>

  </div>
</template>
<script>
export default {
  data() {
    return {
      labelAlign: 'center'
    }
  },
  methods: {
    change() {
      if (this.labelAlign === 'center') {
        this.labelAlign = 'left'
      } else {
        this.labelAlign = 'center'
      }
    }
  }
}
</script>

```

#### doc/demo/steps-status.vue

```vue
<template>
  <div>
    <mtd-steps v-model="handleActive" status="error">
      <mtd-step description="辅助信息"/>
      <mtd-step description="辅助信息"/>
      <mtd-step description="辅助信息"/>
    </mtd-steps>
  </div>
</template>
<script>
export default{
  data() {
    return {
      handleActive:2
    }
  }         
}
</script>
```

#### doc/demo/usage.json

```json
{
    "componentName": "Steps",
    "desc": "步骤条",
    "codeTemplate": "<mtd-steps v-bind='configProps'></mtd-steps>"
  }
```

#### doc/desc.vue

```vue
<template>
  <div class="steps-demo-content">
    <mtd-tabs v-model="activeName" type="text" size="small">
      <mtd-tab-pane label="上下排布" value="tab1">
        <mtd-steps :active="1" type="center">
          <mtd-step title="已完成" description="辅助信息"/>
          <mtd-step title="进行中" description="辅助信息"/>
          <mtd-step title="未开始" description="辅助信息"/>
        </mtd-steps>
      </mtd-tab-pane> 
      <mtd-tab-pane label="左右排布" value="tab2">
        <mtd-steps :active="1" type="right">
          <mtd-step title="已完成" description="辅助信息"/>
          <mtd-step title="进行中" description="辅助信息"/>
          <mtd-step title="未开始" description="辅助信息"/>
        </mtd-steps>
      </mtd-tab-pane>   
    </mtd-tabs>  
  </div>
</template>
<script>
export default {
  data(){
    return{
      activeName:'tab1'  
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .steps-demo-content {
  .mtd-tabs-nav {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    padding: 0px 16px;
    border-bottom: 1px solid #edf0f7;
  }
  .mtd-tab-pane{
    padding-top: 72px;
  }
}
</style>

```

#### doc/direction.vue

```vue
<template>
  <div class="steps-demo-content">
    <mtd-tabs v-model="activeName" type="text" size="small">
      <mtd-tab-pane label="横向" value="tab1">
        <mtd-steps direction="horizontal" :active="1">
          <mtd-step title="已完成" description="辅助信息" />
          <mtd-step title="进行中" description="辅助信息" />
          <mtd-step title="未开始" description="辅助信息" />
        </mtd-steps>
      </mtd-tab-pane>
      <mtd-tab-pane label="纵向" value="tab2">
        <mtd-steps direction="vertical" :active="1">
          <mtd-step title="已完成" description="辅助信息" />
          <mtd-step title="进行中" description="辅助信息" />
          <mtd-step title="未开始" description="辅助信息" />
        </mtd-steps>
      </mtd-tab-pane>
    </mtd-tabs>
  </div>
</template>
<script>
export default {
  data() {
    return {
      activeName: 'tab1'
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .steps-demo-content {
  .mtd-tabs-nav {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    padding: 0px 16px;
    border-bottom: 1px solid #edf0f7;
  }
  .mtd-tab-pane{
    padding-top: 72px;
  }
}
</style>
```

#### doc/doc-scope.vue

```vue
<template>
  <div class="steps-demo-content">
    <mtd-tabs v-model="activeName" type="text" size="small">
      <mtd-tab-pane label="横向" value="tab1">
        <mtd-steps :active="1" dot label-align="left">
          <mtd-step title="已完成" description="2022-07-16"/>
          <mtd-step title="进行中" description="2022-07-18"/>
          <mtd-step title="未开始" description="2022-07-20"/>
        </mtd-steps>
      </mtd-tab-pane>
      <mtd-tab-pane label="纵向" value="tab2">
        <mtd-steps :active="1" dot direction="vertical">
          <mtd-step title="已完成">
            <template #description>
              第一个节点内容的辅助信息<br>
              2022-07-16 12:45
            </template>
          </mtd-step>
          <mtd-step title="进行中">
            <template #description>
              第二个节点内容的辅助信息<br>
              2022-07-18 10:23
            </template>
          </mtd-step>
          <mtd-step title="未开始">
            <template #description>
              第三个节点内容的辅助信息<br>
              2022-07-20 22:56
            </template>
          </mtd-step>
        </mtd-steps>
      </mtd-tab-pane>
    </mtd-tabs>
  </div>
</template>
<script>
export default {
  data(){
    return{
      activeName:'tab1'
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .steps-demo-content {
  .mtd-tabs-nav {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    padding: 0px 16px;
    border-bottom: 1px solid #edf0f7;
  }
  .mtd-tab-pane{
    padding-top: 72px;
  }
}
</style>

```

#### doc/length-wise-direction.vue

```vue
<template>
  <div style="height: 700px;width:250px;text-align: left;">
    <div style="height: 350px;">
      <p>1、带任务状态纵向流程(固定间距}</p>
      <mtd-steps :active="1" direction="vertical" :space="100">
        <mtd-step
          title="步骤 1"
          description="这是描述这" />
        <mtd-step
          title="步骤 2"
          description="这是描述这是描述这是描述" />
        <mtd-step
          title="步骤 3"
          description="这是描述这是描述这是描述这是描述这是描述这是描述这是描述" />

      </mtd-steps>
    </div>
    <br>
    <div style="height: 350px;">
      <p>2、无状态纵向多条步骤条</p>
      <mtd-steps :active="1" direction="vertical" dot>
        <mtd-step
          title="步骤 1"
          description="这是描述这是描述这是描述这是描述这是描述这是描述这是描述这是描述这是描述这是描述这是描述这是描述这是描述这是描述这是描述这是描述这是描述这是描述这是描述这是描述这是描述这是描述这是描述" />
        <mtd-step
          title="步骤 2"
          description="这是描述这是描述这是描述这是描述这是描述这是描述这是描述" />
        <mtd-step
          title="步骤 3"
          description="这是描述这是描述这是描述" />
      </mtd-steps>
    </div>
  </div>
</template>

```

#### doc/navigation.vue

```vue
<template>
  <div class="step-navigation-demo">
    <mtd-steps v-model:active="active" type="navigation">
      <mtd-step title="第一步" status="finish" />
      <mtd-step title="第二步" status="process" />
      <mtd-step title="第三步" status="wait" />
    </mtd-steps>
  </div>
</template>
<script>
export default {
  data() {
    return {
      active: 1,
    }
  },
  watch: {
    active(newVal) {
      console.log(newVal)
    }
  },
}
</script>
<style scoped lang="scss">
.step-navigation-demo {
  ::v-deep .mtd-steps-right {
    .mtd-step-head {
      left: -30%;
    }

    .mtd-step-main {
      text-align: center;
    }
  }
}
</style>

```

#### doc/picture.vue

```vue

<template>
  <div>
    <mtd-steps :active="1">
      <mtd-step title="步骤一" description="辅助信息">
        <template #icon>
          <mtd-icon name='mtdicon-camera-fill'></mtd-icon>
        </template>
      </mtd-step>
      <mtd-step title="步骤二" icon="video-fill" description="辅助信息"/>
      <mtd-step title="步骤三" icon="review-fill" description="辅助信息"/>
      <mtd-step title="步骤四" icon="paperplane-fill" description="辅助信息"/>
    </mtd-steps>
  </div>
</template>

```

#### doc/position.vue

```vue
<template>
  <div class="demo-full-width">

    <div>
      <span>指示器位置：</span>
      <mtd-radio-group v-model="type">
        <mtd-radio-button value="center">居中</mtd-radio-button>
        <mtd-radio-button value="right">右边</mtd-radio-button>
        <!-- <mtd-radio-button value="navigation">导航</mtd-radio-button> -->
      </mtd-radio-group>
    </div>
    <br><br><br>

    <mtd-steps v-model="active" :type="type">
      <mtd-step title="步骤 1"
        description="这是可控制描述文案位置的步骤条中关于此步骤的具体描述" />
      <mtd-step title="步骤 2"
        description="这是可控制描述文案位置的步骤条中关于此步骤的具体描述" />
      <mtd-step title="步骤 3"
        description="这是可控制描述文案位置的步骤条中关于此步骤的具体描述" />
    </mtd-steps>
  </div>
</template>

<script>
export default {
  data () {
    return {
      type: 'right',
      active: 1,
    };
  },
};
</script>


```

#### doc/progress.vue

```vue
<template>
  <div class="steps-progress-demo">
    <mtd-steps :active="1">
      <mtd-step title="已完成" description="辅助信息" />

      <mtd-step title="进行中" description="剩余 00:05:53">
        <template #icon>
          <mtd-progress type="circle" :percentage="60" :stroke-width="2" :width="28">
            <template #percentage>
              <span class="percentage">2</span>
            </template>
          </mtd-progress>
        </template>
      </mtd-step>
      <mtd-step title="未开始" description="辅助信息" />

    </mtd-steps>
  </div>
</template>
<style lang="scss" scoped>
.steps-progress-demo {
  .percentage {
    width: 22px;
    height: 22px;
    line-height: 22px;
    background: #166FF7;
    border-radius: 100%;
    color: white;
    font-weight: 500;
  }
}
</style>

```

#### doc/scroll.vue

```vue
<template>
  <div class="steps-demo-content">
    <mtd-tabs v-model="activeName" type="text" size="small">
      <mtd-tab-pane label="固定宽度" value="tab1">
        <mtd-steps :active="1" space="150">
          <mtd-step title="已完成" description="辅助信息" />
          <mtd-step title="进行中" description="辅助信息" />
          <mtd-step title="未开始" description="辅助信息" />
          <mtd-step title="未开始" description="辅助信息" />
          <mtd-step title="未开始" description="辅助信息" />
        </mtd-steps>
        <br /><br />
        <div class="steps-demo-content-scroll">
          <mtd-steps
            :active="1"
            space="150"
            style="flex-wrap: nowrap; overflow-x: scroll"
          >
            <mtd-step title="已完成" description="辅助信息" />
            <mtd-step title="进行中" description="辅助信息" />
            <mtd-step title="未开始" description="辅助信息" />
            <mtd-step title="未开始" description="辅助信息" />
            <mtd-step title="未开始" description="辅助信息" />
            <mtd-step title="未开始" description="辅助信息" />
            <mtd-step title="未开始" description="辅助信息" />
          </mtd-steps>
        </div>
      </mtd-tab-pane>
      <mtd-tab-pane label="不固定宽度" value="tab2">
        <mtd-steps :active="1">
          <mtd-step title="已完成" description="辅助信息" />
          <mtd-step title="进行中" description="辅助信息" />
          <mtd-step title="未开始" description="辅助信息" />
          <mtd-step title="未开始" description="辅助信息" />
          <mtd-step title="未开始" description="辅助信息" />
        </mtd-steps>
        <br /><br />
        <div class="steps-demo-content-scroll">
          <mtd-steps :active="1" style="flex-wrap: nowrap; overflow-x: scroll">
            <mtd-step title="已完成" description="辅助信息" />
            <mtd-step title="进行中" description="辅助信息" />
            <mtd-step title="未开始" description="辅助信息" />
            <mtd-step title="未开始" description="辅助信息" />
            <mtd-step title="未开始" description="辅助信息" />
            <mtd-step title="未开始" description="辅助信息" />
            <mtd-step title="未开始" description="辅助信息" />
          </mtd-steps>
        </div>
      </mtd-tab-pane>
    </mtd-tabs>
  </div>
</template>
<script>
export default {
  data() {
    return {
      activeName: 'tab1',
    };
  },
};
</script>
<style lang="scss" scoped>
::v-deep .steps-demo-content {
  overflow-x: hidden;
  white-space: nowrap;

  .mtd-tabs-nav {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    padding: 0px 16px;
    border-bottom: 1px solid #edf0f7;
  }
  .mtd-tab-pane {
    padding-top: 72px;
  }
  .steps-demo-content-scroll {
    width: 100%;
    overflow-x: hidden;
  }
}
</style>

```

#### doc/size.vue

```vue
<template>
  <div>
    <mtd-steps :active="1" size="small">
      <mtd-step title="已完成" description="辅助信息"/>
      <mtd-step title="进行中" description="辅助信息"/>
      <mtd-step title="未开始" description="辅助信息"/>
    </mtd-steps>
    <br>
    <mtd-steps :active="1">
      <mtd-step title="已完成" description="辅助信息"/>
      <mtd-step title="进行中" description="辅助信息"/>
      <mtd-step title="未开始" description="辅助信息"/>
    </mtd-steps>
  </div>
</template>

```

#### doc/space.vue

```vue
<template>
  <div class="steps-demo-content">
    <mtd-tabs v-model="activeName" type="text" size="small">
      <mtd-tab-pane label="固定宽度" value="tab1">
        <mtd-steps :active="3" space="150">
          <mtd-step title="步骤 1" description="辅助信息"/>
          <mtd-step title="步骤 2" description="辅助信息"/>
          <mtd-step title="步骤 3" description="辅助信息"/>
          <mtd-step title="步骤 4" description="辅助信息"/>
          <mtd-step title="步骤 5" description="辅助信息"/>
        </mtd-steps>
        <br><br>
        <mtd-steps :active="3" space="150">
          <mtd-step title="步骤 1" description="辅助信息"/>
          <mtd-step title="步骤 2" description="辅助信息"/>
          <mtd-step title="步骤 3" description="辅助信息"/>
          <mtd-step title="步骤 4" description="辅助信息"/>
        </mtd-steps>
      </mtd-tab-pane>
      <mtd-tab-pane label="不固定宽度" value="tab2">
        <mtd-steps :active="3">
          <mtd-step title="步骤 1" description="辅助信息"/>
          <mtd-step title="步骤 2" description="辅助信息"/>
          <mtd-step title="步骤 3" description="辅助信息"/>
          <mtd-step title="步骤 4" description="辅助信息"/>
          <mtd-step title="步骤 5" description="辅助信息"/>
        </mtd-steps>
        <br><br>
        <mtd-steps :active="3">
          <mtd-step title="步骤 1" description="辅助信息"/>
          <mtd-step title="步骤 2" description="辅助信息"/>
          <mtd-step title="步骤 3" description="辅助信息"/>
          <mtd-step title="步骤 4" description="辅助信息"/>
        </mtd-steps>
      </mtd-tab-pane>
    </mtd-tabs>
  </div>
</template>
<script>
export default {
  data(){
    return{
      activeName:'tab1'
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .steps-demo-content {
  .mtd-tabs-nav {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    padding: 0px 16px;
    border-bottom: 1px solid #edf0f7;
  }
  .mtd-tab-pane{
    padding-top: 72px;
  }
}
</style>

```

#### doc/status.vue

```vue
<template>
  <div class="steps-status-demo">
    <div class="demo-title">
      <span v-for="item in extraTexts" class="title-text">{{ item }}</span>
    </div>
    <mtd-steps ref="steps">
      <mtd-step title="已完成" status="finish" description="辅助信息" />
      <mtd-step title="进行中" status="process" description="辅助信息" />
      <mtd-step title="提示" status="cancel" description="辅助信息" />
      <mtd-step status="error">
        <template #title>
          <span style="color: #FF1F1F;">失败</span>
        </template>
        <template #description>
          <span style="color: #FF1F1F;">辅助信息</span>
        </template>
      </mtd-step>
      <mtd-step title="未开始" status="wait" description="辅助信息" />
    </mtd-steps>
  </div>
</template>

<script>
export default {
  data() {
    return {
      extraTexts: ['已完成', '进行中', '提示', '失败', '未开始']
    };
  }
}
</script>
<style lang="scss" scoped>
.demo-title {
  display: flex;
  margin-bottom: 12px;

  .title-text {
    color: rgba(0, 0, 0, .5);
    width: 150px;
  }
}
</style>

```

#### index.md

```markdown
# 步骤条 / Steps
步骤条用于展示任务进度及当前步骤、并引导用户按步骤完成任务。

## 互动演示
:::include(src="./demo/steps-interactive")
:::


## 代码示例

### 尺寸
步骤条支持小、大两种尺寸。
:::include(src="./doc/size.vue")
:::

### 步骤条方向
横向步骤条引导用户使用，是最常见的步骤条；纵向步骤条适用于步骤很多、且需要尽量一屏展示、且内容较少的特殊情况。
:::include(src="./doc/direction.vue")
:::

### 描述信息
:::include(src="./doc/desc.vue")
:::

### 信息状态
:::include(src="./doc/status.vue")
:::

### 点状步骤条
点状步骤条更多展示任务完成流程，任务状态表达较为隐晦，多适用于步骤数过多展示。该步骤条不支持小尺寸。
:::include(src="./doc/doc-scope.vue")
:::

### 自定义间距
支持通过space自定义步骤条的宽度。横向步骤条的min-width为150px，设置小于150的space可能将导致不生效（纵向则是min-height为50px）；若不设置宽度则根据外侧容器自适应等分。
:::include(src="./doc/space.vue")
:::

### 步骤条过多滚动
当步骤条每项宽度为固定值时：若（容器宽度/步骤数）<步骤条每项宽度，触发滚动；当步骤条每项宽度根据容器宽度自适应：若（容器宽度/步骤数）<最小宽度时，触发滚动。
:::include(src="./doc/scroll.vue")
:::

### 自定义图标
步骤条内可以启用各种自定义的图标，通过 icon 属性来设置图标，图标的类型可以参考 icon 组件的文档。
:::include(src="./doc/picture.vue")
:::

### 可切换步骤
可通过点击切换步骤，也可搭配按钮一起使用。
:::include(src="./doc/base.vue")
:::

### 带进度
某一步骤完成时间很长时，可以在节点上使用[进度条](./progress)。
:::include(src="./doc/progress.vue")
:::

### 导航步骤条
:::include(src="./doc/navigation.vue")
:::

<!-- ### 可控制描述文案位置的步骤条
:::include(src="./doc/position.vue")
:::

### 纵向步骤条
多适用于任务条数较多，状态流程较长，同时描述的信息较多等场景展示。
:::include(src="./doc/length-wise-direction.vue")
::: -->

## API
<api-doc name="Steps" :doc="require('./api.json')"></api-doc>
<api-doc name="Step" :doc="require('./../step/api.json')"></api-doc>

```

#### index.ts

```typescript
import Steps from './steps'
import { withInstall } from '@ss/mtd-adapter'
export default withInstall(Steps)

```

#### steps.tsx

```tsx
import {
  defineComponent,
  PropType,
  computed,
  ref,
  onMounted,
  onUpdated,
  vueInstance,
  getSlotsInRender,
  getChildInsList,
} from '@ss/mtd-adapter'
import useConfig from '@hooks/config'
import useProvide from './useProvide'
import { StepLabelAlign } from './types'

import { IStep, StepType } from '@components/step/types'

export default defineComponent({
  name: 'MtdSteps',
  inheritAttrs: true,
  model: {
    prop: 'active',
    event: 'update:active',
  },
  props: {
    size: String,
    dot: {
      type: Boolean,
      default: false,
    },
    space: [String, Number],
    direction: {
      type: String,
      default: 'horizontal',
    },
    active: {
      type: Number,
      default: 0,
    },
    status: {
      type: String,
      default: 'process',
    },
    type: {
      type: String as PropType<StepType>,
      default: 'center',
    },
    simple: Boolean,
    labelAlign: {
      type: String as PropType<StepLabelAlign>,
      default: 'center',
    },
  },
  emits: ['update:active'],
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  setup(props, { emit }) {
    const config = useConfig()
    const prefix = computed(() => config.getPrefixCls('steps'))
    const componentsPrefixName = computed(() => config.getComponentsPrefixName('Step'))
    const ins = vueInstance()

    const stepList = ref<IStep[]>([])

    const { provideSteps, provideStepList } = useProvide()
    provideSteps(ins)
    provideStepList(stepList)

    onMounted(() => {
      updateStepList()
    })
    onUpdated(() => {
      updateStepList()
    })

    function updateActive(value: number) {
      emit('update:active', value)
    }

    // @Methods
    function updateStepList() {
      const stepChildren = getChildInsList(ins, [componentsPrefixName.value]) as unknown as IStep[]
      stepList.value = stepChildren.map((step, index) => {
        step.setIndex(index)
        return step
      })
    }

    return {
      prefix, stepList, updateActive,
    }
  },
  render() {
    const {
      prefix, space, size, type, direction,
    } = this
    return <div
      style={{ 'flex-wrap': space ? 'wrap' : 'nowrap' }}
      class={[prefix, {
        [`${prefix}-${direction}`]: direction,
        [`${prefix}-${size}`]: size,
        [`${prefix}-right`]: type !== 'center',
        [`${prefix}-nav`]: type === 'navigation' && direction === 'horizontal',
      }]}>
      {getSlotsInRender(this)}
    </div>
  },
})


```

#### types.ts

```typescript
import Steps from './index'
import { CPI } from '@components/types/component'


export type ISteps = InstanceType<typeof Steps>

export interface StepsProvider extends CPI {
  active: number
  status: string
  space: number | string
  direction: string
  dot: boolean
  stepList: any[]
  type: StepType
  labelAlign: StepLabelAlign

  updateActive: (v: number) => void
}

export type StepType = 'center' | 'right' | 'navigation'

export type StepLabelAlign = 'center' | 'left'


```

#### useProvide.ts

```typescript
import { provide, inject, Ref, ComponentPublicInstance } from '@ss/mtd-adapter'
import { StepsProvider } from './types'
import { IStep } from '@components/step/types'

export const stepsSymbol ='mtui-vue/steps'
export const stepListSymbol = 'mtui-vue/stepList'

export const useProvider = () => {

  function provideSteps(ins: StepsProvider) {
    provide<StepsProvider>(stepsSymbol, ins)
  }
  function injectSteps() {
    return inject<StepsProvider>(stepsSymbol)
  }

  function provideStepList(stepList: Ref<IStep[]>) {
    provide<Ref<IStep[]>>(stepListSymbol, stepList)
  }
  function injectStepList() {
    return inject<Ref<IStep[]> | null>(stepListSymbol, null)
  }


  return {
    provideSteps,
    provideStepList,
    injectSteps,
    injectStepList,
  }
}

export default useProvider
```

#### useSteps.ts

```typescript
import { computed, Ref, reactive } from '@ss/mtd-adapter'
import useProvide from './useProvide'

export function useSteps(props: any, ins: {
  index: Ref<number>
}) {
  const { index } = ins

  const { injectSteps } = useProvide()
  const steps = injectSteps()!

  const internalStatus = computed(() => {
    if (index.value > -1) {
      const val = steps ? steps.active : -1
      if (index.value < val) {
        return 'finish'
      } else if (index.value === val) {
        return steps?.status || 'process'
      } else if (index.value > val) {
        return 'wait'
      }
    }
    return ''
  })

  const currentStatus = computed(() => props.status || internalStatus.value)
  const space = computed(() => steps.space)
  const direction = computed(() => steps.direction)
  const dot = computed(() => steps.dot)
  const type = computed(() => steps.type)
  const stepList = computed(() => steps.stepList)
  const labelAlign = computed(() => steps.labelAlign)
  const currentActive = computed(() => index.value === steps.active)

  function updateActive(v: number) {
    steps.updateActive(v)
  }

  const state = reactive({
    m_status: currentStatus,
    space: space,
    direction: direction,
    dot: dot,
    stepList: stepList,
    type: type,
    labelAlign: labelAlign,
    m_active: currentActive,
    updateActive,
  })

  return state
}

```

