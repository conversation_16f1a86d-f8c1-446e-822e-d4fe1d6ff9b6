## 源码解析

### 文件依赖

- anchor.tsx
- demo.md
- demo/anchor-interactive.vue
- doc/base.vue
- doc/demo/offset-top.vue
- doc/direction.vue
- doc/fixed.vue
- doc/horizontal-text.vue
- index.md
- index.ts
- types.ts
- usage.json
- useProvide.ts

### 源码内容

#### anchor.tsx

```tsx
import {
  defineComponent,
  computed,
  reactive,
  toRefs,
  nextTick,
  vueInstance,
  getSlotsInRender,
  useClassStyle,
} from '@ss/mtd-adapter'
import useConfig from '@hooks/config'
import { scrollTop, sharpMatcherRegx } from '@utils/util'
import MtdAffix from '@components/affix'
import { on, off } from '@utils/dom'
import mitt from '@utils/mitt'
import { IAnchorLink } from '@components/anchor-link/types'
import { isString } from '@utils/type'


import useProvide from './useProvide'

export const DEFAULT_INK_WIDTH = 2

export default defineComponent({
  name: 'MtdAnchor',
  components: {
    MtdAffix,
  },
  inheritAttrs: true,
  props: {
    affix: {
      type: Boolean,
      default: true,
    },
    direction: {
      type: String,
      default: 'vertical', // horizontal
    },
    placement: {
      type: String,
      default: 'left',
      validator (v: string) {
        return ['left', 'right'].indexOf(v) > -1
      },
    },
    type: {
      type: String,
      default: 'card',
      validator (v: string) {
        return ['card', 'text'].indexOf(v) > -1
      },
    },
    offsetTop: {
      type: Number,
      default: 0,
    },
    offsetBottom: Number,
    bounds: {
      type: Number,
      default: 5,
    },
    container: null,
    showInk: {
      type: Boolean,
      default: true,
    },
    scrollOffset: {
      type: Number,
      default: 0,
    },
    preventRouterDefault: {
      type: Boolean,
      default: false,
    },
  },
  emits: ['select', 'change'],
  setup() {
    const config = useConfig()
    const prefix = computed(() => config.getPrefixCls('anchor'))
    const ins = vueInstance()
    const { provideAnchor } = useProvide()
    provideAnchor(ins)

    const state = reactive({
      isAffixed: false, // current affixed state
      inkTop: -1,
      inkLeft: DEFAULT_INK_WIDTH / 2,
      inkWidth: DEFAULT_INK_WIDTH,
      inkVisible: false,
      animating: false, // if is scrolling now
      currentLink: '', // current show link =>  #href -> currentLink = #href
      currentId: '', // current show title id =>  #href -> currentId = href
      scrollContainer: undefined as HTMLElement | Window | undefined,
      scrollElement: undefined as HTMLElement | undefined,
      titlesOffsetArr: [] as any[],
      wrapperTop: 0,
      upperFirstTitle: true,
      links: [] as IAnchorLink[],
      emitter: mitt(),
      isMounted: false,
    })

    const classStyle = useClassStyle()

    return {
      ...toRefs(state),
      prefix, classStyle,
    }
  },

  computed: {
    wrapperComponent(): any {
      return this.affix ? MtdAffix : 'div'
    },
    wrapperStyle(): any {
      return {
        maxHeight: this.offsetTop
          ? `calc(100vh - ${this.offsetTop}px)`
          : '100vh',
      }
    },
    containerIsWindow(): boolean {
      return this.scrollContainer === window
    },
  },
  watch: {
    container() {
      this.init()
    },
    currentLink(newHref, oldHref) {
      this.$emit('change', newHref, oldHref)
    },
    direction() {
      nextTick(() => {
        this.handleSetInkDirection()
      })
    },
    placement() {
      nextTick(() => {
        this.handleSetInkDirection()
      })
    },
  },
  created() {
    this.emitter.on('addAnchorLink', this.addAnchorLink)
    this.emitter.on('removeAnchorLink', this.removeAnchorLink)
    this.emitter.on('go', this.goAnchor)
  },
  mounted() {
    this.init()
    this.isMounted = true
  },
  beforeUnmount() {
    this.removeListener()
  },
  beforeDestroy() {
    this.removeListener()
  },
  methods: {
    addAnchorLink(link: IAnchorLink) {
      this.links.push(link)
      if (this.isMounted) {
        this.updateTitleOffset()
      }
    },
    removeAnchorLink(link: IAnchorLink) {
      this.links = this.links.filter((item) => item !== link)
    },
    goAnchor(href: string) {
      this.currentLink = href
      this.currentId = href.split('#')[1] // 放到这里是因为$router.push方法在vue2和vue3对应的版本下不一致
      this.$emit('select', href)

      if (this.preventRouterDefault) return

      // @todo 后续代码待删除，目前看调用了会有问题，但是怕有影响，先通过preventRouterDefault来手动控制
      const isRoute = (this as any).$router
      if (isRoute) {
        const sharpLinkMatch = sharpMatcherRegx.exec(href)
        if (sharpLinkMatch) {
          (this as any).$router.push({
            ...(this as any).$route,
            hash: decodeURIComponent(sharpLinkMatch[0]),
          })
        } else {
          (this as any).$router.push(decodeURIComponent(href))
        }
      } else {
        window.location.href = href
        nextTick(() => {
          this.handleHashChange()
          this.handleScrollTo()
        })
      }
      // // 在上面路由改变之后，会触发页面刷新，滚动条会回到0的位置，所以要将滚动条回归到上次的位置
      // if (this.scrollElement) {
      //   this.scrollElement.scrollTop = lastScrollTop || 0;
      // }
    },
    handleAffixStateChange(state: boolean) {
      this.isAffixed = this.affix && state
    },
    handleScroll(e: MouseEvent) {
      if (this.titlesOffsetArr[0]) {
        this.upperFirstTitle =
          (e.target as HTMLElement).scrollTop < this.titlesOffsetArr[0].offset
      }
      if (this.animating) return
      this.updateTitleOffset()
      const scrollTop =
        (document.documentElement.scrollTop ||
          document.body.scrollTop ||
          (e.target as HTMLElement).scrollTop) + this.scrollOffset
      this.getCurrentScrollAtTitleId(scrollTop)
    },
    handleHashChange() {
      const url = window.location.href
      const sharpLinkMatch = sharpMatcherRegx.exec(url)
      if (!sharpLinkMatch) return
      this.currentLink = sharpLinkMatch[0]
      this.currentId = sharpLinkMatch[1]
    },
    handleScrollTo() {
      const anchor = document.getElementById(this.currentId)
      const currentLinkElementA = this.$el.querySelector(
        `a[data-href="${this.currentLink}"]`,
      )
      let offset = this.scrollOffset
      if (currentLinkElementA) {
        const offsetAttr = currentLinkElementA.getAttribute(
          'data-scroll-offset',
        )
        if (offsetAttr) {
          offset = parseFloat(offsetAttr)
        }
      } else {
        // 如果在container中没有找到该锚点，就不滚动
        return
      }

      if (!anchor) return
      const offsetTop = anchor.offsetTop - this.wrapperTop - offset
      this.animating = true
      scrollTop(
        this.scrollContainer!,
        this.scrollElement!.scrollTop,
        offsetTop,
        600,
        () => {
          this.animating = false
        },
      )
      this.handleSetInkDirection()
    },
    handleSetInkDirection() {
      const currentLinkElementA = this.$el.querySelector(
        `a[data-href="${this.currentLink}"]`,
      )
      if (!currentLinkElementA) {
        this.inkVisible = false
        return
      }
      const elementATop = (currentLinkElementA as HTMLElement).offsetTop
      const elementALeft = (currentLinkElementA as HTMLElement).offsetLeft
      const elementAWidth = (currentLinkElementA as HTMLElement).offsetWidth
      const top = elementATop < 0 ? this.offsetTop : elementATop

      if (this.direction === 'vertical') {
        this.inkTop = top
        this.inkLeft = this.placement === 'left' ? DEFAULT_INK_WIDTH / 2 : 0
        this.inkWidth = DEFAULT_INK_WIDTH
      } else {
        this.inkLeft = elementALeft
        this.inkTop = -1
        this.inkWidth = elementAWidth
      }

      this.inkVisible = true
    },
    updateTitleOffset() {
      const hrefs = this.links.map((link) => {
        return link.href!
      })
      const idArr: string[] = hrefs.map((href) => {
        return href.split('#')[1]
      })
      const offsetArr: { link: string; offset: number }[] = []
      idArr.forEach((id) => {
        const titleEle = document.getElementById(id)
        if (titleEle) {
          offsetArr.push({
            link: `#${id}`,
            offset: titleEle.offsetTop - this.scrollElement!.offsetTop,
          })
        }
      })
      this.titlesOffsetArr = offsetArr
    },
    getAffixTarget(): HTMLElement | Window {
      return this.container
        ? isString(this.container)
          ? document.querySelector(this.container)
          : this.container
        : window
    },
    getCurrentScrollAtTitleId(scrollTop: number) {
      let i = -1
      const len = this.titlesOffsetArr.length
      let titleItem = {
        link: '#',
        offset: 0,
      }
      scrollTop += this.bounds
      while (++i < len) {
        const currentEle = this.titlesOffsetArr[i]
        const nextEle = this.titlesOffsetArr[i + 1]
        if (
          scrollTop >= currentEle.offset &&
          scrollTop < ((nextEle && nextEle.offset) || Infinity)
        ) {
          titleItem = this.titlesOffsetArr[i]
          break
        }
      }
      this.currentLink = titleItem.link
      this.handleSetInkDirection()
    },
    getContainer() {
      this.scrollContainer = this.getAffixTarget()
      this.scrollElement = this.container
        ? (this.scrollContainer as HTMLElement)
        : document.documentElement || document.body
    },
    removeListener() {
      off(this.scrollContainer, 'scroll', this.handleScroll as any)
      off(window, 'hashchange', this.handleHashChange)
    },
    init() {
      this.handleHashChange()
      nextTick(() => {
        this.removeListener()
        this.getContainer()
        this.wrapperTop = this.containerIsWindow
          ? 0
          : this.scrollElement!.offsetTop
        this.handleScrollTo()
        this.updateTitleOffset()
        if (this.titlesOffsetArr[0]) {
          this.upperFirstTitle =
            this.scrollElement!.scrollTop < this.titlesOffsetArr[0].offset
        }
        on(this.scrollContainer, 'scroll', this.handleScroll as any)
        on(window, 'hashchange', this.handleHashChange)
      })
    },
  },
  render() {
    const {
      prefix, wrapperComponent, offsetTop, offsetBottom, affix, showInk, inkVisible,
      wrapperStyle, inkTop, classStyle, placement, direction, inkLeft, inkWidth, type,
    } = this
    const {
      getAffixTarget, handleAffixStateChange,
    } = this
    const Component = wrapperComponent
    return <Component
      {...classStyle}
      offset-top={offsetTop}
      offset-bottom={offsetBottom}
      get-target={affix ? getAffixTarget : undefined}
      onChange={handleAffixStateChange}
    >
      <div class={`${prefix}-wrapper ${prefix}-${direction}`} style={wrapperStyle}>
        <div class={`${prefix} ${prefix}-${direction}-${type}`}>
          <div class={`${prefix}-ink ${prefix}-ink-${placement
        }`}>
            <span
              v-show={showInk && inkVisible}
              class={`${prefix}-ink-ball`}
              style={{ top: `${inkTop}px`, left: `${inkLeft}px`, width: `${inkWidth}px` }}
            />
          </div>
          {getSlotsInRender(this)}
        </div>
      </div>
    </Component>
  },
})

```

#### demo.md

```markdown
# 锚点 / Anchor 补充demo

## offset-top
:::include(src="./doc/demo/offset-top.vue")
:::
```

#### demo/anchor-interactive.vue

```vue
<template>
  <div>
    <mtd-doc-usage name="anchor" :apiJson="apiJson" :usageJson="usageJson" @propChange="propChange">
      <template slot="anchor" slot-scope="{ configProps }">
          <div class="slot-container">
            <mtd-anchor v-bind="configProps"></mtd-anchor>
          </div>
        </template>
      <template #anchor="{ configProps }">
        <div :class="`demo-full-width wrap-${direction}`">
          <div class="anchor-wrap">
            <mtd-anchor :class="`title-wrap-${direction}`" container="#demo-base-anchor-containter" v-bind="configProps">
              <mtd-anchor-link v-for="link in linkList" :href="link.href" :title="link.title" :key="link.href" />
            </mtd-anchor>
            <div class="content-wrap" id="demo-base-anchor-containter">
              <div v-for="item in 5" :key="item" :id=" 'demo-base-anchor-' + item" class="anchor-item"
                :style="{ backgroundColor: getColor(item) }">
                内容{{ item }}
              </div>
            </div>
          </div>
        </div>
      </template>
    </mtd-doc-usage>
  </div>
</template>
<script>
import apiJson from '../api.json'

export default {
  data() {
    return {
      linkList: [
        { title: '位置一', href: '#demo-base-anchor-1' },
        { title: '位置二', href: '#demo-base-anchor-2' },
        { title: '位置三', href: '#demo-base-anchor-3' },
        { title: '位置四', href: '#demo-base-anchor-4' },
      ],
      apiJson: apiJson,
      usageJson: {
        componentName: "anchor",
        desc: "锚点",
        codeTemplate: "<mtd-anchor v-bind='configProps'></mtd-anchor>"
      },
      direction: 'vertical'
    }
  },
  methods: {
    propChange(propertyName, val) {
      console.log('prop changed: , propertyName: ' + propertyName + ', val: ' + val)
      if (propertyName === 'direction') {
        this.direction = val
      }
    },
    getColor(item) {
      const colors = ['#F0F6FF', '#BFD8FE', '#8FBCFF', '#468DF8', '#166FF7'];
      return colors[item - 1];
    }
  }
}
</script>
<style lang="scss" scoped>
.title-wrap-vertical {
  position: absolute;
  top: 0px;
  left: 10px;
  width: 50px;
}

.wrap-horizontal {
  .title-wrap-horizontal {
    position: absolute;
    width: auto;
    left: 87px;
  }

  .anchor-wrap {
    display: flex;
    .content-wrap {
      margin: 50px 0 20px 87px;
    }
  }
}


.anchor-wrap {
  position: relative;
  display: flex;
  width: 670px;

  .content-wrap {
    height: 260px;
    overflow-y: auto;
    margin-left: 87px;

    .anchor-item {
      height: 130px;
      width: 560px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}
</style>
  
```

#### doc/base.vue

```vue
<template>
  <div class="demo-full-width">
    <div class="anchor-wrap">
      <ul class="content-wrap" id="demo-base-anchor-containter">
        <li v-for="item in 4" :key="item" :id="`demo-base-anchor-${item}`">
          位置{{ item }}
        </li>
      </ul>
      <mtd-anchor class="title-wrap"
        :offset-top="10" :affix="false"
        container="#demo-base-anchor-containter">
        <mtd-anchor-link
          v-for="link in linkList"
          :href="link.href"
          :title="link.title"
          :key="link.href" />
      </mtd-anchor>
    </div>
  </div>
</template>

<script >
export default {
  data () {
    return {
      linkList: [
        {title: '位置一', href: '#demo-base-anchor-1'},
        {title: '位置二', href: '#demo-base-anchor-2'},
        {title: '位置三', href: '#demo-base-anchor-3'},
        {title: '位置四', href: '#demo-base-anchor-4'},
      ],
    };
  },
};
</script>

<style lang="scss" scoped>
  .anchor-wrap {
    position: relative;
    .content-wrap{
      height: 200px;
      overflow-y: auto;
      li {
        list-style: none;
        height: 100px;
      }
    }
    .title-wrap {
      position: absolute;
      top: 0px;
      right: 100px;
    }
  }
</style>

```

#### doc/demo/offset-top.vue

```vue
<template>
  <div class="demo-full-width">
    <div class="anchor-wrap-1">
      <mtd-anchor 
        direction="vertical"
        @select="handleSelect"
        @change="handleChange"
        :offset-top="200"
        :affix="true"
        placement="right"
        :show-ink="true"
        :bounds="10"
        class="title-wrap-horizontal"
        type="text"
        container=".app"
      >
        <mtd-anchor-link v-for="link in linkList" :href="link.href" :title="link.title" :key="link.href" />
      </mtd-anchor>
      <div class="content-wrap-1" id="custom-fixed-anchor-containter-6">
        <div v-for="item1 in 5" :key="item1" :id=" 'custom6-fixed-anchor-' + item1" class="anchor-item-1"
          :style="{ backgroundColor: getColor(item1) }">
          内容{{ item1 }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      linkList: [
        { title: '位置一', href: '#custom6-fixed-anchor-1' },
        { title: '位置二', href: '#custom6-fixed-anchor-2' },
        { title: '位置三', href: '#custom6-fixed-anchor-3' },
        { title: '位置四', href: '#custom6-fixed-anchor-4' },
      ]
    }
  },
  methods: {
    handleSelect(...args) {
      console.log(args);
    },
    handleChange(...args) {
      console.log(args);
    },
    getColor(item1) {
      const colors = ['#F0F6FF', '#BFD8FE', '#8FBCFF', '#468DF8', '#166FF7'];
      return colors[item1 - 1];
    }
  },
};
</script>

<style lang="scss" scoped>
.custom-fixed-anchor-1 {
  position: absolute;
  top: 0px;
  left: 700px;
  width: 100px;
}

.anchor-wrap-1 {
  position: relative;
  display: flex;
  width: 900px;

  .content-wrap-1 {
    height: 260px;
    overflow-x: hidden;
    margin-left: 37px;
    margin-top: 2000px;

    .anchor-item-1 {
      height: 130px;
      width: 800px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}
</style>
```

#### doc/direction.vue

```vue
<template>
  <div class="demo-wrapper">
    <mtd-tabs v-model="tab" type="text" size="small">
      <mtd-tab-pane label="横向" value="horizontal">
        <div class="wrap-horizontal">
          <div class="anchor-wrap">
            <mtd-anchor :prevent-router-default="true" class="title-wrap-horizontal" direction="horizontal" container="#demo-base-anchor-containter2">
              <mtd-anchor-link v-for="link in linkList" :href="link.href" :title="link.title" :key="link.href" />
            </mtd-anchor>
            <div class="content-wrap" id="demo-base-anchor-containter2">
              <div v-for="item in 5" :key="item" :id="'demo2-base-anchor-' + item" class="anchor-item"
                :style="{ backgroundColor: getColor(item) }">
                内容{{ item }}
              </div>
            </div>
          </div>
        </div>
      </mtd-tab-pane>
      <mtd-tab-pane label="纵向" value="vertical">
        <div class="wrap-vertical">
          <div class="anchor-wrap">
            <mtd-anchor :prevent-router-default="true" class="title-wrap-vertical" container="#demo-base-anchor-containter1">
              <mtd-anchor-link v-for="link in linkList2" :href="link.href" :title="link.title" :key="link.href" />
            </mtd-anchor>
            <div class="content-wrap" id="demo-base-anchor-containter1">
              <div v-for="item in 5" :key="item" :id="'demo3-base-anchor-' + item" class="anchor-item"
                :style="{ backgroundColor: getColor(item) }">
                内容{{ item }}
              </div>
            </div>
          </div>
        </div>
      </mtd-tab-pane>
    </mtd-tabs>
  </div>
</template>
<script>
export default {
  data() {
    return {
      linkList: [
        { title: '位置一', href: '#demo2-base-anchor-1' },
        { title: '位置二', href: '#demo2-base-anchor-2' },
        { title: '位置三', href: '#demo2-base-anchor-3' },
        { title: '位置四', href: '#demo2-base-anchor-4' },
      ],
      linkList2: [
        { title: '位置一', href: '#demo3-base-anchor-1' },
        { title: '位置二', href: '#demo3-base-anchor-2' },
        { title: '位置三', href: '#demo3-base-anchor-3' },
        { title: '位置四', href: '#demo3-base-anchor-4' },
      ],
      tab: "horizontal",
    };
  },
  methods: {
    getColor(item) {
      const colors = ['#F0F6FF', '#BFD8FE', '#8FBCFF', '#468DF8', '#166FF7'];
      return colors[item - 1];
    }
  }
};
</script>
<style lang="scss" scoped>
.title-wrap-vertical {
  position: absolute;
  top: 0px;
  left: 10px;
  width: 50px;
}

.wrap-horizontal {
  .title-wrap-horizontal {
    position: absolute;
    width: auto;
    left: 30px;
  }

  .anchor-wrap {
    display: flex;
    .content-wrap {
      margin: 50px 0 20px 30px;
    }
  }
}


.anchor-wrap {
  position: relative;
  display: flex;
  width: 900px;

  .content-wrap {
    height: 260px;
    overflow-x: hidden;
    margin-left: 80px;

    .anchor-item {
      height: 130px;
      width: 800px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}

.demo-wrapper {
  width: 100%;

  ::v-deep .mtd-tab-pane {
    padding: 50px 0;

    .title {
      margin-bottom: 16px;
    }
  }

  ::v-deep .mtd-tabs-nav {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    border-bottom: 1px solid #edf0f7;
    padding: 1.5px 16px;
  }

}
</style>
<style>
.my-modal {
  .mtd-modal-close {
    display: none;
  }
}
</style>
```

#### doc/fixed.vue

```vue
<template>
  <div class="demo-full-width">
    <div class="anchor-wrap-1">
      <mtd-anchor 
        direction="vertical"
        @select="handleSelect"
        @change="handleChange"
        :offset-top="200"
        :affix="true"
        placement="left"
        :show-ink="true"
        :bounds="10"
        class="title-wrap-horizontal"
        type="text"
        container=".app"
        :prevent-router-default="true"
      >
        <mtd-anchor-link v-for="link in linkList" :href="link.href" :title="link.title" :key="link.href" />
      </mtd-anchor>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      linkList: [
        { title: '代码示例', href: '#%E4%BB%A3%E7%A0%81%E7%A4%BA%E4%BE%8B' },
        { title: '方向', href: '#%E6%96%B9%E5%90%91' },
        { title: '横向无轴线型', href: '#%E6%A8%AA%E5%90%91%E6%97%A0%E8%BD%B4%E7%BA%BF%E5%9E%8B' },
        { title: '固定位置', href: '#%E5%9B%BA%E5%AE%9A%E4%BD%8D%E7%BD%AE' },
      ]
    }
  },
  methods: {
    handleSelect(...args) {
      console.log(args);
    },
    handleChange(...args) {
      console.log(args);
    },
    getColor(item1) {
      const colors = ['#F0F6FF', '#BFD8FE', '#8FBCFF', '#468DF8', '#166FF7'];
      return colors[item1 - 1];
    }
  },
};
</script>

<style lang="scss" scoped>
::v-deep {
  .demo-source {
    overflow: hidden;
  }
}
.custom-fixed-anchor-1 {
  position: absolute;
  top: 0px;
  left: 700px;
  width: 100px;
}

.demo-full-width {
  position: relative;
  margin: 50px 0;
  left: 700px;
}

.anchor-wrap-1 {
  position: relative;
  display: flex;
  width: 900px;

  .content-wrap-1 {
    height: 260px;
    overflow-x: hidden;
    margin-left: 37px;

    .anchor-item-1 {
      height: 130px;
      width: 800px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}
</style>
```

#### doc/horizontal-text.vue

```vue
<template>
  <div class="demo-wrapper">
      <div class="wrap-horizontal">
        <div class="anchor-wrap">
          <mtd-anchor :prevent-router-default="true" class="title-wrap-horizontal" type="text" direction="horizontal" container="#demo-base-anchor-containter4">
            <mtd-anchor-link v-for="link in linkList" :href="link.href" :title="link.title" :key="link.href" />
          </mtd-anchor>
          <div class="content-wrap" id="demo-base-anchor-containter4">
            <div v-for="item in 5" :key="item" :id="'demo4-base-anchor-' + item" class="anchor-item"
              :style="{ backgroundColor: getColor(item) }">
              内容{{ item }}
            </div>
          </div>
        </div>
      </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      linkList: [
        { title: '位置一', href: '#demo4-base-anchor-1' },
        { title: '位置二', href: '#demo4-base-anchor-2' },
        { title: '位置三', href: '#demo4-base-anchor-3' },
        { title: '位置四', href: '#demo4-base-anchor-4' },
      ],
    };
  },
  methods: {
    getColor(item) {
      const colors = ['#F0F6FF', '#BFD8FE', '#8FBCFF', '#468DF8', '#166FF7'];
      return colors[item - 1];
    }
  }
};
</script>
<style lang="scss" scoped>
.title-wrap-vertical {
  position: absolute;
  top: 0px;
  left: 10px;
  width: 50px;
}

.wrap-horizontal {
  .title-wrap-horizontal {
    position: absolute;
    width: auto;
    left: 30px;
  }

  .anchor-wrap {
    display: flex;
    .content-wrap {
      margin: 50px 0 20px 30px;
    }
  }
}


.anchor-wrap {
  position: relative;
  display: flex;
  width: 900px;

  .content-wrap {
    height: 260px;
    overflow-x: hidden;
    margin-left: 80px;

    .anchor-item {
      height: 130px;
      width: 800px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}

.demo-wrapper {
  width: 100%;
}
</style>
<style>
.my-modal {
  .mtd-modal-close {
    display: none;
  }
}
</style>
```

#### index.md

```markdown
# 锚点 / Anchor
锚点用于快速定位到信息内容在当前页面的指定位置。

## 互动演示
:::include(src="./demo/anchor-interactive.vue")
:::

## 代码示例
### 方向
:::include(src="./doc/direction.vue")
:::

### 横向无轴线型
:::include(src="./doc/horizontal-text.vue")
:::

### 固定位置
随着页面滚动，锚点浮动在页面固定位置。
:::include(src="./doc/fixed.vue")
:::

## API
<api-doc name="Anchor" :doc="require('./api.json')"></api-doc>
<api-doc name="AnchorLink" :doc="require('../anchor-link/api.json')"></api-doc>

```

#### index.ts

```typescript
import Anchor from './anchor'
import { withInstall } from '@ss/mtd-adapter'
export default withInstall(Anchor)

```

#### types.ts

```typescript
import Anchor from './index'

export type IAnchor = InstanceType<typeof Anchor>
```

#### usage.json

```json
{
    "componentName": "Anchor",
    "desc": "锚点",
    "codeTemplate": "<mtd-anchor v-bind='configProps'>内容一</mtd-anchor>"
  }
```

#### useProvide.ts

```typescript
import { Emitter } from '@utils/mitt'
import { provide, inject, ComponentPublicInstance } from '@ss/mtd-adapter'
import { IAnchor } from './types'

export const anchorSymbol = 'mtui-vue/anchor'

export type AnchorProvider = {
  currentLink: string
  scrollOffset: number
  emitter: Emitter
}

export const useProvider = () => {

  function provideAnchor(ins: ComponentPublicInstance) {
    provide<ComponentPublicInstance>(anchorSymbol, ins)
  }

  function injectAnchor() {
    return inject<IAnchor | null>(anchorSymbol, null)
  }

  return {
    provideAnchor,
    injectAnchor,
  }
}

export default useProvider

```

