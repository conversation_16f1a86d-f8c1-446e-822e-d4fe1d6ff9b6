## 源码解析

### 文件依赖

- cascader.tsx
- demo.md
- doc/addendum.vue
- doc/base.vue
- doc/cascader-panel.vue
- doc/changeOnSelect.vue
- doc/custom-formatter.vue
- doc/custom.vue
- doc/demo/custom-tag.vue
- doc/demo/default-visible.vue
- doc/demo/formatter.vue
- doc/demo/load.vue
- doc/demo/methods.vue
- doc/demo/popper.vue
- doc/demo/props.vue
- doc/demo/select-all.vue
- doc/demo/status.vue
- doc/demo/text.vue
- doc/demo/unmatch-value.vue
- doc/demo/virtual.vue
- doc/expand.vue
- doc/formatter.vue
- doc/load.vue
- doc/multi-level.vue
- doc/multiple.vue
- doc/search.vue
- doc/size.vue
- doc/status.vue
- hook.ts
- index.md
- index.ts
- types.ts
- usage.json
- util.ts

### 源码内容

#### cascader.tsx

```tsx
import {
  computed,
  defineComponent,
  nextTick,
  PropType,
  reactive,
  toRefs,
  vueInstance,
  useResetAttrs,
  toProps,
  getScopedSlotsInRender,
  getSlotsInRender,
  vSlots,
  enhanceInsListeners,
  getAllScopedSlots,
} from '@ss/mtd-adapter'
import useConfig from '@hooks/config'
import { Popper, Reference, Drop } from '@components/popper'
import MtdSelectInput from '@components/select-input'
import MtdCascaderMenus from '@components/cascader-menus'
// import MtdCascaderMenus from './menus'
import { DEFAULT_FIELD_NAMES, getActivePaths, isArrayEqual } from './util'
import { debounce } from '@utils/debounce'
// import MtdMultipleInput from '@components/multiple-input';
import { Node, TCheckedStrategy, CascaderFieldName, ICascaderPanel, CascaderState } from './types'
import { IPopper } from '@components/popper/types'
import useControlled from '@hooks/controlled'
import { useFormItem } from '@components/form-item/useFormItem'
import { InputStatus } from '@components/input/types'
import { VirtualOption } from '@components/virtual/types'
import { getPropsText } from '@utils/util'
import { isArray } from '@components/__utils__/type'

export default defineComponent({
  name: 'MtdCascader',
  components: {
    Popper,
    Reference,
    Drop,
    MtdSelectInput,
    MtdCascaderMenus,
  },
  inheritAttrs: true,
  model: {
    prop: 'modelValue',
    event: 'update:modelValue',
  },
  props: {
    icon: {
      type: String,
      default: 'down-thick',
    },
    data: {
      type: Array,
      default: () => [],
    },
    // 多选的时候他是二维数组
    modelValue: {
      type: Array,
      default: () => [],
    },
    noDataText: String,
    fieldNames: {
      type: Object,
      default: () => ({}),
    },
    changeOnSelect: Boolean,
    expandTrigger: String,
    formatter: Function,
    separator: {
      type: String,
      default: ' / ',
    },
    loadData: Function,
    filterable: Boolean,
    debounce: {
      type: Number,
      default: 300,
    },
    filterMethod: Function,
    remote: Boolean,
    remoteMethod: Function,
    noMatchText: String,
    loading: Boolean,
    loadingText: String,
    disabled: Boolean,
    clearable: Boolean,
    size: String,
    placeholder: {
      type: String,
    },
    popperClass: String,
    placement: {
      type: String,
      default: 'bottom-start',
    },
    visible: Boolean,
    appendToContainer: {
      type: Boolean,
      default: true,
    },
    getPopupContainer: Function,

    multiple: Boolean,
    checkStrictly: Boolean,
    checkedStrategy: {
      type: String as PropType<TCheckedStrategy>,
      default: 'parent',
      validator: (v: string) => ['all', 'parent', 'children'].indexOf(v) > -1,
    },
    maxCount: {
      type: [Number, String] as PropType<Number | 'responsive'>,
    },
    popperOptions: Object,
    closeOnSelect: Boolean,
    reserveKeyword: {
      type: Boolean,
      default: true,
    },
    model: {
      type: String,
      default: 'normal',
    },
    status: {
      type: String as PropType<InputStatus>,
    },
    expandableOnDisabled: {
      type: Boolean,
      default: false,
    },
    disabledStrictly: {
      type: Boolean,
      default: true,
    },
    defaultExpandFirstItem: {
      type: Boolean,
      default: false,
    },
    updateAfterLoaded: Boolean,

    showSelectAll: {
      type: Boolean,
      default: false,
    },
    participateDisabled: {
      type: Boolean,
      default: false,
    },
    genre: String,
    defaultVisible: Boolean,
    useCache: {
      // todo:临时属性，用于是否启用 value 匹配缓存
      type: Boolean,
      default: false,
    },
    virtual: {
      type: Boolean,
      default: false,
    },
    virtualOption: {
      type: Object as PropType<VirtualOption>,
    },
  },
  emits: [
    'clickoutside',
    'update:visible',
    'focus',
    'blur',
    'clear',
    'filter',
    'input',
    'change',
    'active-item-change',
    'update:modelValue',
  ],
  setup(props, ctx) {
    const config = useConfig()
    const m_placeholder = computed(() => getPropsText(props.placeholder, config.t('el.cascader.placeholder')))

    const self = reactive({
      ins: vueInstance(),
      prefix: config.getPrefixCls('cascader'),
      restAttrs: useResetAttrs(ctx.attrs, true),
    })
    const state: CascaderState = reactive({
      inputValue: '',
      // eslint-disable-next-line no-nested-ternary
      expandedValue: props.multiple ? isArray(props.modelValue[0]) ? props.modelValue[0] : [] : props.modelValue,
      focused: false,
      previousQuery: null,
      filter: '',
      inputWidth: '',
      valueStrs: [],
      debouncedQuery: null,
      isOnComposition: false,
      cachedValues: new Map(),
    })
    const option = computed(() => ({
      label: state.inputValue,
      value: state.inputValue,
    }))
    const options = computed(() => state.valueStrs.map((item: string) => ({
      label: item,
      value: item,
    })))
    const m_fieldNames = computed((): CascaderFieldName => ({
      ...DEFAULT_FIELD_NAMES,
      ...props.fieldNames,
    }))
    const hasValue = computed(() => props.modelValue && props.modelValue.length)
    const [visible, setVisible] = useControlled<boolean>('visible', props, ctx, {
      defaultName: 'defaultVisible',
    })

    const [m_icon, setIcon] = useControlled<string>('icon', props, ctx)

    const currentPlaceholder = (): string => (!props.filterable || !state.focused
      ? m_placeholder.value
      : state.valueStrs[0] || m_placeholder.value)

    const formItemHook = useFormItem(props, ctx)

    return {
      ...toRefs(self),
      ...toRefs(state),
      m_visible: visible,
      m_status: formItemHook.status,
      m_disabled: formItemHook.disabled,
      m_loading: formItemHook.loading,
      m_handleBlur: formItemHook.m_handleBlur,
      m_handleChange: formItemHook.m_handleChange,
      m_size: formItemHook.m_size,
      option,
      options,
      m_fieldNames,
      currentPlaceholder,
      setVisible,
      hasValue,
      m_placeholder,
      m_icon,
      setIcon,
    }
  },
  watch: {
    m_visible: {
      immediate: true,
      handler(n) {
        n ? this.handleMenuVisible() : this.handleMenuHidden()
      },
    },
    modelValue: {
      immediate: true,
      handler() {
        /**
         * 保留历史计算结果，所以没有将其作为计算属性
         * 存在远程搜索的情况，在此情况下历史 data 会被重新赋值，如果重新计算会找不到原有对应值
         *
         */
        this.updateValueStrs()
      },
    },
    valueStrs: {
      immediate: true,
      handler(n, old) {
        if (!this.multiple) {
          if (!old || n[0] !== old[0]) {
            this.setInputValue(n, { force: true })
          }
        } else {
          nextTick(() => {
            this.updatePopper()
          })
        }
      },
    },
    expandedValue() {
      nextTick(() => {
        this.updatePopper()
      })
    },
    data(n, v) {
      if ((!v || !v.length) && (n || n.length)) {
        /**
         * 初始值的情况下，可能存在 value 先赋值， data 后赋值的情况
         * 此情况下前一次的 data 为 undefined、[]
         */

        this.updateValueStrs()
      } else if (this.useCache && n?.length) {
        this.updateValueStrs()
      }

      if (this.m_visible && n !== v) {
        this.expandedValue = this.multiple ? [] : this.modelValue
      }

      nextTick(() => {
        this.updatePopper()
      })
    },
    filterable(newValue) {
      if (!newValue) {
        this.setIcon('down-thick', { force: true })
        return
      }

      if (this.focused) {
        this.setIcon('search', { force: true })
      }
    },
  },
  created() {
    this.debouncedQuery = !this.debounce
      ? this.handleQuery
      : debounce(this.debounce, this.handleQuery)
  },
  methods: {
    openMenu() {
      if (!this.m_visible) {
        this.setVisible(true, { emit: true })
      }
    },
    closeMenu() {
      if (this.m_visible) {
        this.setVisible(false, { emit: true })
      }
    },
    handleMenuVisible() {
      // eslint-disable-next-line no-nested-ternary
      const nextExpandedValue = (this.multiple ? (isArray(this.modelValue[0]) ? this.modelValue[0] : []) : this.modelValue) as Array<String | Number>
      if (nextExpandedValue.length !== 0) {
        this.expandedValue = nextExpandedValue
      }

      this.focused = true
      const { selectInputRef } = this.$refs
      if (selectInputRef) {
        this.inputWidth = `${(selectInputRef as any).$el.getBoundingClientRect().width}px`
      }

      if (this.filterable) {
        this.setIcon('search', { force: true })
      }

      nextTick(this.scrollMenu)
    },
    handleMenuHidden() {
      this.setInputValue(this.valueStrs)
      this.focused = false
      this.filter = ''
      this.previousQuery = ''
      this.setIcon('down-thick', { force: true })
      const { selectInputRef } = this.$refs;
      (selectInputRef as any)?.reset()
    },
    handleClickoutside(e: Event) {
      if (this.m_disabled) {
        return
      }

      this.closeMenu()
      this.$emit('clickoutside', e)
      this.m_handleBlur(e)
      const { selectInputRef } = this.$refs
      selectInputRef && (selectInputRef as any).blur()
    },
    handleFocus(e: Event) {
      this.focused = true
      this.$emit('focus', e)
    },
    handleClear() {
      // 如果是过滤中点击清空按钮则清空当前搜索条件，否则清空当前值
      if (this.m_visible && this.filterable && this.inputValue) {
        this.setInputValue([''])
        const { selectInputRef } = this.$refs
        selectInputRef && (selectInputRef as any).reset()
      } else {
        this.$emit('clear')
        this.setValue([], [])
      }
    },
    handleInput() {
      if (!this.m_visible) {
        // to fix: https://tt.sankuai.com/ticket/detail?id=5934989
        this.openMenu()
      }

      this.debouncedQuery && this.debouncedQuery(this.inputValue)
    },

    handleInputChange(v: string) {
      this.inputValue = v
      nextTick(this.updatePopper)
      this.handleInput()
    },
    handleComposition(e: CompositionEvent) {
      const { type } = e
      if (type === 'compositionend') {
        this.isOnComposition = false
        // 当混合输入前后值不变时，不会触发后续的 input 事件，所以需要再次触发 query
        this.debouncedQuery && this.debouncedQuery(this.inputValue)
      } else {
        this.isOnComposition = true
      }
    },
    handleInputClick() {
      if (this.m_disabled) {
        return
      }

      if (!this.m_visible) {
        this.openMenu()
      } else if (!this.filterable) {
        this.closeMenu()
      }
    },
    handleQuery(val: string) {
      if (this.previousQuery === val || this.isOnComposition) {
        return
      }

      this.previousQuery = val
      if (this.remote) {
        this.remoteMethod && this.remoteMethod(val)
      } else {
        this.filter = val
      }

      // input
      nextTick(() => {
        this.updatePopper()
      })
      this.$emit('filter', val)
    },

    setValue(values: any[], nodes: Node[]) {
      this.$emit('update:modelValue', values)
      this.$emit('input', values)
      const datas = nodes.map((node) => node.data)
      this.m_handleChange(values, datas, nodes)
    },
    setInputValue(values: string[], option: { force?: boolean } = {}) {
      if (this.filterable || option.force) {
        this.inputValue = this.multiple ? '' : values[0]
      }
    },
    setMultipleValue(values: any[], paths: Node[][], options: {
      isTagRemove?: boolean,
      removeValues?: string[],
      lastNode?: Node,
    } = { isTagRemove: false, removeValues: [] }) {
      const { isTagRemove, removeValues, lastNode } = options
      const lastValues = this.modelValue as string[][]
      let unmatchValues = [] as string[][]
      /**
       * 根据当前数据获取未匹配上的选中值
       *  主要场景是异步更新数据时，数据可能未包含已选中值
       *
       */
      lastValues.forEach((lastValue) => {
        if (lastValue?.length) {
          const actived = getActivePaths(this.data, lastValue, this.m_fieldNames)
          if (actived?.length !== lastValue?.length) {
            unmatchValues.push(lastValue)
          }
        }
      })

      // 未匹配的数据需要过滤掉输入框中删除的项
      if (isTagRemove && removeValues?.length) {
        unmatchValues = unmatchValues.filter(valueArr => !isArrayEqual(valueArr, removeValues))
      }

      this.$emit('update:modelValue', values, unmatchValues)
      this.$emit('input', values)
      const datas = paths.map((path) => path.map((node) => node.data))
      this.m_handleChange(values, datas, paths, lastNode, unmatchValues)
      if (this.filterable) {
        this.focus()
      }
    },
    handleSelect(value: any, nodes: Node[], closeMenu = true) {
      this.setValue(value, nodes)
      if (!closeMenu) return
      this.closeMenu()
    },
    handleClickItem(value: any, nodes: Node[]) {
      this.setValue(value, nodes)
      if (this.closeOnSelect) {
        this.closeMenu()
      }
    },
    handleExpandedChange(values: any[], nodes: Node[], emit = true) {
      this.expandedValue = values
      if (!emit) return
      const datas = nodes.map((node) => node.data)
      // 展开项发生变化
      this.$emit('active-item-change', datas)
    },
    handleCheckedChange(values: any[], paths: Node[][], lastNode: Node) {
      if (!this.reserveKeyword) {
        this.filter = ''
        this.inputValue = ''
        if (this.multiple) {
          const selectInputRef = this.$refs.selectInputRef as any
          // 非受控组件，需要用组件的Methods实现
          selectInputRef?.reset()
        }
      }

      this.setMultipleValue(values, paths, { lastNode })
    },
    handleTagRemove(tag: string, index: number) {
      // 删除的原理是通过index 定位
      const nextValue = [...this.modelValue]
      const { menusRef } = this.$refs
      const removeValues = nextValue.splice(index, 1) as string[][]
      const nodes = nextValue.map((pathValue) => (menusRef as unknown as ICascaderPanel).getNodesByValues(pathValue as any[]))
      const lastNodes = (menusRef as unknown as ICascaderPanel).getNodesByValues(removeValues[0])
      const lastNode = lastNodes?.length ? lastNodes[lastNodes.length - 1] : undefined
      this.setMultipleValue(nextValue, nodes, { isTagRemove: true, removeValues: removeValues[0], lastNode })
    },
    scrollMenu() {
      const { menusRef } = this.$refs
      if (menusRef) {
        (menusRef as any).scrollIntoView()
      }
    },
    focus() {
      (this.$refs.selectInputRef as any).focus()
      this.openMenu()
    },
    blur() {
      (this.$refs.selectInputRef as HTMLElement).blur()
      this.closeMenu()
    },
    updatePopper() {
      if (this.m_visible) {
        (this.$refs.popperRef as unknown as IPopper).updatePopper()
      }
    },
    updateValueStrs() { // 更新期望有缓存
      const labelField = this.m_fieldNames.label
      const valueField = this.m_fieldNames.value
      const cachedValues = this.cachedValues
      const values = this.multiple ? this.modelValue : [this.modelValue]
      this.valueStrs = values.map((value: any) => {
        // 🤡精确匹配问题
        const actived = getActivePaths(this.data, value, this.m_fieldNames)
        const valueStr = isArray(value) ? value.join(',') : value
        const cachedActived = cachedValues.get(valueStr)

        if (this.useCache) {
          // 以最新数据的label回显，异步搜索时，相同value的label可能会变
          if (cachedActived?.length) {
            const currentActivedVal = actived.map(item => item[valueField])
            const cachedActivedVal = cachedActived.map(item => item[valueField])
            if (isArrayEqual(currentActivedVal, cachedActivedVal)) {
              cachedValues.set(valueStr, actived)
            }
          } else {
            cachedValues.set(valueStr, actived)
          }
        }

        const currentActived = this.useCache ? cachedValues.get(valueStr) : actived
        const labels = currentActived?.length ? currentActived.map((item) => item[labelField]) : []
        if (this.formatter) {
          return this.formatter(labels, actived, value)
        }

        return labels.join(`${this.separator}`)
      }).filter((str) => str !== null && str !== undefined)
    },
    handleLoadDataEnd() {
      // 在 loadData 结束之后，并不会触发 data的watch，这里手动更新popper 的位置
      nextTick(() => {
        this.updatePopper()
      })
    },

    // render function
    renderSelectInput() {
      const selectInputProps = this.multiple
        ? toProps({
          value: this.options,
          readonly: this.m_disabled || !this.filterable,
          filterable: this.filterable,
          clearable: this.clearable,
          disabled: this.m_disabled,
          closable: !this.m_disabled, // 🤡this.closable
          placeholder: this.m_placeholder,
          'max-count': this.maxCount,
          icon: this.m_icon,
          multiple: this.multiple,
          fieldNames: this.m_fieldNames,
          status: this.m_status,
          size: this.m_size,
          genre: this.genre,
        })
        : toProps({
          value: this.option,
          size: this.m_size,
          placeholder: this.m_placeholder,
          filterable: this.filterable,
          disabled: this.m_disabled,
          readonly: !this.filterable,
          clearable: this.clearable,
          icon: this.m_icon,
          multiple: this.multiple,
          fieldNames: this.m_fieldNames,
          autoClearQuery: true,
          status: this.m_status,
          genre: this.genre,
        })

      const selectInputListeners = enhanceInsListeners(this.multiple
        ? {
          query: this.handleInputChange,
          remove: this.handleTagRemove,
          clear: this.handleClear,
          focus: this.handleFocus,
          click: this.handleInputClick,
          compositionstart: this.handleComposition,
          compositionupdate: this.handleComposition,
          compositionend: this.handleComposition,
        }
        : {
          focus: this.handleFocus,
          click: this.handleInputClick,
          clear: this.handleClear,
          query: this.handleInputChange,
          compositionstart: this.handleComposition,
          compositionupdate: this.handleComposition,
          compositionend: this.handleComposition,
        }, [
        'clickoutside',
        'update:visible',
        'focus',
        'blur',
        'clear',
        'filter',
        'input',
        'change',
        'active-item-change',
        'update:modelValue',
      ])
      const slots = getAllScopedSlots(this)

      return <mtd-select-input
        {...selectInputProps}
        ref="selectInputRef"
        {...selectInputListeners}
        {...vSlots(slots)}
        v-slots={slots}
      />
    },
    renderCascaderMenus() {
      const $render = getScopedSlotsInRender(this)
      const cascaderMenusProps = toProps({
        filterable: this.filterable && !this.remote,
        filter: this.filter,
        'filter-method': this.filterMethod,
        'filter-parent': !this.changeOnSelect,
        'menu-width': this.inputWidth,
        options: this.data,
        fieldNames: this.m_fieldNames,
        'change-on-select': this.changeOnSelect,
        'expand-trigger': this.expandTrigger,
        'load-data': this.loadData,
        loading: this.m_loading,
        'loading-text': this.loadingText,
        'no-data-text': this.noDataText,
        'no-match-text': this.noMatchText,
        'expanded-value': this.expandedValue,
        value: this.modelValue,
        multiple: this.multiple,
        'check-strictly': this.checkStrictly,
        'checked-strategy': this.checkedStrategy,
        'checked-values': this.modelValue,
        expandableOnDisabled: this.expandableOnDisabled,
        disabledStrictly: this.disabledStrictly,
        defaultExpandFirstItem: this.defaultExpandFirstItem && this.modelValue.length === 0,
        updateAfterLoaded: this.updateAfterLoaded,
        showSelectAll: this.showSelectAll,
        participateDisabled: this.participateDisabled,
        virtual: this.virtual,
        virtualOption: this.virtualOption,
      })
      const cascaderMenusListeners = {
        'expanded-change': this.handleExpandedChange,
        select: this.handleSelect,
        click: this.handleClickItem,
        'update:checkedValues': this.handleCheckedChange,
        'load-data-end': this.handleLoadDataEnd,
      }

      const cascaderMenusScopedSlots = {
        default: $render
          ? (props: { node: Node, data: any }) => $render({ node: props.node, data: props.node.data })
          : (props: { node: Node, data: any }) => <span>{props.node.label}</span>,
        ['addendum-header']: getSlotsInRender(this, 'addendum-header') ? () => getSlotsInRender(this, 'addendum-header') : undefined,
        ['addendum-footer']: getSlotsInRender(this, 'addendum-footer') ? () => getSlotsInRender(this, 'addendum-footer') : undefined,
      }

      return <mtd-cascader-menus
        {...cascaderMenusProps}
        on={cascaderMenusListeners}
        v-slots={cascaderMenusScopedSlots}
        {...vSlots(cascaderMenusScopedSlots)}
        ref="menusRef"
      />
    },
  },
  render() {
    const { prefix, disabled, m_visible, placement, appendToContainer, getPopupContainer, popperOptions,
      m_size, multiple, popperClass, restAttrs, hasValue, m_status,
    } = this
    return <popper
      popper-disabled={disabled}
      visible={m_visible}
      trigger="click"
      ref="popperRef"
      placement={placement}
      append-to-container={appendToContainer}
      get-popup-container={getPopupContainer}
      popper-options={popperOptions}
      onClickoutside={this.handleClickoutside}

      {...restAttrs}
      wrapperTag="div"
      class={{
        [prefix]: true,
        [`${prefix}-disabled`]: disabled,
        [`${prefix}-${m_size}`]: m_size,
        [`${prefix}-selected`]: hasValue,
        [`${prefix}-${m_status}`]: m_status,
        focus: m_visible,
      }}
    >
      <reference>
        {this.renderSelectInput()}
      </reference>
      <drop useShow={multiple} lazy={false} classProp={[popperClass, `${prefix}-popper`, m_size ? `${prefix}-popper-${m_size}` : '']}
      >
        {this.renderCascaderMenus()}
      </drop >
    </popper >
  },
})

```

#### demo.md

```markdown
# 级联 / Cascader

## 补充 Demo

### 默认展示下拉框

:::include(src="./doc/demo/default-visible.vue")
:::

### 属性

:::include(src="./doc/demo/props.vue")
:::

### 文本属性

:::include(src="./doc/demo/text.vue")
:::

### 格式化

:::include(src="./doc/demo/formatter.vue")
:::

### popper

:::include(src="./doc/demo/popper.vue")
:::

### 异步加载

:::include(src="./doc/demo/load.vue")
:::

### 多选/全选

:::include(src="./doc/demo/select-all.vue")
:::

### methods

:::include(src="./doc/demo/methods.vue")
:::

### 状态

:::include(src="./doc/demo/status.vue")
:::

### 虚拟滚动

:::include(src="./doc/demo/virtual.vue")
:::

### 多选自定义标签

:::include(src="./doc/demo/custom-tag.vue")
:::

### 下拉数据未包含 value

:::include(src="./doc/demo/unmatch-value.vue")
:::

```

#### doc/addendum.vue

```vue
<template>
  <div class="demo-wrapper">
    <mtd-tabs v-model="tab" type="text" size="small">
      <mtd-tab-pane label="顶部拓展" value="tab1">
        <mtd-cascader v-model="value1" :data="options" @change="handleChange">
          <template #addendum-header>
            <div class="demo-cascader-addendum">这是一段说明文字</div>
          </template>

        </mtd-cascader>
      </mtd-tab-pane>

      <mtd-tab-pane label="底部拓展" value="tab2">
        <mtd-cascader v-model="value2" :data="options" @change="handleChange">
          <template #addendum-footer>
            <div class="demo-cascader-addendum">这是一段说明文字</div>
          </template>
        </mtd-cascader>
      </mtd-tab-pane>


    </mtd-tabs>
  </div>
</template>
<script>
export default {
  data() {
    return {
      tab: 'tab1',
      options: [
        {
          value: 'zhinan',
          label: '指南',
          children: [
            {
              value: 'shejiyuanze',
              label: '设计原则',
              children: [
                {
                  value: 'yizhi',
                  label: '一致',
                },
                {
                  value: 'fankui',
                  label: '反馈',
                },
                {
                  value: 'xiaolv',
                  label: '效率',
                },
                {
                  value: 'kekong',
                  label: '可控',
                },
              ],
            },
            {
              value: 'daohang',
              label: '导航',
              children: [
                {
                  value: 'cexiangdaohang',
                  label: '侧向导航',
                },
                {
                  value: 'dingbudaohang',
                  label: '顶部导航',
                },
              ],
            },
          ],
        },
        {
          value: 'zujian',
          label: '组件',
          children: [
            {
              value: 'basic',
              label: 'Basic',
              disabled: true,
              children: [
                {
                  value: 'layout',
                  label: 'Layout 布局',
                },
                {
                  value: 'color',
                  label: 'Color 色彩',
                },
                {
                  value: 'typography',
                  label: 'Typography 字体',
                },
                {
                  value: 'icon',
                  label: 'Icon 图标',
                },
                {
                  value: 'button',
                  label: 'Button 按钮',
                },
              ],
            },
            {
              value: 'form',
              label: 'Form',
              isLeaf: true,
            },
            {
              value: 'data',
              label: 'Data',
              children: [
                {
                  value: 'table',
                  label: 'Table 表格',
                },
                {
                  value: 'tag',
                  label: 'Tag 标签',
                },
                {
                  value: 'progress',
                  label: 'Progress 进度条',
                },
                {
                  value: 'tree',
                  label: 'Tree 树形控件',
                },
                {
                  value: 'pagination',
                  label: 'Pagination 分页',
                },
                {
                  value: 'badge',
                  label: 'Badge 标记',
                },
              ],
            },
            {
              value: 'notice',
              label: 'Notice',
              children: [
                {
                  value: 'alert',
                  label: 'Alert 警告',
                },
                {
                  value: 'loading',
                  label: 'Loading 加载',
                },
                {
                  value: 'message',
                  label: 'Message 消息提示',
                },
                {
                  value: 'message-box',
                  label: 'MessageBox 弹框',
                },
                {
                  value: 'notification',
                  label: 'Notification 通知',
                },
              ],
            },
            {
              value: 'navigation',
              label: 'Navigation',
              children: [
                {
                  value: 'menu',
                  label: 'NavMenu 导航菜单',
                },
                {
                  value: 'tabs',
                  label: 'Tabs 标签页',
                },
                {
                  value: 'breadcrumb',
                  label: 'Breadcrumb 面包屑',
                },
                {
                  value: 'dropdown',
                  label: 'Dropdown 下拉菜单',
                },
                {
                  value: 'steps',
                  label: 'Steps 步骤条',
                },
              ],
            },
            {
              value: 'others',
              label: 'Others',
              children: [
                {
                  value: 'dialog',
                  label: 'Dialog 对话框',
                },
                {
                  value: 'tooltip',
                  label: 'Tooltip 文字提示',
                },
                {
                  value: 'popover',
                  label: 'Popover 弹出框',
                },
                {
                  value: 'card',
                  label: 'Card 卡片',
                },
                {
                  value: 'carousel',
                  label: 'Carousel 走马灯',
                },
                {
                  value: 'collapse',
                  label: 'Collapse 折叠面板',
                },
              ],
            },
          ],
        },
        {
          value: 'ziyuan',
          label: '资源',
          children: [
            {
              value: 'axure',
              label: 'Axure Components',
            },
            {
              value: 'sketch',
              label: 'Sketch Templates',
            },
            {
              value: 'jiaohu',
              label: '组件交互文档',
            },
          ],
        },
      ],
      value1: ['zhinan', 'shejiyuanze', 'yizhi'],
      value2: ['zhinan', 'shejiyuanze', 'yizhi'],
    }
  },
  methods: {
    handleChange(value, selectOptons) {
      console.log(value, selectOptons)
    },
  },
}
</script>
<style lang="scss" scoped>
.demo-cascader {
  ::v-deep .demo-source {
    padding: 0 16px;
  }
}

.demo-wrapper {
  width: 100%;

  ::v-deep .mtd-tab-pane {
    padding: 130px 0;
    display: flex;
    justify-content: space-around;

    .title {
      margin-bottom: 12px;
    }
  }

  ::v-deep .mtd-tabs-nav {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    border-bottom: 1px solid #edf0f7;
    padding: 1.5px 16px;
  }
}

.demo-cascader-addendum {
  text-align: left;
  height: 36px;
  line-height: 36px;
  color: rgba(0, 0, 0, 0.35);
  padding-left: 12px;
  font-size: 12px;
}
</style>

```

#### doc/base.vue

```vue
<template>
  <mtd-cascader 
    v-model="value" 
    :data="options" 
    @change="handleChange" 
    @update:visible="vis" 
    clearable
    defaultExpandFirstItem
  />
</template>
<script>
export default {
  data() {
    return {
      options: [
        {
          value: 'zhinan',
          label: '指南',
          children: [
            {
              value: 'shejiyuanze',
              label: '设计原则',
              children: [
                {
                  value: 'yizhi',
                  label: '一致',
                },
                {
                  value: 'fankui',
                  label: '反馈',
                },
                {
                  value: 'xiaolv',
                  label: '效率',
                },
                {
                  value: 'kekong',
                  label: '可控',
                },
              ],
            },
            {
              value: 'daohang',
              label: '导航',
              children: [
                {
                  value: 'cexiangdaohang',
                  label: '侧向导航',
                },
                {
                  value: 'dingbudaohang',
                  label: '顶部导航',
                },
              ],
            },
          ],
        },
        {
          value: 'zujian',
          label: '组件',
          children: [
            {
              value: 'basic',
              label: 'Basic',
              disabled: true,
              children: [
                {
                  value: 'layout',
                  label: 'Layout 布局',
                },
                {
                  value: 'color',
                  label: 'Color 色彩',
                },
                {
                  value: 'typography',
                  label: 'Typography 字体',
                },
                {
                  value: 'icon',
                  label: 'Icon 图标',
                },
                {
                  value: 'button',
                  label: 'Button 按钮',
                },
              ],
            },
            {
              value: 'form',
              label: 'Form',
              isLeaf: true,
            },
            {
              value: 'data',
              label: 'Data',
              children: [
                {
                  value: 'table',
                  label: 'Table 表格',
                },
                {
                  value: 'tag',
                  label: 'Tag 标签',
                },
                {
                  value: 'progress',
                  label: 'Progress 进度条',
                },
                {
                  value: 'tree',
                  label: 'Tree 树形控件',
                },
                {
                  value: 'pagination',
                  label: 'Pagination 分页',
                },
                {
                  value: 'badge',
                  label: 'Badge 标记',
                },
              ],
            },
            {
              value: 'notice',
              label: 'Notice',
              children: [
                {
                  value: 'alert',
                  label: 'Alert 警告',
                },
                {
                  value: 'loading',
                  label: 'Loading 加载',
                },
                {
                  value: 'message',
                  label: 'Message 消息提示',
                },
                {
                  value: 'message-box',
                  label: 'MessageBox 弹框',
                },
                {
                  value: 'notification',
                  label: 'Notification 通知',
                },
              ],
            },
            {
              value: 'navigation',
              label: 'Navigation',
              children: [
                {
                  value: 'menu',
                  label: 'NavMenu 导航菜单',
                },
                {
                  value: 'tabs',
                  label: 'Tabs 标签页',
                },
                {
                  value: 'breadcrumb',
                  label: 'Breadcrumb 面包屑',
                },
                {
                  value: 'dropdown',
                  label: 'Dropdown 下拉菜单',
                },
                {
                  value: 'steps',
                  label: 'Steps 步骤条',
                },
              ],
            },
            {
              value: 'others',
              label: 'Others',
              children: [
                {
                  value: 'dialog',
                  label: 'Dialog 对话框',
                },
                {
                  value: 'tooltip',
                  label: 'Tooltip 文字提示',
                },
                {
                  value: 'popover',
                  label: 'Popover 弹出框',
                },
                {
                  value: 'card',
                  label: 'Card 卡片',
                },
                {
                  value: 'carousel',
                  label: 'Carousel 走马灯',
                },
                {
                  value: 'collapse',
                  label: 'Collapse 折叠面板',
                },
              ],
            },
          ],
        },
        {
          value: 'ziyuan',
          label: '资源',
          children: [
            {
              value: 'axure',
              label: 'Axure Components',
            },
            {
              value: 'sketch',
              label: 'Sketch Templates',
            },
            {
              value: 'jiaohu',
              label: '组件交互文档',
            },
          ],
        },
      ],
      value: [],
      visible: false,
    }
  },
  methods: {
    handleChange(value, selectOptons) {
      console.log(value, selectOptons)
    },

    vis(v) {
      console.log(v)
    },
  },
}
</script>

```

#### doc/cascader-panel.vue

```vue
<template>
  <div class="demo-cascader-menus">
    <mtd-cascader-panel v-model="value1" :data="options" @change="handleChange" />
  </div>
</template>
<script>
export default {
  data() {
    return {
      options: [
        {
          value: 'zhinan',
          label: '指南',
          children: [
            {
              value: 'shejiyuanze',
              label: '设计原则',
              disabled: true,
              children: [
                {
                  value: 'yizhi',
                  label: '一致',
                },
                {
                  value: 'fankui',
                  label: '反馈',
                },
                {
                  value: 'xiaolv',
                  label: '效率',
                },
                {
                  value: 'kekong',
                  label: '可控',
                },
              ],
            },
            {
              value: 'daohang',
              label: '导航',
              children: [
                {
                  value: 'cexiangdaohang',
                  label: '侧向导航',
                },
                {
                  value: 'dingbudaohang',
                  label: '顶部导航',
                },
              ],
            },
          ],
        },
        {
          value: 'zujian',
          label: '组件',
          children: [
            {
              value: 'basic',
              label: 'Basic',
              disabled: true,
              children: [
                {
                  value: 'layout',
                  label: 'Layout 布局',
                },
                {
                  value: 'color',
                  label: 'Color 色彩',
                },
                {
                  value: 'typography',
                  label: 'Typography 字体',
                },
                {
                  value: 'icon',
                  label: 'Icon 图标',
                },
                {
                  value: 'button',
                  label: 'Button 按钮',
                },
              ],
            },
            {
              value: 'form',
              label: 'Form',
              isLeaf: true,
            },
            {
              value: 'data',
              label: 'Data',
              children: [
                {
                  value: 'table',
                  label: 'Table 表格',
                },
                {
                  value: 'tag',
                  label: 'Tag 标签',
                },
                {
                  value: 'progress',
                  label: 'Progress 进度条',
                },
                {
                  value: 'tree',
                  label: 'Tree 树形控件',
                },
                {
                  value: 'pagination',
                  label: 'Pagination 分页',
                },
                {
                  value: 'badge',
                  label: 'Badge 标记',
                },
              ],
            },
            {
              value: 'notice',
              label: 'Notice',
              children: [
                {
                  value: 'alert',
                  label: 'Alert 警告',
                },
                {
                  value: 'loading',
                  label: 'Loading 加载',
                },
                {
                  value: 'message',
                  label: 'Message 消息提示',
                },
                {
                  value: 'message-box',
                  label: 'MessageBox 弹框',
                },
                {
                  value: 'notification',
                  label: 'Notification 通知',
                },
              ],
            },
            {
              value: 'navigation',
              label: 'Navigation',
              children: [
                {
                  value: 'menu',
                  label: 'NavMenu 导航菜单',
                },
                {
                  value: 'tabs',
                  label: 'Tabs 标签页',
                },
                {
                  value: 'breadcrumb',
                  label: 'Breadcrumb 面包屑',
                },
                {
                  value: 'dropdown',
                  label: 'Dropdown 下拉菜单',
                },
                {
                  value: 'steps',
                  label: 'Steps 步骤条',
                },
              ],
            },
            {
              value: 'others',
              label: 'Others',
              children: [
                {
                  value: 'dialog',
                  label: 'Dialog 对话框',
                },
                {
                  value: 'tooltip',
                  label: 'Tooltip 文字提示',
                },
                {
                  value: 'popover',
                  label: 'Popover 弹出框',
                },
                {
                  value: 'card',
                  label: 'Card 卡片',
                },
                {
                  value: 'carousel',
                  label: 'Carousel 走马灯',
                },
                {
                  value: 'collapse',
                  label: 'Collapse 折叠面板',
                },
              ],
            },
          ],
        },
        {
          value: 'ziyuan',
          label: '资源',
          children: [
            {
              value: 'axure',
              label: 'Axure Components',
            },
            {
              value: 'sketch',
              label: 'Sketch Templates',
            },
            {
              value: 'jiaohu',
              label: '组件交互文档',
            },
          ],
        },
      ],
      value1: ['ziyuan', 'jiaohu'],
    }
  },
  methods: {
    handleChange(value, selectOptons) {
      console.log(value, selectOptons)
    },
  },
}
</script>

```

#### doc/changeOnSelect.vue

```vue
<template>
  <div class="demo-full-width">
    <div class="demo-cascader-change-on-select">
      <p>只能选择末级节点</p>
      <mtd-cascader :data="options" v-model="value" @active-item-change="handleChange" />
    </div>
    <div class="demo-cascader-change-on-select">
      <p>可选任意一级节点</p>
      <mtd-cascader :data="options" v-model="value2" change-on-select @active-item-change="handleChange" />
    </div>

  </div>
</template>

<script>
export default {
  data() {
    return {
      value: ['ziyuan', 'jiaohu'],
      value2: ['ziyuan'],
      options: [
        {
          value: 'zhinan',
          label: '指南',
          children: [
            {
              value: 'shejiyuanze',
              label: '设计原则',
              disabled: true,
              children: [
                {
                  value: 'yizhi',
                  label: '一致',
                },
                {
                  value: 'fankui',
                  label: '反馈',
                },
                {
                  value: 'xiaolv',
                  label: '效率',
                },
                {
                  value: 'kekong',
                  label: '可控',
                },
              ],
            },
            {
              value: 'daohang',
              label: '导航',
              children: [
                {
                  value: 'cexiangdaohang',
                  label: '侧向导航',
                },
                {
                  value: 'dingbudaohang',
                  label: '顶部导航',
                },
              ],
            },
          ],
        },
        {
          value: 'zujian',
          label: '组件',
          children: [
            {
              value: 'basic',
              label: 'Basic',
              children: [
                {
                  value: 'layout',
                  label: 'Layout 布局',
                },
                {
                  value: 'color',
                  label: 'Color 色彩',
                },
                {
                  value: 'typography',
                  label: 'Typography 字体',
                },
                {
                  value: 'icon',
                  label: 'Icon 图标',
                },
                {
                  value: 'button',
                  label: 'Button 按钮',
                },
              ],
            },
            {
              value: 'data',
              label: 'Data',
              children: [
                {
                  value: 'table',
                  label: 'Table 表格',
                },
                {
                  value: 'tag',
                  label: 'Tag 标签',
                },
                {
                  value: 'progress',
                  label: 'Progress 进度条',
                },
                {
                  value: 'tree',
                  label: 'Tree 树形控件',
                },
                {
                  value: 'pagination',
                  label: 'Pagination 分页',
                },
                {
                  value: 'badge',
                  label: 'Badge 标记',
                },
              ],
            },
            {
              value: 'notice',
              label: 'Notice',
              children: [
                {
                  value: 'alert',
                  label: 'Alert 警告',
                },
                {
                  value: 'loading',
                  label: 'Loading 加载',
                },
                {
                  value: 'message',
                  label: 'Message 消息提示',
                },
                {
                  value: 'message-box',
                  label: 'MessageBox 弹框',
                },
                {
                  value: 'notification',
                  label: 'Notification 通知',
                },
              ],
            },
            {
              value: 'navigation',
              label: 'Navigation',
              children: [
                {
                  value: 'menu',
                  label: 'NavMenu 导航菜单',
                },
                {
                  value: 'tabs',
                  label: 'Tabs 标签页',
                },
                {
                  value: 'breadcrumb',
                  label: 'Breadcrumb 面包屑',
                },
                {
                  value: 'dropdown',
                  label: 'Dropdown 下拉菜单',
                },
                {
                  value: 'steps',
                  label: 'Steps 步骤条',
                },
              ],
            },
            {
              value: 'others',
              label: 'Others',
              children: [
                {
                  value: 'dialog',
                  label: 'Dialog 对话框',
                },
                {
                  value: 'tooltip',
                  label: 'Tooltip 文字提示',
                },
                {
                  value: 'popover',
                  label: 'Popover 弹出框',
                },
                {
                  value: 'card',
                  label: 'Card 卡片',
                },
                {
                  value: 'carousel',
                  label: 'Carousel 走马灯',
                },
                {
                  value: 'collapse',
                  label: 'Collapse 折叠面板',
                },
              ],
            },
          ],
        },
        {
          value: 'ziyuan',
          label: '资源',
          children: [
            {
              value: 'axure',
              label: 'Axure Components',
            },
            {
              value: 'sketch',
              label: 'Sketch Templates',
            },
            {
              value: 'jiaohu',
              label: '组件交互文档',
            },
          ],
        },
      ],
    };
  },
  methods: {
    handleChange(value) {
      console.log(value)
    }
  }
};
</script>
<style lang="scss" scoped>
.demo-full-width {
  display: flex;
  justify-content: space-around;
}
</style>

```

#### doc/custom-formatter.vue

```vue
<template>
  <mtd-cascader :data="options" v-model="value" :formatter="formatter" />
</template>
<script>
export default {
  data() {
    return {
      value: ['zhinan', 'daohang'],
      options: [
        {
          value: 'zhinan',
          label: '指南',
          children: [
            {
              value: 'shejiyuanze',
              label: '设计原则',
              disabled: true,
              children: [
                {
                  value: 'yizhi',
                  label: '一致',
                },
                {
                  value: 'fankui',
                  label: '反馈',
                },
                {
                  value: 'xiaolv',
                  label: '效率',
                },
                {
                  value: 'kekong',
                  label: '可控',
                },
              ],
            },
            {
              value: 'daohang',
              label: '导航'
            },
          ],
        },
        {
          value: 'zujian',
          label: '组件',
          children: [
            {
              value: 'basic',
              label: 'Basic',
              children: [
                {
                  value: 'layout',
                  label: 'Layout 布局',
                },
                {
                  value: 'color',
                  label: 'Color 色彩',
                },
                {
                  value: 'typography',
                  label: 'Typography 字体',
                },
                {
                  value: 'icon',
                  label: 'Icon 图标',
                },
                {
                  value: 'button',
                  label: 'Button 按钮',
                },
              ],
            },
            {
              value: 'data',
              label: 'Data',
              children: [
                {
                  value: 'table',
                  label: 'Table 表格',
                },
                {
                  value: 'tag',
                  label: 'Tag 标签',
                },
                {
                  value: 'progress',
                  label: 'Progress 进度条',
                },
                {
                  value: 'tree',
                  label: 'Tree 树形控件',
                },
                {
                  value: 'pagination',
                  label: 'Pagination 分页',
                },
                {
                  value: 'badge',
                  label: 'Badge 标记',
                },
              ],
            },
            {
              value: 'notice',
              label: 'Notice',
              children: [
                {
                  value: 'alert',
                  label: 'Alert 警告',
                },
                {
                  value: 'loading',
                  label: 'Loading 加载',
                },
                {
                  value: 'message',
                  label: 'Message 消息提示',
                },
                {
                  value: 'message-box',
                  label: 'MessageBox 弹框',
                },
                {
                  value: 'notification',
                  label: 'Notification 通知',
                },
              ],
            },
            {
              value: 'navigation',
              label: 'Navigation',
              children: [
                {
                  value: 'menu',
                  label: 'NavMenu 导航菜单',
                },
                {
                  value: 'tabs',
                  label: 'Tabs 标签页',
                },
                {
                  value: 'breadcrumb',
                  label: 'Breadcrumb 面包屑',
                },
                {
                  value: 'dropdown',
                  label: 'Dropdown 下拉菜单',
                },
                {
                  value: 'steps',
                  label: 'Steps 步骤条',
                },
              ],
            },
            {
              value: 'others',
              label: 'Others',
              children: [
                {
                  value: 'dialog',
                  label: 'Dialog 对话框',
                },
                {
                  value: 'tooltip',
                  label: 'Tooltip 文字提示',
                },
                {
                  value: 'popover',
                  label: 'Popover 弹出框',
                },
                {
                  value: 'card',
                  label: 'Card 卡片',
                },
                {
                  value: 'carousel',
                  label: 'Carousel 走马灯',
                },
                {
                  value: 'collapse',
                  label: 'Collapse 折叠面板',
                },
              ],
            },
          ],
        },
        {
          value: 'ziyuan',
          label: '资源',
          children: [
            {
              value: 'axure',
              label: 'Axure Components',
            },
            {
              value: 'sketch',
              label: 'Sketch Templates',
            },
            {
              value: 'jiaohu',
              label: '组件交互文档',
            },
          ],
        },
      ],
    };
  },
  methods: {
    formatter(labels, selectOptions, value) {
      const lableStr = labels.join('/');
      return value?.length ? `${lableStr}（${value}）` : ''
    },
  },
};
</script>

```

#### doc/custom.vue

```vue
<template>
  <mtd-cascader :data="options" v-model="value" @change="handleChange">
    <template v-slot="{ node }">
      <span>{{ node.label }} ({{ node.value }}) </span>
    </template>
  </mtd-cascader>
</template>
<script>
export default {
  data() {
    return {
      options: [
        {
          value: 'zhinan',
          label: '指南',
          children: [
            {
              value: 'shejiyuanze',
              label: '设计原则',
              disabled: true,
              children: [
                {
                  value: 'yizhi',
                  label: '一致',
                },
                {
                  value: 'fankui',
                  label: '反馈',
                },
                {
                  value: 'xiaolv',
                  label: '效率',
                },
                {
                  value: 'kekong',
                  label: '可控',
                },
              ],
            },
            {
              value: 'daohang',
              label: '导航',
              children: [
                {
                  value: 'cexiangdaohang',
                  label: '侧向导航',
                },
                {
                  value: 'dingbudaohang',
                  label: '顶部导航',
                },
              ],
            },
          ],
        },
        {
          value: 'zujian',
          label: '组件',
          children: [
            {
              value: 'basic',
              label: 'Basic',
              disabled: true,
              children: [
                {
                  value: 'layout',
                  label: 'Layout 布局',
                },
                {
                  value: 'color',
                  label: 'Color 色彩',
                },
                {
                  value: 'typography',
                  label: 'Typography 字体',
                },
                {
                  value: 'icon',
                  label: 'Icon 图标',
                },
                {
                  value: 'button',
                  label: 'Button 按钮',
                },
              ],
            },
            {
              value: 'form',
              label: 'Form',
              isLeaf: true,
            },
            {
              value: 'data',
              label: 'Data',
              children: [
                {
                  value: 'table',
                  label: 'Table 表格',
                },
                {
                  value: 'tag',
                  label: 'Tag 标签',
                },
                {
                  value: 'progress',
                  label: 'Progress 进度条',
                },
                {
                  value: 'tree',
                  label: 'Tree 树形控件',
                },
                {
                  value: 'pagination',
                  label: 'Pagination 分页',
                },
                {
                  value: 'badge',
                  label: 'Badge 标记',
                },
              ],
            },
            {
              value: 'notice',
              label: 'Notice',
              children: [
                {
                  value: 'alert',
                  label: 'Alert 警告',
                },
                {
                  value: 'loading',
                  label: 'Loading 加载',
                },
                {
                  value: 'message',
                  label: 'Message 消息提示',
                },
                {
                  value: 'message-box',
                  label: 'MessageBox 弹框',
                },
                {
                  value: 'notification',
                  label: 'Notification 通知',
                },
              ],
            },
            {
              value: 'navigation',
              label: 'Navigation',
              children: [
                {
                  value: 'menu',
                  label: 'NavMenu 导航菜单',
                },
                {
                  value: 'tabs',
                  label: 'Tabs 标签页',
                },
                {
                  value: 'breadcrumb',
                  label: 'Breadcrumb 面包屑',
                },
                {
                  value: 'dropdown',
                  label: 'Dropdown 下拉菜单',
                },
                {
                  value: 'steps',
                  label: 'Steps 步骤条',
                },
              ],
            },
            {
              value: 'others',
              label: 'Others',
              children: [
                {
                  value: 'dialog',
                  label: 'Dialog 对话框',
                },
                {
                  value: 'tooltip',
                  label: 'Tooltip 文字提示',
                },
                {
                  value: 'popover',
                  label: 'Popover 弹出框',
                },
                {
                  value: 'card',
                  label: 'Card 卡片',
                },
                {
                  value: 'carousel',
                  label: 'Carousel 走马灯',
                },
                {
                  value: 'collapse',
                  label: 'Collapse 折叠面板',
                },
              ],
            },
          ],
        },
        {
          value: 'ziyuan',
          label: '资源',
          children: [
            {
              value: 'axure',
              label: 'Axure Components',
            },
            {
              value: 'sketch',
              label: 'Sketch Templates',
            },
            {
              value: 'jiaohu',
              label: '组件交互文档',
            },
          ],
        },
      ],
      value: ['ziyuan', 'jiaohu'],
    };
  },
  methods: {
    handleChange(value, selectOptons) {
      console.log(value, selectOptons);
    },
  },
};
</script>

```

#### doc/demo/custom-tag.vue

```vue
<template>
  <mtd-cascader style="width:400px" multiple :data="options" v-model="value" closeable size="large" clearable max-count="responsive"
    @change="handleChange">

    <template #tag="scope">
      {{ renderTag(scope) }}
      <mtd-tooltip :content="scope.option.label">
        <mtd-tag theme="blue" type="ghost">{{ scope.option.label }}</mtd-tag>
      </mtd-tooltip>
    </template>

  </mtd-cascader>
</template>
<script>
export default {
  data() {
    return {
      options: [
        {
          value: 'zhinan',
          label: '指南',
          children: [
            {
              value: 'shejiyuanze',
              label: '设计原则',
              children: [
                {
                  value: 'yizhi',
                  label: '一致',
                },
                {
                  value: 'fankui',
                  label: '反馈',
                },
                {
                  value: 'xiaolv',
                  label: '效率',
                },
                {
                  value: 'kekong',
                  label: '可控',
                },
              ],
            },
            {
              value: 'daohang',
              label: '导航',
              children: [
                {
                  value: 'cexiangdaohang',
                  label: '侧向导航',
                },
                {
                  value: 'dingbudaohang',
                  label: '顶部导航',
                },
              ],
            },
          ],
        },
        {
          value: 'zujian',
          label: '组件',
          children: [
            {
              value: 'basic',
              label: 'Basic',
              disabled: true,
              children: [
                {
                  value: 'layout',
                  label: 'Layout 布局',
                },
                {
                  value: 'color',
                  label: 'Color 色彩',
                },
                {
                  value: 'typography',
                  label: 'Typography 字体',
                },
                {
                  value: 'icon',
                  label: 'Icon 图标',
                },
                {
                  value: 'button',
                  label: 'Button 按钮',
                },
              ],
            },
            {
              value: 'form',
              label: 'Form',
              isLeaf: true,
            },
            {
              value: 'data',
              label: 'Data',
              children: [
                {
                  value: 'table',
                  label: 'Table 表格',
                },
                {
                  value: 'tag',
                  label: 'Tag 标签',
                },
                {
                  value: 'progress',
                  label: 'Progress 进度条',
                },
                {
                  value: 'tree',
                  label: 'Tree 树形控件',
                },
                {
                  value: 'pagination',
                  label: 'Pagination 分页',
                },
                {
                  value: 'badge',
                  label: 'Badge 标记',
                },
              ],
            },
            {
              value: 'notice',
              label: 'Notice',
              children: [
                {
                  value: 'alert',
                  label: 'Alert 警告',
                },
                {
                  value: 'loading',
                  label: 'Loading 加载',
                },
                {
                  value: 'message',
                  label: 'Message 消息提示',
                },
                {
                  value: 'message-box',
                  label: 'MessageBox 弹框',
                },
                {
                  value: 'notification',
                  label: 'Notification 通知',
                },
              ],
            },
            {
              value: 'navigation',
              label: 'Navigation',
              children: [
                {
                  value: 'menu',
                  label: 'NavMenu 导航菜单',
                },
                {
                  value: 'tabs',
                  label: 'Tabs 标签页',
                },
                {
                  value: 'breadcrumb',
                  label: 'Breadcrumb 面包屑',
                },
                {
                  value: 'dropdown',
                  label: 'Dropdown 下拉菜单',
                },
                {
                  value: 'steps',
                  label: 'Steps 步骤条',
                },
              ],
            },
            {
              value: 'others',
              label: 'Others',
              children: [
                {
                  value: 'dialog',
                  label: 'Dialog 对话框',
                },
                {
                  value: 'tooltip',
                  label: 'Tooltip 文字提示',
                },
                {
                  value: 'popover',
                  label: 'Popover 弹出框',
                },
                {
                  value: 'card',
                  label: 'Card 卡片',
                },
                {
                  value: 'carousel',
                  label: 'Carousel 走马灯',
                },
                {
                  value: 'collapse',
                  label: 'Collapse 折叠面板',
                },
              ],
            },
          ],
        },
        {
          value: 'ziyuan',
          label: '资源',
          children: [
            {
              value: 'axure',
              label: 'Axure Components',
            },
            {
              value: 'sketch',
              label: 'Sketch Templates',
            },
            {
              value: 'jiaohu',
              label: '组件交互文档',
            },
          ],
        },
      ],
      value: [],
    }
  },
  methods: {
    handleChange(value, selectOptons) {
      console.log(value, selectOptons)
    },
    renderTag(scope) {
      console.log('*** scope', scope)
    }
  },
}
</script>

```

#### doc/demo/default-visible.vue

```vue
<template>
  <mtd-cascader
    v-model="value"
    :data="options"
    default-visible
    @update:visible="vis"
  />
</template>
<script>
export default {
  data() {
    return {
      options: [
        {
          value: 'zhinan',
          label: '指南',
          children: [
            {
              value: 'shejiyuanze',
              label: '设计原则',
              children: [
                {
                  value: 'yizhi',
                  label: '一致',
                },
                {
                  value: 'fankui',
                  label: '反馈',
                },
                {
                  value: 'xiaolv',
                  label: '效率',
                },
                {
                  value: 'kekong',
                  label: '可控',
                },
              ],
            },
            {
              value: 'daohang',
              label: '导航',
              children: [
                {
                  value: 'cexiangdaohang',
                  label: '侧向导航',
                },
                {
                  value: 'dingbudaohang',
                  label: '顶部导航',
                },
              ],
            },
          ],
        },
        {
          value: 'zujian',
          label: '组件',
          children: [
            {
              value: 'basic',
              label: 'Basic',
              disabled: true,
              children: [
                {
                  value: 'layout',
                  label: 'Layout 布局',
                },
                {
                  value: 'color',
                  label: 'Color 色彩',
                },
                {
                  value: 'typography',
                  label: 'Typography 字体',
                },
                {
                  value: 'icon',
                  label: 'Icon 图标',
                },
                {
                  value: 'button',
                  label: 'Button 按钮',
                },
              ],
            },
            {
              value: 'form',
              label: 'Form',
              isLeaf: true,
            },
            {
              value: 'data',
              label: 'Data',
              children: [
                {
                  value: 'table',
                  label: 'Table 表格',
                },
                {
                  value: 'tag',
                  label: 'Tag 标签',
                },
                {
                  value: 'progress',
                  label: 'Progress 进度条',
                },
                {
                  value: 'tree',
                  label: 'Tree 树形控件',
                },
                {
                  value: 'pagination',
                  label: 'Pagination 分页',
                },
                {
                  value: 'badge',
                  label: 'Badge 标记',
                },
              ],
            },
            {
              value: 'notice',
              label: 'Notice',
              children: [
                {
                  value: 'alert',
                  label: 'Alert 警告',
                },
                {
                  value: 'loading',
                  label: 'Loading 加载',
                },
                {
                  value: 'message',
                  label: 'Message 消息提示',
                },
                {
                  value: 'message-box',
                  label: 'MessageBox 弹框',
                },
                {
                  value: 'notification',
                  label: 'Notification 通知',
                },
              ],
            },
            {
              value: 'navigation',
              label: 'Navigation',
              children: [
                {
                  value: 'menu',
                  label: 'NavMenu 导航菜单',
                },
                {
                  value: 'tabs',
                  label: 'Tabs 标签页',
                },
                {
                  value: 'breadcrumb',
                  label: 'Breadcrumb 面包屑',
                },
                {
                  value: 'dropdown',
                  label: 'Dropdown 下拉菜单',
                },
                {
                  value: 'steps',
                  label: 'Steps 步骤条',
                },
              ],
            },
            {
              value: 'others',
              label: 'Others',
              children: [
                {
                  value: 'dialog',
                  label: 'Dialog 对话框',
                },
                {
                  value: 'tooltip',
                  label: 'Tooltip 文字提示',
                },
                {
                  value: 'popover',
                  label: 'Popover 弹出框',
                },
                {
                  value: 'card',
                  label: 'Card 卡片',
                },
                {
                  value: 'carousel',
                  label: 'Carousel 走马灯',
                },
                {
                  value: 'collapse',
                  label: 'Collapse 折叠面板',
                },
              ],
            },
          ],
        },
        {
          value: 'ziyuan',
          label: '资源',
          children: [
            {
              value: 'axure',
              label: 'Axure Components',
            },
            {
              value: 'sketch',
              label: 'Sketch Templates',
            },
            {
              value: 'jiaohu',
              label: '组件交互文档',
            },
          ],
        },
      ],
      value: [],
      visible: false,
    }
  },
  methods: {
    vis(v) {
      console.log(v)
    },
  },
}
</script>

```

#### doc/demo/formatter.vue

```vue
<template>
  <div>
    <mtd-row>
      <mtd-col :span="12">
        <p>separator</p>
        <mtd-cascader v-model="value1" :data="options" separator="---" />
      </mtd-col>
      <mtd-col :span="12">
        <p>separator和formatter混用</p>
        <mtd-cascader v-model="value2" style="width: 300px;" :data="options" separator="---" :formatter="formatter" />
      </mtd-col>
    </mtd-row>
  </div>
</template>
<script>
export default {
  data() {
    return {
      options: [
        {
          value: 'zhinan',
          label: '指南',
          children: [
            {
              value: 'shejiyuanze',
              label: '设计原则',
              children: [
                {
                  value: 'yizhi',
                  label: '一致',
                },
                {
                  value: 'fankui',
                  label: '反馈',
                },
                {
                  value: 'xiaolv',
                  label: '效率',
                },
                {
                  value: 'kekong',
                  label: '可控',
                },
              ],
            },
            {
              value: 'daohang',
              label: '导航',
              children: [
                {
                  value: 'cexiangdaohang',
                  label: '侧向导航',
                },
                {
                  value: 'dingbudaohang',
                  label: '顶部导航',
                },
              ],
            },
          ],
        },
        {
          value: 'zujian',
          label: '组件',
          children: [
            {
              value: 'basic',
              label: 'Basic',
              disabled: true,
              children: [
                {
                  value: 'layout',
                  label: 'Layout 布局',
                },
                {
                  value: 'color',
                  label: 'Color 色彩',
                },
                {
                  value: 'typography',
                  label: 'Typography 字体',
                },
                {
                  value: 'icon',
                  label: 'Icon 图标',
                },
                {
                  value: 'button',
                  label: 'Button 按钮',
                },
              ],
            },
            {
              value: 'form',
              label: 'Form',
              isLeaf: true,
            },
            {
              value: 'data',
              label: 'Data',
              children: [
                {
                  value: 'table',
                  label: 'Table 表格',
                },
                {
                  value: 'tag',
                  label: 'Tag 标签',
                },
                {
                  value: 'progress',
                  label: 'Progress 进度条',
                },
                {
                  value: 'tree',
                  label: 'Tree 树形控件',
                },
                {
                  value: 'pagination',
                  label: 'Pagination 分页',
                },
                {
                  value: 'badge',
                  label: 'Badge 标记',
                },
              ],
            },
            {
              value: 'notice',
              label: 'Notice',
              children: [
                {
                  value: 'alert',
                  label: 'Alert 警告',
                },
                {
                  value: 'loading',
                  label: 'Loading 加载',
                },
                {
                  value: 'message',
                  label: 'Message 消息提示',
                },
                {
                  value: 'message-box',
                  label: 'MessageBox 弹框',
                },
                {
                  value: 'notification',
                  label: 'Notification 通知',
                },
              ],
            },
            {
              value: 'navigation',
              label: 'Navigation',
              children: [
                {
                  value: 'menu',
                  label: 'NavMenu 导航菜单',
                },
                {
                  value: 'tabs',
                  label: 'Tabs 标签页',
                },
                {
                  value: 'breadcrumb',
                  label: 'Breadcrumb 面包屑',
                },
                {
                  value: 'dropdown',
                  label: 'Dropdown 下拉菜单',
                },
                {
                  value: 'steps',
                  label: 'Steps 步骤条',
                },
              ],
            },
            {
              value: 'others',
              label: 'Others',
              children: [
                {
                  value: 'dialog',
                  label: 'Dialog 对话框',
                },
                {
                  value: 'tooltip',
                  label: 'Tooltip 文字提示',
                },
                {
                  value: 'popover',
                  label: 'Popover 弹出框',
                },
                {
                  value: 'card',
                  label: 'Card 卡片',
                },
                {
                  value: 'carousel',
                  label: 'Carousel 走马灯',
                },
                {
                  value: 'collapse',
                  label: 'Collapse 折叠面板',
                },
              ],
            },
          ],
        },
        {
          value: 'ziyuan',
          label: '资源',
          children: [
            {
              value: 'axure',
              label: 'Axure Components',
            },
            {
              value: 'sketch',
              label: 'Sketch Templates',
            },
            {
              value: 'jiaohu',
              label: '组件交互文档',
            },
          ],
        },
      ],
      value1: ['ziyuan', 'axure'],
      value2: ['ziyuan', 'axure']
    }
  },
  methods: {
    formatter(labels, selectOptions, value) {
      const lableStr = labels.join('/');
      return value?.length ? `${lableStr}（${value}）` : ''
    },
  },
}
</script>

```

#### doc/demo/load.vue

```vue
<template>
  <div>
    <div>
      <p>update-after-loaded</p>
      <div>
        <mtd-radio-group v-model="updateAfterLoaded" @change="changeUpdateAfterLoaded">
          <mtd-radio :value=true>true</mtd-radio>
          <mtd-radio :value=false>false</mtd-radio>
        </mtd-radio-group>
      </div>

      <mtd-cascader :data="options" :load-data="loadData" @change="handleChange" v-model="value"
        :update-after-loaded="updateAfterLoaded" @active-item-change="activeItemChange" />

    </div>
    <div>
      <p>异步加载配合fieldNames</p>
      <mtd-cascader :data="options1" :load-data="loadFieldNamesData" @change="handleChange" v-model="value1"
        :fieldNames="fieldNames" :update-after-loaded="updateAfterLoaded" />
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      value: ['zhejiang', 'zhejiang1', 'dynamic2'],
      value1: ['zhejiang', 'zhejiang1', 'dynamic2'],
      updateAfterLoaded: false,
      options: [
        {
          value: 'zhejiang',
          label: 'Zhejiang',
          isLeaf: false,
          children: [{
            value: 'zhejiang1',
            label: 'Zhejiang1',
          }]
        },
        {
          value: 'jiangsu',
          label: 'Jiangsu',
          isLeaf: false,
        },
      ],
      fieldNames: {
        label: 'name',
        value: 'id'
      },
      options1: [
        {
          id: 'zhejiang',
          name: 'Zhejiang',
          children: [{
            id: 'zhejiang1',
            name: 'Zhejiang1',
          }]
        },
        {
          id: 'jiangsu',
          name: 'Jiangsu',

        },
      ],
    };
  },
  methods: {
    handleChange(value) {
      console.log(value);
    },
    loadData(item, callback) {
      setTimeout(() => {
        callback([
          {
            label: `${item.label} Dynamic 1`,
            value: 'dynamic1',
            isLeaf: true,
          },
          {
            label: `${item.label} Dynamic 2`,
            value: 'dynamic2',
            isLeaf: true,
          },
        ]);
      }, 1000);
    },
    loadFieldNamesData(item, callback) {
      setTimeout(() => {
        callback([
          {
            name: `${item.name} Dynamic 1`,
            id: 'dynamic1',
            isLeaf: true,
          },
          {
            name: `${item.name} Dynamic 2`,
            id: 'dynamic2',
            isLeaf: true,
          },
        ]);
      }, 1000);
    },
    changeUpdateAfterLoaded(v) {
      this.updateAfterLoaded = v
    },
    activeItemChange(value) {
      console.log('activeItemChange', value)
    },
  }
};
</script>

```

#### doc/demo/methods.vue

```vue
<template>
  <div style="width: 100%;">
    <mtd-cascader ref="cascader" v-model="value" :data="options" />
    <mtd-button @click="focus">focus, 2s后失焦</mtd-button>
    <mtd-button @click="blur">blur</mtd-button>
  </div>
</template>
<script>
export default {
  data() {
    return {
      options: [
        {
          value: 'zhinan',
          label: '指南',
          children: [
            {
              value: 'shejiyuanze',
              label: '设计原则',
              children: [
                {
                  value: 'yizhi',
                  label: '一致',
                },
                {
                  value: 'fankui',
                  label: '反馈',
                },
                {
                  value: 'xiaolv',
                  label: '效率',
                },
                {
                  value: 'kekong',
                  label: '可控',
                },
              ],
            },
            {
              value: 'daohang',
              label: '导航',
              children: [
                {
                  value: 'cexiangdaohang',
                  label: '侧向导航',
                },
                {
                  value: 'dingbudaohang',
                  label: '顶部导航',
                },
              ],
            },
          ],
        },
        {
          value: 'zujian',
          label: '组件',
          children: [
            {
              value: 'basic',
              label: 'Basic',
              disabled: true,
              children: [
                {
                  value: 'layout',
                  label: 'Layout 布局',
                },
                {
                  value: 'color',
                  label: 'Color 色彩',
                },
                {
                  value: 'typography',
                  label: 'Typography 字体',
                },
                {
                  value: 'icon',
                  label: 'Icon 图标',
                },
                {
                  value: 'button',
                  label: 'Button 按钮',
                },
              ],
            },
            {
              value: 'form',
              label: 'Form',
              isLeaf: true,
            },
            {
              value: 'data',
              label: 'Data',
              children: [
                {
                  value: 'table',
                  label: 'Table 表格',
                },
                {
                  value: 'tag',
                  label: 'Tag 标签',
                },
                {
                  value: 'progress',
                  label: 'Progress 进度条',
                },
                {
                  value: 'tree',
                  label: 'Tree 树形控件',
                },
                {
                  value: 'pagination',
                  label: 'Pagination 分页',
                },
                {
                  value: 'badge',
                  label: 'Badge 标记',
                },
              ],
            },
            {
              value: 'notice',
              label: 'Notice',
              children: [
                {
                  value: 'alert',
                  label: 'Alert 警告',
                },
                {
                  value: 'loading',
                  label: 'Loading 加载',
                },
                {
                  value: 'message',
                  label: 'Message 消息提示',
                },
                {
                  value: 'message-box',
                  label: 'MessageBox 弹框',
                },
                {
                  value: 'notification',
                  label: 'Notification 通知',
                },
              ],
            },
            {
              value: 'navigation',
              label: 'Navigation',
              children: [
                {
                  value: 'menu',
                  label: 'NavMenu 导航菜单',
                },
                {
                  value: 'tabs',
                  label: 'Tabs 标签页',
                },
                {
                  value: 'breadcrumb',
                  label: 'Breadcrumb 面包屑',
                },
                {
                  value: 'dropdown',
                  label: 'Dropdown 下拉菜单',
                },
                {
                  value: 'steps',
                  label: 'Steps 步骤条',
                },
              ],
            },
            {
              value: 'others',
              label: 'Others',
              children: [
                {
                  value: 'dialog',
                  label: 'Dialog 对话框',
                },
                {
                  value: 'tooltip',
                  label: 'Tooltip 文字提示',
                },
                {
                  value: 'popover',
                  label: 'Popover 弹出框',
                },
                {
                  value: 'card',
                  label: 'Card 卡片',
                },
                {
                  value: 'carousel',
                  label: 'Carousel 走马灯',
                },
                {
                  value: 'collapse',
                  label: 'Collapse 折叠面板',
                },
              ],
            },
          ],
        },
        {
          value: 'ziyuan',
          label: '资源',
          children: [
            {
              value: 'axure',
              label: 'Axure Components',
            },
            {
              value: 'sketch',
              label: 'Sketch Templates',
            },
            {
              value: 'jiaohu',
              label: '组件交互文档',
            },
          ],
        },
      ],
      value: [],
    }
  },
  methods: {
    focus() {
      this.$refs.cascader.focus()
      setTimeout(() => {
        this.blur()
      }, 2000)
    },
    blur() {
      this.$refs.cascader.blur()
    }
  }
}
</script>

```

#### doc/demo/popper.vue

```vue
<template>
  <div id="popper-demo" style="width: 100%;">
    <mtd-row>
      <mtd-col :span="12">
        <p>placement</p>
        <mtd-cascader v-model="value1" :data="options" placement="left" />
      </mtd-col>
      <mtd-col :span="12">
        <p>popper-options</p>
        <mtd-cascader v-model="value2" :data="options" :popper-options="popperOptions" />
      </mtd-col>
    </mtd-row>

    <mtd-row>
      <mtd-col :span="12">
        <p>append-to-container</p>
        <mtd-cascader v-model="value3" :data="options" :append-to-container="false" />
      </mtd-col>

      <mtd-col :span="12">
        <p>get-popup-container</p>
        <mtd-cascader v-model="value1" :data="options" :get-popup-container="getContainer" />
      </mtd-col>
    </mtd-row>
  </div>
</template>
<script>
export default {
  data() {
    return {
      popperOptions: {
        offset: {
          offset: 100 // 设置下拉框偏移量，可参考https://popper.js.org/docs/v1/#modifiers..offset
        },
      },
      options: [
        {
          value: 'zhinan',
          label: '指南',
          children: [
            {
              value: 'shejiyuanze',
              label: '设计原则',
              children: [
                {
                  value: 'yizhi',
                  label: '一致',
                },
                {
                  value: 'fankui',
                  label: '反馈',
                },
                {
                  value: 'xiaolv',
                  label: '效率',
                },
                {
                  value: 'kekong',
                  label: '可控',
                },
              ],
            },
            {
              value: 'daohang',
              label: '导航',
              children: [
                {
                  value: 'cexiangdaohang',
                  label: '侧向导航',
                },
                {
                  value: 'dingbudaohang',
                  label: '顶部导航',
                },
              ],
            },
          ],
        },
        {
          value: 'zujian',
          label: '组件',
          children: [
            {
              value: 'basic',
              label: 'Basic',
              disabled: true,
              children: [
                {
                  value: 'layout',
                  label: 'Layout 布局',
                },
                {
                  value: 'color',
                  label: 'Color 色彩',
                },
                {
                  value: 'typography',
                  label: 'Typography 字体',
                },
                {
                  value: 'icon',
                  label: 'Icon 图标',
                },
                {
                  value: 'button',
                  label: 'Button 按钮',
                },
              ],
            },
            {
              value: 'form',
              label: 'Form',
              isLeaf: true,
            },
            {
              value: 'data',
              label: 'Data',
              children: [
                {
                  value: 'table',
                  label: 'Table 表格',
                },
                {
                  value: 'tag',
                  label: 'Tag 标签',
                },
                {
                  value: 'progress',
                  label: 'Progress 进度条',
                },
                {
                  value: 'tree',
                  label: 'Tree 树形控件',
                },
                {
                  value: 'pagination',
                  label: 'Pagination 分页',
                },
                {
                  value: 'badge',
                  label: 'Badge 标记',
                },
              ],
            },
            {
              value: 'notice',
              label: 'Notice',
              children: [
                {
                  value: 'alert',
                  label: 'Alert 警告',
                },
                {
                  value: 'loading',
                  label: 'Loading 加载',
                },
                {
                  value: 'message',
                  label: 'Message 消息提示',
                },
                {
                  value: 'message-box',
                  label: 'MessageBox 弹框',
                },
                {
                  value: 'notification',
                  label: 'Notification 通知',
                },
              ],
            },
            {
              value: 'navigation',
              label: 'Navigation',
              children: [
                {
                  value: 'menu',
                  label: 'NavMenu 导航菜单',
                },
                {
                  value: 'tabs',
                  label: 'Tabs 标签页',
                },
                {
                  value: 'breadcrumb',
                  label: 'Breadcrumb 面包屑',
                },
                {
                  value: 'dropdown',
                  label: 'Dropdown 下拉菜单',
                },
                {
                  value: 'steps',
                  label: 'Steps 步骤条',
                },
              ],
            },
            {
              value: 'others',
              label: 'Others',
              children: [
                {
                  value: 'dialog',
                  label: 'Dialog 对话框',
                },
                {
                  value: 'tooltip',
                  label: 'Tooltip 文字提示',
                },
                {
                  value: 'popover',
                  label: 'Popover 弹出框',
                },
                {
                  value: 'card',
                  label: 'Card 卡片',
                },
                {
                  value: 'carousel',
                  label: 'Carousel 走马灯',
                },
                {
                  value: 'collapse',
                  label: 'Collapse 折叠面板',
                },
              ],
            },
          ],
        },
        {
          value: 'ziyuan',
          label: '资源',
          children: [
            {
              value: 'axure',
              label: 'Axure Components',
            },
            {
              value: 'sketch',
              label: 'Sketch Templates',
            },
            {
              value: 'jiaohu',
              label: '组件交互文档',
            },
          ],
        },
      ],
      value1: ['ziyuan', 'axure'],
      value2: ['ziyuan', 'axure'],
      value3: [],
      value4: []
    }
  },
  methods: {
    getContainer() {
      return document.getElementById('popper-demo')
    }
  }
}
</script>
<style lang="scss" scoped>
#popper-demo {
  .mtd-row+.mtd-row {
    margin-top: 36px;
  }
}
</style>

```

#### doc/demo/props.vue

```vue
<template>
  <div class="demo-wrapper">
    <mtd-row>
      <p>visible受控</p>
      <mtd-radio-group v-model="visible">
        <mtd-radio :value="true">true</mtd-radio>
        <mtd-radio :value="false">false</mtd-radio>
      </mtd-radio-group>
      <mtd-cascader v-model="value1" :data="options" @change="handleChange" @update:visible="handleVis" :visible="visible"
        defaultExpandFirstItem />
    </mtd-row>

    <mtd-row>
      <mtd-col :span="8">
        <p>checked-strategy 等于all时</p>
        <mtd-cascader v-model="value2" :data="options" multiple checked-strategy="all" />
      </mtd-col>

      <mtd-col :span="8">
        <p>checked-strategy 等于parent时</p>
        <mtd-cascader v-model="value2" :data="options" multiple checked-strategy="parent" />
      </mtd-col>

      <mtd-col :span="8">
        <p>checked-strategy 等于children时</p>
        <mtd-cascader v-model="value2" :data="options" multiple checked-strategy="children" />
      </mtd-col>
    </mtd-row>

    <mtd-row>
      <mtd-col :span="12">
        <p>check-strictly true</p>
        <mtd-cascader v-model="value3" :data="options" multiple check-strictly />
      </mtd-col>

      <mtd-col :span="12">
        <p>checked-strategy false</p>
        <mtd-cascader v-model="value3" :data="options" multiple />
      </mtd-col>
    </mtd-row>

    <mtd-row>
      <p>max-count</p>
      <mtd-cascader style="width: 400px;" v-model="value4" :data="options" multiple :max-count="maxCount"
        @update:visible="changeMaxCount" checked-strategy="all" />
    </mtd-row>

    <mtd-row>
      <p> expandable-on-disabled</p>
      <div>
        <mtd-radio-group v-model="expandableOnDisabled" @change="changEexpandableOnDisabled">
          <mtd-radio :value="true">true</mtd-radio>
          <mtd-radio :value="false">false</mtd-radio>
        </mtd-radio-group>
      </div>

      <mtd-cascader v-model="value5" :data="options" expand-trigger="hover"
        :expandable-on-disabled="expandableOnDisabled" />
    </mtd-row>

    <mtd-row>
      <p> disabled-strictly</p>
      <div>
        <mtd-radio-group v-model="disabledStrictly" @change="changDisabledStrictly">
          <mtd-radio :value="true">true</mtd-radio>
          <mtd-radio :value="false">false</mtd-radio>
        </mtd-radio-group>
      </div>

      <mtd-cascader v-model="value6" :data="options" expand-trigger="hover" expandable-on-disabled
        :disabled-strictly="disabledStrictly" />
    </mtd-row>

  </div>
</template>
<script>
export default {
  data() {
    return {
      options: [
        {
          value: 'zhinan',
          label: '指南',
          children: [
            {
              value: 'shejiyuanze',
              label: '设计原则',
              children: [
                {
                  value: 'yizhi',
                  label: '一致',
                },
                {
                  value: 'fankui',
                  label: '反馈',
                },
                {
                  value: 'xiaolv',
                  label: '效率',
                },
                {
                  value: 'kekong',
                  label: '可控',
                },
              ],
            },
            {
              value: 'daohang',
              label: '导航',
              children: [
                {
                  value: 'cexiangdaohang',
                  label: '侧向导航',
                },
                {
                  value: 'dingbudaohang',
                  label: '顶部导航',
                },
              ],
            },
          ],
        },
        {
          value: 'zujian',
          label: '组件',
          children: [
            {
              value: 'basic',
              label: 'Basic',
              disabled: true,
              children: [
                {
                  value: 'layout',
                  label: 'Layout 布局',
                },
                {
                  value: 'color',
                  label: 'Color 色彩',
                },
                {
                  value: 'typography',
                  label: 'Typography 字体',
                },
                {
                  value: 'icon',
                  label: 'Icon 图标',
                },
                {
                  value: 'button',
                  label: 'Button 按钮',
                },
              ],
            },
            {
              value: 'form',
              label: 'Form',
              isLeaf: true,
            },
            {
              value: 'data',
              label: 'Data',
              children: [
                {
                  value: 'table',
                  label: 'Table 表格',
                },
                {
                  value: 'tag',
                  label: 'Tag 标签',
                },
                {
                  value: 'progress',
                  label: 'Progress 进度条',
                },
                {
                  value: 'tree',
                  label: 'Tree 树形控件',
                },
                {
                  value: 'pagination',
                  label: 'Pagination 分页',
                },
                {
                  value: 'badge',
                  label: 'Badge 标记',
                },
              ],
            },
            {
              value: 'notice',
              label: 'Notice',
              children: [
                {
                  value: 'alert',
                  label: 'Alert 警告',
                },
                {
                  value: 'loading',
                  label: 'Loading 加载',
                },
                {
                  value: 'message',
                  label: 'Message 消息提示',
                },
                {
                  value: 'message-box',
                  label: 'MessageBox 弹框',
                },
                {
                  value: 'notification',
                  label: 'Notification 通知',
                },
              ],
            },
            {
              value: 'navigation',
              label: 'Navigation',
              children: [
                {
                  value: 'menu',
                  label: 'NavMenu 导航菜单',
                },
                {
                  value: 'tabs',
                  label: 'Tabs 标签页',
                },
                {
                  value: 'breadcrumb',
                  label: 'Breadcrumb 面包屑',
                },
                {
                  value: 'dropdown',
                  label: 'Dropdown 下拉菜单',
                },
                {
                  value: 'steps',
                  label: 'Steps 步骤条',
                },
              ],
            },
            {
              value: 'others',
              label: 'Others',
              children: [
                {
                  value: 'dialog',
                  label: 'Dialog 对话框',
                },
                {
                  value: 'tooltip',
                  label: 'Tooltip 文字提示',
                },
                {
                  value: 'popover',
                  label: 'Popover 弹出框',
                },
                {
                  value: 'card',
                  label: 'Card 卡片',
                },
                {
                  value: 'carousel',
                  label: 'Carousel 走马灯',
                },
                {
                  value: 'collapse',
                  label: 'Collapse 折叠面板',
                },
              ],
            },
          ],
        },
        {
          value: 'ziyuan',
          label: '资源',
          children: [
            {
              value: 'axure',
              label: 'Axure Components',
            },
            {
              value: 'sketch',
              label: 'Sketch Templates',
            },
            {
              value: 'jiaohu',
              label: '组件交互文档',
            },
          ],
        },
      ],
      value1: [],
      value2: [],
      value3: [],
      value4: [],
      value5: [],
      value6: [],
      visible: false,
      expandableOnDisabled: false,
      disabledStrictly: true,
      maxCount: 'responsive',
    }
  },
  methods: {
    handleChange(value, selectOptons) {
      console.log(value, selectOptons)
    },

    handleVis(v) {
      this.visible = v
    },
    changeMaxCount(v) {
      if (v) {
        this.maxCount = 99
      } else {
        this.maxCount = 'responsive'
      }
    },
    changEexpandableOnDisabled(v) {
      this.expandableOnDisabled = v
    },
    changDisabledStrictly(v) {
      this.disabledStrictly = v
    },
  },
}
</script>
<style lang="scss" scoped>
.demo-wrapper {
  width: 100%;

  ::v-deep .mtd-row+.mtd-row {
    margin-top: 36px;
  }
}
</style>
```

#### doc/demo/select-all.vue

```vue
<template>
  <div class="demo-wrapper">
    <mtd-row>
      <p>show-select-all</p>
      <div>
        <mtd-radio-group v-model="showSelectAll" @change="changeShowSelectAll">
          <mtd-radio :value="true">true</mtd-radio>
          <mtd-radio :value="false">false</mtd-radio>
        </mtd-radio-group>
      </div>
      <mtd-cascader v-model="value1" :data="options" @change="handleChange" multiple :show-select-all="showSelectAll" />
    </mtd-row>

    <mtd-row>
      <p>participate-disabled</p>
      <div>
        <mtd-radio-group v-model="participateDisabled" @change="changeParticipateDisabled">
          <mtd-radio :value="true">true</mtd-radio>
          <mtd-radio :value="false">false</mtd-radio>
        </mtd-radio-group>
      </div>
      <mtd-cascader v-model="value2" :data="options" @change="handleChange" multiple :show-select-all="showSelectAll"
        expandable-on-disabled expand-trigger="hover" :participate-disabled="participateDisabled"/>
    </mtd-row>

  </div>
</template>
<script>
export default {
  data() {
    return {
      options: [
        {
          value: 'zhinan',
          label: '指南',
          children: [
            {
              value: 'shejiyuanze',
              label: '设计原则',
              children: [
                {
                  value: 'yizhi',
                  label: '一致',
                },
                {
                  value: 'fankui',
                  label: '反馈',
                },
                {
                  value: 'xiaolv',
                  label: '效率',
                },
                {
                  value: 'kekong',
                  label: '可控',
                },
              ],
            },
            {
              value: 'daohang',
              label: '导航',
              children: [
                {
                  value: 'cexiangdaohang',
                  label: '侧向导航',
                },
                {
                  value: 'dingbudaohang',
                  label: '顶部导航',
                },
              ],
            },
          ],
        },
        {
          value: 'zujian',
          label: '组件',
          children: [
            {
              value: 'basic',
              label: 'Basic',
              disabled: true,
              children: [
                {
                  value: 'layout',
                  label: 'Layout 布局',
                },
                {
                  value: 'color',
                  label: 'Color 色彩',
                },
                {
                  value: 'typography',
                  label: 'Typography 字体',
                },
                {
                  value: 'icon',
                  label: 'Icon 图标',
                },
                {
                  value: 'button',
                  label: 'Button 按钮',
                },
              ],
            },
            {
              value: 'form',
              label: 'Form',
              isLeaf: true,
            },
            {
              value: 'data',
              label: 'Data',
              children: [
                {
                  value: 'table',
                  label: 'Table 表格',
                },
                {
                  value: 'tag',
                  label: 'Tag 标签',
                },
                {
                  value: 'progress',
                  label: 'Progress 进度条',
                },
                {
                  value: 'tree',
                  label: 'Tree 树形控件',
                },
                {
                  value: 'pagination',
                  label: 'Pagination 分页',
                },
                {
                  value: 'badge',
                  label: 'Badge 标记',
                },
              ],
            },
            {
              value: 'notice',
              label: 'Notice',
              children: [
                {
                  value: 'alert',
                  label: 'Alert 警告',
                },
                {
                  value: 'loading',
                  label: 'Loading 加载',
                },
                {
                  value: 'message',
                  label: 'Message 消息提示',
                },
                {
                  value: 'message-box',
                  label: 'MessageBox 弹框',
                },
                {
                  value: 'notification',
                  label: 'Notification 通知',
                },
              ],
            },
            {
              value: 'navigation',
              label: 'Navigation',
              children: [
                {
                  value: 'menu',
                  label: 'NavMenu 导航菜单',
                },
                {
                  value: 'tabs',
                  label: 'Tabs 标签页',
                },
                {
                  value: 'breadcrumb',
                  label: 'Breadcrumb 面包屑',
                },
                {
                  value: 'dropdown',
                  label: 'Dropdown 下拉菜单',
                },
                {
                  value: 'steps',
                  label: 'Steps 步骤条',
                },
              ],
            },
            {
              value: 'others',
              label: 'Others',
              children: [
                {
                  value: 'dialog',
                  label: 'Dialog 对话框',
                },
                {
                  value: 'tooltip',
                  label: 'Tooltip 文字提示',
                },
                {
                  value: 'popover',
                  label: 'Popover 弹出框',
                },
                {
                  value: 'card',
                  label: 'Card 卡片',
                },
                {
                  value: 'carousel',
                  label: 'Carousel 走马灯',
                },
                {
                  value: 'collapse',
                  label: 'Collapse 折叠面板',
                },
              ],
            },
          ],
        },
        {
          value: 'ziyuan',
          label: '资源',
          children: [
            {
              value: 'axure',
              label: 'Axure Components',
            },
            {
              value: 'sketch',
              label: 'Sketch Templates',
            },
            {
              value: 'jiaohu',
              label: '组件交互文档',
            },
          ],
        },
      ],
      showSelectAll: true,
      participateDisabled: false,
      value1: [],
      value2: [],
    }
  },
  methods: {
    handleChange(value, selectOptons) {
      console.log(value, selectOptons)
    },
    changeShowSelectAll(v) {
      this.showSelectAll = v
    },
    changeParticipateDisabled(v) {
      this.participateDisabled = v
    }

  },
}
</script>
<style lang="scss" scoped>
.demo-wrapper {
  width: 100%;

  ::v-deep .mtd-row+.mtd-row {
    margin-top: 36px;
  }
}
</style>
```

#### doc/demo/status.vue

```vue
<template>
  <div>
    <div style="margin-bottom: 10px;">
      <mtd-radio-group v-model="status" type="line">
        <mtd-radio-button value="success">成功</mtd-radio-button>
        <mtd-radio-button value="warning">警告</mtd-radio-button>
        <mtd-radio-button value="error">失败</mtd-radio-button>
        <mtd-radio-button :value="undefined">不设置</mtd-radio-button>
      </mtd-radio-group>
    </div>
    <mtd-cascader v-model="value" :data="options" :status="status" />
  </div>
</template>
<script>
export default {
  data() {
    return {
      status: undefined,
      options: [
        {
          value: 'zhinan',
          label: '指南',
          children: [
            {
              value: 'shejiyuanze',
              label: '设计原则',
              children: [
                {
                  value: 'yizhi',
                  label: '一致',
                },
                {
                  value: 'fankui',
                  label: '反馈',
                },
                {
                  value: 'xiaolv',
                  label: '效率',
                },
                {
                  value: 'kekong',
                  label: '可控',
                },
              ],
            },
            {
              value: 'daohang',
              label: '导航',
              children: [
                {
                  value: 'cexiangdaohang',
                  label: '侧向导航',
                },
                {
                  value: 'dingbudaohang',
                  label: '顶部导航',
                },
              ],
            },
          ],
        },
        {
          value: 'zujian',
          label: '组件',
          children: [
            {
              value: 'basic',
              label: 'Basic',
              disabled: true,
              children: [
                {
                  value: 'layout',
                  label: 'Layout 布局',
                },
                {
                  value: 'color',
                  label: 'Color 色彩',
                },
                {
                  value: 'typography',
                  label: 'Typography 字体',
                },
                {
                  value: 'icon',
                  label: 'Icon 图标',
                },
                {
                  value: 'button',
                  label: 'Button 按钮',
                },
              ],
            },
            {
              value: 'form',
              label: 'Form',
              isLeaf: true,
            },
            {
              value: 'data',
              label: 'Data',
              children: [
                {
                  value: 'table',
                  label: 'Table 表格',
                },
                {
                  value: 'tag',
                  label: 'Tag 标签',
                },
                {
                  value: 'progress',
                  label: 'Progress 进度条',
                },
                {
                  value: 'tree',
                  label: 'Tree 树形控件',
                },
                {
                  value: 'pagination',
                  label: 'Pagination 分页',
                },
                {
                  value: 'badge',
                  label: 'Badge 标记',
                },
              ],
            },
            {
              value: 'notice',
              label: 'Notice',
              children: [
                {
                  value: 'alert',
                  label: 'Alert 警告',
                },
                {
                  value: 'loading',
                  label: 'Loading 加载',
                },
                {
                  value: 'message',
                  label: 'Message 消息提示',
                },
                {
                  value: 'message-box',
                  label: 'MessageBox 弹框',
                },
                {
                  value: 'notification',
                  label: 'Notification 通知',
                },
              ],
            },
            {
              value: 'navigation',
              label: 'Navigation',
              children: [
                {
                  value: 'menu',
                  label: 'NavMenu 导航菜单',
                },
                {
                  value: 'tabs',
                  label: 'Tabs 标签页',
                },
                {
                  value: 'breadcrumb',
                  label: 'Breadcrumb 面包屑',
                },
                {
                  value: 'dropdown',
                  label: 'Dropdown 下拉菜单',
                },
                {
                  value: 'steps',
                  label: 'Steps 步骤条',
                },
              ],
            },
            {
              value: 'others',
              label: 'Others',
              children: [
                {
                  value: 'dialog',
                  label: 'Dialog 对话框',
                },
                {
                  value: 'tooltip',
                  label: 'Tooltip 文字提示',
                },
                {
                  value: 'popover',
                  label: 'Popover 弹出框',
                },
                {
                  value: 'card',
                  label: 'Card 卡片',
                },
                {
                  value: 'carousel',
                  label: 'Carousel 走马灯',
                },
                {
                  value: 'collapse',
                  label: 'Collapse 折叠面板',
                },
              ],
            },
          ],
        },
        {
          value: 'ziyuan',
          label: '资源',
          children: [
            {
              value: 'axure',
              label: 'Axure Components',
            },
            {
              value: 'sketch',
              label: 'Sketch Templates',
            },
            {
              value: 'jiaohu',
              label: '组件交互文档',
            },
          ],
        },
      ],
      value: [],
    }
  },
}
</script>

```

#### doc/demo/text.vue

```vue
<template>
  <div style="width: 100%;">
    <mtd-row style="margin-bottom: 15px;">
      <mtd-col :span="12">
        <span>修改数据为空文案：</span>
        <mtd-input placeholder="修改数据为空文案" v-model="emptyDataText" @change="handleChangeNoData"></mtd-input>
      </mtd-col>
      <mtd-col :span="12">
        <span>修改搜索为空文案：</span>
        <mtd-input placeholder="修改搜索为空文案" v-model="emptyMatchText" @change="handleChangeNoMatch"></mtd-input>
      </mtd-col>
    </mtd-row>
    <mtd-row>
      <mtd-col :span="12">
        <p>数据为空</p>
        <mtd-cascader v-model="value1" :data="[]" :no-data-text="emptyDataText" />
      </mtd-col>
      <mtd-col :span="12">
        <p>搜索数据为空</p>
        <mtd-cascader v-model="value2" :data="options" filterable :debounce="1000" :no-match-text="emptyMatchText"
          :filter-method="filterMethod" />
      </mtd-col>
    </mtd-row>
  </div>
</template>
<script>
export default {
  data() {
    return {
      options: [
        {
          value: 'zhinan',
          label: '指南',
          children: [
            {
              value: 'shejiyuanze',
              label: '设计原则',
              children: [
                {
                  value: 'yizhi',
                  label: '一致',
                },
                {
                  value: 'fankui',
                  label: '反馈',
                },
                {
                  value: 'xiaolv',
                  label: '效率',
                },
                {
                  value: 'kekong',
                  label: '可控',
                },
              ],
            },
            {
              value: 'daohang',
              label: '导航',
              children: [
                {
                  value: 'cexiangdaohang',
                  label: '侧向导航',
                },
                {
                  value: 'dingbudaohang',
                  label: '顶部导航',
                },
              ],
            },
          ],
        },
        {
          value: 'zujian',
          label: '组件',
          children: [
            {
              value: 'basic',
              label: 'Basic',
              disabled: true,
              children: [
                {
                  value: 'layout',
                  label: 'Layout 布局',
                },
                {
                  value: 'color',
                  label: 'Color 色彩',
                },
                {
                  value: 'typography',
                  label: 'Typography 字体',
                },
                {
                  value: 'icon',
                  label: 'Icon 图标',
                },
                {
                  value: 'button',
                  label: 'Button 按钮',
                },
              ],
            },
            {
              value: 'form',
              label: 'Form',
              isLeaf: true,
            },
            {
              value: 'data',
              label: 'Data',
              children: [
                {
                  value: 'table',
                  label: 'Table 表格',
                },
                {
                  value: 'tag',
                  label: 'Tag 标签',
                },
                {
                  value: 'progress',
                  label: 'Progress 进度条',
                },
                {
                  value: 'tree',
                  label: 'Tree 树形控件',
                },
                {
                  value: 'pagination',
                  label: 'Pagination 分页',
                },
                {
                  value: 'badge',
                  label: 'Badge 标记',
                },
              ],
            },
            {
              value: 'notice',
              label: 'Notice',
              children: [
                {
                  value: 'alert',
                  label: 'Alert 警告',
                },
                {
                  value: 'loading',
                  label: 'Loading 加载',
                },
                {
                  value: 'message',
                  label: 'Message 消息提示',
                },
                {
                  value: 'message-box',
                  label: 'MessageBox 弹框',
                },
                {
                  value: 'notification',
                  label: 'Notification 通知',
                },
              ],
            },
            {
              value: 'navigation',
              label: 'Navigation',
              children: [
                {
                  value: 'menu',
                  label: 'NavMenu 导航菜单',
                },
                {
                  value: 'tabs',
                  label: 'Tabs 标签页',
                },
                {
                  value: 'breadcrumb',
                  label: 'Breadcrumb 面包屑',
                },
                {
                  value: 'dropdown',
                  label: 'Dropdown 下拉菜单',
                },
                {
                  value: 'steps',
                  label: 'Steps 步骤条',
                },
              ],
            },
            {
              value: 'others',
              label: 'Others',
              children: [
                {
                  value: 'dialog',
                  label: 'Dialog 对话框',
                },
                {
                  value: 'tooltip',
                  label: 'Tooltip 文字提示',
                },
                {
                  value: 'popover',
                  label: 'Popover 弹出框',
                },
                {
                  value: 'card',
                  label: 'Card 卡片',
                },
                {
                  value: 'carousel',
                  label: 'Carousel 走马灯',
                },
                {
                  value: 'collapse',
                  label: 'Collapse 折叠面板',
                },
              ],
            },
          ],
        },
        {
          value: 'ziyuan',
          label: '资源',
          children: [
            {
              value: 'axure',
              label: 'Axure Components',
            },
            {
              value: 'sketch',
              label: 'Sketch Templates',
            },
            {
              value: 'jiaohu',
              label: '组件交互文档',
            },
          ],
        },
      ],
      value1: [],
      value2: [],
      emptyDataText: '这里没有数据~',
      emptyMatchText: '这里没有搜索数据~'
    }
  },
  methods: {
    handleChangeNoData(val) {
      this.emptyDataText = val;
    },
    handleChangeNoMatch(val) {
      this.emptyMatchText = val;
    },
    filterMethod(query, item) {
      console.log(query, item)
      if (query.includes('a')) return false
      return item?.find(option => option.label.includes(query))
    }
  }
}
</script>

```

#### doc/demo/unmatch-value.vue

```vue
<template>
  <div class="demo-full-width">
    <mtd-cascader style="width: 350px" :data="options2" :modelValue="value2" filterable remote
      :loading="searching" multiple clearable :remote-method="query" :loading-text="loadingText"
      @update:modelValue="handleUpdateModelValue" use-cache>
    </mtd-cascader>
  </div>
</template>
<script>
export default {
  data() {
    const data = {
      value2: [
        ['177', '1717'],
        ['177', '1718'],
        ['140881'],
        ['16'],
        ['440981'],
        // ['11']
      ],
      loadingText: '加载中~~~~',
      searching: false,
      options: [
        {
          label: '测试城市177',
          value: '177',
          children: [{
            label: '测试城市1717',
            value: '1717'
          }, {
            label: '测试城市1718',
            value: '1718'
          }]
        },
        {
          label: '运城市永济市',
          value: '140881'
        },
        {
          label: '测试城市16',
          value: '16'
        },
        {
          label: '茂名市高州市',
          value: '440981'
        },
        {
          label: '测试城市184',
          value: '184'
        }
      ],
      filterVal: ''
    };
    data.options2 = data.options;
    return data;
  },
  methods: {
    formatter(labels, selectedOptions, value) {
      return value.join(' / ');
    },
    query(query) {
      this.searching = true;
      this.filterVal = query;
      setTimeout(() => {
        if (!query) {
          this.options2 = [...this.options];
        } else {
          this.options2 = [
            {
              label: '新测试177',
              value: '177',
              children: [{
                label: '测试城市1717',
                value: '1717'
              }]
            },
            {
              label: '测试城市16',
              value: '16'
            },
          ];
        }

        this.searching = false;
      }, 2000);
    },
    handleUpdateModelValue(value, unmatch) {
      console.log('unmatch value', unmatch)
      this.value2 = value.concat(unmatch)
    },
  }
};
</script>

```

#### doc/demo/virtual.vue

```vue
<template>
  <mtd-cascader :data="options" v-model="value" virtual :virtual-option="{ height: 188 }" />
</template>
<script>
const createdArr = (level = 0, parent = 0) => {
  const arr = [];
  if (level > 1) return arr

  for (let i = 0; i < 100; i++) {
    const value = `${level} + ${i}`
    const obj = {
      label: `【${parent}】【${level} + ${i}】`,
      value,
      children: createdArr(level + 1, value)
    };
    arr.push(obj);
  }

  return arr
}

export default {
  created() {
    this.options = createdArr()
  },
  data() {
    return {
      value: [],
      options: []
    };
  },
};
</script>

```

#### doc/expand.vue

```vue
<template>
  <div class="demo-full-width">
    <mtd-row>
      <mtd-col :span="12">
        <p>点击触发</p>
        <mtd-cascader :data="options" v-model="value2" />
      </mtd-col>

      <mtd-col :span="12">
        <p>悬停触发</p>
        <mtd-cascader :data="options" v-model="value" expand-trigger="hover" />
      </mtd-col>
    </mtd-row>
  </div>
</template>
<script>
export default {
  data() {
    return {
      value: ['ziyuan', 'jiaohu'],
      value2: ['ziyuan', 'jiaohu'],
      options: [
        {
          value: 'zhinan',
          label: '指南',
          children: [
            {
              value: 'shejiyuanze',
              label: '设计原则',
              disabled: true,
              children: [
                {
                  value: 'yizhi',
                  label: '一致',
                },
                {
                  value: 'fankui',
                  label: '反馈',
                },
                {
                  value: 'xiaolv',
                  label: '效率',
                },
                {
                  value: 'kekong',
                  label: '可控',
                },
              ],
            },
            {
              value: 'daohang',
              label: '导航',
              children: [
                {
                  value: 'cexiangdaohang',
                  label: '侧向导航',
                },
                {
                  value: 'dingbudaohang',
                  label: '顶部导航',
                },
              ],
            },
          ],
        },
        {
          value: 'zujian',
          label: '组件',
          children: [
            {
              value: 'basic',
              label: 'Basic',
              children: [
                {
                  value: 'layout',
                  label: 'Layout 布局',
                },
                {
                  value: 'color',
                  label: 'Color 色彩',
                },
                {
                  value: 'typography',
                  label: 'Typography 字体',
                },
                {
                  value: 'icon',
                  label: 'Icon 图标',
                },
                {
                  value: 'button',
                  label: 'Button 按钮',
                },
              ],
            },
            {
              value: 'data',
              label: 'Data',
              children: [
                {
                  value: 'table',
                  label: 'Table 表格',
                },
                {
                  value: 'tag',
                  label: 'Tag 标签',
                },
                {
                  value: 'progress',
                  label: 'Progress 进度条',
                },
                {
                  value: 'tree',
                  label: 'Tree 树形控件',
                },
                {
                  value: 'pagination',
                  label: 'Pagination 分页',
                },
                {
                  value: 'badge',
                  label: 'Badge 标记',
                },
              ],
            },
            {
              value: 'notice',
              label: 'Notice',
              children: [
                {
                  value: 'alert',
                  label: 'Alert 警告',
                },
                {
                  value: 'loading',
                  label: 'Loading 加载',
                },
                {
                  value: 'message',
                  label: 'Message 消息提示',
                },
                {
                  value: 'message-box',
                  label: 'MessageBox 弹框',
                },
                {
                  value: 'notification',
                  label: 'Notification 通知',
                },
              ],
            },
            {
              value: 'navigation',
              label: 'Navigation',
              children: [
                {
                  value: 'menu',
                  label: 'NavMenu 导航菜单',
                },
                {
                  value: 'tabs',
                  label: 'Tabs 标签页',
                },
                {
                  value: 'breadcrumb',
                  label: 'Breadcrumb 面包屑',
                },
                {
                  value: 'dropdown',
                  label: 'Dropdown 下拉菜单',
                },
                {
                  value: 'steps',
                  label: 'Steps 步骤条',
                },
              ],
            },
            {
              value: 'others',
              label: 'Others',
              children: [
                {
                  value: 'dialog',
                  label: 'Dialog 对话框',
                },
                {
                  value: 'tooltip',
                  label: 'Tooltip 文字提示',
                },
                {
                  value: 'popover',
                  label: 'Popover 弹出框',
                },
                {
                  value: 'card',
                  label: 'Card 卡片',
                },
                {
                  value: 'carousel',
                  label: 'Carousel 走马灯',
                },
                {
                  value: 'collapse',
                  label: 'Collapse 折叠面板',
                },
              ],
            },
          ],
        },
        {
          value: 'ziyuan',
          label: '资源',
          children: [
            {
              value: 'axure',
              label: 'Axure Components',
            },
            {
              value: 'sketch',
              label: 'Sketch Templates',
            },
            {
              value: 'jiaohu',
              label: '组件交互文档',
            },
          ],
        },
      ],
    };
  },
};
</script>

```

#### doc/formatter.vue

```vue
<template>
  <div class="demo-full-width">
    <mtd-row>
      <mtd-col :span="12">
        <p>仅回显末节点</p>
        <mtd-cascader :data="options" v-model="value2" :formatter="formatter" />
      </mtd-col>
      <mtd-col :span="12">
        <p>回显全部路径</p>
        <mtd-cascader :data="options" v-model="value" />
      </mtd-col>
    </mtd-row>
  </div>
</template>
<script>
export default {
  data() {
    return {
      value: ['ziyuan', 'jiaohu'],
      value2: ['ziyuan', 'jiaohu'],
      options: [
        {
          value: 'zhinan',
          label: '指南',
          children: [
            {
              value: 'shejiyuanze',
              label: '设计原则',
              disabled: true,
              children: [
                {
                  value: 'yizhi',
                  label: '一致',
                },
                {
                  value: 'fankui',
                  label: '反馈',
                },
                {
                  value: 'xiaolv',
                  label: '效率',
                },
                {
                  value: 'kekong',
                  label: '可控',
                },
              ],
            },
            {
              value: 'daohang',
              label: '导航',
              children: [
                {
                  value: 'cexiangdaohang',
                  label: '侧向导航',
                },
                {
                  value: 'dingbudaohang',
                  label: '顶部导航',
                },
              ],
            },
          ],
        },
        {
          value: 'zujian',
          label: '组件',
          children: [
            {
              value: 'basic',
              label: 'Basic',
              children: [
                {
                  value: 'layout',
                  label: 'Layout 布局',
                },
                {
                  value: 'color',
                  label: 'Color 色彩',
                },
                {
                  value: 'typography',
                  label: 'Typography 字体',
                },
                {
                  value: 'icon',
                  label: 'Icon 图标',
                },
                {
                  value: 'button',
                  label: 'Button 按钮',
                },
              ],
            },
            {
              value: 'data',
              label: 'Data',
              children: [
                {
                  value: 'table',
                  label: 'Table 表格',
                },
                {
                  value: 'tag',
                  label: 'Tag 标签',
                },
                {
                  value: 'progress',
                  label: 'Progress 进度条',
                },
                {
                  value: 'tree',
                  label: 'Tree 树形控件',
                },
                {
                  value: 'pagination',
                  label: 'Pagination 分页',
                },
                {
                  value: 'badge',
                  label: 'Badge 标记',
                },
              ],
            },
            {
              value: 'notice',
              label: 'Notice',
              children: [
                {
                  value: 'alert',
                  label: 'Alert 警告',
                },
                {
                  value: 'loading',
                  label: 'Loading 加载',
                },
                {
                  value: 'message',
                  label: 'Message 消息提示',
                },
                {
                  value: 'message-box',
                  label: 'MessageBox 弹框',
                },
                {
                  value: 'notification',
                  label: 'Notification 通知',
                },
              ],
            },
            {
              value: 'navigation',
              label: 'Navigation',
              children: [
                {
                  value: 'menu',
                  label: 'NavMenu 导航菜单',
                },
                {
                  value: 'tabs',
                  label: 'Tabs 标签页',
                },
                {
                  value: 'breadcrumb',
                  label: 'Breadcrumb 面包屑',
                },
                {
                  value: 'dropdown',
                  label: 'Dropdown 下拉菜单',
                },
                {
                  value: 'steps',
                  label: 'Steps 步骤条',
                },
              ],
            },
            {
              value: 'others',
              label: 'Others',
              children: [
                {
                  value: 'dialog',
                  label: 'Dialog 对话框',
                },
                {
                  value: 'tooltip',
                  label: 'Tooltip 文字提示',
                },
                {
                  value: 'popover',
                  label: 'Popover 弹出框',
                },
                {
                  value: 'card',
                  label: 'Card 卡片',
                },
                {
                  value: 'carousel',
                  label: 'Carousel 走马灯',
                },
                {
                  value: 'collapse',
                  label: 'Collapse 折叠面板',
                },
              ],
            },
          ],
        },
        {
          value: 'ziyuan',
          label: '资源',
          children: [
            {
              value: 'axure',
              label: 'Axure Components',
            },
            {
              value: 'sketch',
              label: 'Sketch Templates',
            },
            {
              value: 'jiaohu',
              label: '组件交互文档',
            },
          ],
        },
      ],
    };
  },
  methods: {
    formatter(labels, selectOptions, value) {
      return labels[labels.length - 1];
    },
  },
};
</script>

```

#### doc/load.vue

```vue
<template>
  <div>
    <mtd-cascader :data="options" :load-data="loadData" @change="handleChange" v-model="value" />
  </div>
</template>
<script>
export default {
  data() {
    return {
      value: [],
      times: 0,
      options: [
        {
          value: 'zhejiang',
          label: 'Zhejiang',
          isLeaf: false,
          children: [{
            value: 'zhejiang1',
            label: 'Zhejiang1',
          }]
        },
        {
          value: 'jiangsu',
          label: 'Jiangsu',
          isLeaf: false,
        },
      ],
    };
  },
  methods: {
    handleChange(value) {
      console.log(value);
    },
    loadData(item, callback) {
      this.times = this.times + 1
      setTimeout(() => {
        callback([
          {
            label: `${item.label} Dynamic 1`,
            value: `dynamic1-${Math.random().toFixed(2)}`,
            isLeaf: this.times >= 2,
          },
          {
            label: `${item.label} Dynamic 2`,
            value: `dynamic2-${Math.random().toFixed(2)}`,
            isLeaf: true,
          },
        ]);
      }, 1000);
    },
  },
};
</script>

```

#### doc/multi-level.vue

```vue
<template>
  <div class="demo-cascader-menus">
    <mtd-cascader-panel v-model="value1" :data="options" @change="handleChange" />
    <br><br>
    <mtd-dropdown>
      <mtd-button type="text-primary">
        <span style="margin-right: 4px;">点击下拉</span>
        <i class="mtdicon mtdicon-down" />
      </mtd-button>
      <template #dropdown>
        <mtd-cascader-panel v-model="value2" :data="options" @change="handleChange" multiple />
      </template>
    </mtd-dropdown>
  </div>
</template>
<script>
export default {
  data() {
    return {
      options: [
        {
          value: 'zhinan',
          label: '指南',
          children: [
            {
              value: 'shejiyuanze',
              label: '设计原则',
              disabled: true,
              children: [
                {
                  value: 'yizhi',
                  label: '一致',
                },
                {
                  value: 'fankui',
                  label: '反馈',
                },
                {
                  value: 'xiaolv',
                  label: '效率',
                },
                {
                  value: 'kekong',
                  label: '可控',
                },
              ],
            },
            {
              value: 'daohang',
              label: '导航',
              children: [
                {
                  value: 'cexiangdaohang',
                  label: '侧向导航',
                },
                {
                  value: 'dingbudaohang',
                  label: '顶部导航',
                },
              ],
            },
          ],
        },
        {
          value: 'zujian',
          label: '组件',
          children: [
            {
              value: 'basic',
              label: 'Basic',
              disabled: true,
              children: [
                {
                  value: 'layout',
                  label: 'Layout 布局',
                },
                {
                  value: 'color',
                  label: 'Color 色彩',
                },
                {
                  value: 'typography',
                  label: 'Typography 字体',
                },
                {
                  value: 'icon',
                  label: 'Icon 图标',
                },
                {
                  value: 'button',
                  label: 'Button 按钮',
                },
              ],
            },
            {
              value: 'form',
              label: 'Form',
              isLeaf: true,
            },
            {
              value: 'data',
              label: 'Data',
              children: [
                {
                  value: 'table',
                  label: 'Table 表格',
                },
                {
                  value: 'tag',
                  label: 'Tag 标签',
                },
                {
                  value: 'progress',
                  label: 'Progress 进度条',
                },
                {
                  value: 'tree',
                  label: 'Tree 树形控件',
                },
                {
                  value: 'pagination',
                  label: 'Pagination 分页',
                },
                {
                  value: 'badge',
                  label: 'Badge 标记',
                },
              ],
            },
            {
              value: 'notice',
              label: 'Notice',
              children: [
                {
                  value: 'alert',
                  label: 'Alert 警告',
                },
                {
                  value: 'loading',
                  label: 'Loading 加载',
                },
                {
                  value: 'message',
                  label: 'Message 消息提示',
                },
                {
                  value: 'message-box',
                  label: 'MessageBox 弹框',
                },
                {
                  value: 'notification',
                  label: 'Notification 通知',
                },
              ],
            },
            {
              value: 'navigation',
              label: 'Navigation',
              children: [
                {
                  value: 'menu',
                  label: 'NavMenu 导航菜单',
                },
                {
                  value: 'tabs',
                  label: 'Tabs 标签页',
                },
                {
                  value: 'breadcrumb',
                  label: 'Breadcrumb 面包屑',
                },
                {
                  value: 'dropdown',
                  label: 'Dropdown 下拉菜单',
                },
                {
                  value: 'steps',
                  label: 'Steps 步骤条',
                },
              ],
            },
            {
              value: 'others',
              label: 'Others',
              children: [
                {
                  value: 'dialog',
                  label: 'Dialog 对话框',
                },
                {
                  value: 'tooltip',
                  label: 'Tooltip 文字提示',
                },
                {
                  value: 'popover',
                  label: 'Popover 弹出框',
                },
                {
                  value: 'card',
                  label: 'Card 卡片',
                },
                {
                  value: 'carousel',
                  label: 'Carousel 走马灯',
                },
                {
                  value: 'collapse',
                  label: 'Collapse 折叠面板',
                },
              ],
            },
          ],
        },
        {
          value: 'ziyuan',
          label: '资源',
          children: [
            {
              value: 'axure',
              label: 'Axure Components',
            },
            {
              value: 'sketch',
              label: 'Sketch Templates',
            },
            {
              value: 'jiaohu',
              label: '组件交互文档',
            },
          ],
        },
      ],
      value1: ['ziyuan', 'jiaohu'],
      value2: [],
      visible: false,
    }
  },
  methods: {
    handleChange(value, selectOptons) {
      console.log(value, selectOptons)
    },
  },
}
</script>

<style scoped>

.demo-cascader-menus {

}

</style>

```

#### doc/multiple.vue

```vue
<template>
  <div>
    <mtd-row>
      <mtd-cascader style="width:400px" multiple :data="options" v-model="value" clearable max-count="responsive"
        @change="handleChange" />
    </mtd-row>
    <mtd-row>
      <mtd-cascader style="width:400px" multiple :data="options" v-model="value2" clearable @change="handleChange" />
    </mtd-row>
    <mtd-row>
      <mtd-cascader class="popper-scroll" style="width:400px" multiple :data="options" v-model="value3" clearable
        @change="handleChange" />
    </mtd-row>
  </div>
</template>
<script>
export default {
  data() {
    return {
      options: [
        {
          value: 'zhinan',
          label: '指南',
          children: [
            {
              value: 'shejiyuanze',
              label: '设计原则',
              children: [
                {
                  value: 'yizhi',
                  label: '一致',
                },
                {
                  value: 'fankui',
                  label: '反馈',
                },
                {
                  value: 'xiaolv',
                  label: '效率',
                },
                {
                  value: 'kekong',
                  label: '可控',
                },
              ],
            },
            {
              value: 'daohang',
              label: '导航',
              children: [
                {
                  value: 'cexiangdaohang',
                  label: '侧向导航',
                },
                {
                  value: 'dingbudaohang',
                  label: '顶部导航',
                },
              ],
            },
          ],
        },
        {
          value: 'zujian',
          label: '组件',
          children: [
            {
              value: 'basic',
              label: 'Basic',
              disabled: true,
              children: [
                {
                  value: 'layout',
                  label: 'Layout 布局',
                },
                {
                  value: 'color',
                  label: 'Color 色彩',
                },
                {
                  value: 'typography',
                  label: 'Typography 字体',
                },
                {
                  value: 'icon',
                  label: 'Icon 图标',
                },
                {
                  value: 'button',
                  label: 'Button 按钮',
                },
              ],
            },
            {
              value: 'form',
              label: 'Form',
              isLeaf: true,
            },
            {
              value: 'data',
              label: 'Data',
              children: [
                {
                  value: 'table',
                  label: 'Table 表格',
                },
                {
                  value: 'tag',
                  label: 'Tag 标签',
                },
                {
                  value: 'progress',
                  label: 'Progress 进度条',
                },
                {
                  value: 'tree',
                  label: 'Tree 树形控件',
                },
                {
                  value: 'pagination',
                  label: 'Pagination 分页',
                },
                {
                  value: 'badge',
                  label: 'Badge 标记',
                },
              ],
            },
            {
              value: 'notice',
              label: 'Notice',
              children: [
                {
                  value: 'alert',
                  label: 'Alert 警告',
                },
                {
                  value: 'loading',
                  label: 'Loading 加载',
                },
                {
                  value: 'message',
                  label: 'Message 消息提示',
                },
                {
                  value: 'message-box',
                  label: 'MessageBox 弹框',
                },
                {
                  value: 'notification',
                  label: 'Notification 通知',
                },
              ],
            },
            {
              value: 'navigation',
              label: 'Navigation',
              children: [
                {
                  value: 'menu',
                  label: 'NavMenu 导航菜单',
                },
                {
                  value: 'tabs',
                  label: 'Tabs 标签页',
                },
                {
                  value: 'breadcrumb',
                  label: 'Breadcrumb 面包屑',
                },
                {
                  value: 'dropdown',
                  label: 'Dropdown 下拉菜单',
                },
                {
                  value: 'steps',
                  label: 'Steps 步骤条',
                },
              ],
            },
            {
              value: 'others',
              label: 'Others',
              children: [
                {
                  value: 'dialog',
                  label: 'Dialog 对话框',
                },
                {
                  value: 'tooltip',
                  label: 'Tooltip 文字提示',
                },
                {
                  value: 'popover',
                  label: 'Popover 弹出框',
                },
                {
                  value: 'card',
                  label: 'Card 卡片',
                },
                {
                  value: 'carousel',
                  label: 'Carousel 走马灯',
                },
                {
                  value: 'collapse',
                  label: 'Collapse 折叠面板',
                },
              ],
            },
          ],
        },
        {
          value: 'ziyuan',
          label: '资源',
          children: [
            {
              value: 'axure',
              label: 'Axure Components',
            },
            {
              value: 'sketch',
              label: 'Sketch Templates',
            },
            {
              value: 'jiaohu',
              label: '组件交互文档',
            },
          ],
        },
      ],
      value: [],
      value2: [
        ['ziyuan', 'jiaohu'],
        ['zhinan', 'shejiyuanze'],
      ],
      value3: [
        [
          'zhinan',
          'daohang',
          'dingbudaohang'
        ],
        [
          'zujian',
          'form'
        ],
        [
          'zujian',
          'data',
          'table'
        ],
        [
          'zujian',
          'data',
          'tag'
        ],
        [
          'zujian',
          'data',
          'progress'
        ],
        [
          'zujian',
          'data',
          'tree'
        ],
        [
          'zujian',
          'notice',
          'alert'
        ],
        [
          'zujian',
          'notice',
          'loading'
        ],
        [
          'zujian',
          'notice',
          'message'
        ],
        [
          'zujian',
          'notice',
          'message-box'
        ],
        [
          'zujian',
          'navigation',
          'tabs'
        ],
        [
          'zujian',
          'navigation',
          'breadcrumb'
        ]
      ],
    }
  },
  methods: {
    handleChange(value, selectOptons, paths, lastnode) {
      console.log(value, selectOptons, paths)
      console.log('当前操作node', lastnode)
    },
  },
}
</script>
<style lang="scss" scoped>
.popper-scroll {
  ::v-deep .mtd-input {
    max-height: 120px;
    overflow-y: overlay !important;
  }
}
</style>

```

#### doc/search.vue

```vue
<template>
  <div class="demo-full-width">
    <mtd-row>
      <mtd-col :span="12">
        <p>即时搜索</p>
        <mtd-cascader :data="options" v-model="value" filterable>
          <template #default="{ node }">
            <span v-html="node.highLightLabel || node.label"></span>
          </template>
        </mtd-cascader>
      </mtd-col>
      <mtd-col :span="12">
        <p>异步搜索</p>
        <mtd-cascader :data="options2" v-model="value2" filterable remote :loading="searching" :remote-method="query"
          :loading-text="loadingText" @update:visible="handleVisible">
          <template #default="{ node }">
            <span v-html="highLight(node)"></span>
          </template>
        </mtd-cascader>
      </mtd-col>
    </mtd-row>
  </div>
</template>
<script>
export default {
  data() {
    const data = {
      value: ['ziyuan', 'jiaohu'],
      value2: [],
      loadingText: '加载中~~~~',
      searching: false,
      options: [
        {
          value: 'zhinan',
          label: '指南',
          children: [
            {
              value: 'shejiyuanze',
              label: '设计原则',
              children: [
                {
                  value: 'yizhi',
                  label: '一致',
                },
                {
                  value: 'fankui',
                  label: '反馈',
                },
                {
                  value: 'xiaolv',
                  label: '效率',
                },
                {
                  value: 'kekong',
                  label: '可控',
                },
              ],
            },
            {
              value: 'daohang',
              label: '导航',
              children: [
                {
                  value: 'cexiangdaohang',
                  label: '侧向导航',
                },
                {
                  value: 'dingbudaohang',
                  label: '顶部导航',
                },
              ],
            },
          ],
        },
        {
          value: 'zujian',
          label: '组件',
          children: [
            {
              value: 'basic',
              label: 'Basic',
              children: [
                {
                  value: 'layout',
                  label: 'Layout 布局',
                },
                {
                  value: 'color',
                  label: 'Color 色彩',
                },
                {
                  value: 'typography',
                  label: 'Typography 字体',
                },
                {
                  value: 'icon',
                  label: 'Icon 图标',
                },
                {
                  value: 'button',
                  label: 'Button 按钮',
                },
              ],
            },
            {
              value: 'data',
              label: 'Data',
              children: [
                {
                  value: 'table',
                  label: 'Table 表格',
                },
                {
                  value: 'tag',
                  label: 'Tag 标签',
                },
                {
                  value: 'progress',
                  label: 'Progress 进度条',
                },
                {
                  value: 'tree',
                  label: 'Tree 树形控件',
                },
                {
                  value: 'pagination',
                  label: 'Pagination 分页',
                },
                {
                  value: 'badge',
                  label: 'Badge 标记',
                },
              ],
            },
            {
              value: 'notice',
              label: 'Notice',
              children: [
                {
                  value: 'alert',
                  label: 'Alert 警告',
                },
                {
                  value: 'loading',
                  label: 'Loading 加载',
                },
                {
                  value: 'message',
                  label: 'Message 消息提示',
                },
                {
                  value: 'message-box',
                  label: 'MessageBox 弹框',
                },
                {
                  value: 'notification',
                  label: 'Notification 通知',
                },
              ],
            },
            {
              value: 'navigation',
              label: 'Navigation',
              children: [
                {
                  value: 'menu',
                  label: 'NavMenu 导航菜单',
                },
                {
                  value: 'tabs',
                  label: 'Tabs 标签页',
                },
                {
                  value: 'breadcrumb',
                  label: 'Breadcrumb 面包屑',
                },
                {
                  value: 'dropdown',
                  label: 'Dropdown 下拉菜单',
                },
                {
                  value: 'steps',
                  label: 'Steps 步骤条',
                },
              ],
            },
            {
              value: 'others',
              label: 'Others',
              children: [
                {
                  value: 'dialog',
                  label: 'Dialog 对话框',
                },
                {
                  value: 'tooltip',
                  label: 'Tooltip 文字提示',
                },
                {
                  value: 'popover',
                  label: 'Popover 弹出框',
                },
                {
                  value: 'card',
                  label: 'Card 卡片',
                },
                {
                  value: 'carousel',
                  label: 'Carousel 走马灯',
                },
                {
                  value: 'collapse',
                  label: 'Collapse 折叠面板',
                },
              ],
            },
          ],
        },
        {
          value: 'ziyuan',
          label: '资源',
          children: [
            {
              value: 'axure',
              label: 'Axure Components',
            },
            {
              value: 'sketch',
              label: 'Sketch Templates',
            },
            {
              value: 'jiaohu',
              label: '组件交互文档',
            },
          ],
        },
      ],
      filterVal: ''
    };
    data.options2 = data.options;
    return data;
  },
  methods: {
    formatter(labels, selectedOptions, value) {
      return value.join(' / ');
    },
    query(query) {
      this.searching = true;
      this.filterVal = query;
      setTimeout(() => {
        if (!query) {
          this.options2 = this.options;
        } else {
          this.options2 = [
            {
              value: query,
              label: query,
              children: [
                {
                  value: `${query}-children`,
                  label: `${query}-children`,
                },
              ],
            },
            {
              value: `${query}-1`,
              label: `${query}-1`,
              children: [
                {
                  value: `${query}-1-children`,
                  label: `${query}-1-children`,
                },
              ],
            },
          ];
        }
        this.searching = false;
      }, 2000);
    },
    handleVisible(v) {
      if (!v) this.filterVal = ''
    },
    highLight({ label }) {
      const reg = new RegExp('(' + this.filterVal + ')', 'gi')
      if (!this.filterVal) return label
      return label.replace(reg, "<span style='color: #166ff7'>$1</span>")
    }
  },
};
</script>

```

#### doc/size.vue

```vue
<template>
  <mtd-cascader :data="options" v-model="value" size="small" popperClass="popper-small-demo" />
  <mtd-cascader :data="options" v-model="value" />
  <mtd-cascader :data="options" v-model="value" size="large" popperClass="popper-large-demo" />
</template>
<script>
export default {
  data() {
    return {
      value: [],
      options: [
        {
          value: 'zhinan',
          label: '指南',
          children: [
            {
              value: 'shejiyuanze',
              label: '设计原则',
              disabled: true,
              children: [
                {
                  value: 'yizhi',
                  label: '一致',
                },
                {
                  value: 'fankui',
                  label: '反馈',
                },
                {
                  value: 'xiaolv',
                  label: '效率',
                },
                {
                  value: 'kekong',
                  label: '可控',
                },
              ],
            },
            {
              value: 'daohang',
              label: '导航',
              children: [
                {
                  value: 'cexiangdaohang',
                  label: '侧向导航',
                },
                {
                  value: 'dingbudaohang',
                  label: '顶部导航',
                },
              ],
            },
          ],
        },
        {
          value: 'zujian',
          label: '组件',
          children: [
            {
              value: 'basic',
              label: 'Basic',
              disabled: true,
              children: [
                {
                  value: 'layout',
                  label: 'Layout 布局',
                },
                {
                  value: 'color',
                  label: 'Color 色彩',
                },
                {
                  value: 'typography',
                  label: 'Typography 字体',
                },
                {
                  value: 'icon',
                  label: 'Icon 图标',
                },
                {
                  value: 'button',
                  label: 'Button 按钮',
                },
              ],
            },
            {
              value: 'data',
              label: 'Data',
              children: [
                {
                  value: 'table',
                  label: 'Table 表格',
                },
                {
                  value: 'tag',
                  label: 'Tag 标签',
                },
                {
                  value: 'progress',
                  label: 'Progress 进度条',
                },
                {
                  value: 'tree',
                  label: 'Tree 树形控件',
                },
                {
                  value: 'pagination',
                  label: 'Pagination 分页',
                },
                {
                  value: 'badge',
                  label: 'Badge 标记',
                },
              ],
            },
            {
              value: 'notice',
              label: 'Notice',
              children: [
                {
                  value: 'alert',
                  label: 'Alert 警告',
                },
                {
                  value: 'loading',
                  label: 'Loading 加载',
                },
                {
                  value: 'message',
                  label: 'Message 消息提示',
                },
                {
                  value: 'message-box',
                  label: 'MessageBox 弹框',
                },
                {
                  value: 'notification',
                  label: 'Notification 通知',
                },
              ],
            },
            {
              value: 'navigation',
              label: 'Navigation',
              children: [
                {
                  value: 'menu',
                  label: 'NavMenu 导航菜单',
                },
                {
                  value: 'tabs',
                  label: 'Tabs 标签页',
                },
                {
                  value: 'breadcrumb',
                  label: 'Breadcrumb 面包屑',
                },
                {
                  value: 'dropdown',
                  label: 'Dropdown 下拉菜单',
                },
                {
                  value: 'steps',
                  label: 'Steps 步骤条',
                },
              ],
            },
            {
              value: 'others',
              label: 'Others',
              children: [
                {
                  value: 'dialog',
                  label: 'Dialog 对话框',
                },
                {
                  value: 'tooltip',
                  label: 'Tooltip 文字提示',
                },
                {
                  value: 'popover',
                  label: 'Popover 弹出框',
                },
                {
                  value: 'card',
                  label: 'Card 卡片',
                },
                {
                  value: 'carousel',
                  label: 'Carousel 走马灯',
                },
                {
                  value: 'collapse',
                  label: 'Collapse 折叠面板',
                },
              ],
            },
          ],
        },
        {
          value: 'ziyuan',
          label: '资源',
          children: [
            {
              value: 'axure',
              label: 'Axure Components',
            },
            {
              value: 'sketch',
              label: 'Sketch Templates',
            },
            {
              value: 'jiaohu',
              label: '组件交互文档',
            },
          ],
        },
      ],
    };
  },
};
</script>
<style lang="scss">
.popper-large-demo {
  .mtd-cascader-menu {
    width: 200px;
  }
}
</style>
```

#### doc/status.vue

```vue
<template>
  <mtd-cascader :data="options" v-model="value" />
</template>
<script>
export default {
  data() {
    return {
      value: [],
      options: [
        {
          value: 'zhinan',
          label: '指南',
          disabled: true,
          children: [
            {
              value: 'shejiyuanze',
              label: '设计原则',
              children: [
                {
                  value: 'yizhi',
                  label: '一致',
                },
                {
                  value: 'fankui',
                  label: '反馈',
                },
                {
                  value: 'xiaolv',
                  label: '效率',
                },
                {
                  value: 'kekong',
                  label: '可控',
                },
              ],
            },
            {
              value: 'daohang',
              label: '导航',
              children: [
                {
                  value: 'cexiangdaohang',
                  label: '侧向导航',
                },
                {
                  value: 'dingbudaohang',
                  label: '顶部导航',
                },
              ],
            },
          ],
        },
        {
          value: 'zujian',
          label: '组件',
          children: [
            {
              value: 'basic',
              label: 'Basic',
              disabled: true,
              children: [
                {
                  value: 'layout',
                  label: 'Layout 布局',
                },
                {
                  value: 'color',
                  label: 'Color 色彩',
                },
                {
                  value: 'typography',
                  label: 'Typography 字体',
                },
                {
                  value: 'icon',
                  label: 'Icon 图标',
                },
                {
                  value: 'button',
                  label: 'Button 按钮',
                },
              ],
            },
            {
              value: 'data',
              label: 'Data',
              children: [
                {
                  value: 'table',
                  label: 'Table 表格',
                },
                {
                  value: 'tag',
                  label: 'Tag 标签',
                },
                {
                  value: 'progress',
                  label: 'Progress 进度条',
                },
                {
                  value: 'tree',
                  label: 'Tree 树形控件',
                },
                {
                  value: 'pagination',
                  label: 'Pagination 分页',
                },
                {
                  value: 'badge',
                  label: 'Badge 标记',
                },
              ],
            },
            {
              value: 'notice',
              label: 'Notice',
              children: [
                {
                  value: 'alert',
                  label: 'Alert 警告',
                },
                {
                  value: 'loading',
                  label: 'Loading 加载',
                },
                {
                  value: 'message',
                  label: 'Message 消息提示',
                },
                {
                  value: 'message-box',
                  label: 'MessageBox 弹框',
                },
                {
                  value: 'notification',
                  label: 'Notification 通知',
                },
              ],
            },
            {
              value: 'navigation',
              label: 'Navigation',
              children: [
                {
                  value: 'menu',
                  label: 'NavMenu 导航菜单',
                },
                {
                  value: 'tabs',
                  label: 'Tabs 标签页',
                },
                {
                  value: 'breadcrumb',
                  label: 'Breadcrumb 面包屑',
                },
                {
                  value: 'dropdown',
                  label: 'Dropdown 下拉菜单',
                },
                {
                  value: 'steps',
                  label: 'Steps 步骤条',
                },
              ],
            },
            {
              value: 'others',
              label: 'Others',
              children: [
                {
                  value: 'dialog',
                  label: 'Dialog 对话框',
                },
                {
                  value: 'tooltip',
                  label: 'Tooltip 文字提示',
                },
                {
                  value: 'popover',
                  label: 'Popover 弹出框',
                },
                {
                  value: 'card',
                  label: 'Card 卡片',
                },
                {
                  value: 'carousel',
                  label: 'Carousel 走马灯',
                },
                {
                  value: 'collapse',
                  label: 'Collapse 折叠面板',
                },
              ],
            },
          ],
        },
        {
          value: 'ziyuan',
          label: '资源',
          children: [
            {
              value: 'axure',
              label: 'Axure Components',
            },
            {
              value: 'sketch',
              label: 'Sketch Templates',
            },
            {
              value: 'jiaohu',
              label: '组件交互文档',
            },
          ],
        },
      ],
    };
  },
};
</script>
```

#### hook.ts

```typescript

import {
  provide,
  inject,
  computed,
  ComputedRef,
} from '@ss/mtd-adapter'
import { ExpandTrigger } from './types'

const CASCADER_PANEL_PROVIDER = 'CASCADER_PANEL_PROVIDER'


export interface CascaderPanelCtx {
  defaultExpandFirstItem: boolean;
  expandableOnDisabled: boolean;
  multiple: boolean;
  showCheckbox: boolean;
  changeOnSelect: boolean;
  expandTrigger: ExpandTrigger;
  expandedValue: Array<any> 
  getNodes(values: any[]): Node[];
}

export function useCascaderPanel(): ComputedRef<CascaderPanelCtx> {
  const defaultCtx = computed((): CascaderPanelCtx => {
    return {
      showCheckbox: false,
      changeOnSelect: false,
      expandableOnDisabled: false,
      expandTrigger: 'hover',
      multiple: false,
      expandedValue: [],
      defaultExpandFirstItem: false,
      getNodes: () => [],
    }
  })
  const cascaderPanelCtx = inject<ComputedRef<CascaderPanelCtx>>(CASCADER_PANEL_PROVIDER, defaultCtx)
  return cascaderPanelCtx
}

export function useCascaderPanelProvider(props: any, ins: any) {

  const context = computed((): CascaderPanelCtx => {
    return {
      showCheckbox: ins.showCheckbox,
      changeOnSelect: props.changeOnSelect,
      expandTrigger: props.expandTrigger,
      expandableOnDisabled: props.expandableOnDisabled,
      expandedValue: ins.expandedValue,
      multiple: props.multiple,
      getNodes: ins.getNodes,
      defaultExpandFirstItem: props.defaultExpandFirstItem,
    }
  })

  provide<ComputedRef<CascaderPanelCtx>>(CASCADER_PANEL_PROVIDER, context)
}

```

#### index.md

```markdown
# 级联 / Cascader

将数据集合，进行分类展示和选择，建议最多展示 4 级菜单。

## 互动演示

<mtd-doc-usage renderComponentName="MtdCascader" name="Cascader" :apiJson="require('./api.json')"  :usageJson="require('./usage.json')">
</mtd-doc-usage>

## 代码示例

### 尺寸

级联选择器提供了小、中、大三种尺寸。
:::include(src="./doc/size.vue")
:::

### 可多选

支持选择多个节点。当选项数量过多时，支持定义选择框最大行数，超过行数后开启滚动，也支持折叠已选项。
:::include(src="./doc/multiple.vue")
:::

### 可搜索

支持输入内容进行搜索。
:::include(src="./doc/search.vue")
:::

### 可选非末节点

可选择任意一级的节点。
:::include(src="./doc/changeOnSelect.vue")
:::

### 自定义回显节点

支持仅回显末节点，也支持回显选中路径，后者可明确提示和告知用户选中信息的分类来源。`formatter` 仅支持返回 `string`
:::include(src="./doc/formatter.vue")
:::

### 自定义回显内容

支持自定义选中项回显到输入框的内容展示。
:::include(src="./doc/custom-formatter.vue")
:::

### 自定义节点内容

支持自定义下拉面板中选中项的内容展示。
:::include(src="./doc/custom.vue")
:::

### 子菜单触发方式

支持通过不同的触发方式来加载子菜单，当层级、分类较多，选用“悬停展开下级菜单”，可快速预览菜单内容。
:::include(src="./doc/expand.vue")
:::

### 禁用选项

下拉菜单可根据场景配置 disable 状态。
在数据源中配置 `disabled` 为 `true` 来禁用选项。可以通过 `props` 属性来指定(详见 API)
:::include(src="./doc/status.vue")
:::

### 异步加载

由于计算属性在调用 $set 时并不会触发 watch，所以动态加载时并不支持 data 属性值为计算属性。
:::include(src="./doc/load.vue")
:::

### 下拉面板附加信息

下拉面板底部可拓展说明信息，可带图标、链接，建议不超过两行。
:::include(src="./doc/addendum.vue")
:::

### 单独的级联面板

:::include(src="./doc/cascader-panel.vue")
:::

## API

<api-doc name="Cascader" :doc="require('./api.json')"></api-doc>

### data 字段说明

字段名可由 Cascader.props 属性进行更改
| 参数 | 说明 | 类型 | 可选值 | 默认值 |
|--- |--- |--- |--- |--- |
| label | 标题 | string | - | - |
| value | 值 | any | - | - |
| children | 子节点 | data[] | - | - |
| isLeaf | 是否是叶子节点，配合 loadData 使用 | boolean | - | false |
| loading | 是否是加载中 | boolean | - | false |
| disabled | 是否禁用， 禁用后将无法展开 | boolean | - | false |

### node 字段说明

**只读**，由父级传递下来的节点状态信息，相关字段名不受 Tree.props 属性影响
| 参数 | 说明 | 类型 | 可选值 | 默认值 |
|--- |--- |--- |--- |--- |
| $parent | 父节点，如果是根节点则为空 | node | - | - |
| checked | 当前节点是否勾选 | boolean | - | - |
| children | 子节点数组，如果没有则为空 | node[] | - | - |
| data | 当前节点数据 | data | - | - |
| disabled | 节点是否禁用 | boolean | - | - |
| hasChildren | 是否有子节点 | boolean | - |- |
| hasChildrenChecked | 是否有子节点被选中 | boolean | - | - |
| indeterminate | 是否是半选状态 | boolean | - | - |
| isLeaf | 当前节点是否是叶子节点 | boolean | - | false |
| label | 标题 | string | - | - |
| level | 当前节点的层级，从 0 开始算 | number | - | 0 |
| loading | 是否是加载中的状态 | boolean | - | - |
| selected | 当前节点是否选中 | boolean | - | - |
| value | 当前节点的值 | boolean | - | - |

<style lang="scss">
  .demo-cascader{
    .demo-source {
      align-items: center;
    }
    .mtd-row + .mtd-row{
      margin-top: 36px
    }
  }
  </style>

```

#### index.ts

```typescript
import Cascader from './cascader'
import { withInstall } from '@ss/mtd-adapter'
export default withInstall(Cascader)

```

#### types.ts

```typescript
import { Component } from '@components/types/component'
import { PopperPlacement, getPopupContainer } from '@components/popper/types'
import { Option } from '@components/option/types'
import { TreeNode } from '@components/tree/types'

export interface Formatter {
  (labels: string[], values: any[]): string;
}

export interface LoadDataResolve {
  (data: any[]): void;
}

export interface LoadData {
  (item: any, resolve: LoadDataResolve): void;
}

export interface FilterMethod {
  (query: string, item: any): boolean;
}

export interface RemoteMethod {
  (query: string): boolean;
}

export interface Node<T = any, V = any> extends TreeNode {
  $parent?: Node<T, V>;
  $index: number;
  level: number;
  data: T;

  value: V;
  label: string;
  isLeaf: boolean;
  disabled: boolean;
  loading: boolean;

  selected: boolean;
  checked: boolean;
  hasChildrenChecked: boolean;
  indeterminate: boolean;
  children?: Node<T, V>[];
  hasChildren: boolean;

  hover?: boolean;
}

export interface FilteredNode<T = any, V = any> {
  label: string;
  value: V[];
  isLeaf: boolean;
  disabled: boolean;
  checked: boolean;
  m__IS_FILTERED_NODE: true;
  nodes: TreeNode<T, V>[];
}

export type DisplayNode<T = any, V = any> = FilteredNode<T, V> | Node<T, V>;

export interface CascaderFieldName {
  label: string;
  value: string;
  children: string;
  isLeaf: string;
  disabled: string;
  loading: string;
}

export type TCheckedStrategy = 'all' | 'parent' | 'children';

export declare interface ICascader extends Component {
  visible: boolean;
  defaultVisible: boolean;
  value: any[];
  data: any[];

  noDataText: string;
  props: object;

  changeOnSelect: boolean;
  expandTrigger: string;
  separator: string;
  filterable: boolean;

  formatter: Formatter;
  loadData: LoadData;
  debounce: number;

  reserveKeyword: boolean;
  filterMethod: FilterMethod;
  remote: boolean;
  remoteMethod: RemoteMethod;
  noMatchText: string;
  loading: boolean;
  loadingText: string;
  disabled: boolean;
  clearable: boolean;
  size: string;
  placeholder: string;
  popperClass: string;

  placement: PopperPlacement;

  appendToContainer: boolean;
  getPopupContainer: getPopupContainer;
}

export interface CascaderState {
  inputValue: string;
  expandedValue: any;
  focused: boolean;
  previousQuery: string | null;
  filter: string;
  inputWidth: string;
  valueStrs: Array<string>;
  debouncedQuery: Function | null;
  isOnComposition: boolean;

  option?: Option,
  cachedValues: Map<string, any>
}

export type ExpandTrigger = 'click' | 'hover';

export interface ICascaderPanel extends Component {
  showCheckbox: boolean;
  changeOnSelect: boolean;
  expandTrigger: ExpandTrigger;

  getNodesByValues(values: any[]): Node[];
}



declare const Cascader: ICascader
export default Cascader

```

#### usage.json

```json
{
  "componentName": "Cascader",
  "desc": "级联",
  "codeTemplate": "<mtd-cascader v-bind='configProps' />",
  "data": [
    {
      "value": "zhinan",
      "label": "指南",
      "children": [
        {
          "value": "shejiyuanze",
          "label": "设计原则",
          "children": [
            {
              "value": "yizhi",
              "label": "一致"
            },
            {
              "value": "fankui",
              "label": "反馈"
            },
            {
              "value": "xiaolv",
              "label": "效率"
            },
            {
              "value": "kekong",
              "label": "可控"
            }
          ]
        },
        {
          "value": "daohang",
          "label": "导航",
          "children": [
            {
              "value": "cexiangdaohang",
              "label": "侧向导航"
            },
            {
              "value": "dingbudaohang",
              "label": "顶部导航"
            }
          ]
        }
      ]
    },
    {
      "value": "zujian",
      "label": "组件",
      "children": [
        {
          "value": "basic",
          "label": "Basic",
          "disabled": true,
          "children": [
            {
              "value": "layout",
              "label": "Layout 布局"
            },
            {
              "value": "color",
              "label": "Color 色彩"
            },
            {
              "value": "typography",
              "label": "Typography 字体"
            },
            {
              "value": "icon",
              "label": "Icon 图标"
            },
            {
              "value": "button",
              "label": "Button 按钮"
            }
          ]
        },
        {
          "value": "form",
          "label": "Form",
          "isLeaf": true
        },
        {
          "value": "data",
          "label": "Data",
          "children": [
            {
              "value": "table",
              "label": "Table 表格"
            },
            {
              "value": "tag",
              "label": "Tag 标签"
            },
            {
              "value": "progress",
              "label": "Progress 进度条"
            },
            {
              "value": "tree",
              "label": "Tree 树形控件"
            },
            {
              "value": "pagination",
              "label": "Pagination 分页"
            },
            {
              "value": "badge",
              "label": "Badge 标记"
            }
          ]
        },
        {
          "value": "notice",
          "label": "Notice",
          "children": [
            {
              "value": "alert",
              "label": "Alert 警告"
            },
            {
              "value": "loading",
              "label": "Loading 加载"
            },
            {
              "value": "message",
              "label": "Message 消息提示"
            },
            {
              "value": "message-box",
              "label": "MessageBox 弹框"
            },
            {
              "value": "notification",
              "label": "Notification 通知"
            }
          ]
        },
        {
          "value": "navigation",
          "label": "Navigation",
          "children": [
            {
              "value": "menu",
              "label": "NavMenu 导航菜单"
            },
            {
              "value": "tabs",
              "label": "Tabs 标签页"
            },
            {
              "value": "breadcrumb",
              "label": "Breadcrumb 面包屑"
            },
            {
              "value": "dropdown",
              "label": "Dropdown 下拉菜单"
            },
            {
              "value": "steps",
              "label": "Steps 步骤条"
            }
          ]
        },
        {
          "value": "others",
          "label": "Others",
          "children": [
            {
              "value": "dialog",
              "label": "Dialog 对话框"
            },
            {
              "value": "tooltip",
              "label": "Tooltip 文字提示"
            },
            {
              "value": "popover",
              "label": "Popover 弹出框"
            },
            {
              "value": "card",
              "label": "Card 卡片"
            },
            {
              "value": "carousel",
              "label": "Carousel 走马灯"
            },
            {
              "value": "collapse",
              "label": "Collapse 折叠面板"
            }
          ]
        }
      ]
    },
    {
      "value": "ziyuan",
      "label": "资源",
      "children": [
        {
          "value": "axure",
          "label": "Axure Components"
        },
        {
          "value": "sketch",
          "label": "Sketch Templates"
        },
        {
          "value": "jiaohu",
          "label": "组件交互文档"
        }
      ]
    }
  ]
}

```

#### util.ts

```typescript
import { CascaderFieldName } from './types'
export const DEFAULT_FIELD_NAMES: CascaderFieldName = {
  label: 'label',
  value: 'value',
  children: 'children',
  isLeaf: 'isLeaf',
  disabled: 'disabled',
  loading: 'loading',
}

export const FILTERED_NODE_KEY = 'm__IS_FILTERED_NODE'

export function getActivePaths(
  array: any[],
  value: any[],
  fieldNames: CascaderFieldName,
) {
  let target = array
  const result: any[] = []
  for (let i = 0; i < value.length && target; i++) {
    const n = target.find((item) => {
      return item[fieldNames.value] === value[i]
    })
    if (!n) {
      break
    }

    result[i] = n
    target = n[fieldNames.children]
  }

  return result
}

export const isArrayEqual = (arr1: string[], arr2: string[]) => {
  if (arr1.length !== arr2.length) return false
  let res = true
  const sortArr2 = arr2.slice().sort()
  arr1.slice().sort().forEach((item, idx) => {
    if (sortArr2[idx] !== item) {
      res = false
    }
  })
  return res
}

```

