## 源码解析

### 文件依赖

- button.tsx
- demo.md
- doc/async-loading.vue
- doc/base.vue
- doc/base1.vue
- doc/base3.vue
- doc/color.vue
- doc/demo/button-icon.vue
- doc/demo/link.vue
- doc/ghost.vue
- doc/group.vue
- doc/icon.vue
- doc/link.vue
- doc/menu.vue
- doc/size.vue
- doc/status.vue
- doc/suffix.vue
- doc/test-vir.vue
- doc/text.vue
- doc/type.vue
- index.md
- index.ts
- props.ts
- select.vue
- tests/button.spec.js
- types.ts
- usage.json

### 源码内容

#### button.tsx

```tsx
import {
  defineComponent,
  computed,
  ref,
  getSlotsInRender,
  useListeners, getListeners,
  classNames, styles,
  RouterLink,
  useResetAttrs,
} from '@ss/mtd-adapter'
import useConfig from '@hooks/config'
import props from './props'
import { isArray, isFunction, isPromise } from '@utils/type'

import useControlled from '@hooks/controlled'

import MtdIcon from '@components/icon'
import MtdLoadingCircle from '@components/loading/circle'
import { useFormItem } from '@components/form-item/useFormItem'

export default defineComponent({
  name: 'MtdButton',
  components: {
    MtdIcon,
    MtdLoadingCircle,
  },
  inheritAttrs: false,
  props: props(),
  emits: ['mouseleave'], // 🤡 vue3 加上click别想拿到
  setup(props, context) {
    // get prefix class
    const config = useConfig()
    const prefix = computed(() => config.getPrefixCls('btn'))

    // get ref
    const buttonRef = ref<HTMLElement | null>(null)
    const formItemHook = useFormItem(props, context)

    const m_size = formItemHook.m_size
    const m_disabled = formItemHook.disabled

    // get root component
    const tag = computed(() => {
      if (props.to !== undefined && !props.disabled) {
        return RouterLink
      } else if (props.href !== undefined) {
        return 'a'
      } else {
        return 'button'
      }
    })

    let timer = 0
    const clicked = ref(false)
    // eslint-disable-next-line
    function useClicked(e: Event) {
      clearTimeout(timer)
      clicked.value = true
      timer = setTimeout(() => {
        clicked.value = false
      }, 300)
    }

    const listeners = getListeners()
    const [loading, setLoading] = useControlled<boolean>('loading', props, context)
    function useClickLoading(e: Event) {
      const onClick = listeners.value.click
      const handler = () => {
        setLoading(false, { force: true })
      }
      if (isArray(onClick)) {
        const r = Promise.all(onClick.map((fn) => fn && fn(e)))
        setLoading(true, { force: true })
        r.then(handler, handler)
      } else if (isFunction(onClick)) {
        const r = onClick(e)
        if (isPromise(r)) {
          setLoading(true, { force: true })
          r.then(handler, handler)
        }
      }
    }

    const handleClick = (e: Event) => {
      if (m_disabled.value || props.loading) {
        e.preventDefault()
        return
      }
      useClicked(e)
      useClickLoading(e)
    }

    const handleMouseLeave = (e: Event) => {
      // 取消button active样式
      if (isFunction(buttonRef.value?.blur)) {
        buttonRef.value?.blur()
      }

      context.emit('mouseleave', e)
    }

    const restAttrs = useResetAttrs(context.attrs)
    const restListeners = useListeners({
      click: handleClick,
      mouseleave: handleMouseLeave,
    }, ['click', 'mouseleave'])

    const focus = () => {
      if (m_disabled.value || props.loading) return
      buttonRef.value?.focus()
    }
    const blur = () => {
      if (m_disabled.value || props.loading) return
      buttonRef.value?.blur()
    }

    return {
      clicked,
      tag,
      innerLoading: loading,
      prefix,
      restAttrs,
      restListeners,
      handleClick,
      focus,
      blur,
      buttonRef,
      m_size,
      m_disabled,
    }
  },
  render() {
    const {
      clicked, tag,
      innerLoading: loading,
      prefix,
      htmlType,
      type,
      m_size,
      dashed,
      m_disabled: disabled,
      ghost,
      icon,
      suffix,
      href,
      to,
      restListeners,
      restAttrs,
      // circle属性兼容历史
      circle,
      shape,
    } = this
    // innerLoding返回一个计算属性的loading，无loading才用inner
    const Component = tag

    const onlyIcon = (loading || icon || getSlotsInRender(this, 'icon')) && !getSlotsInRender(this)
    return <Component
      class={classNames(this, [
        `${prefix}`,
        `${type ? `${prefix}-${type}` : ''}`,
        `${m_size ? `${prefix}-${m_size}` : ''}`,
        {
          [`${prefix}-dashed`]: dashed,
          [`${prefix}-disabled`]: disabled,
          [`${prefix}-loading`]: loading,
          [`${prefix}-clicked`]: clicked,
          [`${prefix}-ghost`]: ghost,
          [`${prefix}-only-icon`]: onlyIcon && (shape || circle),
          [`${prefix}-circle`]: shape === 'circle' || circle,
          [`${prefix}-square`]: shape === 'square' && !circle,
        },
      ])}
      style={styles(this)}
      href={href}
      to={to}
      type={htmlType}
      /* disabled={disabled || loading ? 'disabled' : undefined} */
      ref={'buttonRef'}
      {...restListeners}
      {...restAttrs}
    >
      {
        (loading || icon || getSlotsInRender(this, 'icon')) &&
        <span class={`${prefix}-before`}
          style={{ marginRight: getSlotsInRender(this) ? undefined : '0px' }}>
          {loading ? <mtd-loading-circle style="color:currentColor" />
            : (getSlotsInRender(this, 'icon') || <mtd-icon name={icon} />)
          }
        </span>
      }
      {getSlotsInRender(this)}
      {
        (suffix || getSlotsInRender(this, 'suffix')) &&
        <span class={`${prefix}-after`}
          style={{ marginLeft: getSlotsInRender(this) ? undefined : '0px' }}>
          {(getSlotsInRender(this, 'suffix') || <mtd-icon name={suffix} />)}
        </span>
      }
    </Component>
  },
})

```

#### demo.md

```markdown
# 按钮 / Button

## 补充Demo

### 链接能力
禁用状态下，href或to属性不跳转
:::include(src="./doc/demo/link.vue")
:::

### IconButton
:::include(src="./doc/demo/button-icon.vue")
:::
```

#### doc/async-loading.vue

```vue
<template>
  <mtd-button @click="handleClick" type="primary">提交</mtd-button>
</template>
<script>
export default {
  methods: {
    async handleClick () {
      // 请注意必须 catch error，否则 vue 自身将捕获到相应的错误并报错。
      try {
        await this.ajax();
      } catch (e) {
        // do something
      }
    },
    ajax () {
      return new Promise((resolve, reject) => {
        setTimeout(() => {
          resolve();
        }, 3000);
      });
    },
  },
};
</script>

```

#### doc/base.vue

```vue
<template>
  <div class="demo-source-content">
    <mtd-button html-type="button" type="primary" @click="test">
      主要按钮
    </mtd-button>
  </div>
</template>
<script>
export default {
  data() {
    return {}
  },

  methods: {
    test() {
      console.log('你按下了按钮')
    },
  },
}
</script>

```

#### doc/base1.vue

```vue
<template>
  <div class="demo-full-width">
    <mtd-table 
      :data="tableData" 
      vertical-virtual
      horizontal-virtual
      bordered
      :height="500" 
      :virtual-options="virtualOptions" 
    >
    <mtd-table-column
        label="操作">
        <template slot-scope="scope">
          <mtd-popover trigger="hover" placement="top">
            <mtd-button>{{ scope.row.name }}</mtd-button>
            <div slot="content" class="name-wrapper">
              <mtd-tag size="small">{{ scope.row.name }}</mtd-tag>
            </div>
          </mtd-popover>
        </template>
      </mtd-table-column>
      <mtd-table-column v-for="(column,index) in columns"
        :prop="column.prop"
        :label="column.label + '_' + index"
        :width="column.width" 
        :key="index"
        :fixed="index < 2 ? 'left' : index === 4 ? 'right' : false"
      />
    
    </mtd-table>

    <br><br><br>
    <mtd-table 
      :data="tableData.slice(0,20)" 
      bordered
      :height="500" 
    >
      <mtd-table-column v-for="(column,index) in columns2"
        :prop="column.prop"
        :label="column.label + '_' + index"
        :key="index"
        :width="column.width"
      />
    </mtd-table>

    <mtd-button @click="addCol">动态增加列</mtd-button>
  </div>
</template>

<script>

const columnsData = [
  {
    prop: 'date',
    label: '日期',
    width: '200',
  },
  {
    prop: 'name',
    label: '姓名',
    width: '100',
  },
  {
    prop: 'province',
    label: '省份',
    width: '150',
  },
  {
    prop: 'city',
    label: '城市/区域',
    width: '250',
  },
  {
    prop: 'address',
    label: '地址',
    width: '300',
  },
  {
    prop: 'zip',
    label: '邮编',
    width: '150',
  },
  {
    prop: 'tag',
    label: '标签',
    width: 100,
  },
]

export default {
  data () {
    return {
      columns: [],
      columns2: [
        {
          prop: 'date',
          label: '日期',
          width: '100',
        },
        {
          prop: 'name',
          label: '姓名',
          width: '100',
        },
        {
          prop: 'province',
          label: '省份',
          width: '150',
        },
        {
          prop: 'city',
          label: '城市/区域',
          width: '250',
        },
        {
          prop: 'address',
          label: '地址',
          width: '300',
        },
        {
          prop: 'zip',
          label: '邮编',
          width: '100',
        },
        {
          prop: 'tag',
          label: '标签',
          width: '1000',
        },
      ],

      tableData: [],

      virtualOptions: {
        rowHeight: 55,
      },

      frameCount: 0,
      frameRateSum: 0,
      lastTimestamp: 0,
    };
  },

  created() {
    let n = 10;
    for(;n > 0;n--){
      this.columns.push(...columnsData)
    }

    let arr = []
    for(let i = 0; i < 100; i++) {
      arr.push({
        date: i,
        name: '美小团',
        province: '北京',
        city: '朝阳区',
        address: '北京市朝阳区望京东路四号',
        zip: 200333,
        tag: '家',
      });
    }
    this.tableData = arr
  },

  methods: {

    addCol() {
      this.columns2.push({
        prop: 'city',
        label: '所在地🤡',
        width: '100',
      })
    }
  },

};
</script>

```

#### doc/base3.vue

```vue
<template>
  <div style="width: 100%">
  <mtd-table
    :data="tableData6"
    :row-col-span="objectRowColSpan"
    bordered
    :height="500"
    vertical-virtual
    :virtual-options="virtualOptions" 
    style="width: 100%; margin-top: 20px">
    <mtd-table-column
      prop="id"
      label="ID"
      width="180" />
    <mtd-table-column
      prop="bg"
      label="部门"
      width="180" />
    <mtd-table-column
      prop="name"
      label="姓名"
      width="180" 
      />
    <mtd-table-column
      prop="amount"
      label="数值 （元）" />

  </mtd-table>
  </div>
</template>

<script>
export default {
  data () {
    return {
      tableData6: [],
      virtualOptions: {
        rowHeight: 45,
        rowSplit: [], 
      }
    };
  },
  created() {
    for(let i = 0;i < 1000;i++){
      this.tableData6.push({
        id: i,
        bg: '部门 ' + Math.floor(i / 10),
        name: '用户 ' + Math.floor(i / 10) + ' - ' + Math.floor(i / 5), 
        amount: i * 100
      })
      if(i % 10 === 0 && i > 0) this.virtualOptions.rowSplit.push(i)
    }
  },
  methods: {
    objectRowColSpan ({ row, column, rowIndex, columnIndex }) {
      // 合并列
      if (columnIndex === 1) {
        if(rowIndex % 10 === 0) {
          return {
            rowspan: 10,
            colspan: 1,
          };
        } else {
          return {
            rowspan: 0,
            colspan: 1,
          };
        }
      }
      if (columnIndex === 2) {
        if(rowIndex % 5 === 0) {
          return {
            rowspan: 5,
            colspan: 1,
          };
        } else {
          return {
            rowspan: 0,
            colspan: 1,
          };
        }
      }
    },
  },
};
</script>
```

#### doc/color.vue

```vue
<template>
  <div class="demo-source-content">
    <mtd-button class="btn-demo-item" type="primary">主要按钮</mtd-button>
    <mtd-button class="btn-demo-item" type="success">成功按钮</mtd-button>
    <mtd-button class="btn-demo-item" type="warning">警告按钮</mtd-button>
    <mtd-button class="btn-demo-item" type="danger">危险按钮</mtd-button>
  </div>
</template>

```

#### doc/demo/button-icon.vue

```vue
<template>
  <div class="demo-source-content">
    <mtd-icon-button type="secondary" icon="search" @click="test"></mtd-icon-button>
    <mtd-icon-button icon="search" html-type="button" disabled @click="test"></mtd-icon-button>
    <mtd-icon-button type="secondary" icon="search" html-type="submit"></mtd-icon-button>
    <mtd-icon-button type="secondary" icon="search" html-type="reset"></mtd-icon-button>
    <mtd-icon-button icon="search"></mtd-icon-button>
  </div>
</template>
<script>

export default {
  methods: {
    test() {
      console.log('你按下了按钮')
    },
  },
}
</script>
<style scoped lang="scss">
.demo-source-content {
  width: 100%;
  padding: 40px;
  display: flex;
  justify-content: space-around;
}
</style>
```

#### doc/demo/link.vue

```vue
<template>
  <div class="demo-source-content">
    <mtd-button type="primary" to="/components/icon" disabled>路由链接</mtd-button>
    <mtd-button type="primary" href="/" target="_blank" disabled>外部链接</mtd-button>
  </div>
</template>
<script>
export default {
  methods: {
    test() {
      console.log('你按下了按钮')
    },
  },
}
</script>
<style scoped lang="scss">
.demo-source-content {
  width: 100%;
  padding: 40px;
  display: flex;
  justify-content: space-around;
}
</style>
```

#### doc/ghost.vue

```vue
<template>
  <div class="demo-source-content ghost-demo-container">
    <mtd-button class="btn-demo-item" ghost type="primary">主要按钮</mtd-button>
    <mtd-button class="btn-demo-item" ghost type="success">成功按钮</mtd-button>
    <mtd-button class="btn-demo-item" ghost type="warning">警告按钮</mtd-button>
    <mtd-button class="btn-demo-item" ghost type="danger">危险按钮</mtd-button>
  </div>
</template>
<style scoped lang="scss">
.ghost-demo-container {
  background: linear-gradient(250deg, #FFFFFF 0%, #D9E8FF 100%);
}
</style>

```

#### doc/group.vue

```vue
<template>
  <div class="demo-source-content demo-btn-group">

    <mtd-button-group>
      <mtd-button type="primary">确定</mtd-button>
      <mtd-button>重置</mtd-button>
      <mtd-button>取消</mtd-button>
    </mtd-button-group>

    <mtd-button-group>
      <mtd-button type="text" icon="file-add-o">确定</mtd-button>
      <mtd-button type="text" icon="delete-o">删除</mtd-button>
      <mtd-button type="text" icon="edit-o">编辑</mtd-button>
    </mtd-button-group>

    <mtd-button-group>
      <mtd-icon-button type="secondary" icon="piechart" />
      <mtd-icon-button type="secondary" icon="barschart" />
      <mtd-icon-button type="secondary" icon="areachart" />
    </mtd-button-group>


  </div>
</template>
<style lang="scss" scoped>
.demo-block {
  .demo-source {
    .demo-btn-group {
      text-align: left;
      display: flex;
      align-items: center;
      justify-content: space-around;

      .mtd-btn {
        margin-left: 12px;
      }
    }
  }
}
</style>

```

#### doc/icon.vue

```vue
<template>
  <div class="demo-source-content">
    <mtd-button class="demo-icon-btn" type="primary" icon="calendar-o">日期</mtd-button>
    <mtd-button class="demo-icon-btn" icon="cart-o">加入购物车</mtd-button>
    <mtd-button class="demo-icon-btn" type="text" icon="export-o">导出</mtd-button>
    <mtd-button class="demo-icon-btn" type="primary" icon="search" shape="square"></mtd-button>
    <mtd-button class="demo-icon-btn" shape="circle" type="primary" icon="search"></mtd-button>
    <mtd-button class="demo-icon-btn" icon="add" shape="square"></mtd-button>
    <mtd-button class="demo-icon-btn" shape="circle">
      <template #icon>
        <mtd-icon name="add" />
      </template>
    </mtd-button>
    <mtd-icon-button html-type="button" class="demo-icon-btn" type="secondary" icon="delete-o" />
  </div>
</template>

<style scoped lang="scss">
.demo-button {
  .demo-source-content {
    .demo-icon-btn {
      margin-left: 36px;
    }
  }
}
</style>

```

#### doc/link.vue

```vue
<template>
  <div class="demo-source-content">
    <mtd-button type="primary" to="/components/icon">路由链接</mtd-button>
    <mtd-button type="primary" href="/" target="_blank">外部链接</mtd-button>
  </div>
</template>

```

#### doc/menu.vue

```vue
<template>
  <div class="demo-source-content">
    <div class="demo-dropdown-btn-group source">

      <mtd-dropdown>
        <mtd-button type="primary">
          更多
          <i class="mtdicon mtdicon-down-thick" />
        </mtd-button>
        <template #dropdown>
          <mtd-dropdown-menu>
            <mtd-dropdown-menu-item>添加</mtd-dropdown-menu-item>
            <mtd-dropdown-menu-item>删除</mtd-dropdown-menu-item>
            <mtd-dropdown-menu-item>复制</mtd-dropdown-menu-item>
          </mtd-dropdown-menu>
        </template>
      </mtd-dropdown>

      <mtd-dropdown>
        <mtd-button>
          <i class="mtdicon mtdicon-edit-o" style="margin-right: 4px" />
          <i class="mtdicon mtdicon-down-thick" />
        </mtd-button>
        <template #dropdown>
          <mtd-dropdown-menu>
            <mtd-dropdown-menu-item>添加</mtd-dropdown-menu-item>
            <mtd-dropdown-menu-item>删除</mtd-dropdown-menu-item>
            <mtd-dropdown-menu-item>复制</mtd-dropdown-menu-item>
          </mtd-dropdown-menu>
        </template>
      </mtd-dropdown>

      <mtd-dropdown>
        <mtd-button type="text">
          <span style="margin-right: 4px">更多</span>
          <i class="mtdicon mtdicon-down-thick" />
        </mtd-button>
        <template #dropdown>
          <mtd-dropdown-menu>
            <mtd-dropdown-menu-item>添加</mtd-dropdown-menu-item>
            <mtd-dropdown-menu-item>删除</mtd-dropdown-menu-item>
            <mtd-dropdown-menu-item>复制</mtd-dropdown-menu-item>
          </mtd-dropdown-menu>
        </template>
      </mtd-dropdown>
     
      <mtd-dropdown>
        <mtd-button type="text">
          <i class="mtdicon mtdicon-edit-o" style="margin-right: 4px" />
          <i class="mtdicon mtdicon-down-thick" />
        </mtd-button>
        <template #dropdown>
          <mtd-dropdown-menu>
            <mtd-dropdown-menu-item>添加</mtd-dropdown-menu-item>
            <mtd-dropdown-menu-item>删除</mtd-dropdown-menu-item>
            <mtd-dropdown-menu-item>复制</mtd-dropdown-menu-item>
          </mtd-dropdown-menu>
        </template>
      </mtd-dropdown>

    
    </div>
  </div>
</template>
<style scoped lang="scss">
.demo-dropdown-btn-group {
  .mtd-dropdown+.mtd-dropdown {
    margin-left: 80px;
  }

  .mtd-btn {
    i {
      margin-left: 4px;
    }
    .mtdicon-down-thick{
      font-size: 16px;
    }
  }
}
</style>

```

#### doc/size.vue

```vue
<template>
  <div class="demo-source-content" style="width: 100%;">

    <div class="col">
      <mtd-row>
        <mtd-button type="primary" size="small">小号按钮</mtd-button>
      </mtd-row>
      <mtd-row>
        <mtd-button type="primary">中号按钮</mtd-button>
      </mtd-row>
      <mtd-row>
        <mtd-button type="primary" size="large">大号按钮</mtd-button>
      </mtd-row>
    </div>

    <div class="col">
      <mtd-row>
        <mtd-button type="primary" size="small" icon="search">小号按钮</mtd-button>
      </mtd-row>
      <mtd-row>
        <mtd-button type="primary" icon="search">中号按钮</mtd-button>
      </mtd-row>
      <mtd-row>
        <mtd-button type="primary" size="large" icon="search">大号按钮</mtd-button>
      </mtd-row>
    </div>

    <div class="col">
      <mtd-row>
        <mtd-button type="primary" size="small" icon="search" shape="square"></mtd-button>
      </mtd-row>
      <mtd-row>
        <mtd-button type="primary" icon="search" shape="square"></mtd-button>
      </mtd-row>
      <mtd-row>
        <mtd-button type="primary" size="large" icon="search" shape="square"></mtd-button>
      </mtd-row>
    </div>

    <div class="col">
      <mtd-row>
        <mtd-button type="primary" size="small" icon="search" shape="circle"></mtd-button>
      </mtd-row>
      <mtd-row>
        <mtd-button type="primary" icon="search" shape="circle"></mtd-button>
      </mtd-row>
      <mtd-row>
        <mtd-button type="primary" size="large" icon="search" shape="circle"></mtd-button>
      </mtd-row>
    </div>

    <div class="col">
      <mtd-row>
        <mtd-icon-button type="secondary" size="small" icon="search"></mtd-icon-button>
      </mtd-row>
      <mtd-row>
        <mtd-icon-button type="secondary" icon="search"></mtd-icon-button>
      </mtd-row>
      <mtd-row>
        <mtd-icon-button type="secondary" size="large" icon="search"></mtd-icon-button>
      </mtd-row>
    </div>

  </div>
</template>
<style scoped lang="scss">
.demo-source-content {
  display: flex;
  justify-content: center;

  .col+.col {
    margin-left: 48px;
  }

  .col {
    .mtd-row {
      margin-bottom: 36px;
    }
  }
}
</style>
```

#### doc/status.vue

```vue
<template>
  <div class="demo-source-content" style="width: 100%;">

    <div class="col">
      <mtd-row>
        <mtd-button type="primary" ref="primaryBtn" @click="handleClickPrimary">主要按钮</mtd-button>
      </mtd-row>
      <mtd-row>
        <mtd-button type="primary" ref="disableBtn" disabled @click="handleClickPrimary">主要按钮-禁用</mtd-button>
      </mtd-row>
      <mtd-row>
        <mtd-button type="primary" ref="loadingBtn" loading @click="handleClickPrimary">加载中</mtd-button>
      </mtd-row>
    </div>

    <div class="col">
      <mtd-row>
        <mtd-button ref="btn" @click="handleClick">次要按钮</mtd-button>
      </mtd-row>
      <mtd-row>
        <mtd-button disabled @click="handleClick">次要按钮-禁用</mtd-button>
      </mtd-row>
      <mtd-row>
        <mtd-button loading @click="handleClick">加载中</mtd-button>
      </mtd-row>
    </div>

    <div class="col">
      <mtd-row>
        <mtd-button type="text">文字按钮</mtd-button>
      </mtd-row>
      <mtd-row>
        <mtd-button type="text" disabled>文字按钮禁用</mtd-button>
      </mtd-row>
      <mtd-row>
        <mtd-button type="text" loading>加载中</mtd-button>
      </mtd-row>
    </div>

    <div class="col">
      <mtd-row>
        <mtd-button type="primary" icon="search">图标按钮</mtd-button>
      </mtd-row>
      <mtd-row>
        <mtd-button type="primary" icon="search" disabled>图标按钮-禁用</mtd-button>
      </mtd-row>
      <mtd-row>
        <mtd-button type="primary" icon="search" loading>加载中</mtd-button>
      </mtd-row>
    </div>

    <div class="col">
      <mtd-row>
        <mtd-button type="primary" icon="search" shape="square"></mtd-button>
      </mtd-row>
      <mtd-row>
        <mtd-button type="primary" icon="search" shape="square" disabled></mtd-button>
      </mtd-row>
      <mtd-row>
        <mtd-button type="primary" icon="search" shape="square" loading></mtd-button>
      </mtd-row>
    </div>

    <div class="col">
      <mtd-row>
        <mtd-icon-button type="secondary" icon="search"></mtd-icon-button>
      </mtd-row>
      <mtd-row>
        <mtd-icon-button type="secondary" icon="search" disabled></mtd-icon-button>
      </mtd-row>
    </div>

  </div>
</template>
<script>
export default {

  methods: {
    handleClickPrimary(e) {
      console.log('你按下了主要按钮', e)
      this.$refs.disableBtn.focus()
      this.$refs.loadingBtn.focus()
      setTimeout(() => {
        this.$refs.disableBtn.blur()
        this.$refs.loadingBtn.blur()
      }, 1000)
    },
    handleClick(e) {
      console.log('你按下了次要按钮', e)
      this.$refs.primaryBtn.focus()
      this.$refs.primaryBtn.focus()
      setTimeout(() => {
        this.$refs.primaryBtn.blur()
        this.$refs.primaryBtn.blur()
      }, 1000)
    },
  },
}
</script>

<style scoped lang="scss">
.demo-source-content {
  display: flex;
  justify-content: center;

  .col+.col {
    margin-left: 48px;
  }

  .col {
    .mtd-row {
      margin-bottom: 36px;
    }
  }
}
</style>
 


```

#### doc/suffix.vue

```vue
<template>
    <div class="demo-source-content">
      <mtd-button html-type="button" type="primary" >
        日期
        <template #suffix>
          <mtd-icon name="calendar-o" />
        </template>
      </mtd-button>
      <mtd-button html-type="reset" suffix="cart-o">加入购物车</mtd-button>
      <mtd-button type="text" suffix="export-o">导出</mtd-button>
    </div>
  </template>
  <style scoped>
  .demo-source-content{
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 260px;
  }
  </style>
```

#### doc/test-vir.vue

```vue
<template>
  <div class="table-content-container">
    <mtd-table
      :data="tableData3"
      bordered
      class="table-content"
    >
      <mtd-table-column
        width="150"
        key="1"
        prop="date"
        label="日期"
        sortable
        :filters="[
          { text: '2016-05-02', value: '2016-05-02' },
          { text: '2016-05-03', value: '2016-05-03' },
          { text: '2016-05-04', value: '2016-05-04' },
        ]"
        :filter-method="filterDate"
      />
      <mtd-table-column
        prop="name"
        key="2"
        label="姓名"
        width="100"
        sortable
      />
      <mtd-table-column
        prop="province"
        label="省份"
        key="3"
        width="150"
      />
      <mtd-table-column prop="city" label="市区" key="4" width="250" />
      <mtd-table-column prop="address" label="地址" key="5" width="250" />
      <mtd-table-column prop="zip" label="邮编" key="6" width="250" />
      <mtd-table-column fixed="right" key="7" label="操作" width="80">
        <template #default="scope">
          <mtd-button
            @click="handleClick(scope.row)"
            type="text-primary"
            size="small"
            >查看</mtd-button
          >
          <mtd-button type="text-primary" style="margin-left: 12px" size="small"
            >编辑</mtd-button
          >
        </template>
      </mtd-table-column>
    </mtd-table>

  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
const tableData3 = [
  {
    date: "2016-05-03",
    name: "美小团",
    province: "北京",
    city: "朝阳区",
    address: "北京市朝阳区望京东路四号 1518",
    detailAddress: "望京东路四号",
    zip: 200333,
  },
  {
    date: "2016-05-02",
    name: "美小团",
    province: "北京",
    city: "朝阳区",
    address: "北京市朝阳区望京东路四号",
    detailAddress: "望京东路四号",
    zip: 200333,
  },
  {
    date: "2016-05-04",
    name: "美小团",
    province: "北京",
    city: "朝阳区",
    address: "北京市朝阳区望京东路四号",
    detailAddress: "望京东路四号",
    zip: 200333,
  },
  {
    date: "2016-05-01",
    name: "美小团",
    province: "北京",
    city: "朝阳区",
    address: "北京市朝阳区望京东路四号",
    detailAddress: "望京东路四号",
    zip: 200333,
  },
  {
    date: "2016-05-08",
    name: "美小团",
    province: "北京",
    city: "朝阳区",
    address: "北京市朝阳区望京东路四号",
    detailAddress: "望京东路四号",
    zip: 200333,
  },
  {
    date: "2016-05-06",
    name: "美小团",
    province: "北京",
    city: "朝阳区",
    address: "北京市朝阳区望京东路四号",
    detailAddress: "望京东路四号",
    zip: 200333,
  },
  {
    date: "2016-05-07",
    name: "美小团",
    province: "北京",
    city: "朝阳区",
    address: "北京市朝阳区望京东路四号",
    detailAddress: "望京东路四号",
    zip: 200333,
  },
];
// import { useRecoveryDataStore } from "../stores/recoveryContent";
// const dataStore = useRecoveryDataStore();
// const props = defineProps(["type"]);
// const { dataSource } = dataStore.tableList[props.type];

let currentPage = ref(1);
let pageSize = ref(50);
onMounted(() => {});

function handlePageChange(current, size) {
  currentPage.value = current;
  pageSize.value = size;
  console.log(
    "🚀🚀🚀🔊 ---> file: TableContent.vue:42 ---> current,size:",
    current,
    size
  );
}
function filterDate(value, row) {
  console.log(
    "🚀🚀🚀🔊 ---> file: TableContent.vue:80 ---> value, row):",
    value,
    row
  );
  return value === row.date;
}
function handleFilterChange(filtered) {
  console.log(
    "🚀🚀🚀🔊 ---> file: TableContent.vue:89 ---> filtered:",
    filtered
  );
  console.log("filtered:", filtered);
}
function filterName(value, row) {
  return value === row.name || row.name.indexOf(value) > -1;
}
</script>

<style lang="scss">
.table-content-container {
  margin-top: 21px;
  width: 800px;
  .table-content {
    width: 100%;
  }
  .table-pagination {
    padding-top: 25px;
    padding-bottom: 25px;
    width: 100%;
    display: flex;
    justify-content: flex-end;
  }
}
</style>

```

#### doc/text.vue

```vue
<template>
  <div>
    <mtd-button class="btn-demo-item" type="text" icon="theme-o">主题</mtd-button>
    <mtd-button class="btn-demo-item" type="text" icon="export-o">导出</mtd-button>
    <mtd-button class="btn-demo-item" type="primary">
      <template #icon>
        <mtd-icon name="calendar-o" />
      </template>
      日期
    </mtd-button>
    <mtd-button class="btn-demo-item" type="primary" icon="visibility-off-o">隐藏</mtd-button>
    <mtd-button class="btn-demo-item" icon="cart-o">加入购物车</mtd-button>
  </div>
</template>

```

#### doc/type.vue

```vue
<template>
  <div class="demo-source-content">
    <mtd-button html-type="button" class="btn-demo-item" type="primary">主要按钮</mtd-button>
    <mtd-button html-type="reset" class="btn-demo-item">次要按钮</mtd-button>
    <mtd-button class="btn-demo-item" type="panel">辅助按钮</mtd-button>
    <mtd-button class="btn-demo-item" type="text-primary">链接按钮</mtd-button>
    <mtd-button class="btn-demo-item" type="text">文字按钮</mtd-button>
    <mtd-button class="btn-demo-item" dashed>虚线按钮</mtd-button>
  </div>
</template>
<style scoped>
.demo-source-content{
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>

```

#### index.md

```markdown
# 按钮 / Button

是用于触发一个行动并形成决策的组件。

## 互动演示

<mtd-doc-usage  name="Button" :apiJson="require('./api.json')"  :usageJson="require('./usage.json')" >
  <template slot="Button" slot-scope="{ configProps }">
    <div>
     <mtd-button v-bind='configProps' :size=configProps.size>确认</mtd-button>
    </div>
  </template>
  <template #Button="{ configProps }">
    <div>
     <mtd-button v-bind='configProps' :size=configProps.size>确认</mtd-button>
    </div>
  </template>
</mtd-doc-usage>

## 代码示例

### 基础类型

根据场景的需要，不同按钮类型可表达操作的重要程度等级。
:::include(src="./doc/type.vue")
:::

### 尺寸

按钮提供了小、中（默认）、大三种尺寸。
:::include(src="./doc/size.vue")
:::

<!-- > 文字按钮的形式和链接的形式非常像但用法不同,请参考 [链接/Link <i class="mtdicon mtdicon-link-o"></i>](/components/link)🤡引入报错-->

### 状态

以主要按钮、次要按钮和文字按钮为例，按钮状态有正常状态、悬停状态、激活（点击）状态、禁用状态、加载状态。
:::include(src="./doc/status.vue")
:::

### 图标按钮

图标按钮由图标+文字或单个图标构成，可以增强辨识度或节省空间。
:::include(src="./doc/icon.vue")
:::

### 信息状态

按钮提供四种主题颜色，用来体现状态。
:::include(src="./doc/color.vue")
:::

### 幽灵按钮

幽灵按钮将按钮的内容反色，按钮底色变为透明，常用在有色背景上。
:::include(src="./doc/ghost.vue")
:::

### 菜单按钮

按钮与下拉箭头之间无分割线，两者作为一个整体进行点击，展开折叠操作。
:::include(src="./doc/menu.vue")
:::

### 按钮组

多个按钮组合使用时，推荐使用 1 个主操作 + N 个次操作。
:::include(src="./doc/group.vue")
:::

### 链接能力

以主要按钮为例，可以通过路由和外链两种方式赋予按钮链接能力。
:::include(src="./doc/link.vue")
:::

### 后缀图标

:::include(src="./doc/suffix.vue")
:::


## API

<api-doc name="Button" :doc="require('./api.json')"></api-doc>
<api-doc name="IconButton" :doc="require('./../icon-button/api.json')"></api-doc>

<style lang="scss">
  .demo-btns{
    .mtd-btn + .mtd-btn{
      margin-left: 90px;
    }
  }
  .demo-btn-group {
    display: inline-block;
    .mtd-btn + .mtd-btn{
      margin-left: 18px;
    }
  }
  .demo-button{
    .demo-source{
      padding:0;
      .demo-source-content{
        width: 100%;
        padding: 40px;
      }
    }
    .mtd-btn + .mtd-btn{
      margin-left: 48px;
    }
  }
</style>

```

#### index.ts

```typescript
import Button from './button'
import { withInstall } from '@ss/mtd-adapter'

export default withInstall(Button)

```

#### props.ts

```typescript
import { MTDUIComponentSize } from '@components/types'
import { ExtractPropTypes, PropType } from '@ss/mtd-adapter'

export type ButtonHtmlType = 'button' | 'submit' | 'reset';
export const ButtonType = ['default', 'primary', 'success', 'warning', 'danger']
export const ButtonSize = ['large', 'normal', 'small', 'mini']
export type ButtonShapeType = 'circle' | 'square'

// 用于组件开发或者二次封装
export const buttonProps = () => ({
  href: String,
  to: [String, Object],
  ghost: {
    type: Boolean,
    default: false,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  size: {
    type: String as PropType<MTDUIComponentSize>,
  },
  htmlType: {
    type: String as PropType<ButtonHtmlType>,
    default: 'button',
  },
  loading: {
    type: Boolean,
    default: false,
  },
  icon: {
    type: String,
  },
  type: {
    type: String,
  },
  dashed: {
    type: Boolean,
    default: false,
  },
  circle: {
    type: Boolean,
    default: false,
  },
  // round: {
  //   type: Boolean,
  //   default: false,
  // },
  shape: {
    type: String as PropType<ButtonShapeType>,
  },
  suffix: {
    type: String,
  },
})

// 用于业务的使用
export type ButtonProps = Partial<ExtractPropTypes<ReturnType<typeof buttonProps>>>

export default buttonProps
```

#### select.vue

```vue
<template>
    <mtd-date-picker v-bind="$attrs"/>
</template>
<script>
export default {
  name: 'DemoSelectBase',
};
</script>
```

#### tests/button.spec.js

```javascript
import Button from '@components/button'
import {
  createDemoTest,
  mount,
  triggerClick,
  wait,
  waitImmediate,
} from '@tests/utils'

describe('Button', function () {
  createDemoTest('button')

  it('未配置 loading 时如果 @click 返回 promise 则需要进入 loading', async function () {
    const onClick = jest.fn()

    const wrapper = mount({
      methods: {
        handleClick () {
          onClick()
          return new Promise(resolve => {
            setTimeout(() => {
              resolve()
            }, 300)
          })
        },
      },
      render () {
        return <Button onClick={this.handleClick}>xxx</Button>
      },
    })
    const button = wrapper.vm.$el
    triggerClick(button)
    await waitImmediate()
    const btn = wrapper.findComponent(Button)
    expect(onClick).toBeCalledTimes(1)
    expect(btn.vm.innerLoading).toBe(true)
    await wait(300)
    expect(btn.vm.innerLoading).toBe(false)
  })

  it ('disabled', async function () {
    const onClick = jest.fn();

    const wrapper = mount({
      methods: {
        handleClick () {
          onClick()
          return new Promise(resolve => {
            setTimeout(() => {
              resolve()
            }, 300)
          })
        },
      },
      render () {
        return <Button disabled={true} onClick={this.handleClick}>xxx</Button>
      },
    })
    const button = wrapper.vm.$el
    triggerClick(button)
    await waitImmediate()
    expect(onClick).not.toHaveBeenCalled()
  })
})

```

#### types.ts

```typescript
import Button from './button'

export type Button = InstanceType<typeof Button>
```

#### usage.json

```json
{
  "componentName": "Button",
  "desc": "按钮组件",
  "codeTemplate": "<mtd-button v-bind='configProps'>确认</mtd-button>"
}
```

