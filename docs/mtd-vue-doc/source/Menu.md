## 源码解析

### 文件依赖

- doc-yellow/base.vue
- doc-yellow/collapse.vue
- doc-yellow/horizontal.vue
- doc-yellow/inline.vue
- doc-yellow/theme.vue
- doc-yellow/vertical.vue
- doc/base.vue
- doc/collapse.vue
- doc/horizontal.vue
- doc/inline.vue
- doc/theme.vue
- doc/vertical.vue
- index.md
- index.ts
- menu.tsx
- types.ts
- useMenu.ts

### 源码内容

#### doc-yellow/base.vue

```vue
<template>
  <mtd-menu v-model="activeName" mode="horizontal">
    <mtd-menu-item name="1">
      一级菜单
    </mtd-menu-item>
    <mtd-submenu name="2">
      <span slot="title">一级菜单</span>
      <mtd-submenu name="2-1">
        <template slot="title">二级菜单</template>
        <mtd-menu-item name="2-1-1">
          三级菜单
        </mtd-menu-item>
        <mtd-menu-item name="2-1-2">
          三级菜单
        </mtd-menu-item>
      </mtd-submenu>
    </mtd-submenu>
    <mtd-submenu name="3">
      <template slot="title">一级菜单</template>
      <mtd-menu-item name="3-1">
        二级菜单
      </mtd-menu-item>
      <mtd-menu-item name="3-2">
        二级菜单
      </mtd-menu-item>
    </mtd-submenu>
  </mtd-menu>
</template>
<script>
export default {
  data() {
    return {
      activeName: "",
    }
  },
}
</script>
<style lang="scss">
.demo-menu-wrap {
  width: 400px;
  display: inline-block;
}
</style>

```

#### doc-yellow/collapse.vue

```vue
<template>
  <div class="demo-full-width">
    <mtd-radio-group v-model="isCollapse" style="margin-bottom: 20px;">
      <mtd-radio-button :value="false">展开</mtd-radio-button>
      <mtd-radio-button :value="true">收起</mtd-radio-button>
    </mtd-radio-group>
    <mtd-menu
      class="demo-menu-collapse"
      v-model="activeName"
      mode="inline"
      theme="dark"
      :collapse="isCollapse">
      <mtd-submenu name="1">
        <mtd-icon slot="icon" name="home-o" />
        <template slot="title">一级菜单</template>
        <mtd-menu-item name="1-1">
          二级菜单
          <mtd-icon name="cart-o" slot="icon" />
        </mtd-menu-item>
        <mtd-menu-item name="1-2">
          二级菜单
          <mtd-icon name="calendar-o" slot="icon" />
        </mtd-menu-item>
      </mtd-submenu>
      <mtd-submenu name="2">
        <mtd-icon slot="icon" name="calendar-o" />
        <span slot="title">一级菜单</span>
        <mtd-submenu name="2-1">
          <mtd-icon name="calendar-o" slot="icon" />
          <template slot="title">二级菜单</template>
          <mtd-menu-item name="2-1-1">
            三级菜单
            <mtd-icon name="calculator-o" slot="icon" />
          </mtd-menu-item>
          <mtd-menu-item name="2-1-2">
            三级菜单
            <mtd-icon name="calendar-o" slot="icon" />
          </mtd-menu-item>
        </mtd-submenu>
      </mtd-submenu>
      <mtd-menu-item name="1-3">
        一级菜单
        <mtd-icon name="time-o" slot="icon" />
      </mtd-menu-item>
    </mtd-menu>
  </div>
</template>
<script>
export default {
  data () {
    return {
      activeName: '',
      isCollapse: false,
    }
  },
}
</script>
<style lang="scss">
.demo-menu-collapse{
  width: 200px;
}
</style>

```

#### doc-yellow/horizontal.vue

```vue
<template>
  <div>
    <div class="demo-menu-horizontal-wrap">
    <mtd-menu v-model="activeName" mode="horizontal">
      <mtd-submenu name="1">
        <mtd-icon slot="icon" name="home-o" />
        <template slot="title">一级菜单</template>
        <mtd-menu-item name="1-1">
          二级菜单
          <mtd-icon slot="icon" name="home-o" />
        </mtd-menu-item>
        <mtd-menu-item name="1-2">
          二级菜单
          <mtd-icon name="calendar-o" slot="icon" />
        </mtd-menu-item>
      </mtd-submenu>
      <mtd-submenu name="2">
        <mtd-icon slot="icon" name="calendar-o" />
        <span slot="title">一级菜单</span>
        <mtd-submenu name="2-1">
          <mtd-icon name="calendar-o" slot="icon" />
          <template slot="title">二级菜单</template>
          <mtd-menu-item name="2-1-1" :route="{path: 'modal'}">
            三级菜单
            <mtd-icon name="calculator-o" slot="icon" />
          </mtd-menu-item>
          <mtd-menu-item name="2-1-2" :route="{ path: 'anchor'}">
            三级菜单
            <mtd-icon name="calculator-o" slot="icon" />
          </mtd-menu-item>
        </mtd-submenu>
      </mtd-submenu>
      <mtd-menu-item name="3-1">
        <mtd-icon name="calculator-o" slot="icon" />
        一级菜单
      </mtd-menu-item>
    </mtd-menu>
    <br>
    <br>
    <br>
    <mtd-menu v-model="activeName2" mode="horizontal" theme="dark">
      <mtd-submenu name="1">
        <mtd-icon slot="icon" name="home-o" />
        <template slot="title">一级菜单</template>
        <mtd-menu-item name="1-1">
          二级菜单
          <mtd-icon name="home-o" slot="icon" />
        </mtd-menu-item>
        <mtd-menu-item name="1-2">
          二级菜单
          <mtd-icon name="calendar-o" slot="icon" />
        </mtd-menu-item>
      </mtd-submenu>
      <mtd-submenu name="2">
        <mtd-icon slot="icon" name="calendar-o" />
        <span slot="title">一级菜单</span>
        <mtd-submenu name="2-1">
          <mtd-icon name="calendar-o" slot="icon" />
          <template slot="title">二级菜单</template>
          <mtd-menu-item name="2-1-1" :route="{path: 'modal'}">
            三级菜单
            <mtd-icon name="calculator-o" slot="icon" />
          </mtd-menu-item>
          <mtd-menu-item name="2-1-2" :route="{ path: 'anchor'}">
            三级菜单
            <mtd-icon name="calculator-o" slot="icon" />
          </mtd-menu-item>
        </mtd-submenu>
      </mtd-submenu>
      <mtd-menu-item name="3-1">
        <mtd-icon name="calculator-o" slot="icon" />
        一级菜单
      </mtd-menu-item>
    </mtd-menu>
    </div>
  </div>
</template>
<script>
export default {
  data () {
    return {
      activeName: '',
      activeName2: '',
    }
  },
}
</script>
<style lang="scss">
.demo-menu-horizontal-wrap {
  width: 100%;
  margin-bottom: 100px;

}
</style>

```

#### doc-yellow/inline.vue

```vue
<template>
  <div>
    <div style="width: 200px;">
      <mtd-menu v-model="activeName" mode="inline">
        <mtd-submenu name="1" icon="home-o">
          <img slot="activeIcon" src="../../assets/img/home-y.png">
          <template slot="title">一级菜单</template>
          <mtd-menu-item name="1-1">
            二级菜单
            <mtd-icon name="cart-o" slot="icon" />
          </mtd-menu-item>
          <mtd-menu-item name="1-2">
            二级菜单
            <template #icon>
              <img src="../../assets/img/calendar-y.png">
            </template>
          </mtd-menu-item>
        </mtd-submenu>
        <mtd-submenu name="2" icon="calendar-o">
          <img slot="activeIcon" src="../../assets/img/calendar-y.png">
          <span slot="title">一级菜单</span>
          <mtd-submenu name="2-1">
            <template #icon>
              <img src="../../assets/img/calendar-y.png">
            </template>
            <template slot="title">二级菜单</template>
            <mtd-menu-item name="2-1-1">
              三级菜单
              <img slot="icon" src="../../assets/img/cart-y.png">
            </mtd-menu-item>
            <mtd-menu-item name="2-1-2">
              三级菜单
              <template #icon>
              <img src="../../assets/img/calendar-y.png">
            </template>
            </mtd-menu-item>
          </mtd-submenu>
        </mtd-submenu>
        <mtd-menu-item name="1-3" icon="time-o">
          一级菜单
          <img slot="activeIcon" src="../../assets/img/time-y.png">
        </mtd-menu-item>
      </mtd-menu>
    </div>
  </div>
</template>
<script>
export default {
  data () {
    return {
      activeName: '',
    }
  },
}
</script>
<style lang="scss">
.demo-menu-wrap {
  width: 400px;
  display: inline-block;
}
</style>

```

#### doc-yellow/theme.vue

```vue
<template>
  <div>
    <mtd-menu class="demo-menu-theme" v-model="activeName" theme="dark">
      <mtd-submenu name="1">
        <template slot="title">一级菜单</template>
        <mtd-icon slot="icon" name="home-o" />
        <mtd-menu-item name="1-1">
          二级菜单
          <mtd-icon name="question-circle-o" slot="icon" />
        </mtd-menu-item>
        <mtd-menu-item name="1-2">
          二级菜单
          <mtd-icon name="question-circle-o" slot="icon" />
        </mtd-menu-item>
        <mtd-icon slot="icon" name="code" />
      </mtd-submenu>
      <mtd-submenu name="2">
        <span slot="title">一级菜单</span>
        <mtd-submenu name="2-1">
          <template slot="title">二级菜单</template>
          <mtd-menu-item name="2-1-1">
            三级菜单
            <mtd-icon name="question-circle-o" slot="icon" />
          </mtd-menu-item>
          <mtd-menu-item name="2-1-2">
            三级菜单
            <mtd-icon name="question-circle-o" slot="icon" />
          </mtd-menu-item>
          <mtd-icon slot="icon" name="code" />
        </mtd-submenu>
        <mtd-icon name="cart-o" slot="icon" />
      </mtd-submenu>
      <mtd-menu-item name="1-3">
        一级菜单
        <mtd-icon name="calendar-o" slot="icon" />
      </mtd-menu-item>
    </mtd-menu>
  </div>
</template>
<script>
export default {
  data () {
    return {
      activeName: '',
      theme: 'light',
    }
  },
}
</script>
<style lang="scss">
.demo-menu-theme {
  width: 200px;
}
</style>

```

#### doc-yellow/vertical.vue

```vue
<template>
  <div class="demo-menu-vertical-wrap">
    <mtd-menu v-model="activeName" mode="vertical">
      <mtd-submenu name="1" icon="home-o">
        <img slot="activeIcon" src="../../assets/img/home-y.png">
        <template slot="title">一级菜单</template>
        <mtd-menu-item name="1-1">
          二级菜单
        </mtd-menu-item>
        <mtd-menu-item name="1-2">
          二级菜单
        </mtd-menu-item>
      </mtd-submenu>
      <mtd-submenu name="2" icon="cart-o">
        <img slot="activeIcon" src="../../assets/img/cart-y.png">
        <span slot="title">一级菜单</span>
        <mtd-submenu name="2-1">
          <template slot="title">二级菜单</template>
          <mtd-menu-item name="2-1-1">
            三级菜单
          </mtd-menu-item>
          <mtd-menu-item name="2-1-2">
            三级菜单
          </mtd-menu-item>
        </mtd-submenu>
      </mtd-submenu>
      <mtd-menu-item name="1-3" icon="time-o">
        一级菜单
        <img slot="activeIcon" src="../../assets/img/time-y.png">
      </mtd-menu-item>
    </mtd-menu>
  </div>
</template>
<script>
export default {
  data () {
    return {
      activeName: '',
    }
  },
}
</script>
<style lang="scss">
.demo-menu-vertical-wrap {
  width: 240px;
}
</style>

```

#### doc/base.vue

```vue
<template>
  <mtd-menu v-model="activeName" mode="horizontal" @select="handleSelect">
    <mtd-menu-item name="1">
      一级菜单
    </mtd-menu-item>
    <mtd-submenu name="2">
      <template #title>一级菜单</template>
      <mtd-submenu name="2-1">
        <template #title>二级菜单</template>
        <mtd-menu-item name="2-1-1">
          三级菜单
        </mtd-menu-item>
        <mtd-menu-item name="2-1-2">
          三级菜单
        </mtd-menu-item>
      </mtd-submenu>
    </mtd-submenu>
    <mtd-submenu name="3">
      <template #title>一级菜单</template>
      <mtd-menu-item name="3-1">
        二级菜单
      </mtd-menu-item>
      <mtd-menu-item name="3-2">
        二级菜单
      </mtd-menu-item>
    </mtd-submenu>
  </mtd-menu>
</template>
<script>
export default {
  data() {
    return {
      activeName: "",
    }
  },
  methods: {
    handleSelect(v) {
      console.log(v)
    }
  }
}
</script>
<style lang="scss">
.demo-menu-wrap {
  width: 400px;
  display: inline-block;
}
</style>

```

#### doc/collapse.vue

```vue
<template>
  <div class="demo-full-width">
    <mtd-radio-group v-model="isCollapse" style="margin-bottom: 20px;">
      <mtd-radio-button :value="false">展开</mtd-radio-button>
      <mtd-radio-button :value="true">收起</mtd-radio-button>
    </mtd-radio-group>
    <mtd-menu
      class="demo-menu-collapse"
      v-model="activeName"
      mode="inline"
      theme="dark"
      :collapse="isCollapse">
      <mtd-submenu name="1" icon="home-o">
        <template #title>一级菜单</template>
        <mtd-menu-item name="1-1">
          二级菜单
        </mtd-menu-item>
        <mtd-menu-item name="1-2">
          二级菜单
        </mtd-menu-item>
      </mtd-submenu>
      <mtd-submenu name="2" icon="calendar-o">
        <template #title>一级菜单</template>
        <mtd-submenu name="2-1">
          <template #title>二级菜单</template>
          <mtd-menu-item name="2-1-1">
            三级菜单
          </mtd-menu-item>
          <mtd-menu-item name="2-1-2">
            三级菜单
          </mtd-menu-item>
        </mtd-submenu>
      </mtd-submenu>
      <mtd-menu-item name="1-3" icon="time-o">
        一级菜单
      </mtd-menu-item>
    </mtd-menu>
  </div>
</template>
<script>
export default {
  data () {
    return {
      activeName: '',
      isCollapse: false,
    }
  },
}
</script>
<style lang="scss">
.demo-menu-collapse{
  width: 200px;
}
</style>

```

#### doc/horizontal.vue

```vue
<template>
  <div>
    <div class="demo-menu-horizontal-wrap">
    <mtd-menu v-model="activeName" mode="horizontal">
      <mtd-submenu name="1">
        <template #title>一级菜单</template>
        <mtd-menu-item name="1-1">
          二级菜单
        </mtd-menu-item>
        <mtd-menu-item name="1-2">
          二级菜单
        </mtd-menu-item>
      </mtd-submenu>
      <mtd-submenu name="2">
        <span slot="title">一级菜单</span>
        <mtd-submenu name="2-1">
          <template #title>二级菜单</template>
          <mtd-menu-item name="2-1-1" :route="{path: 'modal'}">
            三级菜单
            <mtd-icon name="calculator-o" slot="icon" />
          </mtd-menu-item>
          <mtd-menu-item name="2-1-2" :route="{ path: 'anchor'}">
            三级菜单
            <mtd-icon name="calculator-o" slot="icon" />
          </mtd-menu-item>
        </mtd-submenu>
      </mtd-submenu>
      <mtd-menu-item name="3-1">
        <mtd-icon name="calculator-o" slot="icon" />
        一级菜单
      </mtd-menu-item>
    </mtd-menu>
    <br>
    <br>
    <br>
    <mtd-menu v-model="activeName2" mode="horizontal" theme="dark">
      <mtd-submenu name="1">
        <mtd-icon slot="icon" name="home-o" />
        <template #title>一级菜单</template>
        <mtd-menu-item name="1-1">
          二级菜单
          <mtd-icon name="home-o" slot="icon" />
        </mtd-menu-item>
        <mtd-menu-item name="1-2">
          二级菜单
        </mtd-menu-item>
      </mtd-submenu>
      <mtd-submenu name="2">
        <span slot="title">一级菜单</span>
        <mtd-submenu name="2-1">
          <mtd-icon name="calendar-o" slot="icon" />
          <template #title>二级菜单</template>
          <mtd-menu-item name="2-1-1" :route="{path: 'modal'}">
            三级菜单
            <mtd-icon name="calculator-o" slot="icon" />
          </mtd-menu-item>
          <mtd-menu-item name="2-1-2" :route="{ path: 'anchor'}">
            三级菜单
            <mtd-icon name="calculator-o" slot="icon" />
          </mtd-menu-item>
        </mtd-submenu>
      </mtd-submenu>
      <mtd-menu-item name="3-1">
        <mtd-icon name="calculator-o" slot="icon" />
        一级菜单
      </mtd-menu-item>
    </mtd-menu>
    </div>
  </div>
</template>
<script>
export default {
  data () {
    return {
      activeName: '',
      activeName2: '',
    }
  },
}
</script>
<style lang="scss">
.demo-menu-horizontal-wrap {
  width: 100%;
  margin-bottom: 100px;

}
</style>

```

#### doc/inline.vue

```vue
<template>
  <div>
    <div style="width: 200px;">
      <mtd-menu v-model="activeName" mode="inline">
        <mtd-submenu name="1" icon="home-o">
          <template #title>一级菜单</template>
          <mtd-menu-item name="1-1">
            二级菜单
          </mtd-menu-item>
          <mtd-menu-item name="1-2">
            二级菜单
          </mtd-menu-item>
        </mtd-submenu>
        <mtd-submenu name="2" icon="calendar-o">
          <template #title>一级菜单</template>
          <mtd-submenu name="2-1">
            <template #title>二级菜单</template>
            <mtd-menu-item name="2-1-1">
              三级菜单
            </mtd-menu-item>
            <mtd-menu-item name="2-1-2">
              三级菜单
            </mtd-menu-item>
          </mtd-submenu>
        </mtd-submenu>
        <mtd-menu-item name="1-3" icon="time-o">
          一级菜单
        </mtd-menu-item>
      </mtd-menu>
    </div>
  </div>
</template>
<script>
export default {
  data () {
    return {
      activeName: '',
    }
  },
}
</script>
<style lang="scss">
.demo-menu-wrap {
  width: 400px;
  display: inline-block;
}
</style>

```

#### doc/theme.vue

```vue
<template>
  <div>
    <mtd-menu class="demo-menu-theme" v-model="activeName" theme="dark">
      <mtd-submenu name="1" icon="home-o">
        <template #title>一级菜单</template>
        <mtd-menu-item name="1-1">
          二级菜单
        </mtd-menu-item>
        <mtd-menu-item name="1-2">
          二级菜单
        </mtd-menu-item>
      </mtd-submenu>
      <mtd-submenu name="2" icon="calendar-o">
        <template #title>一级菜单</template>
        <mtd-submenu name="2-1">
          <template #title>二级菜单</template>
          <mtd-menu-item name="2-1-1">
            三级菜单
          </mtd-menu-item>
          <mtd-menu-item name="2-1-2">
            三级菜单
          </mtd-menu-item>
        </mtd-submenu>
      </mtd-submenu>
      <mtd-menu-item name="1-3" icon="time-o">
        一级菜单
      </mtd-menu-item>
    </mtd-menu>
  </div>
</template>
<script>
export default {
  data () {
    return {
      activeName: '',
      theme: 'light',
    }
  },
}
</script>
<style lang="scss">
.demo-menu-theme {
  width: 200px;
}
</style>

```

#### doc/vertical.vue

```vue
<template>
  <div class="demo-menu-vertical-wrap">
    <mtd-menu v-model="activeName" mode="vertical">
      <mtd-submenu name="1" icon="home-o">
        <template #title>一级菜单</template>
        <mtd-menu-item name="1-1">
          二级菜单
        </mtd-menu-item>
        <mtd-menu-item name="1-2">
          二级菜单
        </mtd-menu-item>
      </mtd-submenu>
      <mtd-submenu name="2" icon="cart-o">
        <template #title>一级菜单</template>
        <mtd-submenu name="2-1">
          <template #title>二级菜单</template>
          <mtd-menu-item name="2-1-1">
            三级菜单
          </mtd-menu-item>
          <mtd-menu-item name="2-1-2">
            三级菜单
          </mtd-menu-item>
        </mtd-submenu>
      </mtd-submenu>
      <mtd-menu-item name="1-3" icon="time-o">
        一级菜单
      </mtd-menu-item>
    </mtd-menu>
  </div>
</template>
<script>
export default {
  data () {
    return {
      activeName: '',
    }
  },
}
</script>
<style lang="scss">
.demo-menu-vertical-wrap {
  width: 240px;
}
</style>

```

#### index.md

```markdown
<style>
.demo-menu.demo-block .source{
  text-align: left;
}

.demo-menu .mtd-submenu-icon img,
.demo-menu .mtd-menu-item-icon img{
  width: 16px;
  height: 16px;
}
</style>

# 导航菜单 / Menu

## 基础样式

:::include(src="./doc/base.vue")
:::

## 类型与用法

### 内嵌菜单

:::include(src="./doc/inline.vue")
:::

### 缩起内嵌菜单

:::include(src="./doc/collapse.vue")
:::

### 垂直菜单

子菜单是弹出的形式。
:::include(src="./doc/vertical.vue")
:::

### 主题

内建了两套主题 light | dark，默认 light，以下展示的是 dark 主题
:::include(src="./doc/theme.vue")
:::

## API

<api-doc name="Menu" :doc="require('./api.json')"></api-doc>
<api-doc name="SubMenu" :doc="require('./../submenu/api.json')"></api-doc>
<api-doc name="MenuItem" :doc="require('./../menu-item/api.json')"></api-doc>

```

#### index.ts

```typescript
import Menu from './menu'
import { withInstall } from '@ss/mtd-adapter'
export default withInstall(Menu)

```

#### menu.tsx

```tsx

import {
  defineComponent,
  provide,
  reactive,
  computed,
  watch,
  ref,
  markRaw,
  toRefs,
  vueInstance,
  getSlotsInRender,
  PropType,
} from '@ss/mtd-adapter'
import mitt from '@utils/mitt'

import useConfig from '@components/hooks/config'

import { Submenu } from '@components/submenu/types'
import { MenuItem } from '@components/menu-item/types'
import { isFunction } from '@utils/type'
import { CSSPropertyType } from '@components/types'


const getParentKeys = (it: Submenu) => {
  const keys: any[] = []
  const flat = (item: any) => {
    const { submenu } = item
    keys.push(item.name)
    if (submenu) {
      flat(submenu)
    }
  }
  flat(it)
  return keys
}
export default defineComponent({
  name: 'MtdMenu',
  inheritAttrs: true,
  model: {
    prop: 'modelValue',
    event: 'update:modelValue',
  },
  props: {
    mode: {
      type: String,
      default: 'inline',
      validator: (value: string): boolean =>
        ['vertical', 'horizontal', 'inline'].indexOf(value) > -1,
    },
    theme: {
      type: String,
      default: 'light',
    },
    modelValue: [String, Number],
    expandedNames: {
      type: Array as PropType<string[]>,
      default: () => [],
    },
    defaultExpandedNames: {
      type: Array as PropType<string[]>,
      default: () => [],
    },
    accordion: Boolean,
    collapse: Boolean,
    router: Boolean,
    indent: {
      type: Number,
      default: 24,
    },
    baseIndent: {
      type: Number,
      default: 24,
    },
    lineFeedTitle: Boolean, //icon 和 title-text 是否换行
    expandType: {
      type: String,
      default: 'partial', // 全展开、半展开、不展开 
    },
    itemStyle: {
      type: [Object, Function] as PropType<CSSPropertyType | ((item: MenuItem) => CSSPropertyType)>,
      default: () => ({}),
    },
  },
  emits: [
    'select',
    'open',
    'close',
    'update:modelValue',
    'update:expandedNames',
    'expand-change',
  ],
  setup(props, { emit }) {
    // self 代表组件自身的一些 通用 配置
    const config = useConfig()
    const self = reactive({
      prefix: config.getPrefixCls('menu'),
      ins: vueInstance(),
      emitter: markRaw(mitt()),
    })

    provide('menu', self.ins)

    // 💣 重点留意一下这个isExpandedControl判断————如何判断是用户传进来的还是自带的
    // 方案1： 允许 expandedNames 是 undefined，通过是不是undefined或者数组判断
    // 方案2： isExpandedControl: Reflect.has(vnode.props || {}, 'expandedNames'),
    const state = reactive({
      innerExpandedNames: props.defaultExpandedNames,
      isExpandedControl: Array.isArray('expandedNames'),
    })
    const level = ref(-1) // root
    const lastExpended = ref<any[]>([])

    const isCollapse = computed(() => (props.collapse && props.mode == 'inline') || props.expandType === 'none')
    const expanded = computed(() =>
      state.isExpandedControl ? props.expandedNames : state.innerExpandedNames,
    )

    const m_mode = computed(() => props.expandType === 'none' ? 'vertical' : props.mode)

    watch(isCollapse, (n) => {
      if (n) {
        lastExpended.value = state.innerExpandedNames
        state.innerExpandedNames = []
      } else {
        state.innerExpandedNames = lastExpended.value
      }
    })

    watch(
      () => props.defaultExpandedNames,
      () => {
        state.innerExpandedNames = props.defaultExpandedNames
      },
    )

    const isActive = (menuItem: MenuItem) => props.modelValue === menuItem.name
    const isExpanded = (submenu: Submenu) => expanded.value.indexOf(submenu.name as string) > -1
    const getItemStyled = (item: MenuItem): any => {
      const customItemStyle = isFunction(props.itemStyle) ? props.itemStyle(item) : props.itemStyle
      if (
        (m_mode.value === 'inline' && !isCollapse.value) ||
        ((m_mode.value === 'vertical' || isCollapse.value) && item.level === 0)
      ) {
        return {
          paddingLeft: `${item.paddingLeft}px`,
          ...customItemStyle,
        }
      }

      return { ...customItemStyle }
    }

    const handleMenuItemClick = (item: MenuItem) => {
      emit('update:modelValue', item.name)
      emit('select', item)
    }
    self.emitter.on('menuItemClick', handleMenuItemClick)

    const toggleExpanded = (item: Submenu) => {
      let nextExpanded: string[] = []
      const { name: key } = item
      if (!item.isExpanded) {
        if (props.accordion) {
          const keys = getParentKeys(item) // 🤡 需要submenu
          nextExpanded = keys
        } else {
          nextExpanded = [...expanded.value]
          nextExpanded.push(key as string)
        }
        emit('open', key)
      } else {
        const index = expanded.value.indexOf(key as string)
        if (index > -1) {
          nextExpanded = [...expanded.value]
          nextExpanded.splice(index, 1)
        }
        emit('close', key)
      }
      emit('update:expandedNames', nextExpanded)
      emit('expand-change', nextExpanded, {
        name: key,
        expanded: !item.isExpanded,
      })
      state.innerExpandedNames = nextExpanded
    }

    return {
      ...toRefs(self),
      ...toRefs(state),
      isCollapse,
      isActive,
      isExpanded,
      getItemStyled,
      toggleExpanded,
      level,
      expanded,
      m_mode,
    }
  },
  render() {
    const { prefix, theme, m_mode, isCollapse } = this

    return <ul
      role={'menu'}
      class={{
        [prefix]: true,
        [`${prefix}-collapse`]: isCollapse,
        [`${prefix}-${theme}`]: theme,
        [`${prefix}-${m_mode}`]: m_mode,
      }}
    >
      {getSlotsInRender(this)}
    </ul>
  },
})

```

#### types.ts

```typescript
import Menu from './index'

export type Menu = InstanceType<typeof Menu>
```

#### useMenu.ts

```typescript
import { inject, computed, ComponentPublicInstance } from '@ss/mtd-adapter'

export type MenuIns = any

export type submenuIns = any

export const useMenu = (ins: ComponentPublicInstance<any>) => {
  const menu = inject<MenuIns>('menu')!
  const submenu = inject<submenuIns | null>('mtd_submenu', null)

  const parent = computed(() => submenu || menu)

  const level = computed(() => parent.value.level + 1)

  const style = computed(() => menu.getItemStyled(ins))
  const paddingLeft = computed(() => {
    //  expandType类型为none的padding-left为4 写死的，受制于之前的设计暂不修改
    if (level.value === 0 && menu.expandType === 'none') {
      return 4
    }
    return (menu.baseIndent + level.value * menu.indent) - 12
  })
  return {
    menu,
    submenu,
    level,
    style,
    paddingLeft,
  }
}

```

