## 源码解析

### 文件依赖

- demo.md
- doc/auto-width.vue
- doc/base.vue
- doc/clearable.vue
- doc/combination.vue
- doc/controlled-value.vue
- doc/demo/focus.vue
- doc/focus.vue
- doc/genre.vue
- doc/helper.vue
- doc/icon.vue
- doc/input-group.vue
- doc/input-search.vue
- doc/model-value-null.vue
- doc/overflow.vue
- doc/password.vue
- doc/prefix-and-suffix.vue
- doc/show-count.vue
- doc/size.vue
- doc/slot-demo.vue
- doc/status.vue
- doc/text-align.vue
- group.tsx
- index.md
- index.ts
- input.tsx
- props.ts
- search.tsx
- types.ts
- usage.json
- usePassword.ts

### 源码内容

#### demo.md

```markdown
# Input 补充 Demo
## Input 属性
### modelValue 属性
`modelValue` 属性用于双向绑定输入框的值。
:::include(src="./doc/controlled-value.vue"):::

`modelValue` 属性为null。
:::include(src="./doc/model-value-null.vue"):::

### slot-demo
v-show里有插槽的情况
:::include(src="./doc/slot-demo.vue"):::

### focus: preventScroll
`focus` 方法用于聚焦输入框。
:::include(src="./doc/demo/focus.vue"):::


```

#### doc/auto-width.vue

```vue
<template>
  <mtd-input auto-width style="max-width: 300px;" clearable v-model="value" placeholder="宽度自适应" />
</template>

<script>
export default {
  data() {
    return {
      value: "宽度自适应",
    }
  },
}
</script>

```

#### doc/base.vue

```vue
<template>
  <mtd-input v-model="value" placeholder="具体文案" @change="handleChange" @enter="handleEnter" />
</template>

<script>
export default {
  data() {
    return {
      value: "",
    }
  },
  methods: {
    handleEnter() {
      console.log('ENTER');
    },
    handleChange(value) {
      console.log('handleChange', value)
    },
  },
}
</script>

```

#### doc/clearable.vue

```vue
<template>
  <mtd-input v-model="value" clearable @clear="handleClear"/>
</template>

<script>
export default {
  data() {
    return {
      value: "已输入文本",
    };
  },
  methods: {
    handleClear(value) {
      console.log('clear----', value)
    }
  },
};
</script>

```

#### doc/combination.vue

```vue
<template>
  <div style="demo-full-width">
    <div style="display:inline-block; width: 350px;">
      <mtd-input v-model="value16">
        <template #prepend>http://</template>
        <template #append>.com</template>
      </mtd-input>
    </div>
    <br><br><br>
    <div style="display:inline-block; width: 350px;">
      <mtd-input v-model="value17">
        <template #prepend>http://</template>
        <template #append>
          <mtd-select v-model="appendValue" style="width:77px;">
            <mtd-option value=".com" label=".com" />
            <mtd-option value=".cn" label=".cn" />
          </mtd-select>
        </template>
      </mtd-input>
    </div>
  </div>
</template>

<script>
export default {
  data () {
    return {
      value16: '',
      value17: '',
      appendValue: '.com',
    }
  },
}
</script>

```

#### doc/controlled-value.vue

```vue
<template>
  <div class="demo-full-width">
    <mtd-row class="demo-op">
      <div>
        <mtd-checkbox v-model="isControlled">受控组件</mtd-checkbox>
      </div>
      <div v-if="isControlled">
        <mtd-button type="primary" size="small" @click="handlePrint">打印value的值</mtd-button>
      </div>
      <div v-if="isControlled">
        <mtd-button type="primary" size="small" @click="handleSetValue">设置modelValue=xxx</mtd-button>
      </div>
    </mtd-row>
    <mtd-row>
      <mtd-input key="343434" id="1234" v-if="isControlled" v-model="value" placeholder="具体文案2" @change="handleChange"/>
      <mtd-input key="2656443" id="4321" v-else placeholder="具体文案1" @change="handleChange" @input="handleInput" />
    </mtd-row>

  </div>
</template>
<style lang="scss" scoped>
.demo-full-width {
  text-align: left;
  .demo-op {
    margin-bottom: 20px;
    & > div {
      display: inline-block;
      margin-right: 10px;
    }
  }
}
</style>
<script>
export default {
  data() {
    return {
      isControlled: true,
      value: "",
    }
  },
  methods: {
    handleSetValue(){
      this.value = 'xxx'+new Date().getTime()
      console.log('设置value->', this.value)
    },
    handlePrint() {
      console.log('value->', this.value)
    },
    handleInput(val) {
      // console.log('handleInput->', val)
    },
    handleChange(value) {
      console.log('handleChange->', value)
    },
  },
}
</script>

```

#### doc/demo/focus.vue

```vue
<template>
    <div class="demo-flex">
      <div class="demo-btn-focus-container">
      <mtd-button @click="preventScroll(true)">
        preventScroll: true
      </mtd-button>
      <mtd-button @click="preventScroll(false)">
        preventScroll: false
      </mtd-button>
      <mtd-input 
        ref="inputRef" 
        v-model="value" 
        placeholder="请输入" 
        :style="inputStyle" 
      />
      </div>
    </div>
</template>
  
<script>
  export default {
    data() {
      return {
        value: '这是一段测试文本',
        inputStyle: {
          width: '400px',
          marginTop: '1020px'
        }
      };
    },
    methods: {
      focusInput(cursor) {
        this.$refs.inputRef.focus({ cursor });
      },
      preventScroll(preventScroll) {
        this.$refs.inputRef.focus({ focusOptions: { preventScroll } });
      }
    }
  };
</script>

<style scoped>
.demo-btn-focus-container {
  display: flex;
  width: 400px;
  flex-wrap: wrap;
  justify-content: space-between;
}
</style>
```

#### doc/focus.vue

```vue
<template>
    <div class="demo-flex">
      <div class="demo-btn-focus-container">
      <mtd-button 
        v-for="(btn, index) in buttons" 
        :key="index" 
        @click="focusInput(btn.action)"
      >
        {{ btn.text }}
      </mtd-button>
      <mtd-input 
        ref="inputRef" 
        v-model="value" 
        placeholder="请输入" 
        :style="inputStyle" 
      />
      </div>
    </div>
</template>
  
<script>
  export default {
    data() {
      return {
        value: '这是一段测试文本',
        buttons: [
            { text: 'Focus at first', action: 'start' },
            { text: 'Focus at last', action: 'end' },
            { text: 'Focus to select all', action: 'all' }
        ],
        inputStyle: {
          width: '400px',
          margin: '20px 0'
        }
      };
    },
    methods: {
      focusInput(cursor) {
        this.$refs.inputRef.focus({ cursor });
      }
    }
  };
</script>

<style scoped>
.demo-btn-focus-container {
  display: flex;
  width: 400px;
  flex-wrap: wrap;
  justify-content: space-between;
}
</style>
```

#### doc/genre.vue

```vue
<template>
  <div class="demo-flex">
    <div>
      <p>线框</p>
      <mtd-input v-model="value1" placeholder="线框型输入框" @enter="handleEnter" @change="handleChange" @input="handleInput"/>
    </div>
    <div>
      <p>面型</p>
      <mtd-input v-model="value2" genre="area" placeholder="面型输入框" />
    </div>
    <div>
      <p>单线</p>
      <mtd-input v-model="value3" genre="line" placeholder="单线型输入框" />
    </div>
  </div>
</template>

<script>
export default {
  data () {
    return {
      value1: '',
      value2: '',
      value3: '',
    };
  },
  methods: {
    handleEnter() {
      console.log('ENTER');
    },
    handleChange(value) {
      console.log('handleChange', value)
    },
    handleInput(value) {
      console.log('Input: ', value);
    }
  },
};
</script>

```

#### doc/helper.vue

```vue
<template>
  <mtd-form v-model="form" inline class="demo-flex" style="text-align:left;align-items: flex-start">
    <mtd-form-item helper="这是一段底部注释文字">
      <mtd-input v-model="form.value" placeholder="请输入"/>
    </mtd-form-item>
    <mtd-form-item helper="这是一段右侧注释文字" helper-placement="right">
      <mtd-input type="email" placeholder="请输入" v-model="form.value1" />
    </mtd-form-item>
    <mtd-form-item helper="">
      <mtd-input type="email" placeholder="提示文案" v-model="form.value2" >
        <template #suffix>
          <mtd-tooltip content="这是一段悬停出现的注释文字" placement="top">
            <mtd-icon name="info-circle-o"/>
          </mtd-tooltip>
        </template>
      </mtd-input>
    </mtd-form-item>
  </mtd-form>
</template>

<script>

export default {
  data () {
    return {
      form: {
        value: '',
        value1: '',
        value2: '',
      }
    };
  },
};
</script>

```

#### doc/icon.vue

```vue
<template>
  <div>
    <mtd-input prefix-icon="avatar-o" v-model="value" @click-prefix="handleClickPrefix"
      placeholder="" style="margin-right: 100px;" />
    <mtd-input suffix-icon="visibility-off-o" v-model="value1"  @click-suffix="handleClickSuffix"/>
  </div>
</template>

<script>
export default {
  data () {
    return {
      value: '',
      value1: '',
    };
  },
  methods: {
    handleClickSuffix() {
      console.log('click suffix')
    },
    handleClickPrefix() {
      console.log('click prefix')
    }
  }
};
</script>

```

#### doc/input-group.vue

```vue
<template>
  <div class="demo-full-width" style="text-align:left">
    <mtd-input-group>
      <mtd-input style="width: 20%" v-model="value3" />
      <mtd-input style="width: 30%" v-model="value4" />
    </mtd-input-group>
    <br>
    <mtd-input-group size="large">
      <mtd-select v-model="selectValue1">
        <mtd-option value="Zhejiang" label="Zhejiang">Zhejiang</mtd-option>
        <mtd-option value="Jiangsu" label="Jiangsu">Jiangsu</mtd-option>
      </mtd-select>
      <mtd-input style="width: 50%" v-model="value5" />
    </mtd-input-group>
    <br>
    <mtd-input-group>
      <mtd-input style="width: 50%" v-model="value6" />
      <mtd-date-picker v-model="value7" style="width: 50%" />
    </mtd-input-group>
    <br>
    <mtd-input-group>
      <mtd-select v-model="selectValue3">
        <mtd-option value="Option1-1" label="Option1-1">Option1-1</mtd-option>
        <mtd-option value="Option1-2" label="Option1-2">Option1-2</mtd-option>
      </mtd-select>
      <mtd-select v-model="selectValue4">
        <mtd-option value="Option2-1" label="Option2-1">Option2-1</mtd-option>
        <mtd-option value="Option2-2" label="Option2-2">Option2-2</mtd-option>
      </mtd-select>
    </mtd-input-group>
    <br>
    <mtd-input-group>
      <mtd-select v-model="selectValue5">
        <mtd-option value="1" label="Between">Between</mtd-option>
        <mtd-option value="2" label="Except">Except</mtd-option>
      </mtd-select>
      <mtd-input style=" width: 100px; text-align: center" placeholder="Minimum" />
      <mtd-input
style=" width: 30px; border-left: 0;
      pointer-events: none;" placeholder="~" disabled />
      <mtd-input style="width: 100px; text-align: center;" placeholder="Maximum" />
    </mtd-input-group>
    <br>
    <mtd-input-group>
      <mtd-select v-model="selectValue6">
        <mtd-option value="Sign Up" label="Sign Up">Sign Up</mtd-option>
        <mtd-option value="Sign In" label="Sign In">Sign In</mtd-option>
      </mtd-select>
      <mtd-input placeholder="Email" />
    </mtd-input-group>
    <br>
    <mtd-input-group>
      <mtd-select style="width: 30%" v-model="selectValue7">
        <mtd-option value="Home" label="Home">Home</mtd-option>
        <mtd-option value="Company" label="Company">Company</mtd-option>
      </mtd-select>
      <mtd-cascader :data="address" v-model="value8" style="width: 70%" placeholder="Select Address" />
    </mtd-input-group>
    <br>
    <mtd-input-group>
      <mtd-input style="width: 40%" v-model="value9" placeholder="输入用户名">
        <template #prepend>姓名</template>
      </mtd-input>
      <mtd-input style="width: 60%" v-model="value10" placeholder="输入邮箱" />
    </mtd-input-group>
  </div>
</template>
<script>
export default {
  data() {
    return {
      value1: '',
      value2: '',
      value3: '',
      value4: '',
      value5: '',
      value6: '',
      value7: '',
      value8: [],
      value9: '',
      value10: '',
      selectValue1: '',
      selectValue2: '',
      selectValue3: '',
      selectValue4: '',
      selectValue5: '',
      selectValue6: '',
      selectValue7: '',
      address: [{
        value: 'beijing',
        label: '北京',
        children: [{
          value: 'beijing',
          label: '北京',
        }],
      }, {
        value: 'hunan',
        label: '湖南',
        children: [{
          value: 'changsha',
          label: '长沙',
        }, {
          value: 'zhangjiajie',
          label: '张家界',
        }],
      }],
    }
  },
}
</script>
<style lang="scss" scoped>
  .demo-block .source .mtd-input-group {
    text-align: initial;
  }
</style>

```

#### doc/input-search.vue

```vue
<template>
  <div class="demo-full-width demo-flex">
    <div>
      <mtd-input-search
        placeholder="输入关键字"
        v-model="val"
        :loading="loading"
        @search="handleSearch"/>
    </div>
    <div>
      <mtd-input-search
        placeholder="输入关键字"
        v-model="val1"
        :loading="loading1"
        :enter-button="true"
        @search="handleSearch1"/>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      loading: false,
      val: '',
      enterButton: true,

      loading1: false,
      val1: ''
    }
  },
  methods: {
    handleSearch(val) {
      this.loading = true
      setTimeout(() => {
        console.log(val, '搜索完成')
        this.loading = false
      }, 1000)
    },
    handleSearch1(val) {
      this.loading1 = true
      setTimeout(() => {
        console.log(val, '搜索完成')
        this.loading1 = false
      }, 1000)
    }
  }
}
</script>

```

#### doc/model-value-null.vue

```vue
<template>
  <div>
    <p>不符合预期：(输入任意字符之后不应该回显null)</p>
    <mtd-input :modelValue="value1" clearable />
    <p>符合预期：</p>
    <mtd-input :value="value2" clearable />
  </div>
</template>

<script>
export default {
  data () {
    return {
      value1: null,
      value2: null,
    };
  },
};
</script>

```

#### doc/overflow.vue

```vue
<template>
  <mtd-tooltip :content="value" ref="tooltipRef" placement="top-start" :disabled="value.length < 10">
    <mtd-input @change="adjustPosition" type="text" v-model="value" placeholder="在这里输入>10个文字"/>
  </mtd-tooltip>
</template>

<script>
export default {
  data () {
    return {
      value: '这里是一段超超超长文本内容',
    };
  },
  methods: {
    adjustPosition () {
      this.$refs.tooltipRef.updatePopper();
    }
  }
};
</script>

```

#### doc/password.vue

```vue
<template>
  <mtd-input v-model="password" type="password" />
</template>

<script>
export default {
  data() {
    return {
      password: "12345678",
    };
  },
};
</script>

```

#### doc/prefix-and-suffix.vue

```vue
<template>
  <div>
    <mtd-row class="row">
      <mtd-input v-model="value"
                 placeholder="km.sankuai.com" style="margin-right: 100px;">
        <template #prepend>http://</template>
      </mtd-input>
    </mtd-row>
    <mtd-row class="row">
      <mtd-input v-model="value"
                 placeholder="km.sankuai.com" style="margin-right: 100px;">
        <template #append>.com</template>
      </mtd-input>
    </mtd-row>
    <mtd-row class="row">
      <mtd-input v-model="value1" placeholder="km.sankuai.com">
        <template #prepend>http://</template>
        <template #append>.com</template>
      </mtd-input>

    </mtd-row>
  </div>

</template>

<script>
export default {
  data () {
    return {
      value: '',
      value1: '',
    };
  },
};
</script>
<style scoped>
.row {
  margin-bottom: 36px;
}
</style>


```

#### doc/show-count.vue

```vue
<template>
  <mtd-input
    v-model="value"
    placeholder="最大长度为10"
    :maxLength="10"
    showCount
  />
</template>

<script>
export default {
  data() {
    return {
      value: "",
    };
  },
};
</script>

```

#### doc/size.vue

```vue
<template>
  <div class="demo-flex">
    <div>
      <mtd-input v-model="value1" size="small" placeholder="小尺寸输入框" />
    </div>
    <div>
      <mtd-input v-model="value2" placeholder="中/默认尺寸输入框" />
    </div>
    <div>
      <mtd-input v-model="value3" size="large" placeholder="大尺寸输入框" />
    </div>
  </div>
</template>

<script>
export default {
  data () {
    return {
      value1: '',
      value2: '',
      value3: '',
    };
  },
};
</script>

```

#### doc/slot-demo.vue

```vue
<template>
  <div>
    <mtd-input
      v-show="show"
      show-count
      :max-length="100"
      prefix-icon="avatar-o"
      suffix-icon="avatar-o"
      placeholder="具体文案"
    >
      <template #append>
        <span>11</span>
      </template>
      <template #prepend>
        <span>11</span>
      </template>
    </mtd-input>

    <mtd-button @click="handleShowInput">Input {{show ? 'hide' : 'show'}}</mtd-button>
  </div>
</template>
<script>

export default {
  data() {
    return {
      show: false,
    }
  },
  methods: {
    handleShowInput() {
      this.show = !this.show
    },
  },
}
</script>
```

#### doc/status.vue

```vue
<template>
  <div style="width:100%">
    <mtd-tabs v-model="genre" type="text" size="small">
      <mtd-tab-pane label="线框型" value="">
        <div class="demo-block-row">
      <mtd-input v-model="value" :genre="genre" />
      <mtd-input disabled model-value="禁用状态" :genre="genre" />
      <mtd-input readonly model-value="只读状态" :genre="genre" />
    </div>
    <div class="demo-block-row">
      <mtd-input status="success" model-value="成功状态" :genre="genre" />
      <mtd-input status="warning" model-value="警告状态" :genre="genre" />
      <mtd-input status="error" model-value="错误状态" :genre="genre" />
    </div>
    <div class="demo-block-row">
      <div>
        <mtd-input status="success" :genre="genre" model-value="成功状态" :has-feedback="hasFeedback" />
      </div>
      <div>
        <mtd-input status="warning" :genre="genre" model-value="警告状态" :has-feedback="hasFeedback" />
        <div class="mtd-form-item-warning-tip">建议使用中文名字</div>
      </div>
      <div>
        <mtd-input :genre="genre" status="error" model-value="错误状态" :has-feedback="hasFeedback" />
        <div class="mtd-form-item-error-tip">用户不存在</div>
      </div>
    </div>
      </mtd-tab-pane>

      <mtd-tab-pane label="面型" value="area">
        <div class="demo-block-row">
      <mtd-input v-model="value" :genre="genre" />
      <mtd-input disabled model-value="禁用状态" :genre="genre" />
      <mtd-input readonly model-value="只读状态" :genre="genre" />
    </div>
    <div class="demo-block-row">
      <mtd-input status="success" model-value="成功状态" :genre="genre" />
      <mtd-input status="warning" model-value="警告状态" :genre="genre" />
      <mtd-input status="error" model-value="错误状态" :genre="genre" />
    </div>
    <div class="demo-block-row">
      <div>
        <mtd-input status="success" :genre="genre" model-value="成功状态" :has-feedback="hasFeedback" />
      </div>
      <div>
        <mtd-input status="warning" :genre="genre" model-value="警告状态" :has-feedback="hasFeedback" />
        <div class="mtd-form-item-warning-tip">建议使用中文名字</div>
      </div>
      <div>
        <mtd-input :genre="genre" status="error" model-value="错误状态" :has-feedback="hasFeedback" />
        <div class="mtd-form-item-error-tip">用户不存在</div>
      </div>
    </div>
      </mtd-tab-pane>

      <mtd-tab-pane label="单线型" value="line">
        <div class="demo-block-row">
      <mtd-input v-model="value" :genre="genre" />
      <mtd-input disabled model-value="禁用状态" :genre="genre" />
      <mtd-input readonly model-value="只读状态" :genre="genre" />
    </div>
    <div class="demo-block-row">
      <mtd-input status="success" model-value="成功状态" :genre="genre" />
      <mtd-input status="warning" model-value="警告状态" :genre="genre" />
      <mtd-input status="error" model-value="错误状态" :genre="genre" />
    </div>
    <div class="demo-block-row">
      <div>
        <mtd-input status="success" :genre="genre" model-value="成功状态" :has-feedback="hasFeedback" />
      </div>
      <div>
        <mtd-input status="warning" :genre="genre" model-value="警告状态" :has-feedback="hasFeedback" />
        <div class="mtd-form-item-warning-tip">建议使用中文名字</div>
      </div>
      <div>
        <mtd-input :genre="genre" status="error" model-value="错误状态" :has-feedback="hasFeedback" />
        <div class="mtd-form-item-error-tip">用户不存在</div>
      </div>
    </div>
      </mtd-tab-pane>
    </mtd-tabs>
    
  </div>
</template>

<script>
export default {
  data () {
    return {
      value: '默认状态',
      genre:'',
      hasFeedback: true,
    }
  },
}
</script>
<style lang="scss" scoped>
.demo-block-row {
  display: flex;
}
.demo-input {
  ::v-deep .demo-source {
    padding: 0 16px;
  }
}

::v-deep .mtd-tab-pane {
    padding: 90px 0 75px;
    display: flex;
    flex-direction: column;
    align-items: center;
  }

::v-deep .mtd-tabs-nav {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    border-bottom: 1px solid #edf0f7;
    padding: 1.5px 16px;
  }
</style>

```

#### doc/text-align.vue

```vue
<template>
  <div class="demo-full-width demo-vertical-box">
    <div>
      <mtd-input style="width: 440px;" v-model="value1" placeholder="请输入" />
    </div>
    <div>
      <mtd-input style="width: 440px;" v-model="value2" align="center" placeholder="请输入" />
    </div>
    <div>
      <mtd-input style="width: 440px;" v-model="value3" align="right" placeholder="请输入" />
    </div>
  </div>

</template>

<script>
export default {
  data() {
    return {
      value1: "左对齐",
      value2: "居中对齐",
      value3: "右对齐",
    }
  },
}
</script>
<style scoped lang="scss">
.demo-vertical-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap:20px
}
</style>

```

#### group.tsx

```tsx
import {
  defineComponent,
  computed, classNames, styles, getSlotsInRender,
} from '@ss/mtd-adapter'
import useConfig from '@hooks/config'

export default defineComponent({
  name: 'MtdInputGroup',
  inheritAttrs: false,
  props: {
    // compact: { // 废弃
    //   type: Boolean,
    //   default: false,
    // },
  },
  setup() {
    const config = useConfig()
    const prefix = computed(() => config.getPrefixCls('input-group'))
    return {
      prefix,
    }
  },
  render() {
    const { prefix } = this
    return <span
      class={classNames(this, {
        [prefix]: true,
        [`${prefix}-compact`]: true,
      })}
      style={styles(this)}
    >
      {getSlotsInRender(this, 'prepend') && <div class={`${prefix}-prepend`} >
        {getSlotsInRender(this, 'prepend')}
      </div>}
      {getSlotsInRender(this)}
      {getSlotsInRender(this, 'append') && <div class={`${prefix}-append`}>
        {getSlotsInRender(this, 'append')}
      </div>}
    </span>
  },
})

```

#### index.md

```markdown
# 文本输入框 / Input

文本输入框是指允许用户在区域内输入文本、数值等内容的组件。

`Input` 为受控组件，它总会显示 Vue 绑定值。通常情况下，应当处理 `input` 事件，并更新组件的绑定值（或使用`v-model`）。否则，输入框内显示的值将不会改变。

## 互动演示

<mtd-doc-usage
  renderComponentName="MtdInput"
  name="Input"
  :apiJson="require('./api.json')"
  :usageJson="require('./usage.json')">
</mtd-doc-usage>

## 代码示例


### 类型 <design-tag></design-tag>

文本输入框提供了三种基础类型：线框型、面型和单线型。根据场景的需要，可以使用不同的文本输入框类型，来适应页面内不同的信息层级。
:::include(src="./doc/genre.vue")
:::

### 尺寸 <design-tag></design-tag>

文本输入框提供了小、中、大三种尺寸。

:::include(src="./doc/size.vue")
:::


### 状态 <design-tag></design-tag>

文本输入框状态可分为默认状态、悬停状态、激活状态、禁用状态、只读状态和校验状态。其中校验状态又分为成功、警告和失败三种，可以使用不带提示信息和带提示信息两种形式来展示。

:::include(src="./doc/status.vue")
:::

### 自适应宽度的输入框

配置`autoWidth`即可，建议配合 `style`的`min-width`和`max-width`使用

:::include(src="./doc/auto-width.vue")
:::

### 注释信息 <design-tag></design-tag>

根据输入场景需要，使用合适的注释信息可以更好地提示用户如何有效完成信息的输入，降低错误成本。
:::include(src="./doc/helper.vue")
:::

### 可清空 <design-tag></design-tag>

支持快捷清空已输入的内容。

:::include(src="./doc/clearable.vue")
:::

### 长文本 <design-tag></design-tag>

如果文字过多，超出输入框长度，常规方法为遮挡前面输入内容，如需查看全部内容可利用 文字提示/Tooltip 展示。
:::include(src="./doc/overflow.vue")
:::

### 前缀与后缀图标

文本输入框内可以添加前缀或后缀图标，常用于登录或注册场景。

:::include(src="./doc/icon.vue")
:::

### 前置与后置标签

根据输入场景的需求，可以在输入框前后加入固定标签，用于适配固定组合。

:::include(src="./doc/prefix-and-suffix.vue")
:::

### 密码输入框 <design-tag></design-tag>

:::include(src="./doc/password.vue")
:::


### 搜索框 <design-tag></design-tag>

通过点击`input-search`组件的搜索图标或者键入回车触发`search`事件。

:::include(src="./doc/input-search.vue")
:::

### 输入框组合

通过`input-group`可以组合使用文本输入框。

:::include(src="./doc/input-group.vue")
:::

### 文本计数 <design-tag></design-tag>

通过设置 maxLength 限制输入的文本的长度，超过限制则不能输入。 设置 showCount 来控制是否显示文字计数。 只有 maxLength 和 showCount 同时设置才会显示文字计数。
:::include(src="./doc/show-count.vue"):::
>文字提示的出现规则及更多信息，请参考 [文字提示/Tooltip <i class="mtdicon mtdicon-link-o"></i>](#components/tooltip)


### 对齐方式 <design-tag></design-tag>

文本输入框提供三种对齐方式：左对齐、居中对齐和右对齐。
:::include(src="./doc/text-align.vue"):::

### 自动聚焦 <design-tag></design-tag>
:::include(src="./doc/focus.vue"):::

## API

<api-doc name="Input" :doc="require('./api.json')"></api-doc>
<api-doc name="InputGroup" :doc="require('../input-group/api.json')"></api-doc>
<api-doc name="InputSearch" :doc="require('../input-search/api.json')"></api-doc>
其余属性和 Input 一致。

```

#### index.ts

```typescript
import Input from './input'
import { withInstall } from '@ss/mtd-adapter'
export default withInstall(Input)

```

#### input.tsx

```tsx
import {
  defineComponent,
  computed,
  ref,
  useResetAttrs,
  useListeners,
  vueInstance,
  getSlotsInRender,
  classNames,
  styles,
  watch,
  nextTick,
  hasProp,
  toRefs,
  onMounted,
  onUnmounted,
  getScopedSlotsInRender,
} from '@ss/mtd-adapter'
import useConfig from '@hooks/config'
import { usePassword } from './usePassword'
import { isNumber } from '@utils/type'
import Icon from '@components/icon'
import { useFormItem } from '@components/form-item/useFormItem'
import { addResizeListener, removeResizeListener } from '@utils/resize-event'
import inputProps from './props'

const StatusIconMap = {
  error: 'error-circle',
  success: 'success-circle',
  warning: 'warning-circle',
}

// 判断开始输入时，忽略以下特殊按键
const ignoreKeys = ['Enter', 'Tab', 'Shift', 'Meta', 'Alt', 'Control', 'CapsLock', 'Escape', 'Backspace', 'ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight']
const ignoreCodes = ['Space']

export default defineComponent({
  name: 'MtdInput',
  components: {
    MtdIcon: Icon,
  },
  inheritAttrs: false,
  model: {
    prop: 'modelValue',
    event: 'update:modelValue',
  },
  props: inputProps(),
  emits: [
    'input',
    'update:modelValue',
    'change',
    'clear',
    'click-prefix',
    'click-suffix',
    'focus',
    'blur',
    'keyup',
    'keydown',
    'enter',
    'compositionupdate',
    'compositionstart',
    'compositionend',
  ],
  slots: [
    'prepend',
    'append',
    'prefix',
    'suffix',

    'input', // 非公开slot
  ],
  setup(props, { attrs, emit, slots }) {
    const config = useConfig()
    const prefix = computed(() => config.getPrefixCls('input'))
    const iconPrefix = config.getIconCls
    const { modelValue: _modelValueRef, value: _valueRef } = toRefs(props)
    const _innerControlledValueRef = ref<string | number>('')
    const ins = vueInstance()

    const isControlled = computed(() => hasProp(ins, 'modelValue') || hasProp(ins, 'value'))

    const m_modelValue = computed(() => (isControlled.value ? (_modelValueRef?.value ?? _valueRef?.value ?? '') : _innerControlledValueRef.value))

    // use form-item
    const formItemHook = useFormItem(props, { attrs, emit, slots })
    const m_disabled = formItemHook.disabled

    // use focused
    const focused = ref(false)
    // 判断用户是否在输入过程中，从输入第一个字符开始，到输入结束为止，都视为输入过程中。
    const isTyping = ref(false)

    const handleFocus = (e: Event) => {
      focused.value = true
      emit('focus', e)
    }

    const handleBlur = (e: Event) => {
      focused.value = false
      formItemHook.m_handleBlur(e) // emit('blur', e)
      isTyping.value = false
    }

    // use hover
    const hovering = ref(false)
    const handleMouseenter = () => {
      if (!m_disabled.value) {
        hovering.value = true
      }
    }

    const handleMouseleave = () => {
      if (!m_disabled.value) {
        hovering.value = false
      }
    }

    // use input
    const inputRef = ref<HTMLInputElement | null>(null)
    const inputPreRef = ref<HTMLInputElement | null>(null)
    const inputWrapperRef = ref<HTMLInputElement | null>(null)
    const appendSlotRef = ref<HTMLInputElement | null>(null)
    const prependSlotRef = ref<HTMLInputElement | null>(null)
    const prefixRef = ref<HTMLInputElement | null>(null)
    const suffixRef = ref<HTMLInputElement | null>(null)
    const focus = (options: { 
      cursor?: 'start' | 'end' | 'all',
      focusOptions?: FocusOptions 
    } = {}) => {
      inputRef.value?.focus(options.focusOptions)

      if (props.type !== 'text') {
        return
      }

      if (options.cursor === 'start') {
        inputRef.value?.setSelectionRange(0, 0)
      } else if (options.cursor === 'end') {
        const len = (m_modelValue.value as string)?.length ?? 0
        inputRef.value?.setSelectionRange(len, len)
      } else if (options.cursor === 'all') {
        inputRef.value?.select?.()
      }
    }

    const blur = () => {
      inputRef.value?.blur()
    }

    const setNativeInput = () => {
      const { value: inputRefValue } = inputRef
      if (
        inputRefValue
        && m_modelValue.value !== undefined
        && inputRefValue.value !== m_modelValue.value
      ) {
        // 防止 m_modelValue.value 为 null
        inputRefValue.value = String(m_modelValue.value ?? '')
      }
    }

    // use form
    const m_loading = formItemHook.loading
    const m_status = computed(() => {
      if (overstep.value) {
        return 'error'
      }

      return formItemHook.status.value
    })
    const m_hasFeedback = formItemHook.hasFeedback

    /*
     * handle Input
     * use isComposing
     */
    const isComposing = ref(false)
    const handleInput = (e: Event, options?: { change: boolean }) => {
      e.stopPropagation && e.stopPropagation()
      const { value } = e.target as HTMLInputElement
      if (value !== m_modelValue.value) {
        emit('update:modelValue', value)
        emit('input', value)
        // 非受控的时候，需要更新内部维护的状态
        if (!isControlled.value) {
          _innerControlledValueRef.value = value
        }

        if (!isComposing.value) {
          setTimeout(setNativeInput, 0)
        }
      }

      if (!isComposing.value || (options && options.change)) {
        formItemHook.m_handleChange(value) // emit('change', value)
      }
    }

    const handleComposition = (e: InputEvent) => {
      const { type } = e
      emit(type as any, e)
      if (type === 'compositionend') {
        isComposing.value = false
        handleInput(e, { change: true })
      } else {
        isComposing.value = true
      }
    }

    const handleKeyup = (e: KeyboardEvent) => {
      emit('keyup', e)
    }

    // 抛出enter事件。不能在handleKeyup里做，compositionend会在keyup前触发。
    const handleKeydown = (e: KeyboardEvent) => {
      const ableToType = !props.readonly && !m_loading.value && !m_disabled.value
      const isIgnoreEvent = ignoreKeys.includes(e.key) || ignoreCodes.includes(e.code)

      if (ableToType && e.key?.length === 1 && !isIgnoreEvent && !isTyping.value) {
        isTyping.value = true
      }

      emit('keydown', e)

      if (e.key === 'Enter' && !isComposing.value) {
        emit('enter', e)
      }
    }

    // value
    const hasValue = computed(() => m_modelValue.value === 0 || !!m_modelValue.value)
    const len = computed(() => (hasValue.value ? String(m_modelValue.value).length : 0))

    const m_clearable = computed(() => props.clearable
      && !m_disabled.value
      && (props.clearableOnReadonly || !props.readonly))

    const showClear = computed(() => m_clearable.value && hasValue.value && (focused.value || hovering.value))
    const m_showCount = computed(() => !!props.maxLength && props.showCount)

    const hasSuffix = () => m_loading.value
      || props.clearable
      || !!(props.suffixIcon || getSlotsInRender(ins, 'suffix'))
      || props.type === 'password'
      || m_hasFeedback.value
    const hasPrefix = () => !!(props.prefixIcon || getSlotsInRender(ins, 'prefix'))

    const [showPassword, handlePasswordIconClick] = usePassword()
    const m_type = computed(() => {
      if (props.type === 'password' && showPassword.value) {
        return 'text'
      }

      return props.type
    })

    const overstep = computed(() => isNumber(props.maxLength) && (len.value > props.maxLength))

    const handlePrefixClick = (e: Event) => {
      focus()
      emit('click-prefix', e)
    }

    const handleSuffixClick = (e: Event) => {
      focus()
      emit('click-suffix', e)
    }

    const handleClearClick = (e: Event) => {
      e.stopPropagation()
      emit('clear')
      handleInput({ target: { value: '' } } as any)
    }

    const handleClick = () => {
      if (!m_disabled.value) {
        focus()
      }
    }

    const stopClick = (event: Event) => {
      event.stopPropagation()
    }

    // use autoWidth
    function updateInputWidthByPre() {
      const pre = inputPreRef.value as HTMLElement
      if (!pre) return
      /*
       * 在设置 text-overflow: ellipsis; 会出现省略号的问题
       * 这里应该使用ceil与getBoundingClientRect配合使用，getBoundingClientRect 会返回 float 型
       * 兜底使用 offsetWidth 返回的整型使用了 floor，会出现小数误差
       * 见TT：https://tt.sankuai.com/ticket/detail?id=305478422
       */
      const width = Math.ceil(pre.getBoundingClientRect?.().width || pre.offsetWidth)
      if (inputRef.value) {
        (inputRef.value as HTMLInputElement).style.width = `${width}px`
      }
    }

    const handleForceUpdateInput = () => {
      requestAnimationFrame(() => {
        ins?.$forceUpdate()
      })
    }

    onMounted(() => {
      const $appendSlot = getSlotsInRender(ins, 'append')
      const $prependSlot = getSlotsInRender(ins, 'prepend')
      if ($appendSlot || $prependSlot) {
        addResizeListener((inputWrapperRef as any).value, handleForceUpdateInput)
      }
    })

    onUnmounted(() => {
      removeResizeListener((inputWrapperRef as any).value?.$el, handleForceUpdateInput)
    })

    if (props.autoWidth) {
      watch(
        [() => m_modelValue.value, () => props.placeholder],
        () => {
          if (!props.autoWidth) return
          nextTick(() => {
            (inputWrapperRef.value as HTMLInputElement).style.width = 'auto'
            updateInputWidthByPre()
          })
        },
        { immediate: true },
      )
    }

    const resetAttrs = useResetAttrs(attrs)
    const resetListeners = useListeners({
      input: handleInput,
      focus: handleFocus,
      blur: handleBlur,
      keyup: handleKeyup,
      keydown: handleKeydown,
      compositionstart: handleComposition,
      compositionupdate: handleComposition,
      compositionend: handleComposition,
    }, ['change'])
    // it will trigger change event twice if not remove

    return {
      resetAttrs,
      resetListeners,

      m_modelValue,
      m_size: formItemHook.m_size,
      inputRef,
      inputPreRef,
      inputWrapperRef,
      appendSlotRef,
      prependSlotRef,
      suffixRef,
      prefixRef,
      prefix,
      iconPrefix,

      m_type,
      showPassword,
      handlePasswordIconClick,

      focused,
      hovering,
      showClear,
      m_showCount,

      hasSuffix,
      hasPrefix,
      handleClearClick,
      handlePrefixClick,
      handleSuffixClick,

      handleClick,
      stopClick,
      handleMouseenter,
      handleMouseleave,

      len,
      overstep,
      m_loading,
      m_status,
      m_hasFeedback,
      m_disabled,
      isComposing,
      isTyping,
      // public methods
      focus,
      blur,
    }
  },

  render() {
    const {
      prefix, iconPrefix,
    } = this

    const suffixIconRender = () => {
      const {
        m_loading, showClear, type, showPassword,
        suffixIcon,
        handleClearClick, handleSuffixClick,
        handlePasswordIconClick,
      } = this
      if (m_loading) {
        return <mtd-icon name='loading' />
      }

      if (showClear) {
        return <mtd-icon class={`${prefix}-clear`} name='error-circle' onClick={handleClearClick}></mtd-icon>
      }

      if (type === 'password') {
        const passwordIcon = showPassword ? 'visibility-on-o' : 'visibility-off-o'
        return <mtd-icon name={passwordIcon} style="cursor:pointer" onClick={handlePasswordIconClick} />
      }

      return getSlotsInRender(this, 'suffix') || <i class={iconPrefix(suffixIcon)} onClick={handleSuffixClick} />
    }

    const {
      focused, hovering, resetAttrs, resetListeners,
      prefixIcon, hasPrefix, hasSuffix,
      m_disabled: disabled, readonly, noReadonlyClass,
      m_size, genre, m_type, autoWidth,
      m_modelValue, m_showCount, maxLength, len,
      m_hasFeedback, m_status, align,
      stopClick,
      handleClick, handlePrefixClick,
      handleMouseenter, handleMouseleave,
      placeholder,
      appendSlotRef,
      prependSlotRef,
      suffixRef,
      prefixRef,
      isTyping,
    } = this

    const $appendSlot = getSlotsInRender(this, 'append')
    const $prependSlot = getSlotsInRender(this, 'prepend')
    const $label = getScopedSlotsInRender(this, 'label')

    // 输入时或者不存在自定义渲染内容时（label插槽），使用input展示
    const useInput = (isTyping && len) || !$label

    if ($label) {
      if (resetAttrs.props?.placeholder) {
        delete resetAttrs.props.placeholder
      }
    }

    const getSuffixInnerStyle = () => {
      let style = {}
      // 在有append插槽，的时候需要对append-inner的样式做特殊处理
      if ($appendSlot) {
        style = {
          position: 'absolute',
          top: '50%',
          right: `${appendSlotRef?.offsetWidth}px`,
          transform: 'translate(-8px, -50%)', // 8px 是右侧间距padding的大小
        }
      }

      return style
    }

    const getPrefixInnerStyle = () => {
      let style = {}
      // 在有append插槽，的时候需要对append-inner的样式做特殊处理
      if ($prependSlot) {
        style = {
          position: 'absolute',
          top: '50%',
          left: `${prependSlotRef?.offsetWidth}px`,
          transform: 'translate(8px, -50%)', // 8px 是左侧间距padding的大小
        }
      }

      return style
    }

    const getInputStyle = () => {
      const style: any = {}
      // 在有append插槽，的时候需要对append-inner的样式做特殊处理
      if ($prependSlot) {
        style.paddingLeft = `${(prefixRef?.offsetWidth || 0) + 12}px` // 12px = 8px（是左侧间距padding的大小）  + 4px（input离icon的距离）
      }

      if ($appendSlot) {
        style.paddingRight = `${(suffixRef?.offsetWidth || 0) + 12}px` // 12px = 8px（是右侧间距padding的大小）  + 4px（input离icon的距离）
      }

      return style
    }

    return <div
      class={classNames(this, {
        [`${prefix}-wrapper`]: true,
        [`${prefix}-focused`]: !disabled && !readonly && focused,
        [`${prefix}-hover`]: !disabled && hovering,
        [`${prefix}-group`]: getSlotsInRender(this, 'prepend') || getSlotsInRender(this, 'append'),
        [`${prefix}-disabled`]: disabled,
        [`${prefix}-readonly`]: !noReadonlyClass && readonly,
        [`${prefix}-${m_status}`]: m_status,
        [`${prefix}-${m_size}`]: !!m_size,
        [`${prefix}-${genre}`]: !!genre,
      })}
      style={styles(this)}
      ref={'inputWrapperRef'}
      onClick={handleClick}
      onMouseenter={handleMouseenter}
      onMouseleave={handleMouseleave}
    >
      {getSlotsInRender(this, 'prepend')
        && <div onClick={stopClick}
          ref={'prependSlotRef'}
          class={`${prefix}-group-prepend`}>
          {getSlotsInRender(this, 'prepend')}
        </div>
      }
      {hasPrefix()
        && <span class={`${prefix}-prefix-inner`} ref={'prefixRef'} style={getPrefixInnerStyle()}>
          {getSlotsInRender(this, 'prefix') || <i class={iconPrefix(prefixIcon)} onClick={handlePrefixClick} />}
        </span>
      }
      {getSlotsInRender(this, 'input')
        || (<input
          ref={'inputRef'}
          {...resetAttrs}
          {...resetListeners}
          type={m_type}
          value={useInput ? m_modelValue : void (0)}
          readonly={readonly}
          disabled={disabled}
          class={[
            prefix,
            `${prefix}-inner`,
            {
              [`${prefix}-align-center`]: align === 'center',
              [`${prefix}-align-right`]: align === 'right',
            },
          ]}
          maxLength={maxLength}
          placeholder={useInput ? placeholder : void (0)}
          onChange={(e: Event) => { e.stopPropagation() }}
          style={getInputStyle()}
        />)
      }
      {!useInput ? (
        <div
          class={{
            [`${prefix}-label`]: true,
            [`${prefix}-label-focused`]: !readonly && focused,
          }}
          {...resetListeners}
        >{m_modelValue ? $label({ label: m_modelValue, focused }) : <span class={`${prefix}-label-placeholder`}>{placeholder}</span>}</div>
      ) : null}

      {(hasSuffix() || m_showCount)
        && <span class={`${prefix}-suffix-inner`} ref={'suffixRef'} style={getSuffixInnerStyle()}>
          {hasSuffix() && suffixIconRender()}
          {m_showCount && <span class={`${prefix}-count`}>{`${len}/${maxLength}`}</span>}
          {m_hasFeedback && m_status && StatusIconMap[m_status]
            && <mtd-icon name={StatusIconMap[m_status]} class="status-icon" />
          }
        </span>
      }
      {autoWidth && (
        <span ref="inputPreRef" class={`${prefix}-pre`}>{m_modelValue ?? placeholder}</span>
      )}
      {$appendSlot && (
        <div ref={'appendSlotRef'} onClick={stopClick} class={`${prefix}-group-append`}>{$appendSlot}</div>
      )}
    </div >
  },
})

```

#### props.ts

```typescript
import { MTDUIComponentSize } from '@components/types'
import { ExtractPropTypes, PropType } from '@ss/mtd-adapter'
import { InputGenre, InputStatus } from './types'

export const inputProps = () => ({
  type: {
    type: String,
    default: 'text',
  },
  genre: {
    type: String as PropType<InputGenre>,
    default: '',
  },
  size: {
    type: String as PropType<MTDUIComponentSize>,
    default: '',
  },
  clearable: {
    type: Boolean,
    default: false,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  modelValue: [String, Number],
  /**
   * 为了兼容老版本，不建议使用 value，建议使用 modelValue
   * @deprecated
   */
  value: [String, Number],
  prefixIcon: String,
  suffixIcon: String,
  loading: {
    type: Boolean,
    default: false,
  },
  readonly: {
    type: Boolean,
    default: false,
  },
  noReadonlyClass: {
    type: Boolean,
    default: false,
  },
  clearableOnReadonly: { // 当 readonly 时是否可以清空，仅作用于内部模拟 input
    type: Boolean,
    default: false,
  },
  maxLength: Number,
  showCount: {
    type: Boolean,
    default: false,
  },
  status: {
    type: String as PropType<InputStatus>,
  },
  hasFeedback: Boolean,
  formNoValidate: Boolean,
  autoWidth: Boolean,
  placeholder: {
    type: String,
    default: undefined,
  },
  /**
   * 输入框的文本对齐方式
   * @since 1.2.20
   */
  align: {
    type: String as PropType<'left' | 'center' | 'right'>,
    default: 'left',
  },
})

export type InputProps = Partial<ExtractPropTypes<ReturnType<typeof inputProps>>>

export default inputProps

```

#### search.tsx

```tsx
import {
  computed,
  defineComponent,
  ref,
  useResetAttrs,
  toProps,
  vSlots, classNames, styles, useClassStyle, useListeners,
} from '@ss/mtd-adapter'
import MtdInput from '@components/input'
import MtdButton from '@components/button'
import useConfig from '@hooks/config'
import Icon from '@components/icon'

export default defineComponent({
  name: 'MtdInputSearch',
  components: {
    MtdInput,
    MtdButton,
    MtdIcon: Icon,
  },
  inheritAttrs: false,
  model: {
    prop: 'modelValue',
    event: 'update:modelValue',
  },
  props: {
    loading: Boolean,
    enterButton: Boolean,
    modelValue: [String, Number],
  },
  emits: ['input', 'update:modelValue', 'search'],
  setup(props, { attrs, emit }) {
    const config = useConfig()

    const prefix = computed(() => {
      return config.getPrefixCls('input-search')
    })
    const resetAttrs = useResetAttrs(attrs)
    // 重置按钮的属性，防止两者的type属性冲突
    const m_buttonResetAttrs = computed(() => {
      return {
        ...attrs,
        type: 'primary', // 设置 type 属性为 'primary'
      }
    })

    const classStyle = useClassStyle()

    const getIconCls = config.getIconCls

    const inputRef = ref<typeof MtdInput | null>(null)
    const buttonIcon = computed(() => {
      return getIconCls(props.loading ? 'loading' : 'search')
    })
    const handleSerach = () => {
      emit('search', props.modelValue)
    }

    const handleInput = (val: string) => {
      emit('update:modelValue', val)
      emit('input', val)
    }

    const resetListeners = useListeners({}, ['update:modelValue', 'input', 'search'])

    return {
      prefix,
      resetAttrs,
      m_buttonResetAttrs,
      inputRef,
      buttonIcon,
      handleSerach,
      handleInput,
      classStyle,
      resetListeners,
    }
  },
  render() {
    const { prefix, resetAttrs, buttonIcon, enterButton, loading, modelValue, resetListeners, m_buttonResetAttrs } = this

    const slots = {
      suffix: () => !enterButton ? <mtd-icon
        name="search"
        style="cursor:pointer"
        onClick={this.handleSerach}
      /> : undefined,
    }

    return <div
      class={classNames(this, {
        [prefix]: true,
        [`${prefix}-enter-button`]: enterButton,
      })}
      style={styles(this)}
    >
      <mtd-input
        class={[`${prefix}-input`]}
        style={'width:100%'}
        ref="inputRef"
        {...resetAttrs}
        onEnter={this.handleSerach}
        modelValue={modelValue}
        onInput={this.handleInput}
        loading={loading && !enterButton}
        v-slots={slots}
        {...vSlots(slots)}
        {...resetListeners}
      />
      {
        enterButton
        && (
          <mtd-button
            {...toProps(m_buttonResetAttrs)}
            class={`${prefix}-button`}
            loading={loading}
            onClick={this.handleSerach}
            icon={buttonIcon}>
          </mtd-button>
        )
      }
    </div>
  },
})

```

#### types.ts

```typescript
export type InputGenre = 'area' | 'line' | '';
export type InputStatus = 'success' | 'warning' | 'error'

```

#### usage.json

```json
{
  "componentName": "Input",
  "desc": "文本输入框",
  "codeTemplate": "<mtd-input v-bind='configProps'></mtd-input>"
}

```

#### usePassword.ts

```typescript
import { Ref, ref } from '@ss/mtd-adapter'

export const usePassword = (): [Ref<boolean>, () => void] => {
  const showPassword = ref(false)
  function handlePasswordIconClick() {
    showPassword.value = !showPassword.value
  }

  return [showPassword, handlePasswordIconClick]
}

```

