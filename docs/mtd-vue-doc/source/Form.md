## 源码解析

### 文件依赖

- demo.md
- demo/form-interactive.vue
- doc/annotation-info.vue
- doc/base.vue
- doc/clear-rules.vue
- doc/colon.vue
- doc/custom-validate.vue
- doc/demo/disabled-all-component.vue
- doc/demo/form-item-clear-validate.vue
- doc/demo/form-item-error.vue
- doc/demo/form-item-helper-placement.vue
- doc/demo/form-item-showmessage.vue
- doc/demo/form-item-slot.vue
- doc/demo/form-size.vue
- doc/demo/label-for.vue
- doc/demo/label-position-inherit.vue
- doc/demo/label-position-top.vue
- doc/demo/label-suffix.vue
- doc/demo/label-width.vue
- doc/demo/required-reactive.vue
- doc/demo/reset-fields.vue
- doc/demo/rule-trigger-blur.vue
- doc/demo/rule-trigger-change.vue
- doc/demo/rule-trigger-default.vue
- doc/demo/scroll-to-first-error.vue
- doc/demo/use-html-message.vue
- doc/demo/validate-on-rule-change.vue
- doc/demo/validate-placement.vue
- doc/disabled.vue
- doc/dynamic.vue
- doc/label-position.vue
- doc/label-wrap.vue
- doc/linkage.vue
- doc/position.vue
- doc/resetting.vue
- doc/size.vue
- doc/use-html-message.vue
- doc/validate-status.vue
- doc/validate.vue
- form.tsx
- index.md
- index.ts
- types.ts
- useProvide.ts

### 源码内容

#### demo.md

```markdown
<style lang="scss">
  .demo-mtd-form-helper {
    font-size: 8px;
    color: #ADADAD;
    margin-left: 8px;
  }
  .demo-form-tooltip{
    margin-left: 12px;
  }
  .demo-form{
    .mtd-form {
      display: inline-block;
    }
  }
  .demo-form-title{
    text-align: left;
    font-size: 12px;
    color: #999
  }
  .custom-form-position {
    width: 100%;
    .mtd-tabs-content {
      display: flex;
      justify-content: center;
    }
  }
</style>
# 表单 / Form - 补充Demo


##  disabled
:::include(src="./doc/demo/disabled-all-component.vue")
:::

## validate-on-rule-change
:::include(src="./doc/demo/validate-on-rule-change.vue")
:::

## scroll-to-first-error
:::include(src="./doc/demo/scroll-to-first-error.vue")
:::

## rule-trigger-blur
:::include(src="./doc/demo/rule-trigger-blur.vue")
:::

## rule-trigger-change
:::include(src="./doc/demo/rule-trigger-change.vue")
:::

## rule-trigger-default
:::include(src="./doc/demo/rule-trigger-default.vue")
:::

## reset-fields
:::include(src="./doc/demo/reset-fields.vue")
:::

## label-width 继承 form
:::include(src="./doc/demo/label-width.vue")
:::

## label-position 继承 form
:::include(src="./doc/demo/label-position-inherit.vue")
:::

## label-for
:::include(src="./doc/demo/label-for.vue")
:::

## form-item-error
:::include(src="./doc/demo/form-item-error.vue")
:::

## form-item-showmessage
:::include(src="./doc/demo/form-item-showmessage.vue")
:::

## form-item-helper-placement
:::include(src="./doc/demo/form-item-helper-placement.vue")
:::

## form-item-slot
:::include(src="./doc/demo/form-item-slot.vue")
:::

## label-suffix
:::include(src="./doc/demo/label-suffix.vue")
:::

## validate-placement
:::include(src="./doc/demo/validate-placement.vue")
:::

## use-html-message
:::include(src="./doc/demo/use-html-message.vue")
:::

## form-size
:::include(src="./doc/demo/form-size.vue")
:::


## required-reactive
required响应式BUG修复，[TT链接](https://tt.sankuai.com/ticket/detail?id=304775066)
:::include(src="./doc/demo/required-reactive.vue")
:::

## label-position:top 问题
label-position:top时label布局BUG修复，[TT链接](https://tt.sankuai.com/ticket/detail?id=307398126)
:::include(src="./doc/demo/label-position-top.vue")
:::

## form-item-clear-validate
:::include(src="./doc/demo/form-item-clear-validate.vue")
:::




```

#### demo/form-interactive.vue

```vue

<template>
  <div>
    <mtd-doc-usage name="form" :apiJson="apiJson" :usageJson="usageJson" @propChange="propChange">
      <template #form="{ configProps }">
        <div class="slot-container">
          <mtd-form v-bind="configProps" ref="formRef">
            <mtd-form-item label="用户名" prop="name" required>
              <mtd-input type="text" v-model="configProps.model.name"  placeholder="请输入需求名称" style="width: 200px;">
              </mtd-input>
            </mtd-form-item>
            <mtd-form-item label="密码" prop="phone" required>
              <mtd-input type="text" v-model="configProps.model.phone" placeholder="请输入需求名称" style="width: 200px;">
              </mtd-input>
            </mtd-form-item>

            <mtd-form-item label="城市" prop="city" required>
              <mtd-select placeholder="请选择" v-model="configProps.model.city" style="width: 200px;">
                <mtd-option value="北京" >北京</mtd-option>
                <mtd-option value="上海" >上海</mtd-option>
                <mtd-option value="成都" >成都</mtd-option>
              </mtd-select>
            </mtd-form-item>
            <mtd-form-item label="日期" prop="date" required>
              <mtd-date-picker type="date" placeholder="请选择时间" v-model="configProps.model.date" />
            </mtd-form-item>
            <mtd-form-item label="偏好" prop="preference" required>
              <mtd-checkbox-group v-model="configProps.model.preference">
                <mtd-checkbox value="1">偏好1</mtd-checkbox>
                <mtd-checkbox value="2">偏好2</mtd-checkbox>
                <mtd-checkbox value="3">偏好3</mtd-checkbox>
              </mtd-checkbox-group>
            </mtd-form-item>
            <mtd-form-item>
              <mtd-button type="primary" @click="validate">提交</mtd-button>
              <mtd-button type="panel" style="margin-left:12px" @click="handleResetting">重置</mtd-button>
              <mtd-button type="panel" style="margin-left:12px" @click="handleClearValidate">清空校验结果</mtd-button>
            </mtd-form-item>
          </mtd-form>
        </div>
      </template>
    </mtd-doc-usage>
  </div>
</template>
<script>
import apiJson from '../api.json'

export default {
  data() {
    return {
      apiJson: apiJson,
      usageJson: {
        componentName: "form",
        desc: "表单",
        codeTemplate: "<mtd-form v-bind='defaultProps'></mtd-form>"
      }
    }
  },
  methods: {
    propChange(propertyName, val) {
      console.log('prop changed: , propertyName: ' + propertyName + ', val: ' + val)
    },
    handleResetting() {
      this.$refs.formRef.resetFields()
    },
    validate() {
      this.$refs.formRef.validate()
    },
    handleClearValidate() {
      this.$refs.formRef.clearValidate()
    }
  }
}
</script>
<style lang="scss" scoped>

</style>

```

#### doc/annotation-info.vue

```vue
<template>
    <mtd-form :model="formCustom" labelPosition="right">
        <mtd-form-item label="用户名" prop="userName" helper="只能输入中文">
            <mtd-input type="text" v-model="formCustom.name" placeholder="请输入用户名称">
            </mtd-input>
        </mtd-form-item>
        <mtd-form-item label="部门" prop="dept" helper="请输入完整的部门链">
            <mtd-select placeholder="请选择" v-model="formCustom.city">
                <mtd-option :key="item.value" v-for="item in selectOptions" :value="item.value" :label="item.label" />
            </mtd-select>
        </mtd-form-item>
        <mtd-form-item label="评分" prop="rate" helper="1分极不满意、10分非常满意">
            <mtd-rate v-model="formCustom.rate" :count="10" />
        </mtd-form-item>
        <mtd-form-item label="偏好" prop="preference" helper="选项1说明、选项2说明、选项3说明">
            <mtd-checkbox-group v-model="formCustom.preference">
                <mtd-checkbox value="option1">选项1</mtd-checkbox>
                <mtd-checkbox value="option2">选项2</mtd-checkbox>
                <mtd-checkbox value="option3">选项3</mtd-checkbox>
            </mtd-checkbox-group>
        </mtd-form-item>

    </mtd-form>
</template>
      
<script>

export default {
    data() {
        return {
            formCustom: {},
            selectOptions: [
                { label: '部门一', value: '1' },
                { label: '部门二', value: '2' },
                { label: '部门三', value: '3' }
            ]
        }
    },
}
</script>
      
```

#### doc/base.vue

```vue
<template>
  <mtd-form :model="formCustom" ref="formCustom" :rules="ruleCustom" labelPosition="left">
    <mtd-form-item label="主机名称" prop="hostname">
      <mtd-input
        type="text"
        v-model="formCustom.hostname"
        style="width: 260px;">
        <template #prepend>http://</template>
        <template #append>.com</template>
      </mtd-input>
    </mtd-form-item>
    <mtd-form-item label="管理员" prop="owner">
      {{ formCustom.owner }}
    </mtd-form-item>
    <mtd-form-item label="时间" prop="selectTime">
      <mtd-time-picker
        type="time"
        v-model="formCustom.selectTime"
        placeholder="选择时间"
        :steps="[1, 5, 10]"
      />
    </mtd-form-item>
    <mtd-form-item label="时间范围" prop="workTime">
      <mtd-time-picker
        v-model="formCustom.workTime"
        type="timerange"
        format="HH:mm"
        value-format="HH:mm"
        :placeholder="['开始时间', '结束时间']"
        :clearable="true"
        style="width: 360px"
      />
    </mtd-form-item>
    <mtd-form-item label="日期范围" prop="usagePeriod">
      <mtd-date-picker
        type="daterange"
        v-model="formCustom.usagePeriod"
        :placeholder="['开始时间','结束时间']"
        style="width: 260px;"
      />
    </mtd-form-item>
    <mtd-form-item label="机房区域" prop="curzone">
      <mtd-input-group compact>
        <mtd-select type="text" v-model="formCustom.curzone" style="width: 100px;">
          <mtd-option
          :key="item.value"
            v-for="item in list1"
            :value="item.value"
            :label="item.label" />
        </mtd-select>
        <mtd-select type="text" v-model="formCustom.curhost">
          <mtd-option
            :key="item.value"
            v-for="item in list1.filter(v => v.value === formCustom.curzone).length
          ? list1.filter(v => v.value === formCustom.curzone)[0].children
          : []"
            :value="item.value"
            :label="item.label" />
        </mtd-select>
      </mtd-input-group>
    </mtd-form-item>
    <mtd-form-item label="类型" prop="type">
      <mtd-radio-group v-model="formCustom.type">
        <mtd-radio value="windows">windows</mtd-radio>
        <mtd-radio value="linux">Linux</mtd-radio>
      </mtd-radio-group>
    </mtd-form-item>
    <mtd-form-item label="CPU" prop="cpu">
      <mtd-radio-group v-model="formCustom.cpu">
        <mtd-radio-button value="2">2核</mtd-radio-button>
        <mtd-radio-button value="4">4核</mtd-radio-button>
        <mtd-radio-button value="8">8核</mtd-radio-button>
        <mtd-radio-button value="16">16核</mtd-radio-button>
      </mtd-radio-group>
    </mtd-form-item>
    <mtd-form-item label="购买台数" prop="number" style="width: 260px;">
      <mtd-select type="text" v-model="formCustom.number" style="width: 100px;">
        <mtd-option :value="1" label="1台" />
        <mtd-option :value="2" label="2台" />
        <mtd-option :value="3" label="3台" />
        <mtd-option :value="4" label="4台" />
      </mtd-select>
    </mtd-form-item>
    <mtd-form-item label="预估使用率" prop="estimatedUsageRate" required>
      <mtd-input-group>
        <mtd-input-number
          v-model="formCustom.estimatedUsageRate"
          :controls="false"
          :max="100"
          :min="10"
          :precision="0"
          style="width: 112px"
        />
        <template #append>%</template>
      </mtd-input-group>
    </mtd-form-item>
    <mtd-form-item label="硬盘容量" prop="size" content-display="flex">
      <mtd-slider v-model="formCustom.size" style="width: 350px;" />
    </mtd-form-item>
    <mtd-form-item label="主机编号" prop="serilize">
      <div
        v-for="(item, index) in formCustom.serilize"
        :key="index"
        style="display: inline-block">
        <mtd-input
          type="text"
          ref="serilize"
          v-model="formCustom.serilize[index]"
          style="width: 50px;"
          @input="handleSerilize" /><span style="padding: 10px" v-if="index < formCustom.serilize.length - 1">-</span>
      </div>
    </mtd-form-item>
    <mtd-form-item label="访问地址" prop="url">
      <mtd-input v-model="formCustom.url" style="width: 400px;">
        <template #prepend>http://</template>
        <template #append>
          <mtd-select v-model="formCustom.appendValue" style="width: 90px;">
            <mtd-option value=".com" label=".com" />
            <mtd-option value=".cn" label=".cn" />
          </mtd-select>
        </template>
      </mtd-input>
    </mtd-form-item>
    <mtd-form-item label="自动续费" prop="renewal">
      <mtd-checkbox v-model="formCustom.renewal">到期后自动续费</mtd-checkbox>
    </mtd-form-item>
    <mtd-form-item label="标签" prop="tags">
      <mtd-select
v-model="formCustom.tags" placeholder="请选择" multiple
        style="width: 260px;">
        <mtd-option
          v-for="item in list2"
          :key="item.value"
          :label="item.label"
          :value="item.value" />
      </mtd-select>
    </mtd-form-item>
    <mtd-form-item label="描述" prop="desc">
      <mtd-textarea
placeholder="描述文本"
        v-model="formCustom.desc" style="width: 260px;"
        rows="3" :max-length="50" />
    </mtd-form-item>

    <mtd-form-item label="评分" prop="rate">
      <mtd-rate v-model="formCustom.rate" />
    </mtd-form-item>

    <mtd-form-item>
      <mtd-button
type="primary" style="margin-right: 12px;"
        @click="handleSubmit">
        立即创建
      </mtd-button>
      <mtd-button @click="handleReset">取消</mtd-button>
    </mtd-form-item>
  </mtd-form>
</template>

<script>
const validateHostname = (rule, value, callback) => {
  if (value.trim() === '') {
    callback(new Error('请输入主机名称'))
  } else {
    callback()
  }
}
export default {
  data () {
    return {
      formCustom: {
        hostname: '',
        owner: '李明/liming',
        selectTime: '',
        workTime: '',
        usagePeriod: '',
        curzone: '',
        curhost: '',
        type: 'windows',
        cpu: '2',
        number: 1,
        size: 0,
        serilize: ['0', '0', '0', '0'],
        url: '',
        appendValue: '.com',
        estimatedUsageRate: 10,
        renewal: true,
        tags: [],
        desc: '',
        rate: 0,
      },
      ruleCustom: {
        selectTime: { required: true, message: '请选择' },
        workTime: { required: true, message: '请选择' },
        usagePeriod: { required: true, message: '请选择' },
        curzone: {
          required: true,
          validator: (rule, value, callback) => {
            if (!this.formCustom.curzone) {
              return callback(new Error('请选择区域'))
            } else if (!this.formCustom.curhost) {
              return callback(new Error('请选择机房'))
            }
            callback()
          },
        },
        type: { required: true, message: '必填' },
        cpu: { required: true, message: '必填' },
        number: { required: true, type: 'number', message: '必填' },
        size: { required: true, message: '必填' },
        serilize: { required: true, message: '必填' },
        url: { required: true, message: '请输入地址' },
        hostname: [
          { required: true, message: '请输入主机名称' },
          { validator: validateHostname, trigger: 'blur' },
        ],
        rate: { required: true ,validator: (rule, value, callback) => {
          if (!value) {
            return callback(new Error('请输入评分结果（1~5分）'))
          } else {
            callback()
          }
        }},
      },
      list1: [{
        value: '1',
        label: '区域1',
        children: [{
          value: '1',
          label: '主机1',
        }, {
          value: '2',
          label: '主机2',
        }],
      }, {
        value: '2',
        label: '区域2',
        children: [{
          value: '3',
          label: '主机3',
        }, {
          value: '4',
          label: '主机4',
        }],
      }],
      list2: [{
        value: 'tag1',
        label: '标签1',
      }, {
        value: 'tag2',
        label: '标签2',
      }, {
        value: 'tag3',
        label: '标签3',
      }, {
        value: 'tag4',
        label: '标签4',
      }, {
        value: 'tag5',
        label: '标签5',
      }],
      count: 0,
    }
  },
  methods: {
    handleSerilize (v) {
      if (v.length === 3) {
        this.count = (++this.count) % this.$refs.serilize.length
        this.$refs.serilize[this.count].focus()
      }
    },
    handleSubmit (name) {
      this.$refs.formCustom.validate((valid, errors) => {
        if (valid) {
          console.log('Success!')
        } else {
          console.error('Fail!', errors)
        }
      })
    },
    handleReset () {
      this.$refs.formCustom.resetFields()
    },
  },
}
</script>

```

#### doc/clear-rules.vue

```vue
<template>
  <mtd-form :rules="ruleCustom" :model="formCustom" ref="formRef">
    <mtd-form-item label="用户名" prop="username" helper="只能输入中文" required>
      <mtd-input type="text" v-model="formCustom.username" style="width: 360px;" />
    </mtd-form-item>
    <mtd-form-item label="密码" prop="password" helper="6-16个字符，请使用字母加数字的组合密码，不能包含*@#等符号" required>
      <mtd-input type="text" v-model="formCustom.password" style="width: 360px;" />
    </mtd-form-item>
    <mtd-form-item label="邮箱" prop="email" required>
      <mtd-input type="text" v-model="formCustom.email" style="width: 360px;" />
    </mtd-form-item>
    <mtd-form-item>
      <mtd-button type="primary" @click="handleValidate">立即创建</mtd-button>
      <mtd-button type="panel" style="margin-left:12px" @click="handleClearRules">清空校验结果</mtd-button>
      <mtd-button type="panel" style="margin-left:12px" @click="handleClearEmailRules">仅清空邮箱校验结果</mtd-button>
    </mtd-form-item>
  </mtd-form>
</template>

<script>
export default {
  data() {
    const validateUsername = (rule, value, callback) => {
      if (!/^[\u4e00-\u9fa5]+$/.test(value)) {
        callback(new Error('只能输入中文'));
      } else {
        callback();
      }
    };
    const validatePassword = (rule, value, callback) => {
      if (!/^[a-zA-Z0-9]{6,16}$/.test(value)) {
        callback(new Error('只能输入6-16个字符且仅为字母,数字'));
      } else {
        callback();
      }
    };

    const validateEmail = (rule, value, callback) => {
      if (!/^[A-Za-z0-9]+([_\.][A-Za-z0-9]+)*@([A-Za-z0-9\-]+\.)+[A-Za-z]{2,6}$/.test(value)) {
        callback(new Error('请输入正确的邮箱'));
      } else {
        callback();
      }
    };
    return {
      formCustom: {
        username: '',
        password: '',
        email: ''
      },
      ruleCustom:
      {
        username: [
          { validator: validateUsername, trigger: 'change' },
        ],
        password: [
          { validator: validatePassword, trigger: 'change' },
        ],
        email: [
          { validator: validateEmail, trigger: 'change' }
        ]
      },
    };
  },
  methods: {
    handleClearRules() {
      this.$refs.formRef.clearValidate()
    },
    handleClearEmailRules() {
      this.$refs.formRef.clearValidate('email')
    },
    handleValidate() {
      this.$refs.formRef.validate()
    }
  },
};
</script>

```

#### doc/colon.vue

```vue
<template>
  <div class="custom-form-label-suffix">
    <div>
      <mtd-form label-position="right" colon>
        <!-- 这里测试Form的colon和FormItem的colon优先级 -->
        <mtd-form-item label="表单标题1" :colon="false">
          <mtd-input type="text" v-model="text1" placeholder="请输入"/>
        </mtd-form-item>
        <mtd-form-item label="表单标题2" :colon="false">
          <mtd-input type="text" v-model="text2" placeholder="请输入"/>
        </mtd-form-item>
      </mtd-form>
    </div>
    <div>
      <mtd-form label-position="right" colon>
        <mtd-form-item label="表单标题1">
          <mtd-input type="text" v-model="text3" placeholder="请输入"/>
        </mtd-form-item>
        <mtd-form-item label="表单标题2">
          <mtd-input type="text" v-model="text4" placeholder="请输入"/>
        </mtd-form-item>
      </mtd-form>
    </div>
  </div>
</template>

<script>
export default {
  data () {
    return {
      text1: '',
      text2: '',
      text3: '',
      text4: '',
    };
  },
};
</script>
<style lang="scss">
.custom-form-label-suffix {
  display: flex;
}
</style>

```

#### doc/custom-validate.vue

```vue
<template>
  <mtd-form ref="form" :rules="ruleCustom" :model="formCustom" scrollToFirstError>
    <div>
      <mtd-form-item label="用户名" prop="username" helper="只能输入中文">
        <mtd-input type="text" v-model="formCustom.username" placeholder="请输入用户名称" />
      </mtd-form-item>
      <mtd-form-item
        label="密码" prop="password1"
        helper="6-16个字符，请使用字母加数字的组合密码，不能包含*@#等符号"
      >
        <mtd-input type="password" v-model="formCustom.password1" placeholder="请输入密码"/>
      </mtd-form-item>
      <mtd-form-item
        label="确认密码" prop="password2"
        helper="6-16个字符，请使用字母加数字的组合密码，不能包含*@#等符号"
      >
        <mtd-input type="password" v-model="formCustom.password2" placeholder="请输入密码" />
      </mtd-form-item>
    </div>
    <div>
      <mtd-form-item>
        <mtd-button
          type="primary" style="margin-right: 12px;"
          @click="handleSubmit('formCustom')">
          立即创建
        </mtd-button>
        <mtd-button @click="handleReset('formCustom')">取消</mtd-button>
      </mtd-form-item>
    </div>
  </mtd-form>
</template>

<script>
export default {
  data () {
    const checkUsername = (rule, value, callback) => {
      if (!/^[\u4e00-\u9fa5]+$/.test(value)) {
        callback(new Error('只能输入中文'))
      } else {
        callback()
      }
    }
    const checkPassword = (rule, value, callback) => {
      if (!/^[a-zA-Z0-9]{6,16}$/.test(value)) {
        callback(new Error('只能输入6-16个字符且仅为字母,数字'))
      } else {
        if (this.formCustom.password2 !== '') {
          this.$refs.form.validateField('password2')
        }
        callback()
      }
    }
    const checkPasswordEql = (rule, value, callback) => {
      if (this.formCustom.password1 === value) {
        return callback()
      }
      return callback(new Error('两次输入密码不一致!'))
    }

    return {
      formCustom: {
        username: '',
        password1: '',
        password2: '',
      },
      ruleCustom:
        {
          username: [
            { required: true, message: '请输入用户名' },
            { validator: checkUsername },
          ],
          password1: [
            { required: true, message: '请输入密码' },
            { validator: checkPassword },
          ],
          password2: [
            { required: true, message: '请再次输入密码' },
            { validator: checkPasswordEql },
          ],
        },
    }
  },
  methods: {
    handleSubmit (name) {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.$mtd.message('创建成功')
        } else {
          this.$mtd.message({
            type: 'error',
            message: '创建失败',
          })
        }
      })
    },
    handleReset (name) {
      this.$refs.form.resetFields()
    },
  },
}
</script>

```

#### doc/demo/disabled-all-component.vue

```vue
<template>
  <div style="text-align: start">
    <div style="margin-bottom: 30px">
      <mtd-row>
        <mtd-col :span="6">
          disabled:
        </mtd-col>
        <mtd-col :span="18">
          <mtd-radio-group v-model="disabled">
            <mtd-radio :value="true">true</mtd-radio>
            <mtd-radio :value="false">false</mtd-radio>
          </mtd-radio-group>
        </mtd-col>
      </mtd-row>
      <mtd-row>
        <mtd-col :span="6">
          size:
        </mtd-col>
        <mtd-col :span="18">
          <mtd-radio-group v-model="size">
            <mtd-radio value="small">small</mtd-radio>
            <mtd-radio :value="undefined">不设置</mtd-radio>
            <mtd-radio value="large">large</mtd-radio>
          </mtd-radio-group>
        </mtd-col>
      </mtd-row>
      <mtd-row>
        <mtd-col :span="6">
          labelPosition:
        </mtd-col>
        <mtd-col :span="18">
          <mtd-radio-group v-model="labelPosition">
            <mtd-radio value="top">top</mtd-radio>
            <mtd-radio value="left">left</mtd-radio>
            <mtd-radio value="right">right</mtd-radio>
          </mtd-radio-group>
        </mtd-col>
      </mtd-row>
    </div>
    <mtd-form
      :model="formCustom"
      ref="formCustom"
      :rules="ruleCustom"
      :labelPosition="labelPosition"
      :disabled="disabled"
      :size="size"
    >
      <mtd-form-item label="主机名称" prop="hostname">
        <mtd-input
          type="text"
          v-model="formCustom.hostname"
          style="width: 260px;">
          <template #prepend>http://</template>
          <template #append>.com</template>
        </mtd-input>
      </mtd-form-item>
      <mtd-form-item label="autocomplete" prop="hostnameA">
        <mtd-autocomplete :data="autoData" v-model="formCustom.autoValue" placeholder="请输入"
                        @search="handleAutoSearch" @select="handleAutoSelect" />
      </mtd-form-item>
      <mtd-form-item label="cascader" prop="cascader">
        <mtd-cascader
            v-model="formCustom.cascaderMultipleValue"
            :data="cascaderOptions"
            clearable
            defaultExpandFirstItem
        />
      </mtd-form-item>
      <mtd-form-item label="cascader-多选" prop="cascader">
        <mtd-cascader
          v-model="formCustom.cascaderValue"
          :data="cascaderOptions"
          multiple
          clearable
          defaultExpandFirstItem
        />
      </mtd-form-item>
      <mtd-form-item label="管理员" prop="owner">
        {{ formCustom.owner }}
      </mtd-form-item>
      <mtd-form-item label="时间" prop="selectTime">
        <mtd-time-picker
          type="time"
          v-model="formCustom.selectTime"
          placeholder="选择时间"
          :steps="[1, 5, 10]"
        />
      </mtd-form-item>
      <mtd-form-item label="时间范围" prop="workTime">
        <mtd-time-picker
          v-model="formCustom.workTime"
          type="timerange"
          format="HH:mm"
          value-format="HH:mm"
          :placeholder="['开始时间', '结束时间']"
          :clearable="true"
          style="width: 360px"
        />
      </mtd-form-item>
      <mtd-form-item label="日期范围" prop="usagePeriod">
        <mtd-date-picker
          type="daterange"
          v-model="formCustom.usagePeriod"
          :placeholder="['开始时间','结束时间']"
          style="width: 260px;"
        />
      </mtd-form-item>
      <mtd-form-item label="机房区域" prop="curzone">
        <mtd-input-group compact>
          <mtd-select type="text" v-model="formCustom.curzone" style="width: 100px;">
            <mtd-option
            :key="item.value"
              v-for="item in list1"
              :value="item.value"
              :label="item.label" />
          </mtd-select>
          <mtd-select type="text" v-model="formCustom.curhost">
            <mtd-option
              :key="item.value"
              v-for="item in list1.filter(v => v.value === formCustom.curzone).length
            ? list1.filter(v => v.value === formCustom.curzone)[0].children
            : []"
              :value="item.value"
              :label="item.label" />
          </mtd-select>
        </mtd-input-group>
      </mtd-form-item>
      <mtd-form-item label="类型" prop="type">
        <mtd-radio-group v-model="formCustom.type">
          <mtd-radio value="windows">windows</mtd-radio>
          <mtd-radio value="linux">Linux</mtd-radio>
        </mtd-radio-group>
      </mtd-form-item>
      <mtd-form-item label="CPU" prop="cpu">
        <mtd-radio-group v-model="formCustom.cpu">
          <mtd-radio-button value="2">2核</mtd-radio-button>
          <mtd-radio-button value="4">4核</mtd-radio-button>
          <mtd-radio-button value="8">8核</mtd-radio-button>
          <mtd-radio-button value="16">16核</mtd-radio-button>
        </mtd-radio-group>
      </mtd-form-item>
      <mtd-form-item label="购买台数" prop="number" style="width: 260px;">
        <mtd-select type="text" v-model="formCustom.number" style="width: 100px;">
          <mtd-option :value="1" label="1台" />
          <mtd-option :value="2" label="2台" />
          <mtd-option :value="3" label="3台" />
          <mtd-option :value="4" label="4台" />
        </mtd-select>
      </mtd-form-item>
      <mtd-form-item label="预估使用率" prop="estimatedUsageRate" required>
        <mtd-input-group>
          <mtd-input-number
            v-model="formCustom.estimatedUsageRate"
            :controls="false"
            :max="100"
            :min="10"
            :precision="0"
            style="width: 112px"
          />
          <template #append>%</template>
        </mtd-input-group>
      </mtd-form-item>
      <mtd-form-item label="radio-demo" prop="radioVal">
        <mtd-radio-button :value="formCustom.radioVal">demo single radio-button</mtd-radio-button>
        <mtd-radio :value="formCustom.radioVal">demo single radio</mtd-radio>
      </mtd-form-item>
      <mtd-form-item label="treeSelect-demo" prop="treeSelectVal">
        <mtd-tree-select v-model="formCustom.treeSelectVal" :data="treeSelectOptions" />
      </mtd-form-item>
      <mtd-form-item label="硬盘容量" prop="size" content-display="flex">
        <mtd-slider v-model="formCustom.size" style="width: 350px;" />
      </mtd-form-item>
      <mtd-form-item label="主机编号" prop="serilize">
        <div
          v-for="(item, index) in formCustom.serilize"
          :key="index"
          style="display: inline-block">
          <mtd-input
            type="text"
            ref="serilize"
            v-model="formCustom.serilize[index]"
            style="width: 50px;"
            @input="handleSerilize" /><span style="padding: 10px" v-if="index < formCustom.serilize.length - 1">-</span>
        </div>
      </mtd-form-item>
      <mtd-form-item label="访问地址" prop="url">
        <mtd-input v-model="formCustom.url" style="width: 400px;">
          <template #prepend>http://</template>
          <template #append>
            <mtd-select v-model="formCustom.appendValue" style="width: 90px;">
              <mtd-option value=".com" label=".com" />
              <mtd-option value=".cn" label=".cn" />
            </mtd-select>
          </template>
        </mtd-input>
      </mtd-form-item>
      <mtd-form-item label="自动续费" prop="renewal">
        <mtd-checkbox v-model="formCustom.renewal">到期后自动续费</mtd-checkbox>
      </mtd-form-item>
      <mtd-form-item label="标签和多行标签多行标" prop="tags">
        <mtd-select
  v-model="formCustom.tags" placeholder="请选择" multiple
          style="width: 260px;">
          <mtd-option
            v-for="item in list2"
            :key="item.value"
            :label="item.label"
            :value="item.value" />
        </mtd-select>
      </mtd-form-item>
      <mtd-form-item label="描述" prop="desc">
        <mtd-textarea
  placeholder="描述文本"
          v-model="formCustom.desc" style="width: 260px;"
          rows="3" :max-length="50" />
      </mtd-form-item>

      <mtd-form-item label="评分" prop="rate">
        <mtd-rate v-model="formCustom.rate" />
      </mtd-form-item>
      <mtd-form-item label="picker" prop="picker">
        <mtd-picker clearable placeholder="请选择" v-model="formCustom.picker">
          <mtd-option value="北京">北京</mtd-option>
          <mtd-option value="上海">上海</mtd-option>
        </mtd-picker>
      </mtd-form-item>
      <mtd-form-item label="switch" prop="switchVal">
        <mtd-switch v-model="formCustom.switchVal" />
      </mtd-form-item>
      <mtd-form-item label="upload" prop="switchVal">
        <mtd-upload
            class="upload-base"
            action="https://jsonplaceholder.typicode.com/posts/"
            multiple>
        </mtd-upload>
      </mtd-form-item>
      <mtd-form-item>
        <mtd-button
  type="primary" style="margin-right: 12px;"
          @click="handleSubmit">
          立即创建
        </mtd-button>
        <mtd-button @click="handleReset">取消</mtd-button>
        <br>点击右侧icon-button 重置表单
        <mtd-icon-button html-type="button" type="secondary" icon="close" @click="handleReset" />
      </mtd-form-item>
    </mtd-form>
  </div>
</template>

<script>

const validateHostname = (rule, value, callback) => {
  if (value.trim() === '') {
    callback(new Error('请输入主机名称'))
  } else {
    callback()
  }
}
export default {
  data () {
    return {
      autoData: ['a','b'],
      disabled: true,
      size: undefined,
      labelPosition: 'left',
      formCustom: {
        autoValue: '',
        hostname: '',
        hostnameA: '',
        owner: '李明/liming',
        selectTime: '',
        workTime: '',
        usagePeriod: '',
        curzone: '',
        curhost: '',
        type: 'windows',
        cpu: '2',
        number: 1,
        size: 0,
        serilize: ['0', '0', '0', '0'],
        url: '',
        appendValue: '.com',
        estimatedUsageRate: 10,
        treeSelectVal: ['ziyuan', 'jiaohu'],
        radioVal: false,
        renewal: true,
        tags: [],
        desc: '',
        rate: 0,
        textarea1: '',
        cascaderValue: [],
        cascaderMultipleValue: [],
        picker: '',
        switchVal: false,
      },
      ruleCustom: {
        selectTime: { required: true, message: '请选择' },
        workTime: { required: true, message: '请选择' },
        usagePeriod: { required: true, message: '请选择' },
        curzone: {
          required: true,
          validator: (rule, value, callback) => {
            if (!this.formCustom.curzone) {
              return callback(new Error('请选择区域'))
            } else if (!this.formCustom.curhost) {
              return callback(new Error('请选择机房'))
            }
            callback()
          },
        },
        type: { required: true, message: '必填' },
        cpu: { required: true, message: '必填' },
        number: { required: true, type: 'number', message: '必填' },
        size: { required: true, message: '必填' },
        serilize: { required: true, message: '必填' },
        url: { required: true, message: '请输入地址' },
        hostname: [
          { required: true, message: '请输入主机名称' },
          { validator: validateHostname, trigger: 'blur' },
        ],
        rate: { required: true ,validator: (rule, value, callback) => {
          if (!value) {
            return callback(new Error('请输入评分结果（1~5分）'))
          } else {
            callback()
          }
        }},
      },
      list1: [{
        value: '1',
        label: '区域1',
        children: [{
          value: '1',
          label: '主机1',
        }, {
          value: '2',
          label: '主机2',
        }],
      }, {
        value: '2',
        label: '区域2',
        children: [{
          value: '3',
          label: '主机3',
        }, {
          value: '4',
          label: '主机4',
        }],
      }],
      list2: [{
        value: 'tag1',
        label: '标签1',
      }, {
        value: 'tag2',
        label: '标签2',
      }, {
        value: 'tag3',
        label: '标签3',
      }, {
        value: 'tag4',
        label: '标签4',
      }, {
        value: 'tag5',
        label: '标签5',
      }],
      count: 0,
      cascaderOptions: [
        {
          value: 'zhinan',
          label: '指南',
          children: [
            {
              value: 'shejiyuanze',
              label: '设计原则',
              children: [
                {
                  value: 'yizhi',
                  label: '一致',
                },
                {
                  value: 'fankui',
                  label: '反馈',
                },
                {
                  value: 'xiaolv',
                  label: '效率',
                },
                {
                  value: 'kekong',
                  label: '可控',
                },
              ],
            }
          ],
        },
        {
          value: 'ziyuan',
          label: '资源',
          children: [
            {
              value: 'axure',
              label: 'Axure Components',
            },
            {
              value: 'sketch',
              label: 'Sketch Templates',
            },
            {
              value: 'jiaohu',
              label: '组件交互文档',
            },
          ],
        },
      ],
      treeSelectOptions: [
        {
          value: 'zhinan',
          label: '指南',
          children: [
            {
              value: 'shejiyuanze',
              label: '设计原则',
              disabled: true,
              children: [
                {
                  value: 'yizhi',
                  label: '一致',
                },
                {
                  value: 'fankui',
                  label: '反馈',
                },
                {
                  value: 'xiaolv',
                  label: '效率',
                },
                {
                  value: 'kekong',
                  label: '可控',
                },
              ],
            },
            {
              value: 'daohang',
              label: '导航',
              children: [
                {
                  value: 'cexiangdaohang',
                  label: '侧向导航',
                },
                {
                  value: 'dingbudaohang',
                  label: '顶部导航',
                },
              ],
            },
          ],
        },
        {
          value: 'zujian',
          label: '组件',
          children: [
            {
              value: 'basic',
              label: 'Basic',
              disabled: true,
              children: [
                {
                  value: 'layout',
                  label: 'Layout 布局',
                },
                {
                  value: 'color',
                  label: 'Color 色彩',
                },
                {
                  value: 'typography',
                  label: 'Typography 字体',
                },
                {
                  value: 'icon',
                  label: 'Icon 图标',
                },
                {
                  value: 'button',
                  label: 'Button 按钮',
                },
              ],
            },
            {
              value: 'form',
              label: 'Form',
              isLeaf: true,
            },
            {
              value: 'data',
              label: 'Data',
              children: [
                {
                  value: 'table',
                  label: 'Table 表格',
                },
                {
                  value: 'tag',
                  label: 'Tag 标签',
                },
                {
                  value: 'progress',
                  label: 'Progress 进度条',
                },
                {
                  value: 'tree',
                  label: 'Tree 树形控件',
                },
                {
                  value: 'pagination',
                  label: 'Pagination 分页',
                },
                {
                  value: 'badge',
                  label: 'Badge 标记',
                },
              ],
            },
            {
              value: 'notice',
              label: 'Notice',
              children: [
                {
                  value: 'alert',
                  label: 'Alert 警告',
                },
                {
                  value: 'loading',
                  label: 'Loading 加载',
                },
                {
                  value: 'message',
                  label: 'Message 消息提示',
                },
                {
                  value: 'message-box',
                  label: 'MessageBox 弹框',
                },
                {
                  value: 'notification',
                  label: 'Notification 通知',
                },
              ],
            },
            {
              value: 'navigation',
              label: 'Navigation',
              children: [
                {
                  value: 'menu',
                  label: 'NavMenu 导航菜单',
                },
                {
                  value: 'tabs',
                  label: 'Tabs 标签页',
                },
                {
                  value: 'breadcrumb',
                  label: 'Breadcrumb 面包屑',
                },
                {
                  value: 'dropdown',
                  label: 'Dropdown 下拉菜单',
                },
                {
                  value: 'steps',
                  label: 'Steps 步骤条',
                },
              ],
            },
            {
              value: 'others',
              label: 'Others',
              children: [
                {
                  value: 'dialog',
                  label: 'Dialog 对话框',
                },
                {
                  value: 'tooltip',
                  label: 'Tooltip 文字提示',
                },
                {
                  value: 'popover',
                  label: 'Popover 弹出框',
                },
                {
                  value: 'card',
                  label: 'Card 卡片',
                },
                {
                  value: 'carousel',
                  label: 'Carousel 走马灯',
                },
                {
                  value: 'collapse',
                  label: 'Collapse 折叠面板',
                },
              ],
            },
          ],
        },
        {
          value: 'ziyuan',
          label: '资源',
          children: [
            {
              value: 'axure',
              label: 'Axure Components',
            },
            {
              value: 'sketch',
              label: 'Sketch Templates',
            },
            {
              value: 'jiaohu',
              label: '组件交互文档',
            },
          ],
        },
      ],
      value: [],
      visible: false,
    }
  },
  methods: {
    handleSerilize (v) {
      if (v.length === 3) {
        this.count = (++this.count) % this.$refs.serilize.length
        this.$refs.serilize[this.count].focus()
      }
    },
    handleSubmit (name) {
      this.$refs.formCustom.validate((valid, errors) => {
        if (valid) {
          console.log('Success!')
        } else {
          console.error('Fail!', errors)
        }
      })
    },
    handleReset () {
      this.$refs.formCustom.resetFields()
    },
    handleAutoSearch (value) {
      console.log(value)
      this.data = value.split('').map((v, index) => {
        return value.slice(0, index + 1);
      });
    },
    handleAutoSelect (value) {
      console.log('select', value);
    },
  },
}
</script>

```

#### doc/demo/form-item-clear-validate.vue

```vue
<template>
  <div class="custom-form-position">
    <mtd-form :rules="ruleCustom1" :model="formCustom1" ref="form1">
      <mtd-form-item label="用户名" prop="username">
        <mtd-input type="text" v-model="formCustom1.username" />
        <span class="demo-mtd-form-helper">只能输入中文</span>
      </mtd-form-item>
      <mtd-form-item label="密码"
                     prop="password"
                     helper="6-16个字符，请使用字母加数字的组合密码，不能包含*@#等符号">
        <mtd-input type="text" v-model="formCustom1.password" />
      </mtd-form-item>
      <mtd-form-item label="URL"
                     prop="url"
                     required
                     ref="itemUrl"
                     helper="请输入URL地址">
        <mtd-input type="text" v-model="formCustom1.url" />
      </mtd-form-item>
      <mtd-form-item>
        <mtd-button type="primary" @click="triggerValidate">登录</mtd-button>
        <mtd-button type="primary" @click="clearValidateUrl">清理URL校验状态</mtd-button>
      </mtd-form-item>
    </mtd-form>
  </div>
</template>

<script>
export default {
  data () {
    const validateUsername = (rule, value, callback) => {
      if (!/^[\u4e00-\u9fa5]+$/.test(value)) {
        callback(new Error('只能输入中文'));
      } else {
        callback();
      }
    };
    const validatePassword = (rule, value, callback) => {
      if (!/^[a-zA-Z0-9]{6,16}$/.test(value)) {
        callback(new Error('只能输入6-16个字符且仅为字母,数字'));
      } else {
        callback();
      }
    };

    return {
      activeName4: 'Tab1',
      formCustom1: {
        username: '',
        password: '',
        url: '',
      },
      ruleCustom1: {
        username: [
          {validator: validateUsername},
        ],
        password: [
          {validator: validatePassword},
        ],
        url: [
          {type: 'url'},
        ],
      },
    };
  },
  methods: {
    triggerValidate() {
      this.$refs.form1.validate((valid, err) => {
        console.log('valid result, pass ?', valid, err)
      }).then(() => {
        console.log('Promise valid result: pass')
      }).catch(e => {
        console.log('Promise valid result: error', e)
      })
    },
    clearValidateUrl() {
      console.log('itemUrl', this.$refs.itemUrl);
      this.$refs.itemUrl.clearValidate()
    }
  }
};
</script>

```

#### doc/demo/form-item-error.vue

```vue
<template>
  <div class="custom-form-position">
    <mtd-form  :model="formCustom1" ref="form1">
      <mtd-form-item label="用户名" prop="username" label-for="username" type="url" validate-status="error" error="这个是自定义错误">
        <mtd-input type="text" v-model="formCustom1.username" id="username" />
        <span class="demo-mtd-form-helper">只能输入中文</span>
      </mtd-form-item>
      <mtd-form-item label="密码"
                     prop="password"
                     label-for="password"
                     helper="6-16个字符，请使用字母加数字的组合密码，不能包含*@#等符号">
        <mtd-input type="text" v-model="formCustom1.password" id="password" />
      </mtd-form-item>
      <mtd-form-item>
        <mtd-button type="primary" @click="triggerValidate">登录</mtd-button>
      </mtd-form-item>
    </mtd-form>
  </div>
</template>

<script>
export default {
  data () {
    return {
      activeName4: 'Tab1',
      formCustom1: {
        username: '',
        password: '',
      },
    };
  },
  methods: {
    triggerValidate() {
      this.$refs.form1.validate()
    }
  }
};
</script>

```

#### doc/demo/form-item-helper-placement.vue

```vue
<template>
  <div class="custom-form-position">
    <mtd-form :rules="ruleCustom1" :model="formCustom1" ref="form1">
      <mtd-form-item
          label="用户名"
          prop="username"
          helper-placement="bottom"
          validate-placement="bottom"
          helper="helper在输入框下面">
        <mtd-input type="text" v-model="formCustom1.username" />
      </mtd-form-item>
      <mtd-form-item label="密码"
                     prop="password"
                     validate-placement="right"
                     helper-placement="right"
                     helper="helper信息在输入框右面">
        <mtd-input type="text" v-model="formCustom1.password" />
      </mtd-form-item>
      <mtd-form-item label="URL"
                     prop="url"
                     required
                     use-html-message
                     helper="默认的helper位置，在下面">
        <mtd-input type="text" v-model="formCustom1.url" />
      </mtd-form-item>
      <mtd-form-item>
        <mtd-button type="primary" @click="triggerValidate">登录</mtd-button>
      </mtd-form-item>
    </mtd-form>
  </div>
</template>

<script>
export default {
  data () {
    const validateUsername = (rule, value, callback) => {
      if (!/^[\u4e00-\u9fa5]+$/.test(value)) {
        callback(new Error('只能输入中文'));
      } else {
        callback();
      }
    };
    const validatePassword = (rule, value, callback) => {
      if (!/^[a-zA-Z0-9]{6,16}$/.test(value)) {
        callback(new Error('只能输入6-16个字符且仅为字母,数字'));
      } else {
        callback();
      }
    };

    return {
      activeName4: 'Tab1',
      formCustom1: {
        username: '',
        password: '',
        url: '',
      },
      ruleCustom1: {
        username: [
          {validator: validateUsername},
        ],
        password: [
          {validator: validatePassword},
        ],
        url: [
          {required: true, message: '默认的校验信息在下面，且被html包裹'},
        ],
      },
    };
  },
  methods: {
    triggerValidate() {
      this.$refs.form1.validate()
    }
  }
};
</script>

```

#### doc/demo/form-item-showmessage.vue

```vue
<template>
  <div class="custom-form-position">
    <mtd-form :rules="ruleCustom1" :model="formCustom1" ref="form1">
      <mtd-form-item label="用户名" prop="username" :show-message="false">
        <mtd-input type="text" v-model="formCustom1.username" />
        <span class="demo-mtd-form-helper">只能输入中文</span>
      </mtd-form-item>
      <mtd-form-item label="密码"
                     prop="password"
                     show-message
                     helper="6-16个字符，请使用字母加数字的组合密码，不能包含*@#等符号">
        <mtd-input type="text" v-model="formCustom1.password" />
      </mtd-form-item>
      <mtd-form-item label="URL"
                     prop="url"
                     required
                     helper="请输入URL地址">
        <mtd-input type="text" v-model="formCustom1.url" />
      </mtd-form-item>
      <mtd-form-item>
        <mtd-button type="primary" @click="triggerValidate">登录</mtd-button>
      </mtd-form-item>
    </mtd-form>
  </div>
</template>

<script>
export default {
  data () {
    const validateUsername = (rule, value, callback) => {
      if (!/^[\u4e00-\u9fa5]+$/.test(value)) {
        callback(new Error('只能输入中文'));
      } else {
        callback();
      }
    };
    const validatePassword = (rule, value, callback) => {
      if (!/^[a-zA-Z0-9]{6,16}$/.test(value)) {
        callback(new Error('只能输入6-16个字符且仅为字母,数字'));
      } else {
        callback();
      }
    };

    return {
      activeName4: 'Tab1',
      formCustom1: {
        username: '',
        password: '',
        url: '',
      },
      ruleCustom1: {
        username: [
          {validator: validateUsername},
        ],
        password: [
          {validator: validatePassword},
        ],
        url: [
          {required: true},
        ],
      },
    };
  },
  methods: {
    triggerValidate() {
      this.$refs.form1.validate()
    }
  }
};
</script>

```

#### doc/demo/form-item-slot.vue

```vue
<template>
  <div class="custom-form-position">
    <mtd-form :rules="ruleCustom1" :model="formCustom1" ref="form1">
      <mtd-form-item label="用户名" prop="username">
        <mtd-input type="text" v-model="formCustom1.username" />
        <template #error="{message}">
          这里是error的插槽：{{message}}
        </template>
      </mtd-form-item>
      <mtd-form-item prop="password"
                     show-message>
        <template v-slot:label>
          这里是label插槽
        </template>
        <mtd-input type="text" v-model="formCustom1.password" />
      </mtd-form-item>
      <mtd-form-item label="URL"
                     prop="url"
                     required>
        <template v-slot:helper>
          这里是helper插槽
        </template>
        <mtd-input type="text" v-model="formCustom1.url" />
      </mtd-form-item>
      <mtd-form-item>
        <mtd-button type="primary" @click="triggerValidate">登录</mtd-button>
      </mtd-form-item>
    </mtd-form>
  </div>
</template>

<script>
export default {
  data () {
    const validateUsername = (rule, value, callback) => {
      if (!/^[\u4e00-\u9fa5]+$/.test(value)) {
        callback(new Error('只能输入中文'));
      } else {
        callback();
      }
    };
    const validatePassword = (rule, value, callback) => {
      if (!/^[a-zA-Z0-9]{6,16}$/.test(value)) {
        callback(new Error('只能输入6-16个字符且仅为字母,数字'));
      } else {
        callback();
      }
    };

    return {
      activeName4: 'Tab1',
      formCustom1: {
        username: '',
        password: '',
        url: '',
      },
      ruleCustom1: {
        username: [
          {validator: validateUsername},
        ],
        password: [
          {validator: validatePassword},
        ],
        url: [
          {required: true},
        ],
      },
    };
  },
  methods: {
    triggerValidate() {
      this.$refs.form1.validate()
    }
  }
};
</script>

```

#### doc/demo/form-size.vue

```vue
<template>
  <div class="custom-form-position">
    <mtd-form :rules="ruleCustom1" :model="formCustom1" ref="form1" :size="value4">
      <mtd-form-item label="用户名" prop="username" :show-message="false">
        <mtd-input type="text" v-model="formCustom1.username" />
        <span class="demo-mtd-form-helper">只能输入中文</span>
      </mtd-form-item>
      <mtd-form-item label="密码"
        prop="password"
        show-message
        helper="6-16个字符，请使用字母加数字的组合密码，不能包含*@#等符号">
        <mtd-input type="text" v-model="formCustom1.password" />
      </mtd-form-item>
      <mtd-form-item label="URL"
        prop="url"
        required
        helper="请输入URL地址">
        <mtd-input type="text" v-model="formCustom1.url" />
      </mtd-form-item>
      <mtd-form-item>
        <mtd-button type="primary" @click="triggerValidate">登录</mtd-button>
      </mtd-form-item>
      <mtd-form-item>
        <mtd-radio-group v-model="value" class="demo-radio-group-vertical">
          <mtd-radio value="beijing">北京</mtd-radio>
          <mtd-radio value="shanghai">上海</mtd-radio>
        </mtd-radio-group>
      </mtd-form-item>
      <mtd-form-item>
        <mtd-radio value="beijing">北京2</mtd-radio>
      </mtd-form-item>
      <mtd-form-item>
        <mtd-input-number v-model="value2" />
      </mtd-form-item>
      <mtd-form-item>
        <mtd-tree-select v-model="value3" :data="options" />
      </mtd-form-item>
    </mtd-form>

    <mtd-select v-model="value4" style="width: 200px" class="my-class" myAttrs="1">
      <mtd-option
        v-for="item in options2"
        :key="item.value"
        :label="item.label"
        :value="item.value"
        :disabled="item.disabled" />
    </mtd-select>
  </div>
</template>

<script>
export default {
  data () {
    const validateUsername = (rule, value, callback) => {
      if (!/^[\u4e00-\u9fa5]+$/.test(value)) {
        callback(new Error('只能输入中文'));
      } else {
        callback();
      }
    };
    const validatePassword = (rule, value, callback) => {
      if (!/^[a-zA-Z0-9]{6,16}$/.test(value)) {
        callback(new Error('只能输入6-16个字符且仅为字母,数字'));
      } else {
        callback();
      }
    };

    return {
      activeName4: 'Tab1',
      formCustom1: {
        username: '',
        password: '',
        url: '',
      },
      ruleCustom1: {
        username: [
          {validator: validateUsername},
        ],
        password: [
          {validator: validatePassword},
        ],
        url: [
          {required: true},
        ],
      },
      value: 'beijing',
      value2: 8,
      value3:  ['ziyuan', 'jiaohu'],
      options: [
        {
          value: 'zhinan',
          label: '指南',
          children: [
            {
              value: 'shejiyuanze',
              label: '设计原则',
              disabled: true,
              children: [
                {
                  value: 'yizhi',
                  label: '一致',
                },
                {
                  value: 'fankui',
                  label: '反馈',
                },
                {
                  value: 'xiaolv',
                  label: '效率',
                },
                {
                  value: 'kekong',
                  label: '可控',
                },
              ],
            },
            {
              value: 'daohang',
              label: '导航',
              children: [
                {
                  value: 'cexiangdaohang',
                  label: '侧向导航',
                },
                {
                  value: 'dingbudaohang',
                  label: '顶部导航',
                },
              ],
            },
          ],
        },
        {
          value: 'zujian',
          label: '组件',
          children: [
            {
              value: 'basic',
              label: 'Basic',
              disabled: true,
              children: [
                {
                  value: 'layout',
                  label: 'Layout 布局',
                },
                {
                  value: 'color',
                  label: 'Color 色彩',
                },
                {
                  value: 'typography',
                  label: 'Typography 字体',
                },
                {
                  value: 'icon',
                  label: 'Icon 图标',
                },
                {
                  value: 'button',
                  label: 'Button 按钮',
                },
              ],
            },
            {
              value: 'form',
              label: 'Form',
              isLeaf: true,
            },
            {
              value: 'data',
              label: 'Data',
              children: [
                {
                  value: 'table',
                  label: 'Table 表格',
                },
                {
                  value: 'tag',
                  label: 'Tag 标签',
                },
                {
                  value: 'progress',
                  label: 'Progress 进度条',
                },
                {
                  value: 'tree',
                  label: 'Tree 树形控件',
                },
                {
                  value: 'pagination',
                  label: 'Pagination 分页',
                },
                {
                  value: 'badge',
                  label: 'Badge 标记',
                },
              ],
            },
            {
              value: 'notice',
              label: 'Notice',
              children: [
                {
                  value: 'alert',
                  label: 'Alert 警告',
                },
                {
                  value: 'loading',
                  label: 'Loading 加载',
                },
                {
                  value: 'message',
                  label: 'Message 消息提示',
                },
                {
                  value: 'message-box',
                  label: 'MessageBox 弹框',
                },
                {
                  value: 'notification',
                  label: 'Notification 通知',
                },
              ],
            },
            {
              value: 'navigation',
              label: 'Navigation',
              children: [
                {
                  value: 'menu',
                  label: 'NavMenu 导航菜单',
                },
                {
                  value: 'tabs',
                  label: 'Tabs 标签页',
                },
                {
                  value: 'breadcrumb',
                  label: 'Breadcrumb 面包屑',
                },
                {
                  value: 'dropdown',
                  label: 'Dropdown 下拉菜单',
                },
                {
                  value: 'steps',
                  label: 'Steps 步骤条',
                },
              ],
            },
            {
              value: 'others',
              label: 'Others',
              children: [
                {
                  value: 'dialog',
                  label: 'Dialog 对话框',
                },
                {
                  value: 'tooltip',
                  label: 'Tooltip 文字提示',
                },
                {
                  value: 'popover',
                  label: 'Popover 弹出框',
                },
                {
                  value: 'card',
                  label: 'Card 卡片',
                },
                {
                  value: 'carousel',
                  label: 'Carousel 走马灯',
                },
                {
                  value: 'collapse',
                  label: 'Collapse 折叠面板',
                },
              ],
            },
          ],
        },
        {
          value: 'ziyuan',
          label: '资源',
          children: [
            {
              value: 'axure',
              label: 'Axure Components',
            },
            {
              value: 'sketch',
              label: 'Sketch Templates',
            },
            {
              value: 'jiaohu',
              label: '组件交互文档',
            },
          ],
        },
      ],
      options2: [{
        value: 'small',
        label: 'small',
      }, {
        value: undefined,
        label: '不设置',
      }, {
        value: 'large',
        label: 'large',
      }],
      value4: 'small',
    };
  },
  methods: {
    triggerValidate() {
      this.$refs.form1.validate()
    }
  }
};
</script>

```

#### doc/demo/label-for.vue

```vue
<template>
  <div class="custom-form-position">
    <mtd-form :rules="ruleCustom1" :model="formCustom1" ref="form1">
      <mtd-form-item label="用户名" prop="username" label-for="username">
        <mtd-input type="text" v-model="formCustom1.username" id="username" />
        <span class="demo-mtd-form-helper">只能输入中文</span>
      </mtd-form-item>
      <mtd-form-item label="密码"
                     prop="password"
                     label-for="password"
                     helper="6-16个字符，请使用字母加数字的组合密码，不能包含*@#等符号">
        <mtd-input type="text" v-model="formCustom1.password" id="password" />
      </mtd-form-item>
      <mtd-form-item>
        <mtd-button type="primary" @click="triggerValidate">登录</mtd-button>
      </mtd-form-item>
    </mtd-form>
  </div>
</template>

<script>
export default {
  data () {
    const validateUsername = (rule, value, callback) => {
      if (!/^[\u4e00-\u9fa5]+$/.test(value)) {
        callback(new Error('只能输入中文'));
      } else {
        callback();
      }
    };
    const validatePassword = (rule, value, callback) => {
      if (!/^[a-zA-Z0-9]{6,16}$/.test(value)) {
        callback(new Error('只能输入6-16个字符且仅为字母,数字'));
      } else {
        callback();
      }
    };

    return {
      activeName4: 'Tab1',
      formCustom1: {
        username: '',
        password: '',
      },
      ruleCustom1: {
        username: [
          {validator: validateUsername, trigger: 'change'},
        ],
        password: [
          {validator: validatePassword, trigger: 'change'},
        ],
      },
    };
  },
  methods: {
    triggerValidate() {
      this.$refs.form1.validate()
    }
  }
};
</script>

```

#### doc/demo/label-position-inherit.vue

```vue
<template>
  <div class="custom-form-position">
    <div>form label-position居左</div>
    <mtd-form label-position="left">
      <mtd-form-item label="继承自form">
        <mtd-input type="text" v-model="text" placeholder="请输入"/>
      </mtd-form-item>
      <mtd-form-item label="自定义居右" label-position="right">
        <mtd-input type="text" v-model="fields.value" placeholder="请输入"/>
      </mtd-form-item>
    </mtd-form>
  </div>
</template>

<script>
export default {
  data () {
    return {
      text: '',
      fields: {
        value: '',
      },
      activeName4: 'Tab1'
    };
  },
};
</script>

```

#### doc/demo/label-position-top.vue

```vue
<template>
  <div class="demonstration-content">
    <mtd-tabs v-model="activeName4" type="text" size="small">
      <mtd-tab-pane label="水平" value="Tab1">
        <mtd-form style="width: 600px">
          <mtd-form-item label="表单标题1">
            <mtd-input type="text" v-model="text" placeholder="请输入"/>
          </mtd-form-item>
          <mtd-form-item label="表单标题2">
            <mtd-input type="text" v-model="fields.value" placeholder="请输入"/>
          </mtd-form-item>
          <mtd-form-item>
            <mtd-button type="primary">提交</mtd-button>
          </mtd-form-item>
        </mtd-form>
      </mtd-tab-pane>
      <mtd-tab-pane label="垂直" value="Tab2">
        <mtd-form label-position="top" style="width: 600px">
          <mtd-form-item label="表单标题1">
            <mtd-input type="text" v-model="text" placeholder="请输入"/>
          </mtd-form-item>
          <mtd-form-item label="表单标题2">
            <mtd-input type="text" v-model="fields.value" placeholder="请输入"/>
          </mtd-form-item>
          <mtd-form-item>
            <mtd-button type="primary">提交</mtd-button>
          </mtd-form-item>
        </mtd-form>
      </mtd-tab-pane>
      <mtd-tab-pane label="行内" value="Tab3">
        <mtd-form inline :label-width="80" style="width: 600px">
          <mtd-form-item label="表单标题1">
            <mtd-input type="text" v-model="text" placeholder="请输入"/>
          </mtd-form-item>
          <mtd-form-item label="表单标题2">
            <mtd-input type="text" v-model="fields.value" placeholder="请输入"/>
          </mtd-form-item>
          <mtd-form-item>
            <mtd-button type="primary" style="margin-left:12px">提交</mtd-button>
          </mtd-form-item>
        </mtd-form>
      </mtd-tab-pane>
    </mtd-tabs>
  </div>
</template>

<script>
export default {
  data () {
    return {
      text: '',
      fields: {
        value: '',
      },
      activeName4: 'Tab1'
    };
  },
};
</script>

```

#### doc/demo/label-suffix.vue

```vue
<template>
  <div class="custom-form-label-suffix">
    <div>
      <mtd-form label-position="right">
        <mtd-form-item label="表单标题1">
          <mtd-input type="text" v-model="text1" placeholder="请输入"/>
        </mtd-form-item>
        <mtd-form-item label="表单标题2">
          <mtd-input type="text" v-model="text2" placeholder="请输入"/>
        </mtd-form-item>
      </mtd-form>
    </div>
    <div>
      <mtd-form label-position="right" label-suffix="[我是label-suffix的内容]">
        <mtd-form-item label="A">
          <mtd-input type="text" v-model="text3" placeholder="请输入"/>
        </mtd-form-item>
        <mtd-form-item label="B">
          <mtd-input type="text" v-model="text4" placeholder="请输入"/>
        </mtd-form-item>
      </mtd-form>
    </div>
  </div>
</template>

<script>
export default {
  data () {
    return {
      text1: '',
      text2: '',
      text3: '',
      text4: '',
    };
  },
};
</script>
<style lang="scss">
.custom-form-label-suffix {
  display: flex;
}
</style>

```

#### doc/demo/label-width.vue

```vue
<template>
  <div class="custom-form-position">
    <mtd-tabs v-model="activeName4">
      <mtd-tab-pane label="form label-width 200px" value="Tab1">
        <mtd-form :rules="ruleCustom1" :model="formCustom1" ref="form1" :label-width="200">
          <mtd-form-item label="label-width 150" prop="username" :label-width="150">
            <mtd-input type="text" v-model="formCustom1.username" />
            <span class="demo-mtd-form-helper">只能输入中文</span>
          </mtd-form-item>
          <mtd-form-item label="不设置label-width"
                         prop="password"
                         helper="6-16个字符，请使用字母加数字的组合密码，不能包含*@#等符号">
            <mtd-input type="text" v-model="formCustom1.password" />
          </mtd-form-item>
          <mtd-form-item label="label-width为0"
                         :label-width="0"
                         prop="url">
            <mtd-input type="text" v-model="formCustom1.password" />
          </mtd-form-item>
          <mtd-form-item>
            <mtd-button type="primary" @click="triggerValidate">登录</mtd-button>
          </mtd-form-item>
        </mtd-form>
      </mtd-tab-pane>
      <mtd-tab-pane label="form 未设置label-width">
        <mtd-form :rules="ruleCustom1" :model="formCustom1" ref="form1">
          <mtd-form-item label="label-width 150px" prop="username" :label-width="150">
            <mtd-input type="text" v-model="formCustom1.username" />
            <span class="demo-mtd-form-helper">只能输入中文</span>
          </mtd-form-item>
          <mtd-form-item label="label-width 0"
                         :label-width="0"
                         prop="password"
                         helper="6-16个字符，请使用字母加数字的组合密码，不能包含*@#等符号">
            <mtd-input type="text" v-model="formCustom1.password" />
          </mtd-form-item>
          <mtd-form-item label="未设置label-width"
                         prop="url">
            <mtd-input type="text" v-model="formCustom1.password" />
          </mtd-form-item>
          <mtd-form-item>
            <mtd-button type="primary" @click="triggerValidate">登录</mtd-button>
          </mtd-form-item>
        </mtd-form>
      </mtd-tab-pane>
    </mtd-tabs>
  </div>
</template>

<script>
export default {
  data () {
    const validateUsername = (rule, value, callback) => {
      if (!/^[\u4e00-\u9fa5]+$/.test(value)) {
        callback(new Error('只能输入中文'));
      } else {
        callback();
      }
    };
    const validatePassword = (rule, value, callback) => {
      if (!/^[a-zA-Z0-9]{6,16}$/.test(value)) {
        callback(new Error('只能输入6-16个字符且仅为字母,数字'));
      } else {
        callback();
      }
    };
    return {
      activeName4: 'Tab1',
      formCustom1: {
        username: '',
        password: '',
      },
      ruleCustom1: {
        username: [
          {validator: validateUsername, trigger: 'change'},
        ],
        password: [
          {validator: validatePassword, trigger: 'change'},
        ],
      },
    };
  },
  methods: {
    triggerValidate() {
      this.$refs.form1.validate()
    }
  }
};
</script>

```

#### doc/demo/required-reactive.vue

```vue
<template>
  <mtd-form  :model="dynamicValidateForm" ref="form">
    <mtd-form-item label="文案1" prop="text1" :required="required">
      <mtd-input type="text" v-model="dynamicValidateForm.text1" />
    </mtd-form-item>
    <mtd-form-item label="文案2" prop="text2" :rules="rules">
      <mtd-input type="text" v-model="dynamicValidateForm.text2" />
    </mtd-form-item>
    <mtd-form-item label="文案3" prop="text3" :rules="rules" :required="required">
      <mtd-input type="text" v-model="dynamicValidateForm.text3" />
    </mtd-form-item>
    <mtd-form-item label="文案4:无prop" :required="required">
      <mtd-input type="text" v-model="dynamicValidateForm.text4" />
    </mtd-form-item>
    <mtd-button @click="handlerClick">change required, now is {{required}}</mtd-button>
    <mtd-button type="primary" @click="handleValidate">validate</mtd-button>
  </mtd-form>
</template>

<script>
export default {
  data () {
    return {
      required: true,
      dynamicValidateForm: {
        text1: '',
        text2: '',
        text3: '',
        text4: '',
      }
    };
  },
  computed: {
    rules() {
      return [{
        required: this.required
      }]
    }
  },
  methods: {
    handlerClick() {
      this.required = !this.required
    },
    handleValidate() {
      this.$refs.form.validate()
    },
  }
};
</script>

```

#### doc/demo/reset-fields.vue

```vue
<template>
  <div class="custom-form-position">
    <mtd-form :rules="ruleCustom1" :model="formCustom1" ref="form1">
      <mtd-form-item label="用户名" prop="username">
        <mtd-input type="text" v-model="formCustom1.username" />
        <span class="demo-mtd-form-helper">只能输入中文</span>
      </mtd-form-item>
      <mtd-form-item label="密码"
                     prop="password"
                     helper="6-16个字符，请使用字母加数字的组合密码，不能包含*@#等符号">
        <mtd-input type="text" v-model="formCustom1.password" />
      </mtd-form-item>
      <mtd-form-item label="URL"
                     prop="url"
                     required
                     helper="请输入URL地址">
        <mtd-input type="text" v-model="formCustom1.url" />
      </mtd-form-item>
      <mtd-form-item>
        <mtd-button type="primary" @click="triggerValidate">登录</mtd-button>
        <mtd-button type="primary" @click="resetTargetField">重置用户名</mtd-button>
        <mtd-button type="primary" @click="resetAllFields">重置所有输入框</mtd-button>
        <mtd-button type="primary" @click="resetMultipleFields">重置用户名和密码输入框</mtd-button>
      </mtd-form-item>
    </mtd-form>
  </div>
</template>

<script>
export default {
  data () {
    const validateUsername = (rule, value, callback) => {
      if (!/^[\u4e00-\u9fa5]+$/.test(value)) {
        callback(new Error('只能输入中文'));
      } else {
        callback();
      }
    };
    const validatePassword = (rule, value, callback) => {
      if (!/^[a-zA-Z0-9]{6,16}$/.test(value)) {
        callback(new Error('只能输入6-16个字符且仅为字母,数字'));
      } else {
        callback();
      }
    };

    return {
      activeName4: 'Tab1',
      formCustom1: {
        username: '',
        password: '',
        url: '',
      },
      ruleCustom1: {
        username: [
          {validator: validateUsername},
        ],
        password: [
          {validator: validatePassword},
        ],
        url: [
          {type: 'url'},
        ],
      },
    };
  },
  methods: {
    triggerValidate() {
      this.$refs.form1.validate((valid, err) => {
        console.log('valid result, pass ?', valid, err)
      }).then(() => {
        console.log('Promise valid result: pass')
      }).catch(e => {
        console.log('Promise valid result: error', e)
      })
    },
    resetTargetField() {
      this.$refs.form1.resetFields('username')
    },
    resetAllFields() {
      this.$refs.form1.resetFields()
    },
    resetMultipleFields() {
      this.$refs.form1.resetFields(['username', 'password'])
    },
  }
};
</script>

```

#### doc/demo/rule-trigger-blur.vue

```vue
<template>
  <div class="custom-form-position">
    <mtd-form :rules="ruleCustom1" :model="formCustom1" ref="form1">
      <mtd-form-item label="用户名" prop="username">
        <mtd-input type="text" v-model="formCustom1.username" />
        <span class="demo-mtd-form-helper">只能输入中文</span>
      </mtd-form-item>
      <mtd-form-item label="密码"
                     prop="password"
                     helper="6-16个字符，请使用字母加数字的组合密码，不能包含*@#等符号">
        <mtd-input type="text" v-model="formCustom1.password" />
      </mtd-form-item>
      <mtd-form-item>
        <mtd-button type="primary" @click="triggerValidate">登录</mtd-button>
      </mtd-form-item>
    </mtd-form>
  </div>
</template>

<script>
export default {
  data () {
    const validateUsername = (rule, value, callback) => {
      if (!/^[\u4e00-\u9fa5]+$/.test(value)) {
        callback(new Error('只能输入中文'));
      } else {
        callback();
      }
    };
    const validatePassword = (rule, value, callback) => {
      if (!/^[a-zA-Z0-9]{6,16}$/.test(value)) {
        callback(new Error('只能输入6-16个字符且仅为字母,数字'));
      } else {
        callback();
      }
    };

    return {
      activeName4: 'Tab1',
      formCustom1: {
        username: '',
        password: '',
      },
      ruleCustom1: {
        username: [
          {validator: validateUsername, trigger: 'blur'},
        ],
        password: [
          {validator: validatePassword, trigger: 'blur'},
        ],
      },
    };
  },
  methods: {
    triggerValidate() {
      this.$refs.form1.validate()
    }
  }
};
</script>

```

#### doc/demo/rule-trigger-change.vue

```vue
<template>
  <div class="custom-form-position">
    <mtd-form :rules="ruleCustom1" :model="formCustom1" ref="form1">
      <mtd-form-item label="用户名" prop="username">
        <mtd-input type="text" v-model="formCustom1.username" />
        <span class="demo-mtd-form-helper">只能输入中文</span>
      </mtd-form-item>
      <mtd-form-item label="密码"
                     prop="password"
                     helper="6-16个字符，请使用字母加数字的组合密码，不能包含*@#等符号">
        <mtd-input type="text" v-model="formCustom1.password" />
      </mtd-form-item>
      <mtd-form-item>
        <mtd-button type="primary" @click="triggerValidate">登录</mtd-button>
      </mtd-form-item>
    </mtd-form>
  </div>
</template>

<script>
export default {
  data () {
    const validateUsername = (rule, value, callback) => {
      if (!/^[\u4e00-\u9fa5]+$/.test(value)) {
        callback(new Error('只能输入中文'));
      } else {
        callback();
      }
    };
    const validatePassword = (rule, value, callback) => {
      if (!/^[a-zA-Z0-9]{6,16}$/.test(value)) {
        callback(new Error('只能输入6-16个字符且仅为字母,数字'));
      } else {
        callback();
      }
    };

    return {
      activeName4: 'Tab1',
      formCustom1: {
        username: '',
        password: '',
      },
      ruleCustom1: {
        username: [
          {validator: validateUsername, trigger: 'change'},
        ],
        password: [
          {validator: validatePassword, trigger: 'change'},
        ],
      },
    };
  },
  methods: {
    triggerValidate() {
      this.$refs.form1.validate()
    }
  }
};
</script>

```

#### doc/demo/rule-trigger-default.vue

```vue
<template>
  <div class="custom-form-position">
    <mtd-form :rules="ruleCustom1" :model="formCustom1" ref="form1">
      <mtd-form-item label="用户名" prop="username">
        <mtd-input type="text" v-model="formCustom1.username" />
        <span class="demo-mtd-form-helper">只能输入中文</span>
      </mtd-form-item>
      <mtd-form-item label="密码"
                     prop="password"
                     helper="6-16个字符，请使用字母加数字的组合密码，不能包含*@#等符号">
        <mtd-input type="text" v-model="formCustom1.password" />
      </mtd-form-item>
      <mtd-form-item label="URL"
                     prop="url"
                     required
                     helper="请输入URL地址">
        <mtd-input type="text" v-model="formCustom1.url" />
      </mtd-form-item>
      <mtd-form-item>
        <mtd-button type="primary" @click="triggerValidate">登录</mtd-button>
        <mtd-button type="primary" @click="validateField">校验URL</mtd-button>
      </mtd-form-item>
    </mtd-form>
  </div>
</template>

<script>
export default {
  data () {
    const validateUsername = (rule, value, callback) => {
      if (!/^[\u4e00-\u9fa5]+$/.test(value)) {
        callback(new Error('只能输入中文'));
      } else {
        callback();
      }
    };
    const validatePassword = (rule, value, callback) => {
      if (!/^[a-zA-Z0-9]{6,16}$/.test(value)) {
        callback(new Error('只能输入6-16个字符且仅为字母,数字'));
      } else {
        callback();
      }
    };

    return {
      activeName4: 'Tab1',
      formCustom1: {
        username: '',
        password: '',
        url: '',
      },
      ruleCustom1: {
        username: [
          {validator: validateUsername},
        ],
        password: [
          {validator: validatePassword},
        ],
        url: [
          {type: 'url'},
        ],
      },
    };
  },
  methods: {
    triggerValidate() {
      this.$refs.form1.validate((valid, err) => {
        console.log('valid result, pass ?', valid, err)
      }).then(() => {
        console.log('Promise valid result: pass')
      }).catch(e => {
        console.log('Promise valid result: error', e)
      })
    },
    validateField() {
      this.$refs.form1.validateField('url', err => {
        console.log('validate url err:', err)
      })
    }
  }
};
</script>

```

#### doc/demo/scroll-to-first-error.vue

```vue
<template>
  <div class="custom-form-position">
    <mtd-form :rules="ruleCustom1" :model="formCustom1" ref="form1" scroll-to-first-error>
      <mtd-form-item>
        <mtd-button type="primary" @click="triggerValidate">登录</mtd-button>
      </mtd-form-item>
      <mtd-form-item label="用户名" prop="username">
        <mtd-input type="text" v-model="formCustom1.username" />
        <span class="demo-mtd-form-helper">只能输入中文</span>
      </mtd-form-item>
      <div style="height: 2000px; background: #eee"></div>
      <mtd-form-item label="密码"
                     prop="password"
                     helper="6-16个字符，请使用字母加数字的组合密码，不能包含*@#等符号">
        <mtd-input type="text" v-model="formCustom1.password" />
      </mtd-form-item>
      <mtd-form-item>
        <mtd-button type="primary" @click="triggerValidate">登录</mtd-button>
      </mtd-form-item>
    </mtd-form>
  </div>
</template>

<script>
export default {
  data () {
    const validateUsername = (rule, value, callback) => {
      if (!/^[\u4e00-\u9fa5]+$/.test(value)) {
        callback(new Error('只能输入中文'));
      } else {
        callback();
      }
    };
    const validatePassword = (rule, value, callback) => {
      if (!/^[a-zA-Z0-9]{6,16}$/.test(value)) {
        callback(new Error('只能输入6-16个字符且仅为字母,数字'));
      } else {
        callback();
      }
    };
    return {
      activeName4: 'Tab1',
      formCustom1: {
        username: '',
        password: '',
      },
      ruleCustom1: {
        username: [
          {validator: validateUsername, trigger: 'change'},
        ],
        password: [
          {validator: validatePassword, trigger: 'change'},
        ],
      },
    };
  },
  methods: {
    triggerValidate() {
      this.$refs.form1.validate()
    },
    changeRule() {
      this.ruleCustom1.username.push({
        required: true,
        trigger: 'change',
      })
      // this.ruleCustom1 = {
      //   username1: [],
      //   password1: [],
      // }
      // console.log('push rule', this.ruleCustom1)
    }
  }
};
</script>

```

#### doc/demo/use-html-message.vue

```vue
<template>
  <div class="custom-form-position">
    <mtd-form :rules="ruleCustom1" :model="formCustom1" ref="form1">
      <mtd-form-item label="use-html-message=true" prop="username" use-html-message>
        <mtd-input type="text" v-model="formCustom1.username" />
      </mtd-form-item>
      <mtd-form-item label="use-html-message=false"
                     prop="password"
                     validate-placement="bottom"
                     helper="6-16个字符，请使用字母加数字的组合密码，不能包含*@#等符号">
        <mtd-input type="text" v-model="formCustom1.password" />
      </mtd-form-item>
      <mtd-form-item>
        <mtd-button type="primary" @click="triggerValidate">登录</mtd-button>
      </mtd-form-item>
    </mtd-form>
  </div>
</template>

<script>
export default {
  data () {
    const validateUsername = (rule, value, callback) => {
      if (!/^[\u4e00-\u9fa5]+$/.test(value)) {
        callback(new Error('只能输入中文'));
      } else {
        callback();
      }
    };
    const validatePassword = (rule, value, callback) => {
      if (!/^[a-zA-Z0-9]{6,16}$/.test(value)) {
        callback(new Error('只能输入6-16个字符且仅为字母,数字'));
      } else {
        callback();
      }
    };

    return {
      activeName4: 'Tab1',
      formCustom1: {
        username: '',
        password: '',
      },
      ruleCustom1: {
        username: [
          {validator: validateUsername, trigger: 'change'},
        ],
        password: [
          {validator: validatePassword, trigger: 'change'},
        ],
      },
    };
  },
  methods: {
    triggerValidate() {
      this.$refs.form1.validate()
    }
  }
};
</script>

```

#### doc/demo/validate-on-rule-change.vue

```vue
<template>
  <div class="custom-form-position">
    <mtd-form :rules="ruleCustom1" :model="formCustom1" ref="form1" validate-on-rule-change>
      <mtd-form-item label="用户名" prop="username">
        <mtd-input type="text" v-model="formCustom1.username" />
        <span class="demo-mtd-form-helper">只能输入中文</span>
      </mtd-form-item>
      <mtd-form-item label="密码"
                     prop="password"
                     helper="6-16个字符，请使用字母加数字的组合密码，不能包含*@#等符号">
        <mtd-input type="text" v-model="formCustom1.password" />
      </mtd-form-item>
      <mtd-form-item>
        <mtd-button type="primary" @click="triggerValidate">登录</mtd-button>
        <mtd-button type="primary" @click="changeRule">增加rule</mtd-button>
        <mtd-button type="primary" @click="$refs.form1.clearValidate()">清空校验状态</mtd-button>
      </mtd-form-item>
    </mtd-form>
  </div>
</template>

<script>
export default {
  data () {
    const validateUsername = (rule, value, callback) => {
      if (!/^[\u4e00-\u9fa5]+$/.test(value)) {
        callback(new Error('只能输入中文'));
      } else {
        callback();
      }
    };
    const validatePassword = (rule, value, callback) => {
      if (!/^[a-zA-Z0-9]{6,16}$/.test(value)) {
        callback(new Error('只能输入6-16个字符且仅为字母,数字'));
      } else {
        callback();
      }
    };
    return {
      activeName4: 'Tab1',
      formCustom1: {
        username: '',
        password: '',
      },
      ruleCustom1: {
        username: [
          {validator: validateUsername, trigger: 'change'},
        ],
        password: [
          {validator: validatePassword, trigger: 'change'},
        ],
      },
    };
  },
  methods: {
    triggerValidate() {
      this.$refs.form1.validate()
    },
    changeRule() {
      this.ruleCustom1.username.push({
        required: true,
        trigger: 'change',
      })
      // this.ruleCustom1 = {
      //   username1: [],
      //   password1: [],
      // }
      // console.log('push rule', this.ruleCustom1)
    }
  }
};
</script>

```

#### doc/demo/validate-placement.vue

```vue
<template>
  <div class="custom-form-position">
    <mtd-form :rules="ruleCustom1" :model="formCustom1" ref="form1">
      <mtd-form-item label="校验信息在右侧" prop="username" validate-placement="right">
        <mtd-input type="text" v-model="formCustom1.username" />
      </mtd-form-item>
      <mtd-form-item label="校验信息在底部"
                     prop="password"
                     validate-placement="bottom"
                     helper="6-16个字符，请使用字母加数字的组合密码，不能包含*@#等符号">
        <mtd-input type="text" v-model="formCustom1.password" />
      </mtd-form-item>
      <mtd-form-item>
        <mtd-button type="primary" @click="triggerValidate">登录</mtd-button>
      </mtd-form-item>
    </mtd-form>
  </div>
</template>

<script>
export default {
  data () {
    const validateUsername = (rule, value, callback) => {
      if (!/^[\u4e00-\u9fa5]+$/.test(value)) {
        callback(new Error('只能输入中文'));
      } else {
        callback();
      }
    };
    const validatePassword = (rule, value, callback) => {
      if (!/^[a-zA-Z0-9]{6,16}$/.test(value)) {
        callback(new Error('只能输入6-16个字符且仅为字母,数字'));
      } else {
        callback();
      }
    };

    return {
      activeName4: 'Tab1',
      formCustom1: {
        username: '',
        password: '',
      },
      ruleCustom1: {
        username: [
          {validator: validateUsername, trigger: 'change'},
        ],
        password: [
          {validator: validatePassword, trigger: 'change'},
        ],
      },
    };
  },
  methods: {
    triggerValidate() {
      this.$refs.form1.validate()
    }
  }
};
</script>

```

#### doc/disabled.vue

```vue
<template>
  <mtd-form :model="formCustom" labelPosition="right" disabled>
    <mtd-form-item label="姓名" prop="name">
      <mtd-input type="text" v-model="formCustom.name" placeholder="请输入需求名称" style="width: 360px;">
      </mtd-input>
    </mtd-form-item>
    <mtd-form-item label="性别" prop="sex">
      <mtd-radio-group v-model="formCustom.sex">
        <mtd-radio value="1">男性</mtd-radio>
        <mtd-radio value="0">女性</mtd-radio>
      </mtd-radio-group>
    </mtd-form-item>
    <mtd-form-item label="城市" prop="city">
      <mtd-select placeholder="请选择" v-model="formCustom.city" style="width: 360px;">
        <mtd-option :key="item.value" v-for="item in selectOptions" :value="item.value" :label="item.label" />
      </mtd-select>
    </mtd-form-item>
    <mtd-form-item label="详细地址" prop="address">
      <mtd-select placeholder="请选择" v-model="formCustom.address" style="width: 360px;">
        <mtd-option :key="item.value" v-for="item in selectOptions" :value="item.value" :label="item.label" />
      </mtd-select>
    </mtd-form-item>
    <mtd-form-item label="日期" prop="date">
      <mtd-date-picker type="date" placeholder="请选择时间" v-model="formCustom.date" style="width: 160px;" />
    </mtd-form-item>
    <mtd-form-item label="偏好" prop="preference">
      <mtd-checkbox-group v-model="formCustom.preference">
        <mtd-checkbox value="option1">选项1</mtd-checkbox>
        <mtd-checkbox value="option2">选项2</mtd-checkbox>
        <mtd-checkbox value="option3">选项3</mtd-checkbox>
      </mtd-checkbox-group>
    </mtd-form-item>
    <mtd-form-item label="开关" prop="isOpen">
      <mtd-switch v-model="formCustom.isOpen" />
    </mtd-form-item>
  </mtd-form>
</template>

<script>

export default {
  data() {
    return {
      formCustom: {},
      selectOptions: [
        { label: '分类一', value: '1' },
        { label: '分类二', value: '2' },
        { label: '分类三', value: '3' }
      ]
    }
  },
}
</script>

```

#### doc/dynamic.vue

```vue
<template>
  <mtd-form :model="dynamicValidateForm" ref="dynamicValidateForm"
    :label-width="100" class="demo-dynamic" inline>
    <div v-for="(info, index) in dynamicValidateForm.users" :key="index">
      <mtd-form-item
        :label="`用户${index+1}`"
        :prop="`users.${index+1}.name`"
      >
        <mtd-input v-model="info.name" placeholder="姓名" />
      </mtd-form-item>
      <mtd-form-item>
        <mtd-input v-model="info.phone" placeholder="联系方式" />
      </mtd-form-item>
      <mtd-form-item>
        <mtd-icon-button icon="delete-o" style="margin-left: 8px;color: rgba(0,0,0,0.9)" @click.prevent="removeUser(index)" />
      </mtd-form-item>
      <br>
    </div>
    <div class="custom-form-dynamic-btn-group">
      <mtd-button ghost type="primary" @click="addUser">添加新用户</mtd-button>
    </div>
  </mtd-form>
</template>
<script>
import MtdFormItem from "@components/form-item/form-item";

export default {
  components: {MtdFormItem},
  data () {
    return {
      dynamicValidateForm: {
        users: [{
          name: '',
          phone: '',
        }],
      },
    };
  },
  methods: {
    removeUser (index) {
      this.dynamicValidateForm.users.splice(index, 1);
    },
    addUser () {
      this.dynamicValidateForm.users.push({
        value: '',
        key: Date.now(),
      });
    },
  },
};
</script>
<style lang="scss" scoped>
  .custom-form-dynamic-btn-group {
    width: 530px;
    display: flex;
    & > button {
      margin-left: 100px;
    }
  }
</style>

```

#### doc/label-position.vue

```vue
<template>
  <div class="demonstration-content">
    <mtd-tabs v-model="activeName4" type="text" size="small">
      <mtd-tab-pane label="右对齐" value="Tab1">
        <mtd-form label-position="right">
          <mtd-form-item label="表单标题">
            <mtd-input type="text" v-model="text" placeholder="请输入"/>
          </mtd-form-item>
          <mtd-form-item label="表单长标题">
            <mtd-input type="text" v-model="fields.value" placeholder="请输入"/>
          </mtd-form-item>
        </mtd-form>
      </mtd-tab-pane>
      <mtd-tab-pane label="左对齐" value="Tab2">
        <mtd-form label-position="left">
          <mtd-form-item label="表单标题">
            <mtd-input type="text" v-model="text" placeholder="请输入"/>
          </mtd-form-item>
          <mtd-form-item label="表单长标题">
            <mtd-input type="text" v-model="fields.value" placeholder="请输入"/>
          </mtd-form-item>
        </mtd-form>
      </mtd-tab-pane>
      <mtd-tab-pane label="顶对齐" value="Tab3">
        <mtd-form label-position="top">
          <mtd-form-item label="表单标题">
            <mtd-input type="text" v-model="text" placeholder="请输入"/>
          </mtd-form-item>
          <mtd-form-item label="表单长标题">
            <mtd-input type="text" v-model="fields.value" placeholder="请输入"/>
          </mtd-form-item>
        </mtd-form>
      </mtd-tab-pane>
    </mtd-tabs>
  </div>
</template>

<script>
export default {
  data () {
    return {
      text: '',
      fields: {
        value: '',
      },
      activeName4: 'Tab1'
    };
  },
};
</script>

```

#### doc/label-wrap.vue

```vue
<template>
  <div class="custom-form-position">
    <mtd-form label-position="right" inline>
      <mtd-form-item label="表单标题">
        <mtd-input type="text" v-model="text" placeholder="请输入"/>
      </mtd-form-item>
      <mtd-form-item label="表单标题">
        <mtd-input type="text" v-model="fields.value" placeholder="请输入"/>
      </mtd-form-item>
      <br>
      <mtd-form-item label="表单标题特别特别长">
        <mtd-input type="text" v-model="text" placeholder="请输入"/>
      </mtd-form-item>
      <mtd-form-item label="表单标题特别特别长">
        <mtd-input type="text" v-model="fields.value" placeholder="请输入"/>
      </mtd-form-item>
    </mtd-form>
  </div>
</template>

<script>
export default {
  data () {
    return {
      text: '',
      fields: {
        value: '',
      },
    };
  },
};
</script>

```

#### doc/linkage.vue

```vue
<template>
  <mtd-form :model="formCustom" labelPosition="right">
    <mtd-form-item label="操作类型" prop="type">
      <mtd-radio-group v-model="formCustom.type">
        <mtd-radio value="add">新建需求</mtd-radio>
        <mtd-radio value="have">已有需求</mtd-radio>
      </mtd-radio-group>
    </mtd-form-item>
    <template v-if="formCustom.type === 'add'">
      <mtd-form-item label="需求名称" prop="name">
        <mtd-input type="text" v-model="formCustom.name" placeholder="请输入需求名称" style="width: 200px;">
        </mtd-input>
      </mtd-form-item>
      <mtd-form-item label="设计目标" prop="target">
        <mtd-input type="text" v-model="formCustom.target" placeholder="请输入设计目标" style="width: 200px;">
        </mtd-input>
      </mtd-form-item>
      <mtd-form-item label="交付时间" prop="date">
        <mtd-date-picker type="date" placeholder="请选择交付时间" v-model="formCustom.date" style="width: 200px;" />
      </mtd-form-item>
    </template>
    <mtd-form-item label="历史需求" prop="history" v-else>
      <mtd-select placeholder="请选择" v-model="formCustom.history" style="width: 200px;">
        <mtd-option :key="item.value" v-for="item in selectOptions" :value="item.value" :label="item.label" />
      </mtd-select>
    </mtd-form-item>
  </mtd-form>
</template>
      
<script>

export default {
  data() {
    return {
      formCustom: {
        type: 'add'
      },
      selectOptions: [
        { label: '分类一', value: '1' },
        { label: '分类二', value: '2' },
        { label: '分类三', value: '3' }
      ]
    }
  },
}
</script>
      
```

#### doc/position.vue

```vue
<template>
  <div class="demonstration-content">
    <mtd-tabs v-model="activeName4" type="text" size="small">
      <mtd-tab-pane label="水平" value="Tab1">
        <mtd-form>
          <mtd-form-item label="表单标题1">
            <mtd-input type="text" v-model="text" placeholder="请输入"/>
          </mtd-form-item>
          <mtd-form-item label="表单标题2">
            <mtd-input type="text" v-model="fields.value" placeholder="请输入"/>
          </mtd-form-item>
          <mtd-form-item>
            <mtd-button type="primary">提交</mtd-button>
          </mtd-form-item>
        </mtd-form>
      </mtd-tab-pane>
      <mtd-tab-pane label="垂直" value="Tab2">
        <mtd-form label-position="top">
          <mtd-form-item label="表单标题1">
            <mtd-input type="text" v-model="text" placeholder="请输入"/>
          </mtd-form-item>
          <mtd-form-item label="表单标题2">
            <mtd-input type="text" v-model="fields.value" placeholder="请输入"/>
          </mtd-form-item>
          <mtd-form-item>
            <mtd-button type="primary">提交</mtd-button>
          </mtd-form-item>
        </mtd-form>
      </mtd-tab-pane>
      <mtd-tab-pane label="行内" value="Tab3">
        <mtd-form inline :label-width="80">
          <mtd-form-item label="表单标题1">
            <mtd-input type="text" v-model="text" placeholder="请输入"/>
          </mtd-form-item>
          <mtd-form-item label="表单标题2">
            <mtd-input type="text" v-model="fields.value" placeholder="请输入"/>
          </mtd-form-item>
          <mtd-form-item>
            <mtd-button type="primary" style="margin-left:12px">提交</mtd-button>
          </mtd-form-item>
        </mtd-form>
      </mtd-tab-pane>
    </mtd-tabs>
  </div>
</template>

<script>
export default {
  data () {
    return {
      text: '',
      fields: {
        value: '',
      },
      activeName4: 'Tab1'
    };
  },
};
</script>

```

#### doc/resetting.vue

```vue
<template>
  <div class="demonstration-content">
    <mtd-tabs v-model="activeTab" type="text" size="small">
      <mtd-tab-pane label="有初始值" value="1">
        <mtd-form :model="formCustom1" labelPosition="right" ref="formRef1">
          <mtd-form-item label="姓名" prop="name">
            <mtd-input type="text" v-model="formCustom1.name" placeholder="请输入需求名称" style="width: 200px;">
            </mtd-input>
          </mtd-form-item>
          <mtd-form-item label="手机号码" prop="phone">
            <mtd-input type="text" v-model="formCustom1.phone" placeholder="请输入需求名称" style="width: 200px;">
            </mtd-input>
          </mtd-form-item>
          <mtd-form-item label="偏好" prop="preference">
            <mtd-checkbox-group v-model="formCustom1.preference">
              <mtd-checkbox value="option1">偏好1</mtd-checkbox>
              <mtd-checkbox value="option2">偏好2</mtd-checkbox>
              <mtd-checkbox value="option3">偏好3</mtd-checkbox>
            </mtd-checkbox-group>
          </mtd-form-item>
          <mtd-form-item>
            <mtd-button type="primary">提交</mtd-button>
            <mtd-button type="panel" style="margin-left:12px" @click="handleResetting1">重置</mtd-button>
          </mtd-form-item>
        </mtd-form>
      </mtd-tab-pane>
      <mtd-tab-pane label="无初始值" value="2">
        <mtd-form :model="formCustom2" labelPosition="right" ref="formRef2">
          <mtd-form-item label="姓名" prop="name">
            <mtd-input type="text" v-model="formCustom2.name" placeholder="请输入需求名称" style="width: 200px;">
            </mtd-input>
          </mtd-form-item>
          <mtd-form-item label="手机号码" prop="phone">
            <mtd-input type="text" v-model="formCustom2.phone" placeholder="请输入需求名称" style="width: 200px;">
            </mtd-input>
          </mtd-form-item>
          <mtd-form-item label="偏好" prop="preference">
            <mtd-checkbox-group v-model="formCustom2.preference">
              <mtd-checkbox value="option1">偏好1</mtd-checkbox>
              <mtd-checkbox value="option2">偏好2</mtd-checkbox>
              <mtd-checkbox value="option3">偏好3</mtd-checkbox>
            </mtd-checkbox-group>
          </mtd-form-item>
          <mtd-form-item>
            <mtd-button type="primary">提交</mtd-button>
            <mtd-button type="panel" style="margin-left:12px" @click="handleResetting2">重置</mtd-button>
          </mtd-form-item>
        </mtd-form>
      </mtd-tab-pane>
    </mtd-tabs>
  </div>
</template>

<script>

export default {
  data() {
    return {
      activeTab: '1',
      formCustom1: {
        name: '小团',
        phone: '12345678901',
        preference: ['option1'],
      },
      formCustom2: {
        name: '',
        phone: '',
        preference: [],
      },
    }
  },
  methods: {
    handleResetting1() {
      this.$refs.formRef1.resetFields()
    },
    handleResetting2() {
      this.$refs.formRef2.resetFields()
    }
  },
}
</script>

```

#### doc/size.vue

```vue
<template>
  <div class="demonstration-content">
    <mtd-tabs v-model="activeTab" size="small" type="text">
      <mtd-tab-pane label="小尺寸" value="1" />
      <mtd-tab-pane label="中尺寸" value="2" />
      <mtd-tab-pane label="大尺寸" value="3" />
    </mtd-tabs>
    <mtd-form :model="formCustom" labelPosition="right" :size="size">
      <mtd-form-item label="输入框" prop="name">
        <mtd-input type="text" v-model="formCustom.name" placeholder="请输入" />
      </mtd-form-item>
      <mtd-form-item label="选择框" prop="city">
        <mtd-select placeholder="请选择" v-model="formCustom.city">
          <mtd-option :key="item.value" v-for="item in selectOptions" :value="item.value" :label="item.label" />
        </mtd-select>
      </mtd-form-item>
      <mtd-form-item label="单选框" prop="sex">
        <mtd-radio-group v-model="formCustom.sex">
          <mtd-radio value="1">选项1</mtd-radio>
          <mtd-radio value="0">选项2</mtd-radio>
        </mtd-radio-group>
      </mtd-form-item>
      <mtd-form-item label="复选框" prop="preference" placeholder="请选择1">
        <mtd-checkbox-group v-model="formCustom.preference" placeholder="请选择2">
          <mtd-checkbox value="option1">选项1</mtd-checkbox>
          <mtd-checkbox value="option2">选项2</mtd-checkbox>
        </mtd-checkbox-group>
      </mtd-form-item>
      <mtd-form-item label="开关" prop="isOpen">
        <mtd-switch v-model="formCustom.isOpen" :size="switchSize" />
      </mtd-form-item>
      <mtd-form-item label="详细地址" prop="slider">
        <div class="custom-form-size-slide-pos">
          <mtd-slider v-model="formCustom.slider" />
        </div>
      </mtd-form-item>
    </mtd-form>
  </div>
</template>

<script>

export default {
  data() {
    return {
      formCustom: {
        slider: 34,
      },
      activeTab: '2',
      selectOptions: [
        { label: '分类一', value: '1' },
        { label: '分类二', value: '2' },
        { label: '分类三', value: '3' }
      ]
    }
  },
  computed: {
    size() {
      switch (this.activeTab) {
        case '1':
          return 'small'
        case '2':
          return 'middle'
        case '3':
          return 'large'
        default:
          return 'large'
      }
    },
    switchSize() {
      return this.activeTab > '2' ? 'large' : 'small'
    }
  }
}
</script>
<style lang="scss" scoped>
.custom-form-size-slide-pos {
  height: 40px;
  display: flex;
  align-items: center;
  .mtd-slider {
    flex: 1
  }
}

.demo-form {
  .demo-source {
    .demonstration-content {
      margin-top: 40px;
      padding: 0;
    }
  }
}

</style>

```

#### doc/use-html-message.vue

```vue
<template>
  <mtd-form :model="formCustom" ref="formCustom" :rules="ruleCustom">
    <mtd-form-item label="主机名称" prop="hostname"
      use-html-message>
      <mtd-input type="text" v-model="formCustom.hostname" style="width: 260px;" />
    </mtd-form-item>
    <mtd-form-item label="管理员" prop="owner">
      {{ formCustom.owner }}
    </mtd-form-item>
    <mtd-form-item>
      <mtd-button type="primary" style="margin-right: 12px;"
        @click="handleSubmit('formCustom')">
        立即创建
      </mtd-button>
      <mtd-button @click="handleReset('formCustom')">取消</mtd-button>
    </mtd-form-item>
  </mtd-form>
</template>
<script>
export default {
  data () {
    const validateHostname = (rule, value, callback) => {
      if (value.length < 3) {
        callback(new Error('<p style="background-color: #fff9e6;padding: 0 4px;color: #592d00;">主机名称必须大于 3 个字</p>'));
      } else {
        callback();
      }
    };
    return {
      formCustom: {
        hostname: '',
        owner: '李明/liming',
      },
      ruleCustom: {
        hostname: [
          {
            required: true,
            message: '<p style="background-color: #fff9e6;padding: 0 4px;color: #592d00;">请输入主机名称</p>',
          },
          { validator: validateHostname, trigger: 'blur' },
        ],
      },
      list1: [{
        value: '1',
        label: '区域1',
        children: [{
          value: '1',
          label: '主机1',
        }, {
          value: '2',
          label: '主机2',
        }],
      }, {
        value: '2',
        label: '区域2',
        children: [{
          value: '3',
          label: '主机3',
        }, {
          value: '4',
          label: '主机4',
        }],
      }],
      list2: [{
        value: 'tag1',
        label: '标签1',
      }, {
        value: 'tag2',
        label: '标签2',
      }, {
        value: 'tag3',
        label: '标签3',
      }, {
        value: 'tag4',
        label: '标签4',
      }, {
        value: 'tag5',
        label: '标签5',
      }],
    };
  },
  methods: {
    handleSubmit (name) {
      this.$refs[name].validate((valid) => {
        if (valid) {
          console.log('Success!');
        } else {
          console.error('Fail!');
        }
      });
    },
    handleReset (name) {
      this.$refs[name].resetFields();
    },
  },
};
</script>
<style lang='scss'>
  .demo-input-group{
    >:not(:last-child){
      .mtd-input {
        border-top-right-radius: 0px;
        border-bottom-right-radius: 0px;
        border-right: none;
      }
    }
    >:not(:first-child){
      .mtd-input {
        border-top-left-radius: 0px;
        border-bottom-left-radius: 0px;
      }
    }
  }
</style>

```

#### doc/validate-status.vue

```vue
<template>
  <div class="demonstration-content">
    <mtd-tabs v-model="activeTab" type="text" size="small">
      <mtd-tab-pane label="隐藏校验图标" value="1" />
      <mtd-tab-pane label="显示校验图标" value="2" />
    </mtd-tabs>
    <mtd-form :model="formCustom">
      <mtd-form-item
          label="成功"
          prop="username"
          :validate-status="formStatus.username"
          :validate-message="formMsg.username"
          :hasFeedback="activeTab === '2'"
      >
        <mtd-input type="text" v-model="formCustom.username" placeholder="校验成功" style="width: 260px;" />
      </mtd-form-item>

      <mtd-form-item
          label="警告"
          prop="password"
          error="警告说明"
          :validate-status="formStatus.password"
          :validate-message="formMsg.password"
          :hasFeedback="activeTab === '2'"
      >
        <mtd-input type="password" v-model="formCustom.password" placeholder="校验警告" style="width: 260px;"/>
      </mtd-form-item>

      <mtd-form-item
          label="失败"
          prop="tags"
          :validate-status="formStatus.tags"
          :validate-message="formMsg.tags"
          :hasFeedback="activeTab === '2'"
      >
        <mtd-input type="password" v-model="formCustom.tags" placeholder="校验失败" style="width: 260px;"/>
      </mtd-form-item>
      <mtd-form-item
          label="校验中"
          prop="age"
          :validate-status="formStatus.age"
          :validate-message="formMsg.age"
          placeholder="校验中"
          :hasFeedback="activeTab === '2'"
      >
        <mtd-input type="password" v-model="formCustom.age" placeholder="校验中" style="width: 260px;"/>
      </mtd-form-item>
    </mtd-form>
  </div>
</template>

<script>
export default {
  data () {
    return {
      activeTab: '1',
      formCustom: {
        username: '',
        password: '',
        tags: '',
        age: '',
      },
      formStatus:{
        username: 'success',
        password: 'warning',
        tags: 'error',
        age: 'validating',
      },
      formMsg:{
        username: '',
        password: '警告说明',
        tags: '错误原因',
        age: '',
      },
      hasFeedback: false
    };
  },
};
</script>

```

#### doc/validate.vue

```vue
<template>
  <div class="demonstration-content">
    <mtd-tabs v-model="activeName4" type="text" size="small">
      <mtd-tab-pane label="即时校验" value="Tab1">
        <mtd-form :rules="ruleCustom1" :model="formCustom1" ref="form1">
          <mtd-form-item label="用户名" prop="username" helper="只能输入中文">
            <mtd-input type="text" v-model="formCustom1.username" placeholder="请输入用户名" />
          </mtd-form-item>
          <mtd-form-item label="密码"
                         prop="password"
                         helper="6-16个字符，请使用字母加数字的组合密码，不能包含*@#等符号">
            <mtd-input type="text" v-model="formCustom1.password" placeholder="请输入密码" />
          </mtd-form-item>
          <mtd-form-item>
            <mtd-button type="primary" @click="triggerValidate(1)">登录</mtd-button>
          </mtd-form-item>
        </mtd-form>
      </mtd-tab-pane>
      <mtd-tab-pane label="异步校验" value="Tab2">
        <mtd-form :rules="ruleCustom2" :model="formCustom2" ref="form2">
          <mtd-form-item label="用户名" prop="username" helper="只能输入中文" >
            <mtd-input type="text" v-model="formCustom2.username" placeholder="请输入用户名" />
          </mtd-form-item>
          <mtd-form-item label="密码" prop="password"
                         helper="6-16个字符，请使用字母加数字的组合密码，不能包含*@#等符号">
            <mtd-input type="text" v-model="formCustom2.password" placeholder="请输入密码" />
          </mtd-form-item>
          <mtd-form-item>
            <mtd-button type="primary" @click="triggerValidate(2)">登录</mtd-button>
          </mtd-form-item>
        </mtd-form>
      </mtd-tab-pane>
    </mtd-tabs>
  </div>
</template>

<script>
export default {
  data () {
    const validateUsername = (rule, value, callback) => {
      if (!/^[\u4e00-\u9fa5]+$/.test(value)) {
        callback(new Error('只能输入中文'));
      } else {
        callback();
      }
    };
    const validatePassword = (rule, value, callback) => {
      if (!/^[a-zA-Z0-9]{6,16}$/.test(value)) {
        callback(new Error('只能输入6-16个字符且仅为字母,数字'));
      } else {
        callback();
      }
    };

    const validateUsernameSync = (rule, value, callback) => {
      setTimeout(() => {
        if (!/^[\u4e00-\u9fa5]+$/.test(value)) {
          callback(new Error('只能输入中文'));
        } else {
          callback();
        }
      }, 2000);
    };
    const validatePasswordSync = (rule, value, callback) => {
      setTimeout(() => {
        if (!/^[a-zA-Z0-9]{6,16}$/.test(value)) {
          callback(new Error('只能输入6-16个字符且仅为字母,数字'));
        } else {
          callback();
        }
      }, 2000);
    };

    return {
      activeName4: 'Tab1',
      formCustom1: {
        username: '',
        password: '',
      },
      ruleCustom1: {
        username: [
          {validator: validateUsername, trigger: 'change'},
        ],
        password: [
          {validator: validatePassword, trigger: 'change'},
        ],
      },
      formCustom2: {
        username: '',
        password: '',
      },
      ruleCustom2: {
        username: [
          {validator: validateUsernameSync, trigger: 'change'},
        ],
        password: [
          {validator: validatePasswordSync, trigger: 'change'},
        ],
      },
    };
  },
  methods: {
    triggerValidate(num) {
      if (num === 1) {
        this.$refs.form1.validate()
      } else {
        this.$refs.form2.validate()
      }
    }
  }
};
</script>

```

#### form.tsx

```tsx
import {
  defineComponent,
  computed,
  reactive,
  markRaw,
  toRefs,
  watch,
  vueInstance,
  getSlotsInRender,
} from '@ss/mtd-adapter'
import useConfig from '@hooks/config'
import { noop } from '@utils/util'
import mitt from '@utils/mitt'
import { isString } from '@utils/type'
import useFromProvide from './useProvide'
import { FormItemIns } from '../form-item/types'

export default defineComponent({
  name: 'MtdForm',
  inheritAttrs: true,
  props: {
    model: {
      type: Object,
    },
    rules: {
      type: Object,
    },
    labelWidth: {
      type: Number,
      default: 100,
    },
    labelPosition: {
      type: String,
      validator: (value: string) => {
        return ['right', 'top', 'left'].indexOf(value) > -1
      },
      default: 'right',
    },
    showMessage: {
      type: Boolean,
      default: true,
    },
    inline: {
      type: Boolean,
      default: false,
    },
    autocomplete: {
      type: String,
      validator: (value: string) => {
        return ['on', 'off'].indexOf(value) > -1
      },
      default: 'off',
    },
    firstFields: {
      type: Boolean,
      default: true,
    },
    labelSuffix: String,
    contentDisplay: {
      type: String,
      default: 'block',
    },
    disabled: Boolean,
    validateOnRuleChange: {
      type: Boolean,
      default: true,
    },
    scrollToFirstError: {
      type: Boolean,
      default: false,
    },
    size: {
      type: String,
    },
    colon: {
      type: Boolean,
      default: false,
    },
  },
  emits: [],
  setup(props) {
    const config = useConfig()
    const prefix = computed(() => config.getPrefixCls('form'))
    const prefixMTD = computed(() => config.getPrefix())
    const ins = vueInstance()

    const state = reactive({
      emitter: markRaw(mitt()),
      fields: [] as FormItemIns[],
    })

    const { provideForm } = useFromProvide()
    provideForm(ins)

    // @Computed
    const classes = computed(() => {
      return [
        `${prefixMTD.value}-form-${props.labelPosition}`,
        {
          [`${prefixMTD.value}-form-inline`]: props.inline,
          [`${prefixMTD.value}-form-${props.size}`]: !!props.size,
        },
      ]
    })

    // @Watch
    watch(() => props.rules, () => {
      if (props.validateOnRuleChange) {
        validate().catch(function (e) { })
      }
    }, {
      deep: true,
    })

    // @Created
    state.emitter.on('addFormItem', (field: FormItemIns) => {
      if (field && field.prop) state.fields.push(field)
      return false
    })
    state.emitter.on('removeFormItem', (field: FormItemIns) => {
      if (field.prop) state.fields.splice(state.fields.indexOf(field), 1)
      return false
    })

    // @Methods
    // 对整个表单进行重置，将所有字段值重置为空并移除校验结果
    function resetFields(props: string | string[] = []) {
      const fields =
        props && props.length
          ? isString(props)
            ? state.fields.filter((field) => props === field.prop)
            : state.fields.filter((field) => props.indexOf(field.prop) > -1)
          : state.fields
      fields.forEach((field) => {
        field.resetField()
      })
    }
    function validate(callback?: Function) {
      const callbackFn = callback || noop
      return new Promise<void>((resolve, reject) => {
        validateFields('', function (valid, errors) {
          callbackFn(valid, errors)
          valid ? resolve() : reject(errors)
        })
      })
    }
    function validateFields(form_props: string | string[], callback: Function) {
      const fields = form_props && form_props.length
        ? (isString(form_props)
          ? state.fields.filter(field => form_props === field.prop)
          : state.fields.filter(field => form_props.indexOf(field.prop) > -1)
        ) : state.fields
      if (form_props && form_props.length && !fields.length) {
        throw new Error(
          '[warn]: must call validateField with valid prop string!',
        )
      }

      const callbackFn = callback || noop

      // run validate
      let valid = true
      let count = 0
      const errors: any = {}
      const errorFields: any[] = []
      if (!fields.length) {
        callbackFn(valid)
      }
      fields.forEach(field => {
        field.validate('', (error: string) => {
          if (error) {
            valid = false
            errors[field.prop] = error
            errors.$$mtd = true
            errorFields.push(field)
          }
          if (++count === fields.length) {
            // all finish
            if (errorFields.length && errorFields[0].$el && props.scrollToFirstError) {
              errorFields[0].$el.scrollIntoView({
                behavior: 'smooth',
                block: 'center',
              })
            }
            callbackFn(valid, errors)
          }
        })
      })
    }
    function validateField(prop: string, cb?: Function) {
      const field = state.fields.find((field) => field.prop === prop)
      if (!field) {
        throw new Error(
          '[warn]: must call validateField with valid prop string!',
        )
      }
      return field.validate('', cb)
    }
    function clearValidate(props: string | string[] = []) {
      const fields = props.length
        ? isString(props)
          ? state.fields.filter((field) => props === field.prop)
          : state.fields.filter((field) => props.indexOf(field.prop) > -1)
        : state.fields
      fields.forEach((field) => {
        field.clearValidate()
      })
    }

    const computedCollection = {
      classes,
    }
    const methodsCollection = {
      resetFields,
      validate,
      validateFields,
      validateField,
      clearValidate,
    }

    return {
      prefix,
      ...toRefs(state),
      ...computedCollection,
      ...methodsCollection,
    }
  },
  render() {
    const {
      prefix, classes, autocomplete,
    } = this
    return <form
      class={[prefix, classes]}
      autocomplete={autocomplete}
    >
      {getSlotsInRender(this)}
    </form>
  },
})

```

#### index.md

```markdown
<style lang="scss" scoped>
  ::v-deep {
    .demo-mtd-form-helper {
      color: #ADADAD;
      margin-left: 8px;
    }
    .demo-form-tooltip{
      margin-left: 12px;
    }
    .demo-form{
      .mtd-form {
        display: inline-block;
      }
    }
    .demo-form-title{
      text-align: left;
      font-size: 12px;
      color: #999
    }
    .custom-form-position {
      width: 100%;
      .mtd-tabs-content {
        display: flex;
        justify-content: center;
      }
    }
    .custom-form-position {
      .mtd-tabs-content {
        padding: 20px;
      }
    }

    .demonstration-content {
      margin-top: 50px;
      padding: 100px 0;
      & > div {
        width: 100%;
      }
    }

    .demonstration-content > .mtd-tabs-small > .mtd-tabs-nav {
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      border-bottom: 1px solid #edf0f7;
      padding: 9px 16px;
      padding: 1.5px 16px;
    }
    .demo-min-height {
      margin-top: 50px;
      padding: 100px 0;
    }
  }
</style>
# 表单 / Form
表单（Form）用以收集、校验或提交数据，主要由输入框、选择器、单选按钮组、复选框等录入项和操作按钮等构成。

> W3C 标准中有如下[规定](https://www.w3.org/MarkUp/html-spec/html-spec_8.html#SEC8.2)：
>
> When there is only one single-line text input field in a form, the user agent should accept Enter in that field as a request to submit the form.
>
>即：当一个 form 元素中只有一个输入框时，在该输入框中按下回车应提交该表单。如果希望阻止这一默认行为，可以在 ```<mtd-form>``` 标签上添加 @submit.native.prevent。

## 互动演示
:::include(src="./demo/form-interactive.vue")
:::

## 代码示例
###  布局
表单支持三种标签和输入框的布局方式：水平排列、垂直排列和行内排列。默认水平排列，如果表单项少，垂直空间充足，可选择垂直排列。
:::include(src="./doc/position.vue")
:::

### 标签对齐
表单提供左对齐、右对齐、顶对齐三种标签对齐方式，默认对齐为右对齐。
:::include(src="./doc/label-position.vue")
:::

### 校验与报错
为防止用户信息录入出错，应尽早让用户发现并纠正错误，以帮助用户理解问题并了解如何处理。本组件支持两种校验方式：即时校验和异步校验，其中异步校验需要和后端进行数据校验，有loading过程。
:::include(src="./doc/validate.vue")
:::

### 校验状态
校验状态有成功、警告、失败、校验中四个状态。
:::include(src="./doc/validate-status.vue")
:::

### 自定义校验规则
自定义校验时必须调用 callback 参数
:::include(src="./doc/custom-validate.vue")
:::

### 标签可换行
:::include(src="./doc/label-wrap.vue")
:::

### 动态表单
可通过循环的方式生成来动态生成表单项
:::include(src="./doc/dynamic.vue")
:::

### 禁用
可使用 disabled 禁用表单内所有组件，一般通过复选框、开关禁用表单，或由于用户暂无编辑权限触发禁用态。
:::include(src="./doc/disabled.vue")
:::

### 控件联动
录入项之间有联动关系，填写内容后，会影响后面录入项的填写范围，或影响后面录入项是什么、是否可见/可操作等。
:::include(src="./doc/linkage.vue")
:::

### 可重置
:::include(src="./doc/resetting.vue")
:::

### 可清空校验结果
当有大量异常提示、且业务上期望可以隐藏时，可使用“清空校验结果”按钮。支持清空整个表单校验结果，也可以自定义清除部分表单项校验结果。
:::include(src="./doc/clear-rules.vue")
:::

### 注释信息
有时只靠字段的措辞和输入框不足以给用户填写答案提供有用的线索，需要一些帮助信息来说明字段的含义和输入框填写注意事项。
:::include(src="./doc/annotation-info.vue")
:::

### 尺寸
表单提供了小、中、大三种尺寸。
:::include(src="./doc/size.vue")
:::

### 冒号
表单标题可选择带冒号和不带冒号，默认不带冒号。
:::include(src="./doc/colon.vue")
:::


<!-- ### HTML 片段提示
使用 v-html 方式展示错误提示
:::include(src="./doc/use-html-message.vue")
::: -->


## API
<api-doc name="Form" :doc="require('./api.json')"></api-doc>

### 校验规则
| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| trigger | 触发条件, 非必填, 可选值: 'blur'、'change'  | string | - |
| enum | 枚举类型 | string | - |
| len | 字段长度 | number | - |
| max | 最大长度 | number | - |
| message | 校验文案 | string | - |
| min | 最小长度 | number | - |
| pattern | 正则表达式校验 | RegExp | - |
| required | 是否必选 | boolean | `false` |
| transform | 校验前转换字段值 | function(value) => transformedValue:any | - |
| type | 内建校验类型，[可选项](https://github.com/yiminghe/async-validator#type) | string | 'string' |
| validator | 自定义校验（**注意 callback 必须被调用**）, 0.3.6 版本开始支持 source 参数，source 为当前表单 modal 属性值 | function(rule, value, callback, source) | - |
| whitespace | 必选时，空格是否会被视为错误 | boolean | `false` |

更多用法可参考 [async-validator](https://github.com/yiminghe/async-validator)。

<api-doc name="FormItem" :doc="require('../form-item/api.json')"></api-doc>

```

#### index.ts

```typescript
import Form from './form'
import { withInstall } from '@ss/mtd-adapter'
export default withInstall(Form)

```

#### types.ts

```typescript
import { Emitter } from '@utils/mitt'

export interface FormProvider {
  disabled: any
  showMessage?: boolean
  model: any
  rules?: any
  inline?: boolean
  labelWidth?: number
  labelSuffix?: string
  labelPosition?: 'top' | 'right'
  firstFields?: boolean | string[]
  size?: string
  colon?: boolean

  emitter: Emitter
  contentDisplay: string
}

```

#### useProvide.ts

```typescript
import { FormProvider } from './types'
import { provide, inject } from '@ss/mtd-adapter'

export const formSymbol = 'mtui-vue/form'

export const useProvider = () => {

  function provideForm(ins: FormProvider) {
    provide<FormProvider>(formSymbol, ins)
  }

  function injectForm() {
    return inject<FormProvider>(formSymbol) as FormProvider
  }

  return {
    provideForm,
    injectForm,
  }
}

export default useProvider

```

