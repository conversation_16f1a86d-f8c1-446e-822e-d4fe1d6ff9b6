## 源码解析

### 文件依赖

- demo.md
- demo/modal-interactive.vue
- doc/async-close.vue
- doc/base.vue
- doc/custom-area.vue
- doc/custom-menu.vue
- doc/custom.vue
- doc/demo/append-to-container.vue
- doc/demo/destroy-on-close.vue
- doc/demo/get-popup-container.vue
- doc/demo/lock-scroll.vue
- doc/demo/max-title-width2.vue
- doc/demo/modeless-drag-fullscreen.vue
- doc/demo/mount-on-create.vue
- doc/icon-modal.vue
- doc/max-title-width.vue
- doc/modeless-drag.vue
- doc/modeless.vue
- doc/placement.vue
- index.md
- index.ts
- inside.tsx
- modal.tsx
- types.ts
- usage.json

### 源码内容

#### demo.md

```markdown
#Modal-补充Demo

## append-to-container
:::include(src="./doc/demo/append-to-container.vue")
:::

## destroy-on-close
:::include(src="./doc/demo/destroy-on-close.vue")
:::

## get-popup-container
:::include(src="./doc/demo/get-popup-container.vue")
:::

## lock-scroll
:::include(src="./doc/demo/lock-scroll.vue")
:::

## mount-on-create
:::include(src="./doc/demo/mount-on-create.vue")
:::

## BUG FIX: 拖拽后再全屏，弹窗位置异常 [TT链接](https://tt.sankuai.com/ticket/detail?id=305019220)
:::include(src="./doc/demo/modeless-drag-fullscreen.vue")
:::

## max-title-width(验证v-if、v-show)
:::include(src="./doc/demo/max-title-width2.vue")
:::

```

#### demo/modal-interactive.vue

```vue
<template>
  <div>
    <mtd-doc-usage name="modal" :apiJson="apiJson" :usageJson="usageJson" @propChange="propChange">
      <template slot="modal" slot-scope="{ configProps }">
        <div class="slot-container">
          <mtd-button @click="handleModalChange(true)" type="primary">打开弹窗</mtd-button>
          <mtd-modal v-bind="configProps" v-on:update:modelValue="handleModalChange">这是弹窗内容</mtd-modal>
        </div>
      </template>
      <template #modal="{ configProps }">
        <div class="slot-container">
          <mtd-button @click="handleModalChange(true)" type="primary">打开弹窗</mtd-button>
          <mtd-modal v-bind="configProps" v-on:update:modelValue="handleModalChange">这是弹窗内容</mtd-modal>
        </div>
      </template>
    </mtd-doc-usage>
  </div>
</template>
<script>
import apiJson from '../api.json'
import _ from 'lodash'

export default {
  data() {
    return {
      apiJson: apiJson,
      usageJson: {
        componentName: "modal",
        desc: "对话框",
        codeTemplate: "<mtd-modal v-bind='configProps'>这是弹窗内容</mtd-modal>"
      }
    }
  },
  methods: {
    handleModalChange(val) {
      const data = _.cloneDeep(this.apiJson);
      const index = data['props'].findIndex(prop => prop.name === 'modelValue');
      data['props'][index] = { ...data['props'][index], default: val }

      this.apiJson = data
    },
    propChange(propertyName, val) {
      console.log('prop changed: , propertyName: ' + propertyName + ', val: ' + val)
    }
  }
}
</script>
<style lang="">

</style>

```

#### doc/async-close.vue

```vue
<template>
  <div>
    <mtd-button type="primary" @click="openModal5">异步关闭</mtd-button>
    <mtd-modal
      :mask-closable="false"
      width="400px"
      v-model="visible5"
      title="异步关闭的对话框"
      @open="handleOpenEvent"
      @close="handleCloseEvent"
      @closed="handleClosedEvent"
      @input="handleInput"
    >
      春江潮水连海平，海上明月共潮生。滟滟随波千万里，何处春江无月明！
      江流宛转绕芳甸，月照花林皆似霰；空里流霜不觉飞，汀上白沙看不见。
      江天一色无纤尘，皎皎空中孤月轮。江畔何人初见月？江月何年初照人？
      人生代代无穷已，江月年年望相似。不知江月待何人，但见长江送流水。
      <template v-slot:footer>
        <div class="demo-modal-footer">
          <mtd-button @click="cancelModal5" type="panel">取消</mtd-button
          ><mtd-button
            type="primary"
            :loading="submitting"
            @click="submitModal5"
            >提交</mtd-button
          >
        </div>
      </template>
    </mtd-modal>
  </div>
</template>
<script>
export default {
  data() {
    return {
      visible5: false,
      submitting: false,
    };
  },
  methods: {
    openModal5() {
      this.visible5 = true;
    },
    cancelModal5() {
      this.visible5 = false;
    },
    submitModal5() {
      this.submitting = true;
      setTimeout(() => {
        this.submitting = false;
        this.visible5 = false;
      }, 3000);
    },
    handleOpenEvent() {
      console.log('modal open')
    },
    handleCloseEvent() {
      console.log('close icon、mask or ESC clicked')
    },
    handleClosedEvent() {
      console.log('modal closed')
    },
    handleInput() {
      console.log('modal input')
    }
  },
};
</script>

```

#### doc/base.vue

```vue
<template>
  <div>
    <mtd-button type="primary" @click="openModal1">预览效果</mtd-button>
    <mtd-modal title="对话框" v-model="visible1" width="400px" ref="aaa" class="🤡" aaaa="🐴">
      <div>
        春江潮水连海平，海上明月共潮生。滟滟随波千万里，何处春江无月明！
        江流宛转绕芳甸，月照花林皆似霰；空里流霜不觉飞，汀上白沙看不见。
        江天一色无纤尘，皎皎空中孤月轮。江畔何人初见月？江月何年初照人？
        人生代代无穷已，江月年年望相似。不知江月待何人，但见长江送流水。
      </div>
      <template #footer>
        <div class="demo-modal-footer">
          <mtd-button @click="closeModal1" type="panel">取消</mtd-button
          ><mtd-button type="primary" @click="closeModal1">确定</mtd-button>
        </div>
      </template>
    </mtd-modal>
  </div>
</template>
<script>
export default {
  data() {
    return {
      visible1: false,
    }
  },
  methods: {
    openModal1() {
      this.visible1 = true
      console.log(this.$refs.aaa)
    },
    closeModal1() {
      this.visible1 = false
    },
  },
}
</script>

```

#### doc/custom-area.vue

```vue
<template>
  <div class="demo-full-width">
    <div class="demo-modal-btn-groups">
      <mtd-button type="primary" @click="openModal6">无内容对话框</mtd-button>
      <mtd-button type="primary" @click="openModal7">无底部操作对话框</mtd-button>
    </div>
    <mtd-modal v-model="visible6" title="无内容对话框">
      <template v-slot:footer>
          <mtd-button type="primary" @click="closeModal6">我知道了</mtd-button>
      </template>
    </mtd-modal>

    <mtd-modal v-model="visible7" title="无底部操作对话框" width="400px">
      春江潮水连海平，海上明月共潮生。滟滟随波千万里，何处春江无月明！
      江流宛转绕芳甸，月照花林皆似霰；空里流霜不觉飞，汀上白沙看不见。
      江天一色无纤尘，皎皎空中孤月轮。江畔何人初见月？江月何年初照人？
      人生代代无穷已，江月年年望相似。不知江月待何人，但见长江送流水。
    </mtd-modal>
  </div>
</template>
<script>
export default {
  data() {
    return {
      visible6: false,
      visible7: false,
    }
  },
  methods: {
    openModal6() {
      this.visible6 = true;
    },
    closeModal6() {
      this.visible6 = false;
    },
    openModal7() {
      this.visible7 = true;
    },
    closeModal7() {
      this.visible7 = false;
    },
  },
};
</script>

```

#### doc/custom-menu.vue

```vue
<template>
  <div>
    <mtd-button type="primary" @click="openModal1">自定义对话框最小化</mtd-button>
    <mtd-modal title="自定义对话框最小化" v-model="visible1" width="400px" ref="aaa" class="🤡" aaaa="🐴">
      <div>
        点击右上角最小化icon，收起对话框。
      </div>
      <template #footer>
        <div class="demo-modal-footer">
          <mtd-button @click="closeModal1" type="panel">取消</mtd-button
          ><mtd-button type="primary" @click="closeModal1">确定</mtd-button>
        </div>
      </template>
      <template #menu>
        <span class="custom-menu custom-menu-mini" @click="handleMini">
          <mtd-icon name="remove" />
        </span>
        <span class="custom-menu custom-menu-close" @click="closeModal1">
          <mtd-icon name="close" />
        </span>
      </template>
    </mtd-modal>
    <div class="mini-modal" @click="expandModal" v-if="miniVisible">
      <div class="mini-modal-icon">
        <mtd-icon name="fullscreen-o" />
      </div>
      <div class="mini-desc">当前1个弹窗</div>
    </div>
  </div>
</template>
<script>
import MtdIcon from "@components/icon/icon";

export default {
  components: {MtdIcon},
  data() {
    return {
      visible1: false,
      miniVisible: false,
    }
  },
  methods: {
    openModal1() {
      this.visible1 = true
      this.miniVisible = false
    },
    closeModal1() {
      this.visible1 = false
      this.miniVisible = false
    },
    handleMini() {
      this.visible1 = false
      this.miniVisible = true
    },
    expandModal() {
      this.visible1 = true
      this.miniVisible = false
    }
  },
}
</script>
<style lang="scss" scoped>
  .custom-menu {
    font-size: 20px;
    height: 28px;
    width: 28px;
    align-items: center;
    border-radius: 6px;
    justify-content: center;
    cursor: pointer;
    display: flex;
    &:hover,
    &:active {
      background-color: rgba(0,0,0,.06);
      color: rgba(0,0,0,.7);
    }
  }
  .mini-modal {
    position: fixed;
    bottom: 150px;
    right: 150px;
    display: flex;
    width: 123px;
    height: 28px;
    border-radius: 18px;
    box-shadow: 0px 4px 12px 0px rgba(0, 0, 0, 0.1);
    cursor: pointer;
    color: #111925;
    align-items: center;
  }
  .mini-modal-icon {
    font-size: 16px;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 28px;
    height: 28px;
    margin-left: 10px;
  }
  .mini-desc {
    font-size: 12px;
  }
</style>

```

#### doc/custom.vue

```vue
<template>
  <div class="demo-full-width">
    <div class="demo-modal-btn-groups">
      <mtd-button type="primary" @click="openModal6">驳回原因</mtd-button>
      <mtd-button type="primary" @click="openModal7">收货地址</mtd-button>
      <mtd-button type="primary" @click="openModal8">添加成员</mtd-button>
      <!-- <mtd-button type='primary' @click="openModal9">请假申请</mtd-button> -->
    </div>
    <mtd-modal v-model="visible6" title="申请被驳回">
      您的申请已被驳回，如有疑问请联系123456
      <template v-slot:footer>
        <div class="demo-modal-footer">
          <mtd-button type="primary" @click="closeModal6">我知道了</mtd-button>
        </div>
      </template>
    </mtd-modal>

    <mtd-modal v-model="visible7" title="收货地址" width="400px">
      <mtd-table
        :data="addrData"
        style="
          border: 1px solid #ebeef5;
          border-bottom: none;
        "
      >
        <mtd-table-column prop="id" label="Id" width="80px" />
        <mtd-table-column prop="name" label="姓名" width="80px" />
        <mtd-table-column prop="addr" label="地址" />
      </mtd-table>
      <template v-slot:footer>
        <div class="demo-modal-footer">
          <mtd-button type="primary" @click="closeModal7">我知道了</mtd-button>
        </div>
      </template>
    </mtd-modal>

    <mtd-modal v-model="visible8" title="添加成员">
      <mtd-input
        placeholder="输入姓名或mis号搜索"
        v-model="search"
        style="margin-bottom: 10px; width: 100%;"
      />
      <div
        style="
          overflow: auto;
          height: 400px;
          margin-left: -30px;
          margin-right: -30px;
          padding: 0px 30px;
        "
      >
        <mtd-checkbox-group v-model="selected" style="width: 100px;">
          <mtd-checkbox
            v-for="m in filterMembers"
            :key="m"
            :value="m"
            style="display: block;"
            >{{ m }}
          </mtd-checkbox>
        </mtd-checkbox-group>
      </div>
      <template v-slot:footer>
        <div class="demo-modal-footer">
          <mtd-button @click="resetMember" type="panel">取消</mtd-button
          ><mtd-button
            type="primary"
            :loading="submitting"
            @click="submitMember"
            >确定</mtd-button
          >
        </div>
      </template>
    </mtd-modal>
  </div>
</template>
<script>
export default {
  data() {
    return {
      visible6: false,
      visible7: false,
      visible8: false,
      submitting: false,
      search: '',
      members: [
        '张三',
        '李四',
        '王五',
        '赵六',
        '田七',
        '胡八',
        '赵婧文',
        '赵竹林',
        '赵威皓',
        '赵冬梅',
        '赵中锴',
        '赵山川',
        '赵吾光',
        '赵璇海',
        '赵学海',
        '赵午光',
        '赵绚海',
        '赵玉',
        '赵吾行',
        '赵晓珲',
        '赵吾航',
        '钱卫国',
        '钱虹君',
        '钱东亮',
        '钱品阎',
        '钱品妍',
        '钱奕宣',
        '钱品颜',
        '钱浩宇',
        '钱品闫',
        '钱韵澄',
        '钱亚男',
        '钱晓初',
        '钱潆龙',
        '钱桂英',
        '钱浩然',
      ],
      selected: ['张三', '李四', '王五'],
      selectMembers: ['张三', '李四', '王五'],
      addrData: [
        {
          id: '0001',
          name: '小美',
          addr: '北京市望京恒基伟业B/C座',
        },
        {
          id: '0002',
          name: '小团',
          addr: '北京市望京恒基伟业B/C座',
        },
        {
          id: '0003',
          name: '小王',
          addr: '北京市望京恒基伟业B/C座',
        },
      ],
    };
  },
  computed: {
    filterMembers() {
      const { search } = this;
      if (!search) {
        return this.members;
      }
      return this.members.filter((m) => m.indexOf(search) > -1);
    },
  },
  methods: {
    openModal6() {
      this.visible6 = true;
    },
    closeModal6() {
      this.visible6 = false;
    },
    openModal7() {
      this.visible7 = true;
    },
    closeModal7() {
      this.visible7 = false;
    },
    openModal8() {
      this.visible8 = true;
    },
    resetMember() {
      this.visible8 = false;
      this.selected = this.selectMembers;
    },
    submitMember() {
      this.submitting = true;
      setTimeout(() => {
        this.submitting = false;
        this.selectMembers = this.selected;
        this.visible8 = false;
      }, 3000);
    },
  },
};
</script>

```

#### doc/demo/append-to-container.vue

```vue
<template>
  <div class="demo-full-width">
    <div class="demo-modal-btn-groups">
      <mtd-button type="primary" @click="openModal(true)">弹窗挂载容器元素中</mtd-button>
      <mtd-button type="primary" @click="openModal(false)">弹窗挂载当前节点</mtd-button>
      <mtd-modal
          width="400px"
          v-model="visible"
          title="这是标题"
          :append-to-container="appendToContainer"
      >
        是否将弹出框挂载至容器元素中: {{appendToContainer}}
      </mtd-modal>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      visible: false,
      appendToContainer: false
    };
  },
  methods: {
    openModal(val) {
      this.visible = true;
      this.appendToContainer = val
    },
  },
};
</script>

```

#### doc/demo/destroy-on-close.vue

```vue
<template>
  <div class="demo-full-width">
    <div class="demo-modal-btn-groups">
      <mtd-button type="primary" @click="openModal(true)">弹窗关闭时销毁子元素</mtd-button>
      <mtd-button type="primary" @click="openModal(false)">弹窗关闭时不销毁子元素</mtd-button>
      <mtd-modal
          width="400px"
          v-model="visible"
          title="这是标题"
          :destroy-on-close="destroyOnClose"
      >
        测试弹窗关闭子元素是否销毁: {{destroyOnClose}}
      </mtd-modal>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      visible: false,
      destroyOnClose: false
    };
  },
  methods: {
    openModal(val) {
      this.visible = true;
      this.destroyOnClose = val
    },
  },
};
</script>

```

#### doc/demo/get-popup-container.vue

```vue
<template>
  <div class="demo-full-width">
    <div class="demo-modal-btn-groups">
      <div class="custom-container" ref="customContainer">这是自定义节点：custom-container</div>
      <mtd-button type="primary" @click="openModal(1)">弹窗挂载默认body节点</mtd-button>
      <mtd-button type="primary" @click="openModal(2)">弹窗挂载自定义节点：custom-container</mtd-button>
      <mtd-modal
          width="400px"
          v-model="visible1"
          title="这是标题"
      >
        弹窗挂载默认body节点
      </mtd-modal>
      <mtd-modal
          width="400px"
          v-model="visible2"
          title="这是标题"
          :get-popup-container="getContainer"
      >
        弹窗挂载自定义节点：custom-container
      </mtd-modal>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      visible1: false,
      visible2: false,
    };
  },
  methods: {
    openModal(val) {
      if (val === 1) {
        this.visible1 = true;
      } else if (val === 2) {
        this.visible2 = true;
      }
    },
    getContainer() {
      return this.$refs.customContainer;
    }
  },
};
</script>

```

#### doc/demo/lock-scroll.vue

```vue
<template>
  <div class="demo-full-width">
    <div class="demo-modal-btn-groups">
      <mtd-button type="primary" @click="openModal(true)">弹窗出现时页面滚动锁定</mtd-button>
      <mtd-button type="primary" @click="openModal(false)">弹窗出现时页面滚动不锁定</mtd-button>
      <mtd-modal
          width="400px"
          v-model="visible"
          title="这是标题"
          :lock-scroll="lockScroll"
      >
        测试弹窗出现时页面滚动是否锁定：{{lockScroll}}
      </mtd-modal>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      visible: false,
      lockScroll: false
    };
  },
  methods: {
    openModal(val) {
      this.visible = true;
      this.lockScroll = val
    },
  },
};
</script>

```

#### doc/demo/max-title-width2.vue

```vue
<template>
  <div class="demo-full-width">
    <div></div>
    <div class="demo-modal-btn-groups">
      <mtd-button type="primary" @click="openModal1">打开对话框: v-if</mtd-button>
      <mtd-button type="primary" @click="openModal2">打开对话框: v-show</mtd-button>
    </div>

    <mtd-modal
      v-if="visible1"
      v-model="visible1"
      title="设定标题的最大宽度500px，超出时用点点省略，可悬停查看设定标题的完整内容。春江潮水连海平，海上明月共潮生。滟滟随波千万里，何处春江无月明！江流宛转绕芳甸，月照花林皆似霰；空里流霜不觉飞，汀上白沙看不见。"
      width="800px"
      :max-title-width="500"
    >
      验证v-if模式下是否会影响判断是否应该显示tooltip
      <template v-slot:footer>
        <div class="demo-modal-footer">
          <mtd-button @click="closeModal" type="panel">取消</mtd-button
          ><mtd-button type="primary" @click="closeModal">确定</mtd-button>
        </div>
      </template>
    </mtd-modal>
    <mtd-modal
      v-show="visible2"
      v-model="visible2"
      title="设定标题的最大宽度500px，超出时用点点省略，可悬停查看设定标题的完整内容。春江潮水连海平，海上明月共潮生。滟滟随波千万里，何处春江无月明！江流宛转绕芳甸，月照花林皆似霰；空里流霜不觉飞，汀上白沙看不见。"
      width="800px"
      :max-title-width="500"
    >
      验证v-show模式下是否会影响判断是否应该显示tooltip
      <template v-slot:footer>
        <div class="demo-modal-footer">
          <mtd-button @click="closeModal" type="panel">取消</mtd-button
          ><mtd-button type="primary" @click="closeModal">确定</mtd-button>
        </div>
      </template>
    </mtd-modal>
  </div>
</template>
<script>
export default {
  data() {
    return {
      visible1: false,
      visible2: false,
    };
  },
  methods: {
    openModal1() {
      this.visible1 = true;
    },
    openModal2() {
      this.visible2 = true;
    },
    closeModal() {
      this.visible1 = false;
      this.visible2 = false;
    }
  },
};
</script>

```

#### doc/demo/modeless-drag-fullscreen.vue

```vue
<template>
  <div>
    <mtd-button type="primary" @click="openModal1">可拖拽对话框</mtd-button>
    <mtd-button type="primary" @click="changeFullScreen">切换全屏</mtd-button>
    <mtd-modal title="对话框" v-model="visible1" :mask="false" drag :fullscreen="fullscreen">
      <div>
        支持拖拽的对话框<br/>
        内容<br/>
      </div>
      <template v-slot:footer>
        <div class="demo-modal-footer">
          <mtd-button type="primary" @click="changeFullScreen">切换全屏</mtd-button>
          <mtd-button @click="closeModal1" type="panel">取消</mtd-button
          ><mtd-button type="primary" @click="closeModal1">确定</mtd-button>
        </div>
      </template>
    </mtd-modal>
  </div>
</template>
<script>
export default {
  data() {
    return {
      visible1: false,
      fullscreen: false
    };
  },
  methods: {
    openModal1() {
      this.visible1 = true;
    },
    closeModal1() {
      this.visible1 = false;
    },
    changeFullScreen() {
      this.fullscreen = !this.fullscreen;
    },
  },
};
</script>

```

#### doc/demo/mount-on-create.vue

```vue
<template>
  <div class="demo-full-width">
    <div class="demo-modal-btn-groups">
      <mtd-button type="primary" @click="openModal(true)">创建时初始化内部元素</mtd-button>
      <mtd-button type="primary" @click="openModal(false)">创建时不初始化内部元素</mtd-button>
      <mtd-modal
          width="400px"
          v-model="visible"
          title="这是标题"
          :mount-on-create="mountOnCreate"
      >
        测试创建时是否初始化内部元素: {{mountOnCreate}}
      </mtd-modal>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      visible: false,
      mountOnCreate: false
    };
  },
  methods: {
    openModal(val) {
      this.visible = true;
      this.mountOnCreate = val
    },
  },
};
</script>

```

#### doc/icon-modal.vue

```vue
<template>
  <div class="demo-full-width">
    <div class="demo-modal-btn-groups">
      <mtd-button type="primary" @click="openModal('info')">信息提示</mtd-button>
      <mtd-button type="primary" @click="openModal(('success'))">成功提示</mtd-button>
      <mtd-button type="primary" @click="openModal('warning')">警告提示</mtd-button>
      <mtd-button type="primary" @click="openModal('error')">失败提示</mtd-button>
    </div>
    <mtd-modal v-model="visible">
      <template #title>
        <div class="mtd-modal-title demo-icon-title">
          <mtd-icon :name="titleInfo.iconName" :class="titleInfo.statusIcon"/>{{titleInfo.title}}
        </div>
      </template>
      这是一段常规描述性文字
      <template #footer>
          <mtd-button type="primary" @click="visible=false">我知道了</mtd-button>
      </template>
    </mtd-modal>
  </div>
</template>
<script>
import MtdIcon from "@components/icon/icon";

export default {
  components: {MtdIcon},
  data() {
    return {
      visible: false,
      status: 'info',
    }
  },
  methods: {
    openModal(status) {
      this.visible = true;
      this.status = status;
    },
  },
  computed: {
    titleInfo() {
      const statusConfig = {
        info: {
          iconName: 'info-circle',
          title: '信息提示',
          statusIcon: 'modal-title-icon modal-title-icon-info'
        },
        warning: {
          iconName: 'warning-circle',
          title: '警告提示',
          statusIcon: 'modal-title-icon modal-title-icon-warning'
        },
        success: {
          iconName: 'success-circle',
          title: '成功提示',
          statusIcon: 'modal-title-icon modal-title-icon-success'
        },
        error: {
          iconName: 'error-circle',
          title: '失败提示',
          statusIcon: 'modal-title-icon modal-title-icon-error'
        }
      }
      return statusConfig[this.status] || {}
    }
  }
};
</script>
<style lang="scss" scoped>
.modal-title-icon {
  font-size: 24px;
  display: block;
  width: 24px;
  height: 24px;
  margin-right: 5px;
  &.modal-title-icon-info {
    color: #166ff7;
  }
  &.modal-title-icon-success {
    color: #00ba73;
  }
  &.modal-title-icon-warning {
    color: #ff9d00;
  }
  &.modal-title-icon-error {
    color: #ff4a47;
  }
}
.demo-icon-title {
  display: flex;
  align-items: center;
}

</style>

```

#### doc/max-title-width.vue

```vue
<template>
  <div class="demo-full-width">
    <div class="demo-modal-btn-groups">
      <mtd-button type="primary" @click="openModal">打开对话框</mtd-button>
    </div>

    <mtd-modal
      v-model="visible"
      title="设定标题的最大宽度500px，超出时用点点省略，可悬停查看设定标题的完整内容。春江潮水连海平，海上明月共潮生。滟滟随波千万里，何处春江无月明！江流宛转绕芳甸，月照花林皆似霰；空里流霜不觉飞，汀上白沙看不见。"
      width="800px"
      :max-title-width="500"
    >
      春江潮水连海平，海上明月共潮生。滟滟随波千万里，何处春江无月明！
      江流宛转绕芳甸，月照花林皆似霰；空里流霜不觉飞，汀上白沙看不见。
      江天一色无纤尘，皎皎空中孤月轮。江畔何人初见月？江月何年初照人？
      人生代代无穷已，江月年年望相似。不知江月待何人，但见长江送流水。
      <template v-slot:footer>
        <div class="demo-modal-footer">
          <mtd-button @click="closeModal" type="panel">取消</mtd-button
          ><mtd-button type="primary" @click="closeModal">确定</mtd-button>
        </div>
      </template>
    </mtd-modal>
  </div>
</template>
<script>
export default {
  data() {
    return {
      visible: false,
    };
  },
  methods: {
    openModal() {
      this.visible = true;
    },
    closeModal() {
      this.visible = false;
    }
  },
};
</script>

```

#### doc/modeless-drag.vue

```vue
<template>
  <div>
    <mtd-button type="primary" @click="openModal1">可拖拽对话框</mtd-button>
    <mtd-modal title="对话框" v-model="visible1" width="400px" :mask="false" drag>
      <div>
        支持拖拽的对话框<br/>
        内容<br/>
      </div>
      <template v-slot:footer>
        <div class="demo-modal-footer">
          <mtd-button @click="closeModal1" type="panel">取消</mtd-button
          ><mtd-button type="primary" @click="closeModal1">确定</mtd-button>
        </div>
      </template>
    </mtd-modal>
  </div>
</template>
<script>
export default {
  data() {
    return {
      visible1: false,
    };
  },
  methods: {
    openModal1() {
      this.visible1 = true;
    },
    closeModal1() {
      this.visible1 = false;
    },
  },
};
</script>

```

#### doc/modeless.vue

```vue
<template>
  <div class="demo-full-width">
    <div class="demo-modal-btn-groups">
      <mtd-button type="primary" @click="openModal1">模态对话框</mtd-button>
      <mtd-button type="primary" @click="openModal2">非模态对话框</mtd-button>
      <mtd-button type="primary" @click="openModal3">全屏对话框</mtd-button>
    </div>

    <mtd-modal title="对话框" v-model="visible1" width="400px">
      <div>
        模态对话框<br/>
        内容<br/>
      </div>
      <template v-slot:footer>
        <div class="demo-modal-footer">
          <mtd-button @click="closeModal1" type="panel">取消</mtd-button>
          <mtd-button type="primary" @click="closeModal1">确定</mtd-button>
        </div>
      </template>
    </mtd-modal>

    <mtd-modal title="对话框" v-model="visible2" width="400px" :mask="false">
      <div>
        非模态对话框<br/>
        内容<br/>
      </div>
      <template v-slot:footer>
        <div class="demo-modal-footer">
          <mtd-button @click="closeModal2" type="panel">取消</mtd-button>
          <mtd-button type="primary" @click="closeModal2">确定</mtd-button>
        </div>
      </template>
    </mtd-modal>

    <mtd-modal title="对话框" v-model="visible3" fullscreen>
      <div>
        全屏对话框<br/>
        内容<br/>
      </div>
      <template v-slot:footer>
        <div class="demo-modal-footer">
          <mtd-button @click="closeModal3" type="panel">取消</mtd-button>
          <mtd-button type="primary" @click="closeModal3">确定</mtd-button>
        </div>
      </template>
    </mtd-modal>
  </div>
</template>
<script>
export default {
  data() {
    return {
      visible1: false,
      visible2: false,
      visible3: false,
    };
  },
  methods: {
    openModal1() {
      this.visible1 = true;
    },
    closeModal1() {
      this.visible1 = false;
    },
    openModal2() {
      this.visible2 = true;
    },
    closeModal2() {
      this.visible2 = false;
    },
    openModal3() {
      this.visible3 = true;
    },
    closeModal3() {
      this.visible3 = false;
    },
  },
};
</script>

```

#### doc/placement.vue

```vue
<style lang="scss" scoped>
.demo-modal-top-center {
  ::v-deep {
    .mtd-modal {
      margin-bottom: 20vh;
    }

  }
}
</style>
<template>
  <div class="demo-full-width">
    <div class="demo-modal-btn-groups">
      <mtd-button type="primary" @click="openModal2">顶部对话框</mtd-button>
      <mtd-button type="primary" @click="openModal4">居中的对话框</mtd-button>
      <mtd-button type="primary" @click="openModal3">调整弹出位置的对话框</mtd-button>
    </div>

    <mtd-modal
      v-model="visible2"
      title="顶部对话框"
      width="400px"
      placement="top"
    >
      春江潮水连海平，海上明月共潮生。滟滟随波千万里，何处春江无月明！
      江流宛转绕芳甸，月照花林皆似霰；空里流霜不觉飞，汀上白沙看不见。
      江天一色无纤尘，皎皎空中孤月轮。江畔何人初见月？江月何年初照人？
      人生代代无穷已，江月年年望相似。不知江月待何人，但见长江送流水。
      <template v-slot:footer>
        <div class="demo-modal-footer">
          <mtd-button @click="closeModal2" type="panel">取消</mtd-button
          ><mtd-button type="primary" @click="closeModal2">确定</mtd-button>
        </div>
      </template>
    </mtd-modal>

    <mtd-modal
      v-model="visible3"
      class="demo-modal-top-center"
      title="调整弹出位置的对话框"
      width="400px"
    >
      春江潮水连海平，海上明月共潮生。滟滟随波千万里，何处春江无月明！
      江流宛转绕芳甸，月照花林皆似霰；空里流霜不觉飞，汀上白沙看不见。
      江天一色无纤尘，皎皎空中孤月轮。江畔何人初见月？江月何年初照人？
      人生代代无穷已，江月年年望相似。不知江月待何人，但见长江送流水。
      <template v-slot:footer>
        <div class="demo-modal-footer">
          <mtd-button @click="closeModal3" type="panel">取消</mtd-button
          ><mtd-button type="primary" @click="closeModal3">确定</mtd-button>
        </div>
      </template>
    </mtd-modal>

    <mtd-modal v-model="visible4" title="居中的对话框" width="400px">
      春江潮水连海平，海上明月共潮生。滟滟随波千万里，何处春江无月明！
      江流宛转绕芳甸，月照花林皆似霰；空里流霜不觉飞，汀上白沙看不见。
      江天一色无纤尘，皎皎空中孤月轮。江畔何人初见月？江月何年初照人？
      人生代代无穷已，江月年年望相似。不知江月待何人，但见长江送流水。
      <template v-slot:footer>
        <div class="demo-modal-footer">
          <mtd-button @click="closeModal4" type="panel">取消</mtd-button
          ><mtd-button type="primary" @click="closeModal4">确定</mtd-button>
        </div>
      </template>
    </mtd-modal>
  </div>
</template>
<script>
export default {
  data() {
    return {
      visible2: false,
      visible3: false,
      visible4: false,
    };
  },
  methods: {
    openModal2() {
      this.visible2 = true;
    },
    closeModal2() {
      this.visible2 = false;
    },
    openModal3() {
      this.visible3 = true;
    },
    closeModal3() {
      this.visible3 = false;
    },
    openModal4() {
      this.visible4 = true;
    },
    closeModal4() {
      this.visible4 = false;
    },
  },
};
</script>

```

#### index.md

```markdown
<style lang='scss'>
  .demo-modal-btn-groups{
    display: flex;
    align-items: center;
    justify-content: center;
    button {
      margin-left: 24px;
      margin-right: 24px;
    }
  }
</style>
# 对话框 / Modal
对话框（Modal）是覆盖在主窗口上的临时窗口，启动系统与用户之间的对话。用于提供有关当前任务流程的关键信息、需要用户做出决策的信息及要求用户录入信息

## 互动演示
:::include(src="./demo/modal-interactive.vue")
:::


## 代码示例

### 弹出位置
根据应用场景的需要，提供2种弹出位置：顶部、居中，并允许修改基于居中位置的偏移值，来调整对话框的弹出位置。
:::include(src="./doc/placement.vue")
:::

### 自定义元素
可自定义是否配置对话框内容和底部操作按钮。
:::include(src="./doc/custom-area.vue")
:::

### 自定义内容
通过自定义对话框的标题、内容区域和操作按钮，可以用对话框承载详情信息或相关功能操作，这里列举一些常见情况。
:::include(src="./doc/custom.vue")
:::

### 可拖拽
支持用户拖拽对话框。
:::include(src="./doc/modeless-drag.vue")
:::

### 模态与非模态对话框
对话框提供三种模式。其中模态对话框会中断用户操作，用户必须处理对话框内容；非模态对话框不中断用户操作，用户可以继续与主页面内容交互。
:::include(src="./doc/modeless.vue")
:::

### 消息提示对话框
消息提示对话框包含信息提示、成功提示、警告提示、失败提示四种类型，仅提供一个确认按钮用于关闭消息提示对话框。
:::include(src="./doc/icon-modal.vue")
:::


### 异步关闭
当对话框按钮触发的操作需要和后端进行校验时，会出现 loading 状态，异步关闭对话框。
:::include(src="./doc/async-close.vue")
:::

### 设置对话框标题最大宽度
支持设置对话框标题的最大宽度，当标题超出设定最大宽度时，超出部分省略，可悬停查看。
:::include(src="./doc/max-title-width.vue")
:::

### 自定义右上角控制操作
支持在对话框右上角添加自定义操作，这里以非模态对话框支持最小化为例。
:::include(src="./doc/custom-menu.vue")
:::

## API
<api-doc name="Modal" :doc="require('./api.json')"></api-doc>

```

#### index.ts

```typescript
import Modal from './modal'
import { withInstall } from '@ss/mtd-adapter'
export default withInstall(Modal)

```

#### inside.tsx

```tsx
import {
  defineComponent,
  ref,
  computed,
  reactive,
  watch,
  getSlotsInRender,
  styles,
  onMounted,
  nextTick,
} from '@ss/mtd-adapter'
import useConfig from '@hooks/config'
import MtdIcon from '@components/icon'
import MtdButton from '@components/button'
import Tooltip from '@components/tooltip'

export default defineComponent({
  name: 'ModalInside',
  components: {
    MtdIcon,
    MtdButton,
  },
  inheritAttrs: true,
  props: {
    title: String,
    closable: Boolean,
    drag: Boolean,
    visible: Boolean,
    maxTitleWidth: Number,
    fullscreen: Boolean,
  },
  emits: ['close'],
  setup(props, { emit }) {
    const config = useConfig()
    const prefix = computed(() => config.getPrefixCls('modal'))
    const titleTipVisible = ref<boolean>(false)
    const shandowTitleRef = ref<HTMLElement | null>(null)
    const startXY = reactive({
      x: 0,
      y: 0,
    })
    const translate = reactive({
      x: 0,
      y: 0,
    })
    const transform = ref('translate(0,0)')
    // 最后一次拖拽的位置
    const lastTranslate = {
      x: 0,
      y: 0,
    }
    let lastTransform = 'translate(0,0)'

    watch(() => props.visible, (v) => {
      if (v) {
        init()
      }
    })

    function handleClose() {
      emit('close')
    }
    function handleMouseDown(e: MouseEvent) {
      if (!props.drag) {
        return
      }
      startXY.x = e.screenX
      startXY.y = e.screenY

      e.preventDefault()
      window.addEventListener('mousemove', onDragging)
      window.addEventListener('mouseup', onDragEnd)
    }
    function onDragging(e: MouseEvent) {
      const offsetXY = {
        x: translate.x + (e.screenX - startXY.x),
        y: translate.y + (e.screenY - startXY.y),
      }
      transform.value = `translate(${offsetXY.x}px,${offsetXY.y}px)`
    }
    function onDragEnd(e: MouseEvent) {
      translate.x = translate.x + (e.screenX - startXY.x)
      translate.y = translate.y + (e.screenY - startXY.y)
      window.removeEventListener('mousemove', onDragging)
      window.removeEventListener('mouseup', onDragEnd)
    }
    function init() {
      translate.x = 0
      translate.y = 0
      transform.value = 'translate(0,0)'
    }

    const checkTitleTooltipVisible = () => {
      let visible = false
      if (props.maxTitleWidth && shandowTitleRef.value) {
        const titleRect = shandowTitleRef.value.getBoundingClientRect()
        if (titleRect.width > props.maxTitleWidth) {
          visible = true
        }
      }
      titleTipVisible.value = visible
    }

    onMounted(() => {
      nextTick(() => {
        checkTitleTooltipVisible()
      })
    })

    watch([() => props.title, () => props.maxTitleWidth], () => {
      nextTick(() => {
        checkTitleTooltipVisible()
      })
    })

    // 退出全屏时恢复拖拽位置
    watch(() => props.fullscreen, (newVal) => {
      if (newVal) {
        lastTranslate.x = translate.x
        lastTranslate.y = translate.y
        lastTransform = transform.value
        init()
      } else {
        translate.x = lastTranslate.x
        translate.y = lastTranslate.y
        transform.value = lastTransform
      }
    })

    return {
      prefix,
      transform,
      handleClose,
      handleMouseDown,
      shandowTitleRef,
      titleTipVisible,
      checkTitleTooltipVisible,
    }
  },
  render() {
    const {
      prefix, title, closable, transform, maxTitleWidth, titleTipVisible,
    } = this

    const titleContent = (
      <div
        class={`${prefix}-title`}
        style={maxTitleWidth ? {'max-width': `${maxTitleWidth}px`} : undefined}
      >
        {title}
      </div>
    )

    return <div class={`${prefix}`} style={{
      transform: transform,
    }}>
      {
        getSlotsInRender(this, 'menu')
          ? <div class={`${prefix}-menu`}>
            {getSlotsInRender(this, 'menu')}
          </div>
          : (closable &&
            <span class={`${prefix}-close`} onClick={this.handleClose}>
              <MtdIcon name='close' />
            </span>)
      }
      {(title || getSlotsInRender(this, 'title')) &&
        <div
          class={`${prefix}-header`}
          onMousedown={this.handleMouseDown}
        >
          {
            getSlotsInRender(this, 'title')
              ? getSlotsInRender(this, 'title')
              : (
                titleTipVisible
                  ? <mtd-tooltip content={title} placement='top'>
                    {titleContent}
                  </mtd-tooltip>
                  : titleContent
              )
          }
          <div ref="shandowTitleRef"
            class={`${prefix}-title`}
            style={{
              visibility: 'hidden',
              position: 'absolute',
            }}
          >
            {title}
          </div>
        </div>
      }

      <div class={`${prefix}-content-wrapper`}>
        <div class={`${prefix}-content`}>{getSlotsInRender(this)}</div>
      </div>

      {getSlotsInRender(this, 'footer') && <div class={`${prefix}-footer`}>
        {getSlotsInRender(this, 'footer')}
      </div>}
    </div >
  },
})

```

#### modal.tsx

```tsx
import {
  defineComponent,
  PropType,
  computed,
  reactive,
  markRaw,
  watch,
  onMounted,
  onActivated,
  onDeactivated,
  toRefs,
  ref,
  onBeforeUnmount, classNames, styles,
  getSlotsInRender,
  vueInstance,
  Transition,
  useResetAttrs,
  vSlots,
  hasProp,
} from '@ss/mtd-adapter'
import useConfig from '@hooks/config'
import { lock, unlock } from '@utils/lock-scroll'
import { PopupManage, getDefaultPopupContainer } from '@utils/popper'
import ModalInside from './inside'
import mitt from '@utils/mitt'
import { hasClass } from '@utils/dom'
import { getPopupContainer } from './types'
import { isNumber, isDef } from '@utils/type'
import getElement from '@components/hooks/getElement'
import teleportHook from '@components/hooks/teleport'

export default defineComponent({
  name: 'MtdModal',
  components: {
    ModalInside,
  },
  inheritAttrs: false,
  model: {
    prop: 'modelValue',
    event: 'update:modelValue',
  },
  props: {
    appendToContainer: {
      type: Boolean,
      default: true,
    },
    getPopupContainer: {
      type: Function as PropType<getPopupContainer>,
      default: getDefaultPopupContainer,
    },
    mask: {
      type: Boolean,
      default: true,
    },
    closable: {
      type: Boolean,
      default: true,
    },
    maskClosable: {
      type: Boolean,
      default: false,
    },
    fullscreen: Boolean,
    modelValue: Boolean,
    destroyOnClose: Boolean,
    lockScroll: {
      type: Boolean,
      default: true,
    },
    placement: {
      type: String,
      validator: (v: string) => {
        return ['top', 'center'].indexOf(v) > -1
      },
      default: 'center',
    },
    width: [String, Number],
    mountOnCreate: {
      type: Boolean,
      default: false,
    },
    drag: Boolean,
    title: String,
    maxTitleWidth: Number,
    keyboard: {
      type: Boolean,
      default: true,
    },
  },
  emits: ['close', 'closed', 'open', 'opened', 'input', 'update:modelValue'],
  setup(props, { attrs, emit }) {
    const config = useConfig()
    const prefix = computed(() => config.getPrefixCls('modal'))
    const prefixMTD = computed(() => config.getPrefix())

    const ins = vueInstance()
    const resetAttrs = useResetAttrs(attrs)
    const maskRef = ref<HTMLElement | null>(null)
    const modalRef = ref<any | null>(null)

    const state = reactive({
      modalVisible: !props.mountOnCreate ? props.modelValue : true,
      wrapVisible: props.modelValue,
      zIndex: '2000',
      emitter: markRaw(mitt()),
    })

    const { createTele, destroyTele } = teleportHook(props)

    // @Created
    state.emitter.on('esc', handleEscClose)

    // @Mounted
    onMounted(() => { init() })
    onActivated(() => { init() })

    // @Computed
    const modalWidth = computed(() => {
      return props.width
        ? isNumber(props.width)
          ? `${props.width}px`
          : (props.width)
        : undefined
    })

    // @Watch
    watch(() => props.modelValue, (val) => {
      val ? openModal() : closeModal()
    })

    onBeforeUnmount(() => {
      destroy()
    })
    onDeactivated(() => {
      destroy()
    })

    // @Methods
    function init() {
      if (props.modelValue) {
        openModal()
      }
    }
    function destroy() {
      closeModal()
      destroyTele()
      if (ins.$el && ins.$el.remove) {
        ins.$el.remove()
      } else if (ins.$el && (ins.$el as any).removeNode) {
        // to fix ie
        (ins.$el as any).removeNode()
      }
    }
    function openModal() {
      state.modalVisible = true
      state.wrapVisible = true
      state.zIndex = PopupManage.nextZIndex()
      PopupManage.open(ins)
      createTele()
      if (props.lockScroll) {
        lock(document.body, prefixMTD.value ?? 'mtd')
      }
      emit('open')
    }
    function closeModal() {
      PopupManage.close(ins)
      if (props.lockScroll) {
        unlock(document.body, prefixMTD.value ?? 'mtd')
      }
    }
    function close() {
      emit('close')
      emit('update:modelValue', false)
      emit('input', false)
    }
    function handleAfterLeave() {
      if (props.destroyOnClose) {
        state.modalVisible = false
      }
      state.wrapVisible = false
      emit('closed')
    }
    function handleAfterEnter() {
      emit('opened')

      /**
       * 用于max-title-width props。
       * 为了排除v-if、v-show等场景下可能无法正确
       * 获取标题宽度，因此在动画结束后再次检查标题宽度
       */
      if (modalRef.value) {
        modalRef.value.checkTitleTooltipVisible()
      }
    }
    function handleMaskClick() {
      props.maskClosable && props.modelValue && close()
    }
    function handleClose() {
      props.modelValue && close()
    }
    function handleWrapClick(event: Event) { // 🤡
      if (hasClass(event.target as HTMLElement, `${prefixMTD.value}-modal-wrapper`)) {
        handleMaskClick()
      }
    }
    function handleEscClose() {
      if ((hasProp(ins, 'keyboard') && isDef(props.keyboard) ? props.keyboard : props.closable) && props.modelValue) {
        close()
      }
    }

    const computedCollection = {
      modalWidth,
    }
    const methodsCollection = {
      handleMaskClick,
      handleWrapClick,
      handleAfterEnter,
      handleAfterLeave,
      handleClose,
      destroy,
    }

    return {
      ...toRefs(state),
      ...computedCollection,
      ...methodsCollection,
      prefix, resetAttrs,
      maskRef, prefixMTD,
      modalRef,
    }
  },
  render() {
    const {
      prefix, resetAttrs, modalVisible, zIndex, title,
      modelValue, mask, wrapVisible, placement, closable, fullscreen, modalWidth, drag,
      maxTitleWidth,
    } = this

    const slots = {
      title: getSlotsInRender(this, 'title') ? () => getSlotsInRender(this, 'title') : undefined,
      footer: getSlotsInRender(this, 'footer') ? () => getSlotsInRender(this, 'footer') : undefined,
      menu: getSlotsInRender(this, 'menu') ? () => getSlotsInRender(this, 'menu') : undefined,
    }
    return <div
      class={classNames(this, [prefix + '-container'])}
      style={styles(this)}
      {...resetAttrs}
    >
      {/* 遮罩层 */}
      {mask &&
        <Transition name="fade-in" enter-from-class={'fade-in' + '-enter'}>
          <div
            v-show={modelValue}
            ref={'maskRef'}
            class={`${prefix}-mask`}
            style={{ 'z-index': zIndex as any }}
            onClick={this.handleMaskClick}
          />
        </Transition>
      }

      {/* 实际内容 */}
      <div
        class={{
          [`${prefix}-wrapper`]: true,
          [`${prefix}-${placement}`]: placement,
          [`${prefix}-drag`]: drag && !mask && !fullscreen,
        }}

        v-show={wrapVisible}
        style={{ 'z-index': zIndex as any }}
        onClick={this.handleWrapClick}
      >
        <Transition
          name="modal-fade"
          enter-from-class={'modal-fade' + '-enter'}
          appear
          onAfterEnter={this.handleAfterEnter}
          onAfterLeave={this.handleAfterLeave}
        >
          {modalVisible &&
            <modal-inside
              title={title}
              maxTitleWidth={maxTitleWidth}
              closable={closable}
              drag={drag && !mask && !fullscreen}
              fullscreen={fullscreen}
              ref={'modalRef'}
              class={{
                [`${prefix}-fullscreen`]: fullscreen,
              }}
              style={{ width: modalWidth }}
              v-show={modelValue}
              visible={modelValue}
              onClose={this.handleClose}
              {...vSlots(slots)}
              v-slots={slots}
            >
              {getSlotsInRender(this)}
            </modal-inside>
          }
        </Transition>
      </div >
    </div >
  },
})

```

#### types.ts

```typescript
import { Component } from '@components/types/component'
import { getPopupContainer } from '@components/popper/types'
export { getPopupContainer } from '@components/popper/types'

export declare interface IModal extends Component {
  appendToContainer?: boolean;
  getPopupContainer?: getPopupContainer;
  mask?: boolean;
  closable?: boolean;
  maskClosable?: boolean;
  fullscreen?: boolean;
  visible?: boolean;
  destroyOnClose?: boolean;
  lockScroll?: boolean;
  title?: string;
  placement?: 'top' | 'center';
  width?: string | number;
}
declare const Modal: IModal
export default Modal

```

#### usage.json

```json
{
  "componentName": "Modal",
  "desc": "对话框",
  "codeTemplate": "<mtd-modal v-bind=\"defaultProps\" v-on:update:modelValue=\"handleModalChange\">这是弹窗内容</mtd-modal>",
  "data": {}
}

```

