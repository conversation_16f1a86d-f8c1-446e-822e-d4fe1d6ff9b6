## 源码解析

### 文件依赖

- badge.tsx
- demo.md
- demo/badge-interactive.vue
- doc/composite-styles.vue
- doc/demo/offset-and-expand-direction.vue
- doc/location.vue
- doc/max.vue
- doc/red-badge.vue
- doc/text-badge.vue
- doc/text-direction.vue
- index.md
- index.ts
- tests/__snapshots__/badge.spec.jsx.snap
- tests/badge.spec.jsx
- types.ts
- usage.json

### 源码内容

#### badge.tsx

```tsx
import { defineComponent, computed, PropType, getSlotsInRender } from '@ss/mtd-adapter'
import useConfig from '@hooks/config'
import {BadgeStatus} from './types'
import { isNumber } from '@utils/type'

export default defineComponent({
  name: 'MtdBadge',
  inheritAttrs: true,
  props: {
    value: [String, Number],
    dot: { type: Boolean, default: false },
    max: Number,
    hidden: { type: Boolean, default: false },
    status: String as PropType<BadgeStatus>,
    offset: {
      type: Array as PropType<number[]>,
      default: () => [0, 0],
    },

    // 文字型徽标变宽时的拓宽方向
    textDirection: {
      type: String as PropType<'left' | 'right' | 'center'>,
      default: 'right',
      validator: (value: string) => {
        return ['left', 'right', 'center'].includes(value)
      },
    },
  },
  setup(props) {
    const text = computed(() => {
      if (props.dot) return
      if (isNumber(props.value) && isNumber(props.max)) {
        return props.max < props.value ? `${props.max}+` : props.value
      }
      return props.value
    })
    const rounded = computed(() => String(text.value).length === 1)
    const config = useConfig()
    const prefix = config.getPrefixCls('badge')

    const positionStyle = computed(() => {
      const [offsetX, offsetY] = props.offset
      const translateX = props.textDirection === 'center' ? '-50%' : '0px'
      const style: any = {
        transform: `translate(calc(${translateX} + ${offsetX}px), calc(-50% + ${offsetY}px))`,
      }
      return style
    })
    return {
      prefix,
      text,
      rounded,
      positionStyle,
    }
  },
  render() {
    const { text, rounded, prefix, positionStyle } = this
    const { hidden, dot, status, textDirection } = this.$props
    const hasSlot = !!getSlotsInRender(this)
    return (
      <div class={prefix}>
        {getSlotsInRender(this)}
        {(text || text === 0 || dot) && (
          <span
            class={[
              `${prefix}-text`,
              {
                [`${prefix}-position`]: hasSlot,
                [`${prefix}-position-expand-${textDirection}`]: hasSlot && !dot,
                [`${prefix}-dot`]: dot,
                [`${prefix}-hidden`]: hidden,
                [`${prefix}-rounded`]: rounded,
                [`${prefix}-${status!}`]: status,
              },
            ]}
            style={hasSlot ? positionStyle : null}
          >
            {text}
          </span>
        )}
      </div>
    )
  },
})

```

#### demo.md

```markdown
# 徽标 / Badge
补充demo

## 代码示例

### offset

:::include(src="./doc/demo/offset-and-expand-direction.vue")
:::

```

#### demo/badge-interactive.vue

```vue
<template>
  <div>
    <mtd-doc-usage name="badge" :apiJson="apiJson" :usageJson="usageJson" @propChange="propChange">
      <template slot="badge" slot-scope="{ configProps }">
        <div class="slot-container">
          <mtd-badge v-bind="configProps">
            <mtd-button type="primary">消息</mtd-button>
          </mtd-badge>
        </div>
      </template>
      <template #badge="{ configProps }">
        <mtd-badge v-bind="configProps">
          <mtd-button type="primary">消息</mtd-button>
        </mtd-badge>
      </template>
    </mtd-doc-usage>
  </div>
</template>

<script>
import apiJson from '../api.json'

export default {
  data() {
    return {
      apiJson: apiJson,
      usageJson: {
        componentName: "badge",
        desc: "徽标",
        codeTemplate: "<mtd-badge v-bind='configProps'><div class=\"square\"/></mtd-badge>"
      },
    }
  },
  methods: {
    propChange(propertyName, val) {
      console.log('prop changed: , propertyName: ' + propertyName + ', val: ' + val)
      if (propertyName === 'direction') {
        this.direction = val
      }
    },
  }
}
</script>

<style scoped lang="scss">

</style>

```

#### doc/composite-styles.vue

```vue
<template>
  <div class="demo-flex demo-full-width demo-min-height">
    <span style="display: inline-block;"  class="example-badge">
      <mtd-badge dot>
        <mtd-icon name='bell-o' style="color: #646971;"/>
      </mtd-badge>
      <span style="padding: 12px;color: #646971;">系统通知</span>
    </span>
    <mtd-badge :value="99" text-direction="right">
      <mtd-button type="primary">消息</mtd-button>
    </mtd-badge>
  </div>
</template>
<style lang="scss" scoped>
  .example-badge{
    margin-right: 40px;
    margin-top: 10px;
    vertical-align: middle;
  }
</style>

```

#### doc/demo/offset-and-expand-direction.vue

```vue
<template>
  <div class="">
    offsetX: <mtd-input-number v-model="offsetX" />
    <br>
    offsetY: <mtd-input-number v-model="offsetY" />
    <br>
    text-direction:
    <mtd-switch v-model="direction"></mtd-switch> {{directionText}}
    <br>
    badgeValue:
    <mtd-input-number v-model="badgeValue" />
    <br>
    max:
    <mtd-input-number v-model="max" />
    <br>
    <mtd-badge
      class="example-badge"
      :value="badgeValue"
      :offset="[offsetX, offsetY]"
      :max="max"
      :text-direction="directionText"
    >
      <span style="color: #646971;">系统通知</span>
    </mtd-badge>
    <span class="example-badge" style="display: inline-block;">
      <span style="display: inline-block; vertical-align: middle;color: #646971;">系统通知</span>
      <mtd-badge :value="badgeValue" style="vertical-align: middle; line-height: 1;" :offset="[offsetX, offsetY]" :max="max"/>
    </span>
    <mtd-badge
      class="example-badge"
      :value="badgeValue"
      :offset="[offsetX, offsetY]"
      :text-direction="directionText"
      :max="max"
      dot
    >
      <span style="color: #646971">系统通知</span>
    </mtd-badge>
  </div>
</template>
<style lang="scss" scoped>
.example-badge{
  margin-right: 40px;
  margin-top: 10px;
  vertical-align: middle;
}
</style>
<script >

export default {
  data() {
    return {
      offsetX: 0,
      offsetY: 0,
      direction: true,
      badgeValue: 99,
      max: 99,
    }
  },
  computed: {
    directionText() {
      return this.direction ? 'right' : 'left'
    }
  }
}
</script>

```

#### doc/location.vue

```vue
<template>
  <div class=" demo-flex demo-full-width demo-min-height">
    <mtd-badge class="example-badge" :value="99" :offset="[14, 0]">
      <span >系统通知</span>
    </mtd-badge>
    <span class="example-badge" style="display: inline-block;">
      <mtd-badge :value="12" style="vertical-align: middle; line-height: 1;" :offset="[14, 8]">
        <span style="display: inline-block; vertical-align: middle; color: #646971;">系统通知</span>
      </mtd-badge>
    </span>
  </div>
</template>
<style lang="scss" scoped>
  .example-badge{
    margin-right: 40px;
    margin-top: 10px;
    vertical-align: middle;
  }
</style>

```

#### doc/max.vue

```vue
<template>
  <div class="demo-flex demo-min-height">
    <mtd-badge :value="11" :max="10" class="example-badge" text-direction="left">
      <div class="square"/>
    </mtd-badge>
    <mtd-badge :value="100" :max="99" class="example-badge" text-direction="left">
      <div class="square"/>
    </mtd-badge>
    <mtd-badge :value="1000" :max="999" class="example-badge" text-direction="left">
      <div class="square"/>
    </mtd-badge>
  </div>
</template>
  <style lang="scss" scoped>
    .square{
      width: 40px;
      height: 40px;
      border-radius: 4px;
      background: rgba(17, 25, 37, 0.05);
    }
    .example-badge{
      margin-right: 40px;
      margin-top: 10px;
      vertical-align: middle;
    }
  </style>

```

#### doc/red-badge.vue

```vue
<template>
  <div class="demo-flex demo-full-width demo-min-height">
    <mtd-badge dot class="example-badge">
      <span class="text">系统通知</span>
    </mtd-badge>
    <mtd-badge dot class="example-badge">
      <mtd-icon name='bell-o' style="color: #646971"/>
    </mtd-badge>
  </div>
</template>
<style lang="scss" scoped>
  .text{
    color: rgba(17, 25, 37, 0.65);
  }
  .example-badge{
    margin-right: 40px;
    margin-top: 10px;
    vertical-align: middle;
  }
</style>

```

#### doc/text-badge.vue

```vue
<template>
  <div class="demo-flex demo-full-width demo-min-height">
    <mtd-badge :value="9" class="example-badge" text-direction="center">
      <div class="square"/>
    </mtd-badge>
    <mtd-badge :value="99" class="example-badge" text-direction="center">
      <div class="square"/>
    </mtd-badge>
    <mtd-badge value="Hot" class="example-badge" text-direction="center">
      <div class="square"/>
    </mtd-badge>
    <mtd-badge value="New" class="example-badge" text-direction="center">
      <div class="square"/>
    </mtd-badge>
  </div>
</template>
<style lang="scss" scoped>
  .square{
    width: 40px;
    height: 40px;
    border-radius: 4px;
    background: rgba(17, 25, 37, 0.05);
  }
  .example-badge{
    margin-right: 40px;
    margin-top: 10px;
    vertical-align: middle;
  }
</style>

```

#### doc/text-direction.vue

```vue
<template>
  <div class="demonstration-content">
    <mtd-tabs v-model="activeTab" type="text" size="small">
      <mtd-tab-pane label="向左扩展" value="Tab1" >
        <div class="demo-flex">
          <mtd-badge :value="11" :max="10" class="example-badge" text-direction="left">
            <div class="square"/>
          </mtd-badge>
          <mtd-badge :value="100" :max="99" class="example-badge" text-direction="left">
            <div class="square"/>
          </mtd-badge>
          <mtd-badge :value="1000" :max="999" class="example-badge" text-direction="left">
            <div class="square"/>
          </mtd-badge>
        </div>
      </mtd-tab-pane>
      <mtd-tab-pane label="向右扩展" value="Tab2">
        <div class="demo-flex">
          <mtd-badge :value="11" :max="10" class="example-badge" text-direction="right">
            <div class="square"/>
          </mtd-badge>
          <mtd-badge :value="100" :max="99" class="example-badge" text-direction="right">
            <div class="square"/>
          </mtd-badge>
          <mtd-badge :value="1000" :max="999" class="example-badge" text-direction="right">
            <div class="square"/>
          </mtd-badge>
        </div>
      </mtd-tab-pane>
      <mtd-tab-pane label="向两边扩展" value="Tab3">
        <div class="demo-flex">
          <mtd-badge :value="11" :max="10" class="example-badge" text-direction="center">
            <div class="square"/>
          </mtd-badge>
          <mtd-badge :value="100" :max="99" class="example-badge" text-direction="center">
            <div class="square"/>
          </mtd-badge>
          <mtd-badge :value="1000" :max="999" class="example-badge" text-direction="center">
            <div class="square"/>
          </mtd-badge>
        </div>
      </mtd-tab-pane>
    </mtd-tabs>
  </div>
</template>
  <style lang="scss" scoped>
    .square{
      width: 40px;
      height: 40px;
      border-radius: 4px;
      background: rgba(17, 25, 37, 0.05);
    }
    .example-badge{
      margin-right: 40px;
      margin-top: 10px;
      vertical-align: middle;
    }
  </style>
<script>
export default {
  data () {
    return {
      activeTab: 'Tab1'
    };
  },
};
</script>

```

#### index.md

```markdown
<style lang="scss" scoped>
  ::v-deep {
    .demonstration-content {
      margin-top: 50px;
      padding: 100px 0;
      width: 100%;
      & > div {
        width: 100%;
      }
    }

    .demonstration-content > .mtd-tabs-small > .mtd-tabs-nav {
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      border-bottom: 1px solid #edf0f7;
      padding: 9px 16px;
      padding: 1.5px 16px;
    }
    .demo-min-height {
      margin-top: 50px;
      padding: 100px 0;
    }
  }
</style>

# 徽标 / Badge
徽标是指出现在图标或文字右上角的红色标记（圆点、数字或者文字），表示有新内容或者待处理的信息。

## 互动演示

:::include(src="./demo/badge-interactive.vue")
:::

## 代码示例

### 红点型徽标

:::include(src="./doc/red-badge.vue")
:::

### 文本型徽标
:::include(src="./doc/text-badge.vue")
:::

### 扩展方向
支持通过配置`text-direction`控制徽标数值增加时动态扩展的方向。
:::include(src="./doc/text-direction.vue")
:::

### 最大值
文本型可设置徽标数值的最大值，超过最大值会显示{max}+。
:::include(src="./doc/max.vue")
:::

### 自定义位置
可通过`offset`自定义徽标位置。
:::include(src="./doc/location.vue")
:::

### 组合样式
:::include(src="./doc/composite-styles")
:::

## API

<api-doc name="Badge" :doc="require('./api.json')"></api-doc>

```

#### index.ts

```typescript
import Badge from './badge'
import { withInstall } from '@ss/mtd-adapter'
export default withInstall(Badge)

```

#### tests/__snapshots__/badge.spec.jsx.snap

```plaintext
// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Badge create badge 1`] = `<div class="mtd-badge">系统通知<span class="mtd-badge-text mtd-badge-position">12</span></div>`;

exports[`Badge renders ./src/badge/doc/base.vue correctly 1`] = `
<div>
  <div class="example-badge mtd-badge" style="padding: 2px;"><span>系统通知</span><span class="mtd-badge-text mtd-badge-position mtd-badge-rounded">8</span></div>
  <div class="example-badge mtd-badge" style="padding: 2px;"><span>系统通知</span><span class="mtd-badge-text mtd-badge-position">99+</span></div>
  <div class="example-badge mtd-badge"><i class="mtdicon mtdicon-bell-o" style="font-size: 34px; color: rgb(128, 138, 177);"></i><span class="mtd-badge-text mtd-badge-position">12</span></div>
  <div class="example-badge mtd-badge"><i class="mtdicon mtdicon-bell-o" style="font-size: 34px; color: rgb(128, 138, 177);"></i><span class="mtd-badge-text mtd-badge-position">99+</span></div>
</div>
`;

exports[`Badge renders ./src/badge/doc/combination.vue correctly 1`] = `
<div><button type="text" class="example-badge mtd-btn mtd-btn-text"><span class="mtd-btn-before"><div class="mtd-badge"><i class="mtdicon mtdicon-theme"></i><span class="mtd-badge-text mtd-badge-position mtd-badge-dot"></span></div></span><span>
    主题
  </span></button>
<div class="example-badge mtd-badge"><button class="mtd-btn"><span class="mtd-btn-before"><i class="mtdicon mtdicon-cart-o"></i></span><span>
      购物车
    </span></button><span class="mtd-badge-text mtd-badge-position">12</span></div>
</div>
`;

exports[`Badge renders ./src/badge/doc/location.vue correctly 1`] = `
<div>
  <div class="example-badge mtd-badge" style="padding: 2px;"><span>系统通知</span><span class="mtd-badge-text mtd-badge-position">12</span></div> <span class="example-badge" style="display: inline-block;"><span style="display: inline-block; padding: 2px; vertical-align: middle;">系统通知</span>
  <div class="mtd-badge" style="vertical-align: middle; line-height: 1;"><span class="mtd-badge-text">12</span></div>
  </span>
  <div class="example-badge mtd-badge"><i class="mtdicon mtdicon-bell-o" style="font-size: 34px; color: rgb(128, 138, 177);"></i><span class="mtd-badge-text mtd-badge-position">12</span></div> <span style="display: inline-block;"><span style="display: inline-block; vertical-align: middle;"><i class="mtdicon mtdicon-bell-o" style="font-size: 34px; color: rgb(128, 138, 177);"></i></span>
  <div class="mtd-badge" style="vertical-align: middle;"><span class="mtd-badge-text">12</span></div>
  </span>
</div>
`;

exports[`Badge renders ./src/badge/doc/num.vue correctly 1`] = `
<div><span class="example-badge">
    design v0.2
    <div class="badge-dot-center badge mtd-badge"><span class="mtd-badge-text mtd-badge-dot"></span></div></span>
<div class="example-badge mtd-badge"><i class="mtdicon mtdicon-bell-o" style="font-size: 34px; color: rgb(128, 138, 177);"></i><span class="mtd-badge-text mtd-badge-position mtd-badge-dot"></span></div>
<div class="example-badge mtd-badge" style="padding: 2px;">
  <div>系统通知</div><span class="mtd-badge-text mtd-badge-position">new</span>
</div>
<div class="example-badge mtd-badge" style="padding: 2px;">
  <div>系统通知</div><span class="mtd-badge-text mtd-badge-position">hot</span>
</div>
</div>
`;

exports[`Badge renders ./src/badge/doc/status.vue correctly 1`] = `<div><span class="example-badge"><div class="my-badge mtd-badge"><span class="mtd-badge-text mtd-badge-dot mtd-badge-error"></span></div> <span>错误</span></span> <span class="example-badge"><div class="my-badge mtd-badge"><span class="mtd-badge-text mtd-badge-dot mtd-badge-success"></span></div> <span>成功</span></span> <span class="example-badge"><div class="my-badge mtd-badge"><span class="mtd-badge-text mtd-badge-dot mtd-badge-process"></span></div> <span>进行中</span></span> <span class="example-badge"><div class="my-badge mtd-badge"><span class="mtd-badge-text mtd-badge-dot mtd-badge-disabled"></span></div> <span>过期</span></span> <span class="example-badge"><div class="my-badge mtd-badge"><span class="mtd-badge-text mtd-badge-dot mtd-badge-warning"></span></div> <span>警告</span></span></div>`;

```

#### tests/badge.spec.jsx

```jsx
import { createDemoTest, mount } from '@tests/utils'
import Badge from '../index'

describe('Badge', function () {
  createDemoTest('badge')
  it('create badge', function () {
    const wrapper = mount({
      render () {
        return <Badge value={12}>系统通知</Badge>
      },
    })
    expect(wrapper.html()).toMatchSnapshot()
  })
})

```

#### types.ts

```typescript
export type BadgePlacement = 'top-end' | 'right' | 'left';
export type BadgeStatus =
  | 'error'
  | 'success'
  | 'process'
  | 'disabled'
  | 'warning';

import Badge from './index'

export type Checkbox = InstanceType<typeof Badge>
```

#### usage.json

```json
{
    "componentName": "Badge",
    "desc": "徽标",
    "codeTemplate": "<mtd-badge v-bind='configProps'></mtd-badge>"
  }
```

