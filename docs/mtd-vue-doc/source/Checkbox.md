## 源码解析

### 文件依赖

- checkbox.tsx
- demo.md
- demo/checkbox-interactive.vue
- demo/usage.json
- doc/all-checked.vue
- doc/base.vue
- doc/controlled.vue
- doc/demo/group-checkbox-max-min.vue
- doc/demo/group-checkbox.vue
- doc/group.vue
- doc/nested.vue
- doc/size.vue
- doc/status.vue
- index.md
- index.ts
- props.ts
- tests/__snapshots__/checkbox.spec.jsx.snap
- tests/checkbox.spec.jsx
- types.ts

### 源码内容

#### checkbox.tsx

```tsx
import {
  defineComponent,
  computed,
  getSlotsInRender,
} from '@ss/mtd-adapter'
import useConfig from '@hooks/config'
import { useCheckboxGroup } from '@components/checkbox-group/useCheckboxGroup'
import MtdIcon from '@components/icon'
import checkboxProps from './props'

export default defineComponent({
  name: 'MtdCheckbox',
  components: {
    MtdIcon,
  },
  inheritAttrs: true,
  model: {
    prop: 'modelValue',
    event: 'update:modelValue',
  },
  props: checkboxProps(),
  emits: ['input', 'change', 'click', 'update:modelValue', 'update:value'], // click 内部使用不暴露
  setup(props, ctx) {
    const config = useConfig()
    const prefix = computed(() => config.getPrefixCls('checkbox'))

    const groupContext = useCheckboxGroup(props, ctx as any)
    const { checked, m_disabled, m_size, changeChecked } = groupContext
    const wrapperCls = computed((): Object => {
      return [
        prefix.value,
        {
          [`${prefix.value}-checked`]: checked.value,
          [`${prefix.value}-disabled`]: m_disabled.value,
          [`${prefix.value}-${m_size.value}`]: m_size.value,
          [`${prefix.value}-indeterminate`]: props.indeterminate,
        },
      ]
    })

    const handleChange = () => {
      if (!m_disabled.value) {
        changeChecked()
      }
    }

    const handleClick = (e: Event) => {
      ctx.emit('click', e)
    }

    return {
      prefix,
      wrapperCls,
      handleChange,
      handleClick,
      ...groupContext,
    }
  },
  render() {
    const { prefix, wrapperCls, handleChange, checked, m_name, m_disabled, handleClick } = this
    const { label, indeterminate } = this.$props

    return <label class={wrapperCls} onClick={handleClick}>
      <input
        class={`${prefix}-input`}
        type="checkbox"
        checked={checked}
        onChange={handleChange}
        name={m_name}
        disabled={m_disabled}
        style="display: none;"
      />
      <span class={`${prefix}-inner`}>
        {indeterminate ?
          <i class={`${prefix}-indeterminate-inner`} /> :
          <MtdIcon name="check-thick" v-show={checked} />}
      </span>
      <span class={`${prefix}-text`}>
        {getSlotsInRender(this) || label}
      </span>
    </label>
  },
})

```

#### demo.md

```markdown
# 复选框 / Checkbox

## 补充Demo

### checkbox-group受控
:::include(src="./doc/demo/group-checkbox.vue")
:::


### checkbox-group极值
:::include(src="./doc/demo/group-checkbox-max-min.vue")
:::
```

#### demo/checkbox-interactive.vue

```vue
<template>
  <mtd-doc-usage name='Checkbox' :apiJson="apiJson" :usageJson="usage">
    <template #Checkbox="{ configProps }">
      <div class="example-checkbox-all">
        <div>
          <mtd-checkbox v-bind="configProps" v-model="checkedAll" @input="handleCheckedAll"
            :indeterminate="isIndeterminate" style="margin-right: 32px;">全选</mtd-checkbox>
        </div>
        <br />
        <mtd-checkbox-group v-bind="configProps" v-model="group" @input="handleCheckItem">
          <mtd-checkbox v-for="(t, index) in options" :key="index" :value="index">{{ t }}</mtd-checkbox>
        </mtd-checkbox-group>
      </div>
    </template>
  </mtd-doc-usage>
</template>
<script>
const usage = require('./usage.json')
const apiJson = require('../api.json')
export default {
  data() {
    return {
      usage,
      apiJson,
      checkedAll: false,
      group: [],
      options: ['选项', '选项', '选项', '选项']
    };
  },
  computed: {
    isIndeterminate() {
      const { length } = this.group;
      return !!length && length !== this.options.length;
    },
  },
  methods: {
    handleCheckedAll(checked) {
      this.group = checked ? this.options.map((_, index) => index) : [];
    },
    handleCheckItem(items) {
      this.checkedAll = items.length === this.options.length;
    },
  },
};
</script>
<style scoped lang="scss">
.example-checkbox-all {
  text-align: left;
  display: inline-block;

  .mtd-checkbox-group {
    display: inline-block;
  }
}
</style>
    
```

#### demo/usage.json

```json
{
    "componentName": "Checkbox",
    "desc": "复选框",
    "codeTemplate": "<mtd-checkbox v-bind='configProps'></mtd-checkbox>"
}
```

#### doc/all-checked.vue

```vue
<template>
  <div class="demo-flex" style="padding: 100px 0;">
    <div class="example-checkbox-all">
      <div>
        <mtd-checkbox v-model="checkedAll" @input="handleCheckedAll" :indeterminate="isIndeterminate"
          style="margin-right: 32px;">全选</mtd-checkbox>
      </div>
      <br />
      <mtd-checkbox-group v-model="group3" @input="handleCheckItem">
        <mtd-checkbox v-for="(t, index) in options" :key="index" :value="index">{{ t }}</mtd-checkbox>
      </mtd-checkbox-group>
    </div>
  </div>
</template>
<script>
const options = ['选项', '选项', '选项', '选项'];
export default {
  data() {
    return {
      options,
      checkedAll: false,
      group3: [],
    };
  },
  computed: {
    isIndeterminate() {
      const { length } = this.group3;
      return !!length && length !== this.options.length;
    },
  },
  methods: {
    handleCheckedAll(checked) {
      this.group3 = checked ? this.options.map((_, index) => index) : [];
    },
    handleCheckItem(items) {
      this.checkedAll = items.length === this.options.length;
    },
  },
};
</script>

<style lang='scss' scoped>
.example-checkbox-all {
  text-align: left;
  display: inline-block;

  .mtd-checkbox-group {
    display: inline-block;
  }
}
</style>

```

#### doc/base.vue

```vue
<template>
  <div 
  class="demo-full-width"
  style="text-align: left;display: flex;align-items: center;justify-content: space-around">
    <mtd-checkbox v-model="checked" @change="handleChange">只有一个选项</mtd-checkbox>
    <mtd-checkbox 
      v-model="value"
      :true-value="1"
      :false-value="0"
    >
      自定义value
    </mtd-checkbox>

    <mtd-checkbox-group class="checkbox-vertical" v-model="group" @change="handleChange">
      <mtd-checkbox value="1">选项 1</mtd-checkbox>
      <mtd-checkbox value="2">选项 2</mtd-checkbox>
      <mtd-checkbox value="3">选项 3</mtd-checkbox>
    </mtd-checkbox-group>
  </div>
</template>
<script>
export default {
  data () {
    return {
      checked: false,
      value: 1,
      group: [],
    }
  },
  methods: {
    handleChange(v) {
      console.log('你选择',v)
    },
  },
}
</script>
<style>
  .checkbox-vertical{
    width: 100px;
  }
  .checkbox-vertical .mtd-checkbox{
    display: block;
    margin-left: 0px;
    margin-bottom: 8px;
  }
</style>

```

#### doc/controlled.vue

```vue
<template>
  <div class="checkbox-demo">
    <div style="text-align: left;"> <mtd-checkbox v-model="checked" :disabled="disabled">受控的复选框</mtd-checkbox></div>
    <br />
    <div>
      <mtd-button type="primary" style="margin-right: 32px;" @click="handleChecked">{{ checked ? 'UnCheck' : 'Check'
      }}</mtd-button>
      <mtd-button type="primary" @click="handleDisable">{{ disabled ? 'Enable' : 'Disable' }}</mtd-button>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      checked: false,
      disabled: false
    };
  },

  methods: {
    handleChecked() {
      this.checked = !this.checked
    },
    handleDisable() {
      this.disabled = !this.disabled
    },
  },
};
</script>
  
<style lang='scss' scoped>
.checkbox-demo {
  padding: 100px 0;
}
</style>
  
```

#### doc/demo/group-checkbox-max-min.vue

```vue
<template>
  <div class="demo-flex" style="padding: 100px 0;">
    <div class="example-checkbox-all">
      <div>
        最大勾选数量：<mtd-input-number v-model="max" :min="1" :max="4" />
        最小勾选数量：<mtd-input-number v-model="min" :max="4" />
      </div>
      <br />
      <mtd-checkbox-group v-model="group3" @input="handleCheckItem" :max="max" :min="min">
        <mtd-checkbox v-for="(t, index) in options" :key="index" :value="index">{{ t }}</mtd-checkbox>
      </mtd-checkbox-group>
    </div>
  </div>
</template>
<script>
const options = ['选项', '选项', '选项', '选项'];
export default {
  data() {
    return {
      options,
      checkedAll: false,
      max: 3,
      min: 1,
      group3: [],
    };
  },
  computed: {
    isIndeterminate() {
      const { length } = this.group3;
      return !!length && length !== this.options.length;
    },
  },
  methods: {
    handleCheckedAll(checked) {
      this.group3 = checked ? this.options.map((_, index) => index) : [];
    },
    handleCheckItem(items) {
      this.checkedAll = items.length === this.options.length;
    },
  },
};
</script>
  
<style lang='scss' scoped>
.example-checkbox-all {
  text-align: left;
  display: inline-block;

  .mtd-checkbox-group {
    display: inline-block;
  }
}
</style>
  
```

#### doc/demo/group-checkbox.vue

```vue
<template>
  <div class="demo-flex" style="padding: 100px 0;">
    <div class="example-checkbox-all">
      <div style="margin-bottom: 10px;">
        是否禁用：<mtd-switch v-model="disabled" />
      </div>

      <div>
        选中项：<mtd-input v-model="groupStr" placeholder="输入要选中的项的索引，以,隔开" @enter="enter" />
      </div>
      
      <br />
      <mtd-checkbox-group v-model="group3" @input="handleCheckItem" :disabled="disabled">
        <mtd-checkbox v-for="(t, index) in options" :key="index" :value="index">{{ t }}</mtd-checkbox>
      </mtd-checkbox-group>
    </div>
  </div>
</template>
<script>
const options = ['选项1', '选项2', '选项3', '选项4'];
export default {
  data() {
    return {
      options,
      checkedAll: false,
      disabled: false,
      group3: [],
      groupStr: '1,2'
    };
  },
  computed: {
    isIndeterminate() {
      const { length } = this.group3;
      return !!length && length !== this.options.length;
    },
  },
  watch: {
    group3(v) {
      this.groupStr = v.join(',')
    }
  },
  methods: {
    handleCheckedAll(checked) {
      this.group3 = checked ? this.options.map((_, index) => index) : [];
    },
    handleCheckItem(items) {
      this.checkedAll = items.length === this.options.length;
    },
    enter() {
      const v = this.groupStr
      if (!v) {
        this.group3 = []
      } else {
        this.group3 = v.split(',').filter(i => !!i).map(i => +i)
      }
    }
  },
};
</script>

<style lang='scss' scoped>
.example-checkbox-all {
  text-align: left;
  display: inline-block;

  .mtd-checkbox-group {
    display: inline-block;
  }
}
</style>
```

#### doc/group.vue

```vue
<template>
  <div class="checkbox-demo">
    <mtd-tabs v-model="activeName" type="text" size="small">
      <mtd-tab-pane label="横向" value="Tab1">
        <div class="checkbox-demo-content">
          <div style="margin-right: 96px;">
            <p>小尺寸</p>
            <mtd-checkbox v-for="(o, index) in options" :key="index" v-model="checkValue3[index]" size="small">{{ o
            }}</mtd-checkbox>
          </div>
          <div style="margin-right: 96px;">
            <p>中尺寸</p>
            <mtd-checkbox v-for="(o, index) in options" :key="index" v-model="checkValue1[index]">{{ o }}</mtd-checkbox>
          </div>
          <div>
            <p>大尺寸</p>
            <mtd-checkbox v-for="(o, index) in options" :key="index" v-model="checkValue2[index]" size="large">{{ o
            }}</mtd-checkbox>
          </div>
        </div>

      </mtd-tab-pane>
      <mtd-tab-pane label="纵向" value="Tab2">
        <div class="checkbox-demo-content">
          <div class="checkbox-demo-content-vertical" style="margin-right: 96px;">
            <p style="width: 100%;">小尺寸</p>
            <mtd-checkbox style="margin-bottom: 4px; margin-left: 0;" v-for="(o, index) in options" :key="index"
              v-model="checkValue4[index]" size="small">{{ o
              }}</mtd-checkbox>
          </div>
          <div class="checkbox-demo-content-vertical" style="margin-right: 96px;">
            <p style="width: 100%;">中尺寸</p>
            <mtd-checkbox style="margin-bottom: 8px; margin-left: 0;" v-for="(o, index) in options" :key="index"
              v-model="checkValue5[index]">{{ o
              }}</mtd-checkbox>
          </div>
          <div class="checkbox-demo-content-vertical">
            <p style="width: 100%;">大尺寸</p>
            <mtd-checkbox style="margin-bottom: 12px; margin-left: 0;" v-for="(o, index) in options" :key="index"
              v-model="checkValue6[index]" size="large">
              {{ o }}
            </mtd-checkbox>
          </div>
        </div>
      </mtd-tab-pane>
    </mtd-tabs>
  </div>
</template>
<script>
export default {
  data() {
    return {
      options: ['选项', '选项', '选项'],
      activeName: 'Tab1',
      checkValue1: [],
      checkValue2: [],
      checkValue3: [],
      checkValue4: [],
      checkValue5: [],
      checkValue6: [],
    };
  },
  methods: {
    t(v) {
      console.log("change", v, this.value);
    },
  },
};
</script>
<style lang="scss" scoped>
.checkbox-demo {
  margin-top: 50px;
  padding: 100px 0;

}

.checkbox-demo-content {
  display: flex;

  &-vertical {
    display: flex;
    flex-direction: column;
    align-items: flex-end
  }
}

::v-deep .checkbox-demo .mtd-tabs-nav {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  border-bottom: 1px solid #edf0f7;
  padding: 1.5px 16px;
}
</style>
  
```

#### doc/nested.vue

```vue
<template>
  <div class="checkbox-demo">
    <mtd-checkbox v-model="showTime" style="margin-bottom: 8px">在菜单栏里展示时间</mtd-checkbox>
    <mtd-checkbox-group class="demo-checkbox-group-vertical" v-model="group5" :disabled="!showTime"
      style="list-style-type: none; margin: 0; padding-left: 24px;">
      <mtd-checkbox value="number" style="display: block; margin-right: 0px; margin-bottom: 8px;">数字时钟</mtd-checkbox>
      <mtd-checkbox value="clock" style="display: block;">拟物时钟</mtd-checkbox>
    </mtd-checkbox-group>
  </div>
</template>
<script>
export default {
  data() {
    return {
      showTime: false,
      group5: [],
    };
  },
};
</script>
<style scoped>
.checkbox-demo {
  padding: 100px 0;
  display: inline-block;
  text-align: left;
}
</style>

```

#### doc/size.vue

```vue
<template>
  <div class="checkbox-demo">
    <mtd-checkbox v-model="checkValue" size="small">北京</mtd-checkbox>
    <mtd-checkbox v-model="checkValue1" style="margin-left: 96px;">北京</mtd-checkbox>
    <mtd-checkbox v-model="checkValue2" size="large" style="margin-left: 96px;">北京</mtd-checkbox>
  </div>
</template>
<script>
export default {
  data() {
    return {
      checkValue: false,
      checkValue1: false,
      checkValue2: false,
    };
  },
  methods: {
    t(v) {
      console.log("change", v, this.value);
    },
  },
};
</script>
<style lang="scss" scoped>
.checkbox-demo {
  padding: 100px 0;
}
</style>

```

#### doc/status.vue

```vue
<template>
  <div class="demo-full-width">
    <div class="checkbox-demo">
      <div class="checkbox-demo-group">
        <p>未选</p>
        <mtd-checkbox v-model="checked5" @input="handleInput" @change="handleChange">选项</mtd-checkbox>
      </div>
      <div class="checkbox-demo-group">
        <p> 半选</p>
        <mtd-checkbox indeterminate v-model="checked7">选项</mtd-checkbox>
      </div>
      <div class="checkbox-demo-group">
        <p>已选</p>
        <mtd-checkbox v-model="checked6">选项</mtd-checkbox>
      </div>
    </div>

    <br />
    <div class="checkbox-demo">
      <div class="checkbox-demo-group">
        <p>未选禁用</p>
        <mtd-checkbox disabled @input="handleInput" @change="handleChange">选项</mtd-checkbox>
      </div>
      <div class="checkbox-demo-group">
        <p>半选禁用</p>
        <mtd-checkbox indeterminate disabled>选项</mtd-checkbox>
      </div>
      <div class="checkbox-demo-group">
        <p>已选禁用</p>
        <mtd-checkbox disabled :value="true">选项</mtd-checkbox>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      checked5: false,
      checked6: true,
      checked7: false,
    };
  },
  methods: {
    handleInput(value) {
      console.log('input', value)
    },
    handleChange(value) {
      console.log('change', value)
    }
  }
};
</script>
<style scoped>
.checkbox-demo {
  display: flex;
  margin-bottom: 36px;
}

.demo-full-width {
  padding: 100px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.checkbox-demo-group {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin-right: 96px;
  width: 56px;
}
</style>

```

#### index.md

```markdown
# 复选框 / Checkbox

单个复选框允许用户对单个称述语句设置真/假值，多个复选框使用户在一组相互独立的选项中选择一或多项。

## 互动演示

:::include(src="./demo/checkbox-interactive.vue")
:::

## 代码示例

### 尺寸

复选框支持小、中、大三种尺寸。
:::include(src="./doc/size.vue")
:::

### 复选框组

复选框组支持横向和纵向两种布局方式。
:::include(src="./doc/group.vue")
:::

### 状态

复选框状态有默认状态、悬停状态、激活（点击）状态、禁用状态。
:::include(src="./doc/status.vue")
:::

### 全选

:::include(src="./doc/all-checked.vue")
:::

### 嵌套型

可使用缩进来表示级联关系；当父级没有被选中时子级应该全部禁用。
:::include(src="./doc/nested.vue")
:::

### 受控/联动

控制复选框是否被选中。
:::include(src="./doc/controlled.vue")
:::

## API

<api-doc name="Checkbox" :doc="require('./api.json')"></api-doc>
<api-doc name="CheckboxGroup" :doc="require('../checkbox-group/api.json')"></api-doc>

```

#### index.ts

```typescript
import Checkbox from './checkbox'
import { withInstall } from '@ss/mtd-adapter'
export default withInstall(Checkbox)

```

#### props.ts

```typescript
import { ExtractPropTypes } from '@ss/mtd-adapter'

export const checkboxProps = () => ({
  value: [String, Number, Boolean, Function, Object],
  modelValue: [String, Number, Boolean, Function, Object],
  trueValue: {
    type: [String, Number, Boolean, Function, Object],
    default: true,
  },
  falseValue: {
    type: [String, Number, Boolean, Function, Object],
    default: false,
  },

  size: String,
  disabled: Boolean,
  name: String,
  indeterminate: Boolean,
  label: [String, Number],
  isUnderControl: {
    type: Boolean,
    default: true,
  }, // 该属性的作用是区分开来是否用于checkboxGroup管理，不对外暴露
})

export type CheckboxProps = Partial<ExtractPropTypes<ReturnType<typeof checkboxProps>>>

export default checkboxProps
```

#### tests/__snapshots__/checkbox.spec.jsx.snap

```plaintext
// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Checkbox renders ./src/checkbox/doc/all-checked.vue correctly 1`] = `
<div style="text-align: left; display: flex; align-items: center; justify-content: space-around;">
  <div class="example-checkbox-all"><label class="mtd-checkbox" style="margin-right: 32px;"><input type="checkbox" class="mtd-checkbox-input" style="display: none;"><span class="mtd-checkbox-inner"><i class="mtdicon mtdicon-check-thick"></i></span><span class="mtd-checkbox-text">全选</span></label>
    <div class="mtd-checkbox-group"><label class="mtd-checkbox"><input type="checkbox" class="mtd-checkbox-input" style="display: none;"><span class="mtd-checkbox-inner"><i class="mtdicon mtdicon-check-thick"></i></span><span class="mtd-checkbox-text">上海</span></label><label class="mtd-checkbox"><input type="checkbox" class="mtd-checkbox-input" style="display: none;"><span class="mtd-checkbox-inner"><i class="mtdicon mtdicon-check-thick"></i></span><span class="mtd-checkbox-text">北京</span></label><label class="mtd-checkbox"><input type="checkbox" class="mtd-checkbox-input" style="display: none;"><span class="mtd-checkbox-inner"><i class="mtdicon mtdicon-check-thick"></i></span><span class="mtd-checkbox-text">广州</span></label></div>
  </div>
  <div class="example-checkbox-all checkbox-vertical"><label class="mtd-checkbox"><input type="checkbox" class="mtd-checkbox-input" style="display: none;"><span class="mtd-checkbox-inner"><i class="mtdicon mtdicon-check-thick"></i></span><span class="mtd-checkbox-text">全选</span></label>
    <div class="mtd-checkbox-group" style="display: block;"><label class="mtd-checkbox" style="margin-right: 0px;"><input type="checkbox" class="mtd-checkbox-input" style="display: none;"><span class="mtd-checkbox-inner"><i class="mtdicon mtdicon-check-thick"></i></span><span class="mtd-checkbox-text">上海</span></label><label class="mtd-checkbox" style="margin-right: 0px;"><input type="checkbox" class="mtd-checkbox-input" style="display: none;"><span class="mtd-checkbox-inner"><i class="mtdicon mtdicon-check-thick"></i></span><span class="mtd-checkbox-text">北京</span></label><label class="mtd-checkbox" style="margin-right: 0px;"><input type="checkbox" class="mtd-checkbox-input" style="display: none;"><span class="mtd-checkbox-inner"><i class="mtdicon mtdicon-check-thick"></i></span><span class="mtd-checkbox-text">广州</span></label></div>
  </div>
</div>
`;

exports[`Checkbox renders ./src/checkbox/doc/base.vue correctly 1`] = `
<div style="text-align: left; display: flex; align-items: center; justify-content: space-around;"><label class="mtd-checkbox"><input type="checkbox" class="mtd-checkbox-input" style="display: none;"><span class="mtd-checkbox-inner"><i class="mtdicon mtdicon-check-thick"></i></span><span class="mtd-checkbox-text">选项</span></label>
  <div class="mtd-checkbox-group"><label class="mtd-checkbox"><input type="checkbox" class="mtd-checkbox-input" style="display: none;"><span class="mtd-checkbox-inner"><i class="mtdicon mtdicon-check-thick"></i></span><span class="mtd-checkbox-text">选项</span></label> <label class="mtd-checkbox"><input type="checkbox" class="mtd-checkbox-input" style="display: none;"><span class="mtd-checkbox-inner"><i class="mtdicon mtdicon-check-thick"></i></span><span class="mtd-checkbox-text">选项</span></label> <label class="mtd-checkbox"><input type="checkbox" class="mtd-checkbox-input" style="display: none;"><span class="mtd-checkbox-inner"><i class="mtdicon mtdicon-check-thick"></i></span><span class="mtd-checkbox-text">选项</span></label></div>
  <div class="checkbox-vertical mtd-checkbox-group"><label class="mtd-checkbox"><input type="checkbox" class="mtd-checkbox-input" style="display: none;"><span class="mtd-checkbox-inner"><i class="mtdicon mtdicon-check-thick"></i></span><span class="mtd-checkbox-text">选项</span></label> <label class="mtd-checkbox"><input type="checkbox" class="mtd-checkbox-input" style="display: none;"><span class="mtd-checkbox-inner"><i class="mtdicon mtdicon-check-thick"></i></span><span class="mtd-checkbox-text">选项</span></label> <label class="mtd-checkbox"><input type="checkbox" class="mtd-checkbox-input" style="display: none;"><span class="mtd-checkbox-inner"><i class="mtdicon mtdicon-check-thick"></i></span><span class="mtd-checkbox-text">选项</span></label></div>
</div>
`;

exports[`Checkbox renders ./src/checkbox/doc/nested.vue correctly 1`] = `
<div style="display: inline-block; text-align: left;"><label class="mtd-checkbox"><input type="checkbox" class="mtd-checkbox-input" style="display: none;"><span class="mtd-checkbox-inner"><i class="mtdicon mtdicon-check-thick"></i></span><span class="mtd-checkbox-text">在菜单栏里展示时间</span></label>
  <div class="mtd-checkbox-group" style="list-style-type: none; margin: 0px; padding-left: 20px;"><label class="mtd-checkbox mtd-checkbox-disabled" style="display: block; margin-right: 0px;"><input type="checkbox" disabled="disabled" class="mtd-checkbox-input" style="display: none;"><span class="mtd-checkbox-inner"><i class="mtdicon mtdicon-check-thick"></i></span><span class="mtd-checkbox-text">数字时钟</span></label> <label class="mtd-checkbox mtd-checkbox-disabled" style="display: block;"><input type="checkbox" disabled="disabled" class="mtd-checkbox-input" style="display: none;"><span class="mtd-checkbox-inner"><i class="mtdicon mtdicon-check-thick"></i></span><span class="mtd-checkbox-text">拟物时钟</span></label></div>
</div>
`;

exports[`Checkbox renders ./src/checkbox/doc/size.vue correctly 1`] = `<div><label class="mtd-checkbox"><input type="checkbox" class="mtd-checkbox-input" style="display: none;"><span class="mtd-checkbox-inner"><i class="mtdicon mtdicon-check-thick"></i></span><span class="mtd-checkbox-text">选项</span></label> <label class="mtd-checkbox mtd-checkbox-large"><input type="checkbox" class="mtd-checkbox-input" style="display: none;"><span class="mtd-checkbox-inner"><i class="mtdicon mtdicon-check-thick"></i></span><span class="mtd-checkbox-text">选项</span></label></div>`;

exports[`Checkbox renders ./src/checkbox/doc/status.vue correctly 1`] = `<div><label class="mtd-checkbox"><input type="checkbox" class="mtd-checkbox-input" style="display: none;"><span class="mtd-checkbox-inner"><i class="mtdicon mtdicon-check-thick"></i></span><span class="mtd-checkbox-text">选项</span></label> <label class="mtd-checkbox mtd-checkbox-checked"><input type="checkbox" class="mtd-checkbox-input" style="display: none;"><span class="mtd-checkbox-inner"><i class="mtdicon mtdicon-check-thick"></i></span><span class="mtd-checkbox-text">选项</span></label> <label class="mtd-checkbox mtd-checkbox-indeterminate"><input type="checkbox" class="mtd-checkbox-input" style="display: none;"><span class="mtd-checkbox-inner"><i class="mtd-checkbox-indeterminate-inner"></i></span><span class="mtd-checkbox-text">中间态</span></label> <label class="mtd-checkbox mtd-checkbox-disabled"><input type="checkbox" disabled="disabled" class="mtd-checkbox-input" style="display: none;"><span class="mtd-checkbox-inner"><i class="mtdicon mtdicon-check-thick"></i></span><span class="mtd-checkbox-text">未选中禁用态</span></label> <label class="mtd-checkbox mtd-checkbox-checked mtd-checkbox-disabled"><input type="checkbox" disabled="disabled" class="mtd-checkbox-input" style="display: none;"><span class="mtd-checkbox-inner"><i class="mtdicon mtdicon-check-thick"></i></span><span class="mtd-checkbox-text">已选中禁用态</span></label> <label class="mtd-checkbox mtd-checkbox-disabled mtd-checkbox-indeterminate"><input type="checkbox" disabled="disabled" class="mtd-checkbox-input" style="display: none;"><span class="mtd-checkbox-inner"><i class="mtd-checkbox-indeterminate-inner"></i></span><span class="mtd-checkbox-text">中间禁用态</span></label></div>`;

```

#### tests/checkbox.spec.jsx

```jsx
import {
  createDemoTest,
  mount,
  triggerClick,
  waitImmediate,
} from '@tests/utils';
import Checkbox from '../index'

describe('Checkbox', function () {
  createDemoTest('checkbox')

  xit('input event',async function () {
    const spy = jest.fn()
    const wrapper = mount({
      data () {
        return {
          checked: false,
        }
      },
      methods: {
        handleChange: spy,
      },
      render () {
        return <Checkbox checked={this.checked}>xxx</Checkbox>
      }
    })
    const input = wrapper.vm.$el.querySelector('input')
    triggerClick(input)
    await waitImmediate()
    expect(spy).toHaveBeenCalledWith(true)
  })
})

```

#### types.ts

```typescript
import Checkbox from './index'

export type Checkbox = InstanceType<typeof Checkbox>
```

