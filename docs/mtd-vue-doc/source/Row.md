## 源码解析

### 文件依赖

- doc/align.vue
- doc/base.vue
- doc/gutter.vue
- doc/index.vue
- doc/mix.vue
- doc/offset.vue
- doc/response.vue
- index.md
- index.ts
- row.tsx
- symbol.ts
- types.ts

### 源码内容

#### doc/align.vue

```vue
<template>
  <div class="demo-full-width">
    <mtd-row type="flex" class="row-bg">
      <mtd-col :span="6"><div class="grid-content bg-purple" /></mtd-col>
      <mtd-col :span="6"><div class="grid-content bg-purple-light" /></mtd-col>
      <mtd-col :span="6"><div class="grid-content bg-purple" /></mtd-col>
    </mtd-row>
    <mtd-row type="flex" class="row-bg" justify="center">
      <mtd-col :span="6"><div class="grid-content bg-purple" /></mtd-col>
      <mtd-col :span="6"><div class="grid-content bg-purple-light" /></mtd-col>
      <mtd-col :span="6"><div class="grid-content bg-purple" /></mtd-col>
    </mtd-row>
    <mtd-row type="flex" class="row-bg" justify="end">
      <mtd-col :span="6"><div class="grid-content bg-purple" /></mtd-col>
      <mtd-col :span="6"><div class="grid-content bg-purple-light" /></mtd-col>
      <mtd-col :span="6"><div class="grid-content bg-purple" /></mtd-col>
    </mtd-row>
    <mtd-row type="flex" class="row-bg" justify="space-between">
      <mtd-col :span="6"><div class="grid-content bg-purple" /></mtd-col>
      <mtd-col :span="6"><div class="grid-content bg-purple-light" /></mtd-col>
      <mtd-col :span="6"><div class="grid-content bg-purple" /></mtd-col>
    </mtd-row>
    <mtd-row type="flex" class="row-bg" justify="space-around">
      <mtd-col :span="6"><div class="grid-content bg-purple" /></mtd-col>
      <mtd-col :span="6"><div class="grid-content bg-purple-light" /></mtd-col>
      <mtd-col :span="6"><div class="grid-content bg-purple" /></mtd-col>
    </mtd-row>
  </div>
</template>

<style lang="scss">
.demo-row {
  .mtd-row {
    margin-bottom: 20px;
    &:last-child {
      margin-bottom: 0;
    }
  }
  .mtd-col {
    border-radius: 4px;
  }
  .bg-purple-dark {
    background: #99a9bf;
  }
  .bg-purple {
    background: #d3dce6;
  }
  .bg-purple-light {
    background: #e5e9f2;
  }
  .grid-content {
    border-radius: 4px;
    min-height: 36px;
  }
  .row-bg {
    padding: 10px 0;
    background-color: #f9fafc;
  }
}
</style>

```

#### doc/base.vue

```vue
<template>
  <div class="demo-full-width">
    <mtd-row>
      <mtd-col :span="24">
        <div class="grid-content bg-purple-dark" />
      </mtd-col>
    </mtd-row>
    <mtd-row>
      <mtd-col :span="12">
        <div class="grid-content bg-purple" />
      </mtd-col>
      <mtd-col :span="12">
        <div class="grid-content bg-purple-light" />
      </mtd-col>
    </mtd-row>
    <mtd-row>
      <mtd-col :span="8">
        <div class="grid-content bg-purple" />
      </mtd-col>
      <mtd-col :span="8">
        <div class="grid-content bg-purple-light" />
      </mtd-col>
      <mtd-col :span="8">
        <div class="grid-content bg-purple" />
      </mtd-col>
    </mtd-row>
    <mtd-row>
      <mtd-col :span="6">
        <div class="grid-content bg-purple" />
      </mtd-col>
      <mtd-col :span="6">
        <div class="grid-content bg-purple-light" />
      </mtd-col>
      <mtd-col :span="6">
        <div class="grid-content bg-purple" />
      </mtd-col>
      <mtd-col :span="6">
        <div class="grid-content bg-purple-light" />
      </mtd-col>
    </mtd-row>
    <mtd-row>
      <mtd-col :span="4">
        <div class="grid-content bg-purple" />
      </mtd-col>
      <mtd-col :span="4">
        <div class="grid-content bg-purple-light" />
      </mtd-col>
      <mtd-col :span="4">
        <div class="grid-content bg-purple" />
      </mtd-col>
      <mtd-col :span="4">
        <div class="grid-content bg-purple-light" />
      </mtd-col>
      <mtd-col :span="4">
        <div class="grid-content bg-purple" />
      </mtd-col>
      <mtd-col :span="4">
        <div class="grid-content bg-purple-light" />
      </mtd-col>
    </mtd-row>
  </div>
</template>

<style lang="scss">
.demo-row {
  .mtd-row {
    margin-bottom: 20px;
    &:last-child {
      margin-bottom: 0;
    }
  }
  .mtd-col {
    border-radius: 4px;
  }
  .bg-purple-dark {
    background: #99a9bf;
  }
  .bg-purple {
    background: #d3dce6;
  }
  .bg-purple-light {
    background: #e5e9f2;
  }
  .grid-content {
    border-radius: 4px;
    min-height: 36px;
  }
  .row-bg {
    padding: 10px 0;
    background-color: #f9fafc;
  }
}
</style>

```

#### doc/gutter.vue

```vue
<template>
  <div class="demo-full-width">
    <mtd-row :gutter="20">
      <mtd-col :span="6"><div class="grid-content bg-purple" /></mtd-col>
      <mtd-col :span="6"><div class="grid-content bg-purple" /></mtd-col>
      <mtd-col :span="6"><div class="grid-content bg-purple" /></mtd-col>
      <mtd-col :span="6"><div class="grid-content bg-purple" /></mtd-col>
    </mtd-row>
  </div>
</template>

<style lang="scss">
.demo-row {
  .mtd-row {
    margin-bottom: 20px;
    &:last-child {
      margin-bottom: 0;
    }
  }
  .mtd-col {
    border-radius: 4px;
  }
  .bg-purple {
    background: #d3dce6;
  }
  .grid-content {
    border-radius: 4px;
    min-height: 36px;
  }
}
</style>

```

#### doc/index.vue

```vue
<template>
  <div>
    <h1>布局 / Gird</h1>
    <Base />
    <br />
    <Gutter />
    <br />
    <Mix />
    <br />
    <Offset />
    <br />
    <Align />
    <br />
    <api-doc name="Row" :doc="require('../api.json')"></api-doc>
    <api-doc name="Col" :doc="require('../../col/api.json')"></api-doc>
  </div>
</template>
<script>
import { defineComponent } from 'vue';
import Base from './base';
import Gutter from './gutter';
import Mix from './mix';
import Offset from './offset';
import Align from './align';

export default defineComponent({
  components: {
    Base,
    Gutter,
    Mix,
    Offset,
    Align,
  },
});
</script>

```

#### doc/mix.vue

```vue
<template>
  <div class="demo-full-width">
    <mtd-row :gutter="20">
      <mtd-col :span="16"><div class="grid-content bg-purple" /></mtd-col>
      <mtd-col :span="8"><div class="grid-content bg-purple" /></mtd-col>
    </mtd-row>
    <mtd-row :gutter="20">
      <mtd-col :span="8"><div class="grid-content bg-purple" /></mtd-col>
      <mtd-col :span="8"><div class="grid-content bg-purple" /></mtd-col>
      <mtd-col :span="4"><div class="grid-content bg-purple" /></mtd-col>
      <mtd-col :span="4"><div class="grid-content bg-purple" /></mtd-col>
    </mtd-row>
    <mtd-row :gutter="20">
      <mtd-col :span="4"><div class="grid-content bg-purple" /></mtd-col>
      <mtd-col :span="16"><div class="grid-content bg-purple" /></mtd-col>
      <mtd-col :span="4"><div class="grid-content bg-purple" /></mtd-col>
    </mtd-row>
  </div>
</template>
<style lang="scss">
.demo-row {
  .mtd-row {
    margin-bottom: 20px;
    &:last-child {
      margin-bottom: 0;
    }
  }
  .mtd-col {
    border-radius: 4px;
  }
  .bg-purple {
    background: #d3dce6;
  }
  .grid-content {
    border-radius: 4px;
    min-height: 36px;
  }
}
</style>

```

#### doc/offset.vue

```vue
<template>
  <div class="demo-full-width">
    <mtd-row :gutter="20">
      <mtd-col :span="6"><div class="grid-content bg-purple" /></mtd-col>
      <mtd-col :span="6" :offset="6"
        ><div class="grid-content bg-purple"
      /></mtd-col>
    </mtd-row>
    <mtd-row :gutter="20">
      <mtd-col :span="6" :offset="6"
        ><div class="grid-content bg-purple"
      /></mtd-col>
      <mtd-col :span="6" :offset="6"
        ><div class="grid-content bg-purple"
      /></mtd-col>
    </mtd-row>
    <mtd-row :gutter="20">
      <mtd-col :span="12" :offset="6"
        ><div class="grid-content bg-purple"
      /></mtd-col>
    </mtd-row>
  </div>
</template>

<style lang="scss">
.demo-row {
  .mtd-row {
    margin-bottom: 20px;
    &:last-child {
      margin-bottom: 0;
    }
  }
  .mtd-col {
    border-radius: 4px;
  }
  .bg-purple {
    background: #d3dce6;
  }
  .grid-content {
    border-radius: 4px;
    min-height: 36px;
  }
}
</style>

```

#### doc/response.vue

```vue
<template>
  <div class="demo-full-width">
    <mtd-row :gutter="10">
      <mtd-col :xs="8" :sm="6" :md="4" :lg="3" :xl="1"
        ><div class="grid-content bg-purple"
      /></mtd-col>
      <mtd-col :xs="4" :sm="6" :md="8" :lg="9" :xl="11"
        ><div class="grid-content bg-purple-light"
      /></mtd-col>
      <mtd-col :xs="4" :sm="6" :md="8" :lg="9" :xl="11"
        ><div class="grid-content bg-purple"
      /></mtd-col>
      <mtd-col :xs="8" :sm="6" :md="4" :lg="3" :xl="1"
        ><div class="grid-content bg-purple-light"
      /></mtd-col>
    </mtd-row>
  </div>
</template>

<style lang="scss">
.demo-row {
  .mtd-row {
    margin-bottom: 20px;
    &:last-child {
      margin-bottom: 0;
    }
  }
  .mtd-col {
    border-radius: 4px;
  }
  .bg-purple {
    background: #d3dce6;
  }
  .bg-purple-light {
    background: #e5e9f2;
  }
  .grid-content {
    border-radius: 4px;
    min-height: 36px;
  }
}
</style>

```

#### index.md

```markdown
# 布局 / Gird

通过基础的 24 分栏，迅速简便地创建布局。

## 基础布局

使用单一分栏创建基础的栅格布局。
通过 row 和 col 组件，并通过 col 组件的 `span` 属性我们就可以自由地组合布局。
:::include(src="./doc/base.vue")
:::

## 类型与用法

### 分栏间隔

分栏之间存在间隔。
Row 组件 提供 `gutter` 属性来指定每一栏之间的间隔，默认间隔为 0。
:::include(src="./doc/gutter.vue")
:::

### 混合布局

通过基础的 1/24 分栏任意扩展组合形成较为复杂的混合布局。
:::include(src="./doc/mix.vue")
:::

### 分栏偏移

支持偏移指定的栏数。
通过制定 col 组件的 `offset` 属性可以指定分栏偏移的栏数。
:::include(src="./doc/offset.vue")
:::

### 对齐方式

通过 `flex` 布局来对分栏进行灵活的对齐。
将 `type` 属性赋值为 'flex'，可以启用 flex 布局，并可通过 `justify` 属性来指定 start, center, end, space-between, space-around 其中的值来定义子元素的排版方式。
:::include(src="./doc/align.vue")
:::

### 响应式布局

参照了 Bootstrap 的 响应式设计，预设了六个响应尺寸：`xs`、`sm`、`md`、`lg`、`xl`、`xxl`。
:::include(src="./doc/response.vue")
:::

## API

<api-doc name="Row" :doc="require('./api.json')"></api-doc>
<api-doc name="Col" :doc="require('../col/api.json')"></api-doc>

```

#### index.ts

```typescript
import Row from './row'
import { withInstall } from '@ss/mtd-adapter'

export {
  useRowProvide,
  useRowContext,
} from './row'

export default withInstall(Row)

```

#### row.tsx

```tsx
import useConfig from '@hooks/config'
import { defineComponent, provide, computed, inject, getSlotsInRender, Ref } from '@ss/mtd-adapter'
import ProvideSymbol from './symbol'
import { RowProvide } from './types'

export function useRowProvide(gutter?: Ref<number | undefined>) {
  provide<RowProvide>(ProvideSymbol, { gutter })
}

export function useRowContext() {
  return inject<RowProvide>(ProvideSymbol)
}

export default defineComponent({
  name: 'MtdRow',
  inheritAttrs: true,
  props: {
    tag: {
      type: String,
      default: 'div',
    },
    gutter: Number,
    type: String,
    justify: {
      type: String,
      default: 'start',
    },
    align: {
      type: String,
      default: 'top',
    },
  },
  setup(props) {
    const config = useConfig()
    const prefix = computed(() => config.getPrefixCls('row'))
    const m_gutter = computed(() => props.gutter)
    useRowProvide(m_gutter)
    return {
      prefix,
    }
  },
  render() {
    const { prefix, tag, justify, align, type } = this
    const Component = tag as any
    return <Component
      class={[
        prefix,
        justify !== 'start' ? `${prefix}-justify-${justify}` : '',
        align !== 'top' ? `${prefix}-align-${align}` : '',
        { [`${prefix}-flex`]: type === 'flex' },
      ]}
    >
      {getSlotsInRender(this)}
    </Component>
  },
})

```

#### symbol.ts

```typescript
export default 'collapse'

```

#### types.ts

```typescript
import { Component } from '@components/types/component'
import { Ref } from '@ss/mtd-adapter'

export declare interface IRow extends Component {
  tag: string;
  gutter: number;
  type: string;
  justify: string;
  align: string;
}
export interface RowProvide {
  gutter?: Ref<number | undefined>;
}

export type RowStyle =
  | {
      marginLeft: string;
      marginRight: string;
    }
  | {};


declare const Row: IRow
export default Row

```

