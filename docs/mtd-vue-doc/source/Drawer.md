## 源码解析

### 文件依赖

- demo.md
- demo/drawer-interactive.vue
- doc/asideform.vue
- doc/base.vue
- doc/close-position.vue
- doc/form.vue
- doc/mask.vue
- doc/multiple.vue
- doc/placement.vue
- doc/size.vue
- doc/title-footer.vue
- drawer.tsx
- index.md
- index.ts
- inside.tsx
- tests/__snapshots__/drawer.spec.js.snap
- tests/drawer.spec.js
- types.ts

### 源码内容

#### demo.md

```markdown
# 抽屉 / Drawer

## 补充 Demo


```

#### demo/drawer-interactive.vue

```vue

<template>
  <div>
    <mtd-doc-usage name="Drawer" :apiJson="apiJson" :usageJson="usageJson" @propChange="propChange">
      <template slot="Drawer" slot-scope="{ configProps }">
        <div class="slot-container">
          <mtd-button @click="visible = true">打开抽屉</mtd-button>
          <mtd-Drawer v-model="visible" v-bind="configProps" title="基础样式">
            <div>
              春江潮水连海平，海上明月共潮生。
              滟滟随波千万里，何处春江无月明！
              江流宛转绕芳甸，月照花林皆似霰。
              空里流霜不觉飞，汀上白沙看不见。
              江天一色无纤尘，皎皎空中孤月轮。
              江畔何人初见月？江月何年初照人？
              人生代代无穷已，江月年年只相似。
              不知江月待何人，但见长江送流水。
              白云一片去悠悠，青枫浦上不胜愁。
              谁家今夜扁舟子？何处相思明月楼？
            </div>
          </mtd-Drawer>
        </div>
      </template>
      <template #Drawer="{ configProps }">
        <div class="slot-container">
          <mtd-button @click="visible = true">打开抽屉</mtd-button>
          <mtd-Drawer v-model="visible" v-bind="configProps" title="基础样式">
            <div>
              春江潮水连海平，海上明月共潮生。
              滟滟随波千万里，何处春江无月明！
              江流宛转绕芳甸，月照花林皆似霰。
              空里流霜不觉飞，汀上白沙看不见。
              江天一色无纤尘，皎皎空中孤月轮。
              江畔何人初见月？江月何年初照人？
              人生代代无穷已，江月年年只相似。
              不知江月待何人，但见长江送流水。
              白云一片去悠悠，青枫浦上不胜愁。
              谁家今夜扁舟子？何处相思明月楼？
            </div>
          </mtd-Drawer>
        </div>
      </template>
    </mtd-doc-usage>
  </div>
</template>
<script>
import apiJson from '../api.json'

export default {
  data() {
    return {
      apiJson: apiJson,
      visible: false,
      usageJson: {
        componentName: "Drawer",
        desc: "抽屉",
        codeTemplate: `<mtd-Drawer v-model='visible' v-bind='configProps' title='基础样式'>
          <div>
              春江潮水连海平，海上明月共潮生。
              滟滟随波千万里，何处春江无月明！
              江流宛转绕芳甸，月照花林皆似霰。
              空里流霜不觉飞，汀上白沙看不见。
              江天一色无纤尘，皎皎空中孤月轮。
              江畔何人初见月？江月何年初照人？
              人生代代无穷已，江月年年只相似。
              不知江月待何人，但见长江送流水。
              白云一片去悠悠，青枫浦上不胜愁。
              谁家今夜扁舟子？何处相思明月楼？
            </div>
          </mtd-Drawer>`
      }
    }
  },
  methods: {
    propChange(propertyName, val) {
      console.log('prop changed: , propertyName: ' + propertyName + ', val: ' + val)
    }
  }
}
</script>
<style lang="scss" scoped></style>
  
```

#### doc/asideform.vue

```vue
<template>
  <div>
    <mtd-button type="primary" @click="visible = true">打开抽屉</mtd-button>
    <mtd-drawer
      v-model="visible"
      
      class="demo-drawer-form"
      width="800px"
      destroy-on-close
    >
      <template #title>
        <h3 class="title">创建 Offer</h3>
        <div class="demo-action">
          <mtd-button type="primary" @click="handleSubmit">提交</mtd-button>
          <mtd-button @click="visible = false">取消</mtd-button>
        </div>
      </template>
      <h4 class="sub-title">原雇主薪酬</h4>
      <mtd-form :rules="rules" :model="model" ref="form" label-width="120">
        <mtd-row>
          <mtd-col :span="12">
            <mtd-form-item label="公司主体:" prop="company">
              <mtd-input v-model="model.company" placeholder="请输入公司主体" />
            </mtd-form-item>
          </mtd-col>
          <mtd-col :span="12">
            <mtd-form-item label="开户行:" prop="openingbank">
              <mtd-input
                v-model="model.openingbank"
                placeholder="请输入开户行"
              />
            </mtd-form-item>
          </mtd-col>
        </mtd-row>
        <mtd-row>
          <mtd-col :span="12">
            <div>
              <mtd-form-item
                label="期望提交审核的日期:"
                prop="submitdata"
                class="formheight1"
              >
                <mtd-select v-model="model.submitdata" placeholder="请选择日期">
                  <mtd-option label="研发岗" value="1" />
                  <mtd-option label="产品岗" value="2" />
                  <mtd-option label="安全岗" value="3" />
                </mtd-select>
              </mtd-form-item>
            </div>
          </mtd-col>
          <mtd-col :span="12">
            <mtd-form-item label="个人银行账号:" prop="bank">
              <mtd-input v-model="model.bank" placeholder="请输入银行账号" />
            </mtd-form-item>
          </mtd-col>
        </mtd-row>
        <mtd-row>
          <mtd-col :span="12">
            <mtd-form-item label="岗位类别:" prop="worktype">
              <mtd-select v-model="model.worktype" placeholder="请选择类别">
                <mtd-option label="研发岗" value="1" />
                <mtd-option label="产品岗" value="2" />
                <mtd-option label="安全岗" value="3" />
              </mtd-select>
            </mtd-form-item>
          </mtd-col>
          <mtd-col :span="12">
            <mtd-form-item label="基本薪资:" prop="salary">
              <mtd-input v-model="model.salary" placeholder="请选择薪资范围" />
            </mtd-form-item>
          </mtd-col>
        </mtd-row>
      </mtd-form>

      <h4 class="sub-title">竞争Offer</h4>
      <mtd-form :rules="rules" :model="model" ref="form" label-width="120">
        <mtd-row>
          <mtd-col :span="12">
            <mtd-form-item label="公司名称:" prop="company">
              <mtd-input v-model="model.company" placeholder="请输入公司名称" />
            </mtd-form-item>
          </mtd-col>
          <mtd-col :span="12">
            <mtd-form-item label="职位序列:" prop="workrank">
              <mtd-input
                v-model="model.workrank"
                placeholder="请输入职位序列"
              />
            </mtd-form-item>
          </mtd-col>
        </mtd-row>
        <mtd-row>
          <mtd-col :span="12">
            <mtd-form-item label="入职职级:" prop="grade">
              <mtd-select v-model="model.grade" placeholder="请选择职级">
                <mtd-option label="等级1" value="1" />
                <mtd-option label="等级2" value="2" />
                <mtd-option label="等级3" value="3" />
              </mtd-select>
            </mtd-form-item>
          </mtd-col>
          <mtd-col :span="12">
            <mtd-form-item label="离职职级:" prop="grade">
              <mtd-select v-model="model.grade" placeholder="请选择离职职级">
                <mtd-option label="等级1" value="1" />
                <mtd-option label="等级2" value="2" />
                <mtd-option label="等级3" value="3" />
              </mtd-select>
            </mtd-form-item>
          </mtd-col>
        </mtd-row>
        <mtd-row>
          <mtd-col :span="12">
            <mtd-form-item label="简历来源:" prop="source">
              <mtd-checkbox-group v-model="model.source">
                <mtd-checkbox value="1">官网</mtd-checkbox>
                <mtd-checkbox value="2">伯乐网</mtd-checkbox>
                <mtd-checkbox value="3">猎头网</mtd-checkbox>
              </mtd-checkbox-group>
            </mtd-form-item>
          </mtd-col>
          <mtd-col :span="12">
            <mtd-form-item label="部门名称:" prop="department">
              <mtd-input
                v-model="model.department"
                placeholder="请输入部门名称"
              />
            </mtd-form-item>
          </mtd-col>
        </mtd-row>
        <mtd-row>
          <mtd-col :span="12">
            <mtd-form-item label="期望收入:" prop="expectincome">
              <mtd-input
                v-model="model.expectincome"
                placeholder="请输入金额"
              />
            </mtd-form-item>
          </mtd-col>
          <mtd-col :span="12">
            <mtd-form-item label="基本薪资:" prop="salary">
              <mtd-input v-model="model.salary" placeholder="请输入基本薪资" />
            </mtd-form-item>
          </mtd-col>
        </mtd-row>
      </mtd-form>
    </mtd-drawer>
  </div>
</template>
<script>
export default {
  data () {
    return {
      visible: false,
      model: {
        company: '',
        bank: '',
        type: '',
        salary: '',
        hiredate: '',
        leavedate: '',
        source: [],
        department: '',
        remarks: '',
        submitdata: '',
        grade: '',
        expectincome: '',
        workrank: '',
        worktype: '',
      },
      submitting: false,
      rules: {
        company: { required: true, message: '请填写公司名称' },
        bank: { required: true },
        type: { required: true },
        submitdata: { required: true },
        grade: { required: true },
        expectincome: { required: true },
      },
    }
  },
  methods: {
    handleSubmit () {
      this.submitting = true
      this.$refs.form.validate().then(() => {
        this.submitting = false
        this.visible = false
        this.$mtd.message('提交成功')
      })
    },
  },
}
</script>
<style lang="scss" scoped>
.demo-drawer-form {
  ::v-deep {
    .mtd-checkbox {
      margin-right: 30px;
    }
    .mtd-drawer-close {
      left: 16px;
      right: auto;
    }
    .mtd-input-wrapper,
    .mtd-select {
      width: 100%;
    }
    .mtd-date-picker {
      width: 100%;
    }
    .formheight1 {
      ::v-deep {
        .mtd-form-item-label {
          line-height: 20px;
        }
      }
    }
  }
}
.demo-action {
  text-align: right;
  > button {
    min-width: 80px;
  }
}
.demo-remarks {
  width: 100%;
  height: 300px;
}
.title {
  margin: 16px 0;
  font-size: 20px;
  line-height: 28px;
  font-weight: 500;
}
.sub-title {
  margin: 0 0 16px 0;
  font-size: 16px;
  line-height: 24px5;
}
</style>

```

#### doc/base.vue

```vue
<template>
  <div>
    <mtd-button type="primary" @click="visible = true">打开抽屉</mtd-button>
    <mtd-drawer v-model="visible"  title="基础样式">
      <div>
        春江潮水连海平，海上明月共潮生。
        滟滟随波千万里，何处春江无月明！
        江流宛转绕芳甸，月照花林皆似霰。
        空里流霜不觉飞，汀上白沙看不见。
        江天一色无纤尘，皎皎空中孤月轮。
        江畔何人初见月？江月何年初照人？
        人生代代无穷已，江月年年只相似。
        不知江月待何人，但见长江送流水。
        白云一片去悠悠，青枫浦上不胜愁。
        谁家今夜扁舟子？何处相思明月楼？
      </div>
      <template #footer>
        <div class="demo-drawer-footer-btn-wrapper">
          <mtd-button>取消</mtd-button>
          <mtd-button type="primary">确认</mtd-button>
        </div>
      </template>
    </mtd-drawer>
  </div>
</template>
<script>
export default {
  data () {
    return {
      visible: false,
    }
  },
}
</script>

<style lang="scss">
.demo-drawer-footer-btn-wrapper {
  float: right;
  .mtd-btn {
    margin-left: 8px;
  }
}
</style>

```

#### doc/close-position.vue

```vue
<template>
  <div class="demo-wrapper">
    <mtd-button @click="openLeft">关闭按钮在标题栏左侧</mtd-button>&nbsp;&nbsp;
    <mtd-button @click="openRight">关闭按钮在标题栏右侧</mtd-button>&nbsp;&nbsp;
    <mtd-drawer v-model="visible" title="基础样式" :close-position="closePosition">
      <div>
        春江潮水连海平，海上明月共潮生。
        滟滟随波千万里，何处春江无月明！
        江流宛转绕芳甸，月照花林皆似霰。
        空里流霜不觉飞，汀上白沙看不见。
        江天一色无纤尘，皎皎空中孤月轮。
        江畔何人初见月？江月何年初照人？
        人生代代无穷已，江月年年只相似。
        不知江月待何人，但见长江送流水。
        白云一片去悠悠，青枫浦上不胜愁。
        谁家今夜扁舟子？何处相思明月楼？
      </div>
    </mtd-drawer>
  </div>
</template>
<script>
export default {
  data() {
    return {
      v: false,
      visible: false,
      placement: 'top',
      closePosition: 'right'
    }
  },
  methods: {
    openRight() {
      this.visible = true
      this.closePosition = 'right'
    },
    openLeft() {
      this.visible = true
      this.closePosition = 'left'
    },
    close() {
      this.visible = false
    }
  },
}
</script>
<style scoped lang="scss">
.demo-wrapper {
  display: flex;
  flex: 1;
  justify-content: center;

  .mtd-btn+.mtd-btn {
    margin-left: 48px;
  }
}
</style>

```

#### doc/form.vue

```vue
<template>
  <div>
    <mtd-button type="primary" @click="visible = true">打开抽屉</mtd-button>
    <mtd-drawer
      v-model="visible"
      
      class="demo-drawer-form"
      width="800px"
      destroy-on-close
      :closable="false"
    >
      <template #title>
        <div style="display:flex;align-items:center;justify-content: space-between">
          <div style="display:flex">
            <div class="demo-drawer-close-icon" @click="visible = false">
              <mtd-icon name="close" />
            </div>
            <h3 class="title" style="margin:0 0 0 16px">创建 Offer</h3>
          </div>
          <div class="demo-action">
            <mtd-button type="primary" @click="handleSubmit">提交</mtd-button>
            <mtd-button @click="visible = false">取消</mtd-button>
          </div>
        </div>
      </template>
      <h4 class="sub-title" style="margin-top:4px">原雇主薪酬</h4>
      <mtd-form :rules="rules" :model="model" ref="form">
        <mtd-row>
          <mtd-col :span="12">
            <mtd-form-item label="公司主体:" prop="company">
              <mtd-input v-model="model.company" style="width: 200px" />
            </mtd-form-item>
          </mtd-col>
          <mtd-col :span="12">
            <mtd-form-item label="开户行:" prop="bank">
              <mtd-input v-model="model.bank" style="width: 200px" />
            </mtd-form-item>
          </mtd-col>
        </mtd-row>
        <mtd-row>
          <mtd-col :span="12">
            <mtd-form-item label="岗位类别:" prop="type">
              <mtd-select v-model="model.type" style="width: 200px">
                <mtd-option label="研发岗" value="1" />
                <mtd-option label="产品岗" value="2" />
                <mtd-option label="安全岗" value="3" />
              </mtd-select>
            </mtd-form-item>
          </mtd-col>
          <mtd-col :span="12">
            <mtd-form-item label="基本薪资:" prop="salary">
              <mtd-input v-model="model.salary" style="width: 200px" />
            </mtd-form-item>
          </mtd-col>
        </mtd-row>
        <mtd-row>
          <mtd-col :span="12">
            <mtd-form-item label="入职时间:" prop="hiredate">
              <mtd-date-picker type="date" v-model="model.hiredate" />
            </mtd-form-item>
          </mtd-col>
          <mtd-col :span="12">
            <mtd-form-item label="离职时间:" prop="leavedate">
              <mtd-date-picker type="date" v-model="model.leavedate" />
            </mtd-form-item>
          </mtd-col>
        </mtd-row>
        <mtd-row>
          <mtd-col :span="12">
            <mtd-form-item label="简历来源:" prop="source">
              <mtd-checkbox-group v-model="model.source">
                <mtd-checkbox value="1">官网</mtd-checkbox>
                <mtd-checkbox value="2">伯乐网</mtd-checkbox>
                <mtd-checkbox value="3">猎头网</mtd-checkbox>
              </mtd-checkbox-group>
            </mtd-form-item>
          </mtd-col>
          <mtd-col :span="12">
            <mtd-form-item label="部门名称:" prop="department">
              <mtd-input v-model="model.department" style="width: 200px" />
            </mtd-form-item>
          </mtd-col>
        </mtd-row>
      </mtd-form>
    </mtd-drawer>
  </div>
</template>
<script>
export default {
  data () {
    return {
      visible: false,
      model: {
        company: '',
        bank: '',
        type: '',
        salary: '',
        hiredate: '',
        leavedate: '',
        source: [],
        department: '',
        remarks: '',
      },
      submitting: false,
      rules: {
        company: { required: true, message: '请填写公司主体' },
        bank: { required: true },
        type: { required: true },
        salary: { required: true },
      },
    }
  },
  methods: {
    handleSubmit () {
      this.submitting = true
      this.$refs.form.validate().then(() => {
        this.submitting = false
        this.visible = false
        this.$mtd.message('提交成功')
      })
    },
  },
}
</script>
<style lang="scss">
.demo-drawer-form {
  ::v-deep {
    .mtd-drawer-close {
      left: 16px;
      right: auto;
    }
    .mtd-input-wrapper,
    .mtd-select {
      width: 100%;
    }
    .mtd-date-picker {
      width: 100%;
    }
  }
}

.demo-drawer-close-icon {
  height: 28px;
  width: 28px;

  cursor: pointer;

  border-radius: 50%;

  display: inline-flex;
  align-items: center;
  justify-content: center;

  &:hover {
    background-color: rgba(0,0,0,0.04);
  }

  &:active {
    background-color: rgba(0,0,0,0.06);
  }
}

.demo-action {
  text-align: right;
  > button {
    min-width: 80px;
  }
  button + button {
    margin-left: 12px;
  }
}
.demo-remarks {
  width: 100%;
  height: 300px;
}
.title {
  margin: 16px 0;
  font-size: 20px;
  line-height: 28px;
  font-weight: 500;
}
.sub-title {
  margin: 0 0 16px 0;
  font-size: 16px;
  line-height: 24px5;
}
</style>

```

#### doc/mask.vue

```vue
<template>
  <div class="demo-wrapper">
    <mtd-button @click="handleClick(true)">打开模态抽屉</mtd-button>
    <mtd-button @click="handleClick(false)">打开非模态抽屉</mtd-button>
    <mtd-drawer v-model="visible" :mask="mask" :title="title">
      <div v-if="!mask">
        春江潮水连海平，海上明月共潮生。
        滟滟随波千万里，何处春江无月明！
        江流宛转绕芳甸，月照花林皆似霰。
        空里流霜不觉飞，汀上白沙看不见。
        江天一色无纤尘，皎皎空中孤月轮。
        江畔何人初见月？江月何年初照人？
        人生代代无穷已，江月年年只相似。
        不知江月待何人，但见长江送流水。
        白云一片去悠悠，青枫浦上不胜愁。
        谁家今夜扁舟子？何处相思明月楼？
      </div>
      <div v-else>
        <mtd-form :rules="ruleCustom" :model="formCustom" ref="form" label-position="left">
          <mtd-form-item label="项目名称" prop="name">
            <mtd-input style="width: 100%;" type="text" v-model="formCustom.name" placeholder="请输入项目名称" />
          </mtd-form-item>
          <mtd-form-item label="项目介绍" prop="desc">
            <mtd-input style="width: 100%;" type="text" v-model="formCustom.desc" placeholder="请输入介绍信息" />
          </mtd-form-item>
        </mtd-form>
      </div>

      <template v-if="mask" #footer>
        <div class="demo-drawer-footer-btn-wrapper">
          <mtd-button type="panel" @click="cancel">取消</mtd-button>
          <mtd-button @click="ok" type="primary">确认</mtd-button>
        </div>
      </template>
    </mtd-drawer>
  </div>
</template>
<script>
export default {
  data() {
    return {
      formCustom: {
        name: '',
        desc: '',
      },
      ruleCustom: {
        name: [
          { required: true, message: '请输入项目名称' },
        ],
        desc: [
          { required: true, message: '请输入介绍信息' },
        ],
      },
      visible: false,
      mask: true,
    }
  },
  computed: {
    title() {
      return this.mask ? '标题' : '基础样式'
    }
  },
  methods: {
    handleClick(mask) {
      this.visible = true
      this.mask = mask
    },
    cancel() {
      this.visible = false
    },
    ok() {
      this.$refs.form.validate().then(() => {
        this.visible = false
      }).catch(e => {
        console.log(e)
      })
    }
  }
}
</script>
<style scoped lang="scss">
.demo-wrapper {
  display: flex;
  flex: 1;
  justify-content: center;

  .mtd-btn+.mtd-btn {
    margin-left: 96px;
  }
}

.demo-drawer-footer-btn-wrapper {
  float: right;

  .mtd-btn {
    margin-left: 8px;
  }
}
</style>

```

#### doc/multiple.vue

```vue
<template>
  <div>
    <mtd-button @click="visible1 = true">打开多层抽屉</mtd-button>
    <mtd-drawer v-model="visible1" v-model:visible="visible1" title="基础样式" width="600px">
      <div>
        春江潮水连海平，海上明月共潮生。<br>
        滟滟随波千万里，何处春江无月明！<br>
        江流宛转绕芳甸，月照花林皆似霰。<br>
        空里流霜不觉飞，汀上白沙看不见。<br>
        江天一色无纤尘，皎皎空中孤月轮。<br>
        江畔何人初见月？江月何年初照人？<br>
        人生代代无穷已，江月年年只相似。<br>
        不知江月待何人，但见长江送流水。<br>
        白云一片去悠悠，青枫浦上不胜愁。<br>
        谁家今夜扁舟子？何处相思明月楼？<br><br><br>
      </div>
      <template #footer>
        <div class="demo-drawer-footer-btn-wrapper">
          <mtd-button type="primary" @click="visible2 = true">打开第二层抽屉</mtd-button>
        </div>
      </template>
    </mtd-drawer>
    <mtd-drawer v-model="visible2" v-model:visible="visible2" title="静夜思" width="400px" >
      <div>
        窗前明月光，疑是地上霜。
        举头望明月，低头思故乡。
      </div>
    </mtd-drawer>
  </div>
</template>
<script>
export default {
  data () {
    return {
      visible1: false,
      visible2: false,
    }
  },
}
</script>

```

#### doc/placement.vue

```vue
<template>
  <div class="demo-wrapper">
    <mtd-button @click="openLeft">打开左侧抽屉</mtd-button>
    <mtd-button @click="openRight">打开右侧抽屉</mtd-button>
    <mtd-button @click="openTop">打开顶部抽屉</mtd-button>
    <mtd-button @click="openBottom">打开底部抽屉</mtd-button>

    <mtd-drawer v-model="visible" :placement="placement" title="基础样式">
      <div>
        春江潮水连海平，海上明月共潮生。
        滟滟随波千万里，何处春江无月明！
        江流宛转绕芳甸，月照花林皆似霰。
        空里流霜不觉飞，汀上白沙看不见。
        江天一色无纤尘，皎皎空中孤月轮。
        江畔何人初见月？江月何年初照人？
        人生代代无穷已，江月年年只相似。
        不知江月待何人，但见长江送流水。
        白云一片去悠悠，青枫浦上不胜愁。
        谁家今夜扁舟子？何处相思明月楼？
      </div>
    </mtd-drawer>
  </div>
</template>
<script>
export default {
  data() {
    return {
      v: false,
      visible: false,
      placement: 'top',
    }
  },
  methods: {
    openTop() {
      this.visible = true
      this.placement = 'top'
    },
    openRight() {
      this.visible = true
      this.placement = 'right'
    },
    openBottom() {
      this.visible = true
      this.placement = 'bottom'
    },
    openLeft() {
      this.visible = true
      this.placement = 'left'
    },
  },
}
</script>
<style scoped lang="scss">
.demo-wrapper {
  display: flex;
  flex: 1;
  justify-content: center;

  .mtd-btn+.mtd-btn {
    margin-left: 48px;
  }
}
</style>

```

#### doc/size.vue

```vue
<template>
  <div class="demo-wrapper">
    <mtd-button @click="handleClick(400)">打开小尺寸抽屉</mtd-button>
    <mtd-button @click="handleClick(600)">打开中尺寸抽屉</mtd-button>
    <mtd-button @click="handleClick(800)">打开大尺寸抽屉</mtd-button>
    <mtd-drawer v-model="visible" :width="width" title="基础样式" @close="handleClose" @open="handleOpen">
      <div>
        春江潮水连海平，海上明月共潮生。
        滟滟随波千万里，何处春江无月明！
        江流宛转绕芳甸，月照花林皆似霰。
        空里流霜不觉飞，汀上白沙看不见。
        江天一色无纤尘，皎皎空中孤月轮。
        江畔何人初见月？江月何年初照人？
        人生代代无穷已，江月年年只相似。
        不知江月待何人，但见长江送流水。
        白云一片去悠悠，青枫浦上不胜愁。
        谁家今夜扁舟子？何处相思明月楼？
      </div>
    </mtd-drawer>
  </div>
</template>
<script>
export default {
  data() {
    return {
      visible: false,
      width: 400
    }
  },
  methods: {
    handleClick(width) {
      this.visible = true
      this.width = width
    },
    handleClose() {
      console.log('close')
    },
    handleOpen() {
      console.log('open')
    }
  }
}
</script>
<style scoped lang="scss">
.demo-wrapper {
  display: flex;
  flex: 1;
  justify-content: center;

  .mtd-btn+.mtd-btn {
    margin-left: 96px;
  }
}
</style>

```

#### doc/title-footer.vue

```vue
<template>
  <div class="demo-wrapper">
    <mtd-button @click="handleClick('title')">打开头部自定义的抽屉</mtd-button>
    <mtd-button @click="handleClick('footer')">打开底部自定义的抽屉</mtd-button>
    <mtd-drawer :class="drawerClass" v-model="visible" title="基础样式" :closable="!title">
      <div>
        春江潮水连海平，海上明月共潮生。
        滟滟随波千万里，何处春江无月明！
        江流宛转绕芳甸，月照花林皆似霰。
        空里流霜不觉飞，汀上白沙看不见。
        江天一色无纤尘，皎皎空中孤月轮。
        江畔何人初见月？江月何年初照人？
        人生代代无穷已，江月年年只相似。
        不知江月待何人，但见长江送流水。
        白云一片去悠悠，青枫浦上不胜愁。
        谁家今夜扁舟子？何处相思明月楼？
      </div>
      <template v-if="title" #title>
        <div class="demo-drawer-title">
          <span class="title">项目概览</span>
          <div class="title-right">

            <div class="demo-drawer-title-btn-wrapper">
              <mtd-button type="panel">取消</mtd-button>
              <mtd-button type="primary">确认</mtd-button>
            </div>

            <mtd-icon name="close" />
          </div>

        </div>
      </template>

      <template v-if="footer" #footer>
        <div class="demo-drawer-footer-btn-wrapper">
          <mtd-button type="panel">取消</mtd-button>
          <mtd-button type="primary">确认</mtd-button>
        </div>
      </template>
    </mtd-drawer>
  </div>
</template>
<script>
export default {
  data() {
    return {
      visible: false,
      title: false,
      footer: false
    }
  },
  computed: {
    drawerClass() {
      return this.title ? "coustom-drawer" : ''
    }
  },
  methods: {
    handleClick(param) {
      this.visible = true
      if (param === 'title') {
        this.title = true
        this.footer = false
      } else {
        this.title = false
        this.footer = true
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.demo-wrapper {
  display: flex;
  flex: 1;
  justify-content: center;

  .mtd-btn+.mtd-btn {
    margin-left: 96px;
  }
}

.coustom-drawer {
  ::v-deep .mtd-drawer-header-wrapper {
    padding: 0;
  }

  .demo-drawer-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 24px;
    border-bottom: 1px solid rgba(17, 25, 37, 0.05);
    margin-bottom: 12px;

    .title {
      font-weight: 500;
      color: #111925;
    }

    .mtd-btn {
      margin-left: 8px;
    }
  }

  .title-right {
    display: flex;
    align-items: center;

    .demo-drawer-title-btn-wrapper {
      margin-right: 12px;
    }
  }
}

.demo-drawer-footer-btn-wrapper {
  float: right;

  .mtd-btn {
    margin-left: 8px;
  }
}
</style>

```

#### drawer.tsx

```tsx
import {
  defineComponent,
  PropType,
  ref,
  computed,
  reactive,
  markRaw,
  onBeforeUnmount,
  onDeactivated,
  onMounted,
  onActivated,
  watch,
  toRefs,
  getSlotsInRender,
  useResetAttrs,
  Transition,
  vSlots,
  classNames,
  styles,
  vueInstance,
  hasProp,
} from '@ss/mtd-adapter'
import useConfig from '@hooks/config'
import { lock, unlock } from '@utils/lock-scroll'
import { PopupManage, getDefaultPopupContainer } from '@utils/popper'
import DrawerInside from './inside'
import mitt from '@utils/mitt'
import { isDef } from '@utils/type'
import { DrawerPlacement } from './types'
import teleportHook from '@components/hooks/teleport'

type getPopupContainerType = () => any

export default defineComponent({
  name: 'MtdDrawer',
  components: {
    DrawerInside,
  },
  inheritAttrs: false,
  model: {
    prop: 'modelValue',
    event: 'update:modelValue',
  },
  props: {
    placement: {
      type: String as PropType<DrawerPlacement>,
      default: 'right',
      validator: (p: string): boolean => {
        return ['left', 'right', 'top', 'bottom'].indexOf(p) > -1
      },
    },
    visible: {
      type: Boolean,
      default: false,
    },
    modelValue: {
      type: Boolean,
      default: false,
    },
    closable: {
      type: Boolean,
      default: true,
    },
    appendToContainer: {
      type: Boolean,
      default: true,
    },
    mask: {
      type: Boolean,
      default: true,
    },
    maskClosable: {
      type: Boolean,
      default: true,
    },
    lockScroll: {
      type: Boolean,
      default: true,
    },
    getPopupContainer: {
      type: Function as PropType<getPopupContainerType>,
      default: getDefaultPopupContainer,
    },
    destroyOnClose: {
      type: Boolean,
      default: false,
    },
    keyboard: {
      type: Boolean,
      default: true,
    },
    closePosition: {
      type: String,
      default: 'right',
      validator: (value: string) => {
        return ['left', 'right'].includes(value)
      },
    },
  },
  emits: ['close', 'update:modelValue', 'update:visible', 'open'],
  setup(props, { emit, attrs }) {
    const config = useConfig()
    const prefix = computed(() => config.getPrefixCls('drawer'))
    const prefixMTD = computed(() => config.getPrefix())

    const ins = vueInstance()

    const removableRef = ref<HTMLElement | null>(null)

    const m_visible = computed(() => hasProp(ins, 'modelValue') ? props.modelValue : props.visible)

    const state = reactive({
      drawVisible: m_visible.value,
      zIndex: '2000',
      emitter: markRaw(mitt()),
    })

    const { createTele, destroyTele } = teleportHook(props)

    // @Created
    state.emitter.on('esc', handleEscClose)

    // @Computed
    const resetAttrs = useResetAttrs(attrs)

    // @Watch
    watch(() => m_visible.value, (val) => {
      val ? openDrawer() : closeDrawer()
    })

    // @Methods
    function handleClickClose() {
      emit('update:modelValue', false)
      emit('update:visible', false)
      emit('close')
    }

    function handleEscClose() {
      if ((hasProp(ins, 'keyboard') && isDef(props.keyboard) ? props.keyboard : props.closable) && m_visible.value) {
        handleClickClose()
      }
    }

    function openDrawer() {
      state.drawVisible = true
      state.zIndex = PopupManage.nextZIndex()
      PopupManage.open(ins)
      createTele()
      if (props.lockScroll) {
        lock(document.body, prefixMTD.value ?? 'mtd')
      }
      emit('open')
    }

    function closeDrawer() {
      PopupManage.close(ins)
      if (props.lockScroll) {
        unlock(document.body, prefixMTD.value ?? 'mtd')
      }
    }

    function init() {
      if (m_visible.value) {
        openDrawer()
      }
    }
    onMounted(init)
    onActivated(init)

    function destory() {
      closeDrawer()
      destroyTele()
    }
    onDeactivated(destory)
    onBeforeUnmount(destory)

    function handleAfterLeave() {
      // 过渡元素的 v-if 或 v-show 发生了变化：当过渡元素的 v-if 或 v-show 的值发生变化时，after-leave 事件会再次触发。这是因为 Vue 3 在重新计算过渡元素的显示状态时，会重新执行离开过渡。
      // so,这里vue3会触发两次哦
      if (props.destroyOnClose && state.drawVisible) {
        state.drawVisible = false
      }
    }

    function handleMaskClick() {
      if (props.maskClosable) {
        handleClickClose()
      }
    }

    return {
      prefix,
      ...toRefs(state),
      handleClickClose,
      handleAfterLeave,
      handleMaskClick,
      removableRef,
      resetAttrs,
      m_visible,
    }
  },
  render() {
    const {
      prefix, resetAttrs, mask,
      zIndex, placement, closable, drawVisible,
      m_visible, closePosition,
    } = this

    const slots = {
      title: getSlotsInRender(this, 'title') ? () => getSlotsInRender(this, 'title') : undefined,
      footer: getSlotsInRender(this, 'footer') ? () => getSlotsInRender(this, 'footer') : undefined,
    }

    return <div
      class={classNames(this, [prefix + '-container'])}
      style={styles(this)}
    >
      {/* 遮罩层 */}
      <Transition name="fade-in" enter-from-class={'fade-in' + '-enter'}>
        <div
          v-show={m_visible}
          class={mask ? `${prefix}-mask` : `${prefix}-wrapper`}
          style={{ 'z-index': zIndex } as any}
          onClick={this.handleMaskClick}
        />
      </Transition>
      <Transition
        name={`fade-in-${placement}`}
        enter-from-class={'fade-in' + '-enter'}
        onAfterLeave={this.handleAfterLeave}
      >
        {drawVisible &&
          <DrawerInside
            v-show={m_visible}
            {...resetAttrs}
            placement={placement}
            closable={closable}
            zIndex={zIndex}
            onClose={this.handleClickClose}
            closePosition={closePosition}
            {...vSlots(slots)}
            v-slots={slots}
          >
            {getSlotsInRender(this)}
          </DrawerInside>}
      </Transition >
    </div >
  },
})

```

#### index.md

```markdown
# 抽屉 / Drawer
抽屉（Drawer）是从屏幕边缘滑出的浮层面板。

## 互动演示

:::include(src="./demo/drawer-interactive.vue")
:::

## 互动演示

### 尺寸
:::include(src="./doc/size.vue")
:::

### 位置
抽屉提供4种展示位置，点击触发按钮抽屉从对应的位置滑出，默认右侧，用户可根据实际场景自定义位置。
:::include(src="./doc/placement.vue")
:::

### 模态与非模态
当需要用户注意力聚焦在当前抽屉内的交互时，建议使用模态抽屉；当抽屉以呈现信息为主，不打断用户操作时，建议使用非模态抽屉。
:::include(src="./doc/mask.vue")
:::

### 自定义头部和底部
通过 title 和 footer 插槽，可自定义抽屉的头部和底部。
:::include(src="./doc/title-footer.vue")
:::

### 多层抽屉
在抽屉内打开新的抽屉，用以解决多分支任务的复杂状况（嵌套出现的抽屉需覆盖原始抽屉）。
:::include(src="./doc/multiple.vue")
:::

### 关闭按钮位置
关闭按钮可配置在标题栏的左侧和右侧。
:::include(src="./doc/close-position.vue")
:::

## API
<api-doc name="Drawer" :doc="require('./api.json')"></api-doc>

```

#### index.ts

```typescript
import Drawer from './drawer'
import { withInstall } from '@ss/mtd-adapter'
export default withInstall(Drawer)

```

#### inside.tsx

```tsx
import {
  defineComponent,
  PropType,
  computed,
  getSlotsInRender,
  onUnmounted,
  vueInstance,
} from '@ss/mtd-adapter'
import { DrawerPlacement } from './types'
import { isNumber } from '@utils/type'
import useConfig from '@hooks/config'
import MtdIcon from '@components/icon'
export default defineComponent({
  name: 'DrawerInside',
  components: {
    MtdIcon,
  },
  inheritAttrs: true,
  props: {
    title: String,
    width: {
      type: [Number, String],
      default: 400,
    },
    height: {
      type: [Number, String],
      default: 400,
    },
    closable: {
      type: Boolean,
      default: true,
    },
    placement: {
      type: String as PropType<DrawerPlacement>,
      default: 'right',
    },
    zIndex: {
      type: [Number, String],
      required: true,
    },
    closePosition: {
      type: String,
      default: 'right',
      validator: (value: string) => {
        return ['left', 'right'].includes(value)
      },
    },
  },
  emits: ['close'],
  setup(props, { emit }) {
    const config = useConfig()
    const prefix = computed(() => config.getPrefixCls('drawer'))

    const isVertical = computed(
      () => ['left', 'right'].indexOf(props.placement) > -1,
    )
    const drawerStyle = computed(() => {
      const style: Record<string, string | number> = {
        zIndex: props.zIndex,
      }
      if (isVertical.value) {
        style.width = isNumber(props.width) ? `${props.width}px` : props.width
      } else {
        style.height = isNumber(props.height)
          ? `${props.height}px`
          : props.height
      }
      return style
    })
    const handleClickClose = () => emit('close')

    const ins = vueInstance()
    onUnmounted(() => {
      // Vue 3 的自动销毁机制通常是可靠的，但在特定情况下可能会出现延迟或无法自动销毁的情况。在这种情况下，可以通过手动执行清理操作来确保组件的资源得到正确释放。
      ins.$el && (ins.$el as HTMLElement).remove()
    })

    return {
      prefix,
      isVertical,
      drawerStyle,
      handleClickClose,
    }
  },
  render() {
    const {
      prefix, placement, drawerStyle, closable, title,
      closePosition,
    } = this
    return <div
      class={{
        [prefix]: true,
        [`${prefix}-${placement}`]: true,
      }}
      style={drawerStyle}
    >
      <div class={`${prefix}-header-wrapper ${closePosition === 'left' ? prefix + '-header-wrapper-reverse' : ''}`}>
        {(title || getSlotsInRender(this, 'title')) && <div class={`${prefix}-header`}>
          {getSlotsInRender(this, 'title') || title}
        </div>}
        {closable && <div class={`${prefix}-close`} onClick={this.handleClickClose}>
          <mtd-icon name={'close'} />
        </div>}
      </div>
      <div class={`${prefix}-content`}>
        {getSlotsInRender(this)}
      </div>
      <div class={`${prefix}-footer`}>
        {getSlotsInRender(this, 'footer')}
      </div>
    </div >
  },
})

```

#### tests/__snapshots__/drawer.spec.js.snap

```plaintext
// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Drawer render top drawer 1`] = `<div class="mtd-teleport mtd-teleport-hidden"></div>`;

exports[`Drawer 点击遮罩层不要关闭 mask-closable 1`] = `<div class="mtd-teleport mtd-teleport-hidden"></div>`;

```

#### tests/drawer.spec.js

```javascript
import Drawer from '@components/drawer'
import {
  createDemoTest,
  mount,
  triggerClick,
  wait,
  waitImmediate,
  asyncExpect,
} from '@tests/utils'

const drawerClass = '.mtd-drawer'

describe('Drawer', function () {
  createDemoTest('drawer')

  // @Slot
  it('title slot', async () => {
    const wrapper = mount(Drawer, {
      propsData: {
        title: 'Drawer Title',
        visible: true,
      },
      slots: {
        title: '<span>Drawer Title Slot Content</span>'
      }
    })
    expect(wrapper.find('.mtd-drawer-header').text()).toEqual('Drawer Title Slot Content')
  });
})

```

#### types.ts

```typescript
import { Component } from '@components/types/component'
import { getPopupContainer } from '@components/popper/types'

export type DrawerPlacement = 'left' | 'right' | 'top' | 'bottom';

export declare interface IDrawer extends Component {
  visible?: boolean;
  title?: string;
  width?: string | number;
  height?: string | number;
  closable?: boolean;
  mask?: boolean;
  maskClosable?: boolean;
  lockScroll?: boolean;
  placement?: DrawerPlacement;
  getPopupContainer?: getPopupContainer;
  appendToContainer?: boolean;
  destroyOnClose?: boolean;
}

declare const Drawer: IDrawer
export default Drawer

```

