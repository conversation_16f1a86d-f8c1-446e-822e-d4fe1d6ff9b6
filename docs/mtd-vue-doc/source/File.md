## 源码解析

### 文件依赖

- doc/base.vue
- file.tsx
- index.md
- index.ts
- types.ts

### 源码内容

#### doc/base.vue

```vue
<template>
  <div class="demo-full-width">
    <mtd-radio-group v-model="type" >
      <mtd-radio disabled value="text">文字型</mtd-radio>
      <mtd-radio value="border-panel">线框型</mtd-radio>
      <mtd-radio value="filled-panel">面板型</mtd-radio>
      <mtd-radio value="picture-card">图片型</mtd-radio>
    </mtd-radio-group>
    <ul >
      <mtd-file v-for="(file,index) in files" :file="file" :key="index" style="margin:0 8px 8px 0" :type="type"/>
    </ul>
  </div>
</template>

<script>
export default {
  data() {
    return {
      type: 'border-panel',
      files: [
        {
          name: "food1.jepg",
          status: "success",
          uid: 1660048779185,
          url: "https://fuss10.elemecdn.com/3/63/4e7f3a15429bfda99bce42a18cdd1jpeg.jpeg?imageMogr2/thumbnail/360x360/format/webp/quality/100",
          thumbUrl: "https://fuss10.elemecdn.com/3/63/4e7f3a15429bfda99bce42a18cdd1jpeg.jpeg?imageMogr2/thumbnail/360x360/format/webp/quality/100",
          size: 25687,

          user: '团团/tuantaun',
          time: '2022/5/20',
        },
        {
          name: "food2.jepg",
          status: "uploading",
          percentage: 63,
          uid: 1660048779185,
          url: "https://fuss10.elemecdn.com/3/63/4e7f3a15429bfda99bce42a18cdd1jpeg.jpeg?imageMogr2/thumbnail/360x360/format/webp/quality/100",
          thumbUrl: "https://fuss10.elemecdn.com/3/63/4e7f3a15429bfda99bce42a18cdd1jpeg.jpeg?imageMogr2/thumbnail/360x360/format/webp/quality/100",
          size: 25687,
        },
        {
          name: "food3.jepg",
          status: "fail",
          statusMsg: "上传错误",
          uid: 1660048779185,
          url: "https://fuss10.elemecdn.com/3/63/4e7f3a15429bfda99bce42a18cdd1jpeg.jpeg?imageMogr2/thumbnail/360x360/format/webp/quality/100",
          thumbUrl: "https://fuss10.elemecdn.com/3/63/4e7f3a15429bfda99bce42a18cdd1jpeg.jpeg?imageMogr2/thumbnail/360x360/format/webp/quality/100",
          size: 25687,
        },
      ],
    };
  },
  methods: {
    handleChange(value) {
      console.log(value);
    },
  },
};
</script>

```

#### file.tsx

```tsx
import {
  defineComponent,
  PropType,
  computed,
  Fragment,
} from '@ss/mtd-adapter'
import useConfig from '@hooks/config'
import { IRFile, MtdFile } from './types'

import MtdProgress from '@components/progress'
import MtdIcon from '@components/icon'
import { isFunction } from '@utils/type'

const imageSize = {
  width: '46px',
  height: '46px',
}

const fileIconImageSize = {
  width: '36px',
  height: '36px',
}

export default defineComponent({
  name: 'MtdFile',
  components: {
    MtdProgress,
    MtdIcon,
  },
  inheritAttrs: true,
  props: {
    file: {
      type: Object as PropType<MtdFile>,
    },
    type: {
      type: String,
      default: 'border-panel',
    }, // text border-panel filled-panel picture-card
    // 缩略图地址
    thumbUrl: {
      type: [String, Function],
    },
    showFileDown: {
      type: Boolean,
      default: true,
    },
    autoDownload: {
      type: Boolean,
      default: true,
    },
    simple: {
      type: Boolean,
      default: false,
    },
    icon: String,
  },
  emits: ['keydown', 'focus', 'blur', 'click', 'redo', 'remove', 'download', 'retry'],
  setup(props, { emit }) {
    const config = useConfig()
    const prefix = computed(() => config.getPrefixCls('file'))
    const m_icon = computed(() => props.simple ? props.icon ?? 'file-o' : '')

    const retry = (event: Event) => {
      event.stopPropagation()
      emit('retry', props.file, event)
    }

    const remove = (event: Event) => {
      event.stopPropagation()
      emit('remove', props.file, event)
    }

    const download = (event: Event) => {
      event.stopPropagation()
      emit('download', props.file, event)
      if (!props.autoDownload) return
      window.open(props.file?.url)
    }

    const isImageType = (file?: IRFile) => {
      if (!file) return false
      return /\.(png|jpg|jpeg|webp|gif)$/i.test(
        file.name,
      )
    }

    function compatibleDefaultUrl(file: IRFile) {
      if (!isImageType(file) && props.thumbUrl) {
        return isFunction(props.thumbUrl)
          ? props.thumbUrl(props.file)
          : props.file?.thumbUrl
      }

      return file.url
    }

    const click = () => { emit('click', props.file) }

    return {
      prefix,
      retry,
      remove,
      download,
      compatibleDefaultUrl,
      click,
      t: config.t,
      isImageType,
      m_icon,
    }
  },
  render() {
    const {
      prefix, file, type, showFileDown, isImageType, m_icon, simple,
    } = this

    const renderSimpleFileMsg = () => {
      switch (file?.status) {
        case 'fail': {
          return <span class={`${prefix}-msg`}>
            <mtd-icon name="warning-circle" />
          </span>
        }
      }
    }

    const renderFileMsg = () => {
      switch (file?.status) {
        case 'fail':
          return <span class={`${prefix}-msg`}>
            <mtd-icon name="warning-circle-o" />{file.statusMsg || this.t('el.upload.uploadError')}
          </span>
        case 'success':
          return <span class={`${prefix}-msg`}>
            <span>{`${(file.size / (1024 * 1024)).toFixed(2)}M`}</span>
            <span>{file.user}</span>
            <span>{file.time}</span>
          </span>
        case 'uploading':
          return <mtd-progress percentage={file.percentage} stroke-width={4} show-info={false} />
        default: <span></span>
          break
      }
    }

    const renderHandler = () => {
      switch (file?.status) {
        case 'fail':
          return <Fragment>
            <mtd-icon name="refresh-o" onClick={this.retry} />
            <mtd-icon name="delete-o" onClick={this.remove} />
          </Fragment>
        case 'success':
          return <Fragment>
            {showFileDown && <mtd-icon name="download-o" onClick={this.download} />}
            <mtd-icon name="delete-o" onClick={this.remove} />
          </Fragment>
        case 'uploading':
          return simple ? <mtd-progress percentage={file.percentage} type="pie" width={16} /> : <span class={`${prefix}-uploading-text`}>{`${file.percentage}%`}</span>
        case 'ready':
          return <Fragment>
            <mtd-icon name="delete-o" onClick={this.remove} />
          </Fragment>
        default: <span></span>
          break
      }
    }

    const renderPicCard = (picFile: IRFile) => (picFile.status !== 'uploading'
      ? <div>
        <img
          class={`${prefix}-thumbnail`}
          src={this.compatibleDefaultUrl(picFile)}
          alt=""
        />
        <div
          class={`${prefix}-pic-actions`}
        >
          {picFile.status === 'fail'
            ? <div onClick={this.retry}>
              <mtd-icon name="refresh-o" />
            </div>
            : <div onClick={this.download}>
              <mtd-icon name="download-o" />
            </div>
          }
          <div onClick={this.remove}>
            <mtd-icon name="close" />
          </div>
        </div>
      </div>
      : <div class={`${prefix}-type-picture-card-uploading`}>
        <mtd-progress percentage={picFile.percentage} stroke-width={4} show-info={false} />
        <span class={`${prefix}-type-picture-card-uploading-text`}>{`上传中 ${picFile.percentage || 0}%`}</span>
      </div>)

    const renderSuffix = () => {
      if (simple) {
        if (m_icon) {
          return <mtd-icon class={`${prefix}-simple-icon`} name={m_icon} />
        }

        return null
      }

      return (
        <div
          class={{
            [`${prefix}-pic`]: true,
            [`${prefix}-pic-border-less`]: !isImageType(file),
          }}
          style={{
            backgroundImage: `url(${this.compatibleDefaultUrl(file!)})`,
            height: isImageType(file) ? imageSize.height : fileIconImageSize.height,
            width: isImageType(file) ? imageSize.width : fileIconImageSize.width,
          }}
        >
        </div>
      )
    }

    return <li
      class={{
        [prefix]: true,
        [`${prefix}-${file?.status}`]: file && file.status,
        [`${prefix}-type-panel`]: type === 'border-panel' || type === 'filled-panel',
        [`${prefix}-type-${type}`]: true,
        [`${prefix}-simple`]: simple,
      }}
      v-show={file}
      onClick={this.click}
    >

      {type === 'picture-card'
        ? renderPicCard(file!)
        : <Fragment>
          {renderSuffix()}
          <div class={`${prefix}-content`}>
            <div class={`${prefix}-title`}>{file?.name}</div>
            {simple ? renderSimpleFileMsg() : renderFileMsg()}
          </div>
          <div class={`${prefix}-icon`}>
            {renderHandler()}
          </div>
        </Fragment>
      }

    </li>
  },
})

```

#### index.md

```markdown
# 文件 / File
## 基础样式


## 类型与用法


## API
<api-doc name="File" :doc="require('./api.json')"></api-doc>


```

#### index.ts

```typescript
import File from './file'
import { withInstall } from '@ss/mtd-adapter'
export default withInstall(File)

```

#### types.ts

```typescript
export interface IFile extends File {
  uid: number;
}
export interface IRFile {
  url: string;
  name: string;
  size: number;
  uid: number;
  percentage: number;
  status: string; // 'ready' | 'success' |'fail'
  raw: IFile;

  response?: any;
}

export interface MtdFile extends IRFile {
  user?: string
  time?: number | string | Date
  statusMsg?: string
  thumbUrl?: string | Function
}
```

