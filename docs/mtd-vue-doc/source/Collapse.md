## 源码解析

### 文件依赖

- collapse.tsx
- demo/collapse-interactive.vue
- demo/usage.json
- doc/area.vue
- doc/base.vue
- doc/drag.vue
- doc/file.vue
- doc/fold.vue
- doc/icon.vue
- doc/loading.vue
- doc/multi.vue
- doc/position.vue
- doc/sample.vue
- doc/title-solt.vue
- index.md
- index.ts
- types.ts
- useCollapse.ts

### 源码内容

#### collapse.tsx

```tsx
import {
  computed,
  defineComponent,
  PropType,
  getSlotsInRender,
} from '@ss/mtd-adapter'
import useConfig from '@hooks/config'
import { CollapseType } from './types'
import { isArray } from '@utils/type'
import mitt from '@utils/mitt'
import { provideCollapse } from './useCollapse'
import useControlled from '@hooks/controlled'

type BaseType = string | number | boolean | object;
type ModelValueType = BaseType | BaseType[];

export default defineComponent({
  name: 'MtdCollapse',
  inheritAttrs: true,
  model: {
    prop: 'modelValue',
    event: 'update:modelValue',
  },
  props: {
    modelValue: {
      type: [String, Number, Boolean, Object, Array],
    },
    type: {
      type: String as PropType<CollapseType>,
      default: '',
    },
    rightAlignArrow: Boolean,
    triangleArrow: Boolean,
  },
  emits: ['input', 'change', 'update:modelValue'],
  setup(props, ctx) {
    const config = useConfig()
    const prefix = computed(() => config.getPrefixCls('collapse'))
    const emitter = mitt()
    const [value, setValue] = useControlled<ModelValueType>('modelValue', props, ctx)
    const rightAlignArrow = computed(() => props.rightAlignArrow)
    const triangleArrow = computed(() => props.triangleArrow)
    provideCollapse({
      value,
      rightAlignArrow,
      triangleArrow,
      emitter,
    })

    // @Created
    emitter.on('itemClick', ([active, itemValue]) => {
      let nextValue
      if (isArray(value.value)) {
        nextValue = active
          ? [...value.value, itemValue]
          : value.value.filter((v: string) => v !== itemValue)
      } else {
        nextValue = active ? itemValue : ''
      }

      setValue(nextValue)
      ctx.emit('input', nextValue)
      ctx.emit('change', nextValue)
    })
    return {
      prefix,
      emitter,
    }
  },
  render() {
    const {
      prefix, type,
    } = this
    return <div
      class={{
        [prefix]: true,
        [`${prefix}-${type}`]: type,
      }}
    >
      {getSlotsInRender(this)}
    </div>
  },
})

```

#### demo/collapse-interactive.vue

```vue
<template>
  <mtd-doc-usage name='Collapse' :apiJson="apiJson" :usageJson="usage">
    <template #Collapse="{ configProps }">
      <div style="width: 100%; padding: 8px 41px; height: 312px;">
        <mtd-collapse v-bind="configProps" v-model="activeValue1">
          <mtd-collapse-item title="春日" value="1">
            一春略无十日晴，处处浮云将雨行。<br />
            野田春水碧于镜，人影渡傍鸥不惊。<br />
            桃花嫣然出篱笑，似开未开最有情。<br />
            茅茨烟暝客衣湿，破梦午鸡啼一声。
          </mtd-collapse-item>
          <mtd-collapse-item title="夏日山中" value="2">
            懒摇白羽扇，裸体青林中。<br />
            脱巾挂石壁，露顶洒松风。
          </mtd-collapse-item>
          <mtd-collapse-item title="秋词" value="3">
            自古逢秋悲寂寥，我言秋日胜春朝。<br />
            晴空一鹤排云上，便引诗情到碧霄。
          </mtd-collapse-item>
          <mtd-collapse-item title="冬柳" value="4">
            柳汀斜对野人窗，零落衰条傍晓江。<br />
            正是霜风飘断处，寒鸥惊起一双双。
          </mtd-collapse-item>
        </mtd-collapse>
      </div>
    </template>
  </mtd-doc-usage>
</template>
<script>
const usage = require('./usage.json')
const apiJson = require('../api.json')
export default {
  data() {
    return {
      usage,
      activeValue1: [],
      apiJson,
    };
  },
};
</script>
  
```

#### demo/usage.json

```json
{
    "componentName": "Collapse",
    "desc": "折叠面板",
    "codeTemplate": "<mtd-collapse v-bind='configProps'></mtd-collapse>"
}
```

#### doc/area.vue

```vue
<template>
  <div style="text-align: left;" class="demo-full-width">
    <mtd-tabs v-model="type" type="text" size="small">
      <mtd-tab-pane label="常规型" value="" />
      <mtd-tab-pane label="简洁型" value="sample" />
    </mtd-tabs>
    <mtd-collapse :type="type">
      <mtd-collapse-item title="春日" value="1">
        一春略无十日晴，处处浮云将雨行。<br />
        野田春水碧于镜，人影渡傍鸥不惊。<br />
        桃花嫣然出篱笑，似开未开最有情。<br />
        茅茨烟暝客衣湿，破梦午鸡啼一声。
      </mtd-collapse-item>
      <mtd-collapse-item title="夏日山中" value="2">
        懒摇白羽扇，裸体青林中。<br />
        脱巾挂石壁，露顶洒松风。
      </mtd-collapse-item>
      <mtd-collapse-item title="秋词" value="3">
        自古逢秋悲寂寥，我言秋日胜春朝。<br />
        晴空一鹤排云上，便引诗情到碧霄。
      </mtd-collapse-item>
      <mtd-collapse-item value="4">
        <template #title>冬柳</template>
        柳汀斜对野人窗，零落衰条傍晓江。<br />
        正是霜风飘断处，寒鸥惊起一双双。
      </mtd-collapse-item>
    </mtd-collapse>
  </div>
</template>
<script>
export default {
  data() {
    return {
      type: '',
    }
  },
}
</script>

<style scoped>
.demo-full-width {
  padding: 20px 0;
}

::v-deep .demo-full-width .mtd-tabs-nav {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  border-bottom: 1px solid #edf0f7;
  padding: 1.5px 16px;
}
</style>

```

#### doc/base.vue

```vue
<template>
  <div class="demo-full-width">
    <mtd-collapse v-model="activeValue1">
      <mtd-collapse-item title="春日" value="1">
        一春略无十日晴，处处浮云将雨行。<br />
        野田春水碧于镜，人影渡傍鸥不惊。<br />
        桃花嫣然出篱笑，似开未开最有情。<br />
        茅茨烟暝客衣湿，破梦午鸡啼一声。
      </mtd-collapse-item>
      <mtd-collapse-item title="夏日山中" value="2">
        懒摇白羽扇，裸体青林中。<br />
        脱巾挂石壁，露顶洒松风。
      </mtd-collapse-item>
      <mtd-collapse-item title="秋词" value="3" disabled>
        秋词<br />
        自古逢秋悲寂寥，我言秋日胜春朝。<br />
        晴空一鹤排云上，便引诗情到碧霄。
      </mtd-collapse-item>
      <mtd-collapse-item title="冬柳" value="4">
        柳汀斜对野人窗，零落衰条傍晓江。<br />
        正是霜风飘断处，寒鸥惊起一双双。
      </mtd-collapse-item>
    </mtd-collapse>
  </div>
</template>
<script>
export default {
  data() {
    return {
      activeValue1: '1',
    }
  },
}
</script>

```

#### doc/drag.vue

```vue
<template>
  <div class="demo-full-width">
    <p>下拉型</p>
    <mtd-collapse v-model="activeValue2">
      <mtd-collapse-item title="春日" value="1">
        一春略无十日晴，处处浮云将雨行。<br />
        野田春水碧于镜，人影渡傍鸥不惊。<br />
        桃花嫣然出篱笑，似开未开最有情。<br />
        茅茨烟暝客衣湿，破梦午鸡啼一声。
      </mtd-collapse-item>
      <mtd-collapse-item title="夏日山中" value="2">
        懒摇白羽扇，裸体青林中。<br />
        脱巾挂石壁，露顶洒松风。
      </mtd-collapse-item>
      <mtd-collapse-item title="秋词" value="3">
        秋词<br />
        自古逢秋悲寂寥，我言秋日胜春朝。<br />
        晴空一鹤排云上，便引诗情到碧霄。
      </mtd-collapse-item>
      <mtd-collapse-item value="4">
        <template #title>冬柳</template>

        柳汀斜对野人窗，零落衰条傍晓江。<br />
        正是霜风飘断处，寒鸥惊起一双双。
      </mtd-collapse-item>
    </mtd-collapse>
  </div>
</template>
<script>
export default {
  data() {
    return {
      activeValue2: '1',
    }
  },
}
</script>

```

#### doc/file.vue

```vue
<template>
  <div style="width: 200px; text-align: left;">
    <span @click="toggleExpended" class="demo-collapse-poem" :class="{ 'expended': expended }">
      山居秋暝 <i class="mtdicon mtdicon-down-thick" />
    </span>
    <mtd-collapse-transition>
      <div v-show="expended">
        <span style=" display: inline-block; color: rgba(0, 0, 0, 0.7);">
          空山新雨后，天气晚来秋。<br />
          明月松间照，清泉石上流。<br />
          竹喧归浣女，莲动下渔舟。<br />
          随意春芳歇，王孙自可留。
        </span>
      </div>
    </mtd-collapse-transition>
  </div>
</template>
<script>
export default {
  data() {
    return {
      expended: false,
    }
  },
  methods: {
    toggleExpended() {
      this.expended = !this.expended
    },
  },
}
</script>
<style lang='scss' scoped>
.demo-collapse-poem {
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  color: rgba(0, 0, 0, 0.9);
  margin-bottom: 12px;

  .mtdicon-down-thick {
    transition: transform .3s;
    transform: rotate(0deg);
    font-size: 16px;
    color: rgba(0, 0, 0, 0.5);
    margin-left: 4px;
  }

  &.expended {
    .mtdicon-down-thick {
      transform: rotate(180deg);
    }
  }
}
</style>

```

#### doc/fold.vue

```vue
<template>
  <div class="demo-full-width">
    <mtd-collapse v-model="activeValue7">
      <mtd-collapse-item title="春日" value="1">
        <mtd-collapse v-model="activeValue8">
          <mtd-collapse-item title="春日" value="1">
            胜日寻芳泗水滨，无边光景一时新。<br />
            等闲识得东风面，万紫千红总是春。
          </mtd-collapse-item>
          <mtd-collapse-item title="秋词" value="2">
            自古逢秋悲寂寥，我言秋日胜春朝。<br />
            晴空一鹤排云上，便引诗情到碧霄。
          </mtd-collapse-item>
        </mtd-collapse>

      </mtd-collapse-item>
      <mtd-collapse-item title="夏日山中" value="2">
        懒摇白羽扇，裸体青林中。<br />
        脱巾挂石壁，露顶洒松风。
      </mtd-collapse-item>
      <mtd-collapse-item title="秋词" value="3">
        自古逢秋悲寂寥，我言秋日胜春朝。<br />
        晴空一鹤排云上，便引诗情到碧霄。
      </mtd-collapse-item>
      <mtd-collapse-item value="4">
        <template #title>冬柳</template>
        柳汀斜对野人窗，零落衰条傍晓江。<br />
        正是霜风飘断处，寒鸥惊起一双双。
      </mtd-collapse-item>
    </mtd-collapse>
  </div>
</template>
<script>
export default {
  data() {
    return {
      activeValue7: '1',
      activeValue8: '',
    }
  },
}
</script>

```

#### doc/icon.vue

```vue
<template>
  <div style="text-align: left;" class="demo-full-width">
    <mtd-tabs v-model="activeName" type="text" size="small">
      <mtd-tab-pane label="线型箭头" value="Tab1">
        <mtd-collapse v-model="activeValue4">
          <mtd-collapse-item title="春日" value="1">
            一春略无十日晴，处处浮云将雨行。<br />
            野田春水碧于镜，人影渡傍鸥不惊。<br />
            桃花嫣然出篱笑，似开未开最有情。<br />
            茅茨烟暝客衣湿，破梦午鸡啼一声。
          </mtd-collapse-item>
          <mtd-collapse-item title="夏日山中" value="2">
            懒摇白羽扇，裸体青林中。<br />
            脱巾挂石壁，露顶洒松风。
          </mtd-collapse-item>
          <mtd-collapse-item title="秋词" value="3">
            自古逢秋悲寂寥，我言秋日胜春朝。<br />
            晴空一鹤排云上，便引诗情到碧霄。
          </mtd-collapse-item>
          <mtd-collapse-item value="4">
            <template #title>冬柳</template>
            柳汀斜对野人窗，零落衰条傍晓江。<br />
            正是霜风飘断处，寒鸥惊起一双双。
          </mtd-collapse-item>
        </mtd-collapse>

      </mtd-tab-pane>
      <mtd-tab-pane label="面型箭头" value="Tab2">
        <mtd-collapse v-model="activeValue4" triangle-arrow>
          <mtd-collapse-item title="春日" value="1">
            一春略无十日晴，处处浮云将雨行。<br />
            野田春水碧于镜，人影渡傍鸥不惊。<br />
            桃花嫣然出篱笑，似开未开最有情。<br />
            茅茨烟暝客衣湿，破梦午鸡啼一声。
          </mtd-collapse-item>
          <mtd-collapse-item title="夏日山中" value="2">
            懒摇白羽扇，裸体青林中。<br />
            脱巾挂石壁，露顶洒松风。
          </mtd-collapse-item>
          <mtd-collapse-item title="秋词" value="3">
            自古逢秋悲寂寥，我言秋日胜春朝。<br />
            晴空一鹤排云上，便引诗情到碧霄。
          </mtd-collapse-item>
          <mtd-collapse-item value="4">
            <template #title>冬柳</template>
            柳汀斜对野人窗，零落衰条傍晓江。<br />
            正是霜风飘断处，寒鸥惊起一双双。
          </mtd-collapse-item>
        </mtd-collapse>
      </mtd-tab-pane>
    </mtd-tabs>
  </div>
</template>
<script>
export default {
  data() {
    return {
      activeValue4: '',
      activeName: 'Tab1',
    }
  },
}
</script>

<style scoped>
.demo-full-width {
  padding: 20px 0;
}

::v-deep .demo-full-width .mtd-tabs-nav {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  border-bottom: 1px solid #edf0f7;
  padding: 1.5px 16px;
}
</style>

```

#### doc/loading.vue

```vue
<template>
  <div class="demo-full-width">
    <mtd-collapse v-model="activeValue6">
      <mtd-collapse-item title="春日" value="1">
        一春略无十日晴，处处浮云将雨行。<br />
        野田春水碧于镜，人影渡傍鸥不惊。<br />
        桃花嫣然出篱笑，似开未开最有情。<br />
        茅茨烟暝客衣湿，破梦午鸡啼一声。
      </mtd-collapse-item>
      <mtd-collapse-item title="夏日山中" value="2">
        懒摇白羽扇，裸体青林中。<br />
        脱巾挂石壁，露顶洒松风。
      </mtd-collapse-item>
      <mtd-collapse-item title="秋词" value="3" loading>
        秋词<br />
        自古逢秋悲寂寥，我言秋日胜春朝。<br />
        晴空一鹤排云上，便引诗情到碧霄。
      </mtd-collapse-item>
      <mtd-collapse-item value="4">
        <template #title>冬柳</template>
        柳汀斜对野人窗，零落衰条傍晓江。<br />
        正是霜风飘断处，寒鸥惊起一双双。
      </mtd-collapse-item>
    </mtd-collapse>
  </div>
</template>
<script>
export default {
  data() {
    return {
      activeValue6: '',
    }
  },
}
</script>

```

#### doc/multi.vue

```vue
<template>
  <div style="text-align: left;" class="demo-full-width">
    <mtd-tabs v-model="activeName" type="text" size="small">
      <mtd-tab-pane label="同时展开多个" value="Tab1">
        <mtd-collapse v-model="activeValue1">
          <mtd-collapse-item title="春日" value="1">
            一春略无十日晴，处处浮云将雨行。<br />
            野田春水碧于镜，人影渡傍鸥不惊。<br />
            桃花嫣然出篱笑，似开未开最有情。<br />
            茅茨烟暝客衣湿，破梦午鸡啼一声。
          </mtd-collapse-item>
          <mtd-collapse-item title="夏日山中" value="2">
            懒摇白羽扇，裸体青林中。<br />
            脱巾挂石壁，露顶洒松风。
          </mtd-collapse-item>
          <mtd-collapse-item title="秋词" value="3">
            自古逢秋悲寂寥，我言秋日胜春朝。<br />
            晴空一鹤排云上，便引诗情到碧霄。
          </mtd-collapse-item>
          <mtd-collapse-item value="4">
            <template #title>冬柳</template>
            柳汀斜对野人窗，零落衰条傍晓江。<br />
            正是霜风飘断处，寒鸥惊起一双双。
          </mtd-collapse-item>
        </mtd-collapse>

      </mtd-tab-pane>
      <mtd-tab-pane label="每次仅展开一个" value="Tab2">
        <mtd-collapse v-model="activeValue2">
          <mtd-collapse-item title="春日" value="1">
            一春略无十日晴，处处浮云将雨行。<br />
            野田春水碧于镜，人影渡傍鸥不惊。<br />
            桃花嫣然出篱笑，似开未开最有情。<br />
            茅茨烟暝客衣湿，破梦午鸡啼一声。
          </mtd-collapse-item>
          <mtd-collapse-item title="夏日山中" value="2">
            懒摇白羽扇，裸体青林中。<br />
            脱巾挂石壁，露顶洒松风。
          </mtd-collapse-item>
          <mtd-collapse-item title="秋词" value="3">
            自古逢秋悲寂寥，我言秋日胜春朝。<br />
            晴空一鹤排云上，便引诗情到碧霄。
          </mtd-collapse-item>
          <mtd-collapse-item value="4">
            <template #title>冬柳</template>
            柳汀斜对野人窗，零落衰条傍晓江。<br />
            正是霜风飘断处，寒鸥惊起一双双。
          </mtd-collapse-item>
        </mtd-collapse>
      </mtd-tab-pane>
    </mtd-tabs>
  </div>
</template>
<script>
export default {
  data() {
    return {
      activeValue1: [],
      activeValue2: '',
      activeName: 'Tab1',
    }
  },
}
</script>

<style scoped>
.demo-full-width {
  padding: 20px 0;
}

::v-deep .demo-full-width .mtd-tabs-nav {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  border-bottom: 1px solid #edf0f7;
  padding: 1.5px 16px;
}
</style>

```

#### doc/position.vue

```vue
<template>
  <div style="text-align: left;" class="demo-full-width">
    <mtd-tabs v-model="activeName" type="text" size="small">
      <mtd-tab-pane label="左侧" value="Tab1">
        <mtd-collapse v-model="activeValue4">
          <mtd-collapse-item title="春日" value="1">
            一春略无十日晴，处处浮云将雨行。<br />
            野田春水碧于镜，人影渡傍鸥不惊。<br />
            桃花嫣然出篱笑，似开未开最有情。<br />
            茅茨烟暝客衣湿，破梦午鸡啼一声。
          </mtd-collapse-item>
          <mtd-collapse-item title="夏日山中" value="2">
            懒摇白羽扇，裸体青林中。<br />
            脱巾挂石壁，露顶洒松风。
          </mtd-collapse-item>
          <mtd-collapse-item title="秋词" value="3">
            自古逢秋悲寂寥，我言秋日胜春朝。<br />
            晴空一鹤排云上，便引诗情到碧霄。
          </mtd-collapse-item>
          <mtd-collapse-item value="4">
            <template #title>冬柳</template>
            柳汀斜对野人窗，零落衰条傍晓江。<br />
            正是霜风飘断处，寒鸥惊起一双双。
          </mtd-collapse-item>
        </mtd-collapse>

      </mtd-tab-pane>
      <mtd-tab-pane label="右侧" value="Tab2">
        <mtd-collapse v-model="activeValue4" right-align-arrow>
          <mtd-collapse-item title="春日" value="1">
            一春略无十日晴，处处浮云将雨行。<br />
            野田春水碧于镜，人影渡傍鸥不惊。<br />
            桃花嫣然出篱笑，似开未开最有情。<br />
            茅茨烟暝客衣湿，破梦午鸡啼一声。
          </mtd-collapse-item>
          <mtd-collapse-item title="夏日山中" value="2">
            懒摇白羽扇，裸体青林中。<br />
            脱巾挂石壁，露顶洒松风。
          </mtd-collapse-item>
          <mtd-collapse-item title="秋词" value="3">
            自古逢秋悲寂寥，我言秋日胜春朝。<br />
            晴空一鹤排云上，便引诗情到碧霄。
          </mtd-collapse-item>
          <mtd-collapse-item value="4">
            <template #title>冬柳</template>
            柳汀斜对野人窗，零落衰条傍晓江。<br />
            正是霜风飘断处，寒鸥惊起一双双。
          </mtd-collapse-item>
        </mtd-collapse>
      </mtd-tab-pane>
    </mtd-tabs>
  </div>
</template>
<script>
export default {
  data() {
    return {
      activeValue4: '',
      activeName: 'Tab1',
    }
  },
}
</script>
  
<style scoped>
.demo-full-width {
  padding: 20px 0;
}

::v-deep .demo-full-width .mtd-tabs-nav {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  border-bottom: 1px solid #edf0f7;
  padding: 1.5px 16px;
}
</style>
  
```

#### doc/sample.vue

```vue
<template>
  <div class="demo-full-width">
    <p>简洁型</p>
    <mtd-collapse v-model="activeValue3" type="sample">
      <mtd-collapse-item title="春日" value="1">
        一春略无十日晴，处处浮云将雨行。<br />
        野田春水碧于镜，人影渡傍鸥不惊。<br />
        桃花嫣然出篱笑，似开未开最有情。<br />
        茅茨烟暝客衣湿，破梦午鸡啼一声。
      </mtd-collapse-item>
      <mtd-collapse-item title="夏日山中" value="2">
        懒摇白羽扇，裸体青林中。<br />
        脱巾挂石壁，露顶洒松风。
      </mtd-collapse-item>
      <mtd-collapse-item title="秋词" value="3">
        秋词<br />
        自古逢秋悲寂寥，我言秋日胜春朝。<br />
        晴空一鹤排云上，便引诗情到碧霄。
      </mtd-collapse-item>
      <mtd-collapse-item value="4">
        <template #title>冬柳</template>
        柳汀斜对野人窗，零落衰条傍晓江。<br />
        正是霜风飘断处，寒鸥惊起一双双。
      </mtd-collapse-item>
    </mtd-collapse>
  </div>
</template>
<script>
export default {
  data() {
    return {
      activeValue3: '1',
    }
  },
}
</script>

```

#### doc/title-solt.vue

```vue
<template>
    <div class="demo-full-width">
        <mtd-collapse v-model="activeValue7">
            <mtd-collapse-item title="春日" value="1">
                <template #title>
                    <div class="demo-title-content">
                        <span>春日</span>
                        <mtd-button @click.stop="handleClick" type="text-primary">更多</mtd-button>
                    </div>
                </template>
                一春略无十日晴，处处浮云将雨行。<br />
                野田春水碧于镜，人影渡傍鸥不惊。<br />
                桃花嫣然出篱笑，似开未开最有情。<br />
                茅茨烟暝客衣湿，破梦午鸡啼一声。
            </mtd-collapse-item>
            <mtd-collapse-item title="夏日山中" value="2">
                <template #title>
                    <div class="demo-title-content">
                        <span>夏日山中</span>
                        <mtd-button @click.stop="handleClick" type="text-primary">更多</mtd-button>
                    </div>
                </template>
                懒摇白羽扇，裸体青林中。<br />
                脱巾挂石壁，露顶洒松风。
            </mtd-collapse-item>
            <mtd-collapse-item title="秋词" value="3">
                <template #title>
                    <div class="demo-title-content">
                        <span>秋词</span>
                        <mtd-button @click.stop="handleClick" type="text-primary">更多</mtd-button>
                    </div>
                </template>
                自古逢秋悲寂寥，我言秋日胜春朝。<br />
                晴空一鹤排云上，便引诗情到碧霄。
            </mtd-collapse-item>
            <mtd-collapse-item value="4">
                <template #title>
                    <div class="demo-title-content">
                        <span>冬柳</span>
                        <mtd-button @click.stop="handleClick" type="text-primary">更多</mtd-button>
                    </div>
                </template>
                柳汀斜对野人窗，零落衰条傍晓江。<br />
                正是霜风飘断处，寒鸥惊起一双双。
            </mtd-collapse-item>
        </mtd-collapse>
    </div>
</template>
<script>
export default {
    data() {
        return {
            activeValue7: '',
        }
    },
    methods: {
        handleClick() {
            alert('点击更多')
        }
    }
}
</script>
<style scoped>
.demo-title-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}
</style>
  
```

#### index.md

```markdown
<style lang='scss'>
  .demo-collapse{
    .source{
      p{
        text-align: left;
      }
      pre{
        padding: 0px;
        margin: 0px;
      }
    }
  }
</style>
# 折叠面板 / Collapse
折叠面板是用于容纳可折叠/展开内容的容器。

## 互动演示
:::include(src="./demo/collapse-interactive.vue")
:::

## 代码示例
### 基础类型
:::include(src="./doc/area.vue")
:::

### 展开方式
将 value 值设置成数组可同时展开多个面板，也可设置为一次只能打开一个面板的手风琴式折叠面板。
:::include(src="./doc/multi.vue")
:::

### 可嵌套
面板抽屉内可嵌套多个子级折叠面板。
:::include(src="./doc/fold.vue")
:::

### 折叠按钮样式
:::include(src="./doc/icon.vue")
:::

### 折叠按钮位置
:::include(src="./doc/position.vue")
:::

### 独立折叠面板
用于展开或收起更多与之相关的内容。 使用 mtd-collapse-transition 组件能够实现下拉动画。
:::include(src="./doc/file.vue")
:::


### 标题带操作
父标题区域支持添加其他自定义操作，该场景下折叠按钮必须置于标题左侧。
:::include(src="./doc/title-solt.vue")
:::


## API
<api-doc name="Collapse" :doc="require('./api.json')"></api-doc>
<api-doc name="CollapseItem" :doc="require('../collapse-item/api.json')"></api-doc>

```

#### index.ts

```typescript
import Collapse from './collapse'
import { withInstall } from '@ss/mtd-adapter'
export default withInstall(Collapse)

```

#### types.ts

```typescript
import { Ref } from '@ss/mtd-adapter'
import { Emitter } from 'mitt'

export type CollapseType = '' | 'sample' | 'area';

export interface CollapseProvide {
  value: Ref<any>;
  rightAlignArrow: Ref<boolean>;
  triangleArrow: Ref<boolean>;
  emitter: Emitter<any>;
}

import Collapse from './index'

export type Collapse = InstanceType<typeof Collapse>

```

#### useCollapse.ts

```typescript
import { inject, computed, provide } from '@ss/mtd-adapter'
import { CollapseProvide } from './types'

export const collapseSymbol = 'mtui-vue/collapse'

export interface CollapseProps {
  value?: any,
  type?: string,
}

export const provideCollapse = (collapseProvide: CollapseProvide) => {
  provide<CollapseProvide>(collapseSymbol, collapseProvide)
}

export const useCollapse = (props: CollapseProps) => {
  const collapse = inject<CollapseProvide>(collapseSymbol) as CollapseProvide

  const active = computed(() => {
    const isArray = Array.isArray(collapse.value.value)
    return isArray
      ? collapse.value.value.indexOf(props.value) > -1
      : collapse.value.value === props.value
  })

  function emitItemClick() {
    collapse.emitter.emit('itemClick', [!active.value, props.value])
  }

  return {
    active,
    rightAlignArrow: collapse.rightAlignArrow,
    triangleArrow: collapse.triangleArrow,
    emitItemClick,
  }
}

```

