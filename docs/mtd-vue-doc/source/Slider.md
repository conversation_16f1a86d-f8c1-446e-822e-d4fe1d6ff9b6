## 源码解析

### 文件依赖

- button.tsx
- demo.md
- demo/slider-interactive.vue
- demo/usage.json
- doc/base.vue
- doc/demo/disabled.vue
- doc/demo/events.vue
- doc/demo/fixed-range.vue
- doc/demo/fixed-value.vue
- doc/demo/format-tooltip.vue
- doc/demo/gradients.vue
- doc/demo/marks.vue
- doc/demo/min-max.vue
- doc/demo/modelValue.vue
- doc/demo/no-shade.vue
- doc/demo/range.vue
- doc/demo/size.vue
- doc/demo/step.vue
- doc/demo/steps.vue
- doc/demo/thresholds.vue
- doc/demo/toFixed.vue
- doc/demo/tooltip-open.vue
- doc/demo/vertical.vue
- doc/discrete.vue
- doc/fixed-value.vue
- doc/fixedValue.vue
- doc/icon.vue
- doc/input.vue
- doc/marks.vue
- doc/no-shade.vue
- doc/range.vue
- doc/scale.vue
- doc/size.vue
- doc/status.vue
- doc/thresholds.vue
- doc/vertical.vue
- index.md
- index.ts
- range-button.tsx
- slider.tsx
- types.ts

### 源码内容

#### button.tsx

```tsx
import {
  computed,
  defineComponent,
  reactive,
  ref,
  toRefs,
  watch,
  useListeners,
} from '@ss/mtd-adapter'
import useConfig from '@hooks/config'
import MtdTooltip from '@components/tooltip'
import { ITooltip } from '@components/tooltip/types'

export default defineComponent({
  name: 'MtdSlider',
  components: {
    MtdTooltip,
  },
  inheritAttrs: true,
  props: {
    value: {
      type: Number,
      default: 0,
    },
    vertical: {
      type: Boolean,
      default: false,
    },
    type: {
      type: [String, Number],
    },
    formatTooltip: Function,
    getPercent: Function,
    tooltipOpen: Boolean,
  },
  emits: ['dragging', 'change', 'input', 'drag-end'],
  setup(props) {
    const config = useConfig()
    const prefix = computed(() => config.getPrefixCls('slider-button'))

    const state = reactive({
      tip: false,
      dragging: false,
      hover: false,
      startValue: undefined as number | undefined,
    })

    const tooltipRef = ref<ITooltip | null>(null)

    const resetListeners = useListeners({
      ['update:visible']: handleTooltipChange,
    })

    //@Methods
    function handleTooltipChange(v: boolean) {
      state.tip = v
    }

    //@Watch
    watch(() => props.value, () => {
      tooltipRef.value?.updatePopper()
    })

    return {
      ...toRefs(state),
      prefix, resetListeners,
      handleTooltipChange,
      tooltipRef,
    }
  },

  computed: {
    tooltipContent(): string {
      return this.formatTooltip
        ? this.formatTooltip(this.value)
        : this.value.toString()
    },
    placement(): string {
      return this.vertical ? 'right' : 'top'
    },
    tooltipVisible(): boolean {
      // 增加slider组件不展示tooltip属性
      if (!this.tooltipOpen) {
        return false
      }
      return this.dragging ? true : this.tip
    },
  },

  methods: {
    onMouseDown(e: TouchEvent | MouseEvent) {
      e.preventDefault()
      window.addEventListener('mousemove', this.onDragging)
      window.addEventListener('mouseup', this.onDragEnd)
    },
    onMouseOver(e: MouseEvent) {
      this.hover = true
      e.preventDefault()
    },
    onMouseLeave(e: MouseEvent) {
      this.hover = false
      e.preventDefault()
    },
    onDragging(e: MouseEvent) {
      if (!this.dragging) {
        this.startValue = this.value
      }
      this.dragging = true
      this.$emit('dragging', e, this.type)
      this.$emit('input')
    },
    onDragEnd(e: MouseEvent) {
      this.dragging = false
      this.$emit('drag-end', e, this.type)
      if (this.startValue !== this.value) {
        this.$emit('input')
        this.$emit('change')
      }
      window.removeEventListener('mousemove', this.onDragging)
      window.removeEventListener('mouseup', this.onDragEnd)
    },
  },
  render() {
    const {
      tooltipContent, placement, tooltipVisible, vertical, value,
      prefix, resetListeners, dragging, hover,
    } = this

    return <mtd-tooltip
      content={tooltipContent}
      placement={placement}
      visible={tooltipVisible}
      ref="tooltipRef"
      size="small"
      {...resetListeners}
    >
      <div
        class={{
          [prefix]: true,
          [prefix + '-hover']: dragging || hover,
        }}
        onMousedown={this.onMouseDown}
        onMouseover={this.onMouseOver}
        onMouseleave={this.onMouseLeave}
        onTouchstart={this.onMouseDown}
        onClick={(e: Event) => e.stopPropagation()}
        style={vertical ? { bottom: this.getPercent?.(value) } : { left: this.getPercent?.(value) }}
      />
    </mtd-tooltip >
  },
})

```

#### demo.md

```markdown
## 补充Demo
### min
:::include(src="./doc/demo/min-max.vue")
::: 

### modelValue
:::include(src="./doc/demo/modelValue.vue")
::: 


### disabled
:::include(src="./doc/demo/disabled.vue")
::: 


### step
:::include(src="./doc/demo/step.vue")
::: 

### steps
:::include(src="./doc/demo/steps.vue")
::: 

### thresholds
:::include(src="./doc/demo/thresholds.vue")
::: 

### fixed-value
:::include(src="./doc/demo/fixed-value.vue")
::: 

### format-tooltip
:::include(src="./doc/demo/format-tooltip.vue")
::: 

### vertical
:::include(src="./doc/demo/vertical.vue")
::: 

### size
:::include(src="./doc/demo/size.vue")
::: 

### range
:::include(src="./doc/demo/range.vue")
::: 


### marks
:::include(src="./doc/demo/marks.vue")
::: 

### fixed-range
:::include(src="./doc/demo/fixed-range.vue")
::: 

### no-shade
:::include(src="./doc/demo/no-shade.vue")
::: 


### gradients
:::include(src="./doc/demo/gradients.vue")
::: 


### tooltip-open
:::include(src="./doc/demo/tooltip-open.vue")
::: 

### events
:::include(src="./doc/demo/events.vue")
::: 

### toFixed
:::include(src="./doc/demo/toFixed.vue")
::: 



```

#### demo/slider-interactive.vue

```vue
<template>
  <mtd-doc-usage name='Slider' :apiJson="apiJson" :usageJson="usage">
    <template #Slider="{ configProps }">
      <div :class="configProps.vertical ? 'demo-box-vertical' : 'demo-box'">
        <mtd-slider v-bind="configProps" v-model="value" :size="configProps.size" />
      </div>
    </template>
  </mtd-doc-usage>
</template>
<script>
const usage = require('./usage.json')
const apiJson = require('../api.json')
export default {
  data() {
    return {
      usage,
      apiJson,
      value: 0,
    };
  },

};
</script>

<style scoped>
.demo-box-vertical {
  height: 300px;
}

.demo-box {
  width: 500px;
}
</style>
```

#### demo/usage.json

```json
{
    "componentName": "Slider",
    "desc": "滑块",
    "codeTemplate": "<mtd-slider v-bind='configProps'></mtd-slider>"
}
```

#### doc/base.vue

```vue
<template>
    <div class="demo-box" style="width:500px">
      <mtd-row type="flex" class="demo-slider-row" style="width: 100%">
        <mtd-col :span="16" >
          <mtd-slider v-model="value" @change="handleChange" />
        </mtd-col>
        <mtd-col :span="4" :offset="2">
          <mtd-input-number v-model="value" controls-position="right" :min="0" :max="100"/>
        </mtd-col>
      </mtd-row>
    </div>
</template>
<script>
export default {
  data () {
    return {
      value: 0,
    };
  },
  methods: {
    handleChange (v) {
      console.log(v);
    },
  },
};
</script>

<style lang="scss">

.demo-slider-row{

  .mtd-col{
    display: inline-flex;
    align-items: center;
    .mtd-slider{
      width: 100%;
    }
  }
}

</style>

```

#### doc/demo/disabled.vue

```vue
<template>
  <div class="demo-box" style="width: 600px">
    <div>
      是否开启禁用 <mtd-switch v-model="isDisabled" />
    </div>
    <br />
    <mtd-slider v-model="value" :disabled="isDisabled" />
  </div>
</template>
<script>
export default {
  data() {
    return {
      value: 20,
      isDisabled: false
    };
  },
  methods: {
  },
};
</script>
    
```

#### doc/demo/events.vue

```vue
<template>
  <div class="demo-box">
    <mtd-slider v-model="value2" @input="handleInput" @change="handleChange" />
    <br />
    <div>input 事件触发值{{ inputValue }}</div>
    <div>change 事件触发值{{ changeValue }}</div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      value2: 50,
      inputValue: '',
      changeValue: ''
    };
  },
  methods: {
    handleInput(val) {
      this.inputValue = val
    },
    handleChange(val) {
      this.changeValue = val
    }
  }
};
</script>

<style scoped>
.demo-box {
  width: 650px;
}
</style>
```

#### doc/demo/fixed-range.vue

```vue
<template>
  <div class="demo-box">
    <div>
      fixed-range: <mtd-switch v-model="fixedRange" />
    </div>
    <br />
    {{ fixedRange }}
    <mtd-slider v-model="value" range :fixed-range="fixedRange" />
  </div>
</template>
<script>
export default {
  data() {
    return {
      value: [10, 20],
      fixedRange: false
    };
  },
  methods: {

  },
};
</script>
<style scoped>
.demo-box {
  width: 600px
}
</style>
    
```

#### doc/demo/fixed-value.vue

```vue
<template>
  <div class="demo-box" style="width: 600px">
    <div>
      滑块初始值：<mtd-input-number v-model="fixedValue" :controls="false" :min="0" :max="100" />
      区间范围(设置min-max)：<mtd-input-number v-model="min" :controls="false" :min="0" :max="100" />-
      <mtd-input-number v-model="max" :controls="false" :min="0" :max="100" />
    </div>
    <br />
    <mtd-slider v-model="value" :max="max" :min="min" :fixed-value="fixedValue" />
  </div>
</template>
<script>
export default {
  data() {
    return {
      value: 20,
      fixedValue: 10,
      min: 0,
      max: 100
    };
  },
  methods: {
  },
};
</script>
    
```

#### doc/demo/format-tooltip.vue

```vue
<template>
  <div class="demo-box" style="width: 600px">
    <mtd-slider v-model="value" :format-tooltip="formatTooltip" />
  </div>
</template>
<script>
export default {
  data() {
    return {
      value: 20,
    };
  },
  methods: {
    formatTooltip(val) {
      return '当前值为：' + val
    }
  },
};
</script>
    
```

#### doc/demo/gradients.vue

```vue
<template>
  <div class="demo-box">
    <div>
      gradients: <mtd-switch v-model="gradients" />
    </div>
    <br />
    <mtd-slider v-model="value1" :thresholds="thresholds" :gradients="gradients" />
  </div>
</template>
<script>
export default {
  data() {
    return {
      value1: 50,
      gradients: false,
      thresholds: {
        "values": [25, 50, 75, 100],
        "colors": ["#FF4A47", "#FF9D00", "#00BA73", "#166FF7"]
      }
    };
  },
  methods: {
  }
};
</script>
  
<style scoped>
.demo-box {
  width: 650px;
}
</style>
```

#### doc/demo/marks.vue

```vue
<template>
  <div :class="vertical ? 'demo-box-vertical' : 'demo-box'">
    <div>
      是否垂直展示: <mtd-switch v-model="vertical" />
    </div>
    <br />
    <mtd-slider v-model="value" :vertical="vertical" :marks="marks" />
  </div>
</template>
<script>
export default {
  data() {
    return {
      value: 20,
      vertical: false,
      marks: { 0: '0°C', 25: '25°C', 50: '50°C', 75: '75°C', 100: '100°C' }
    };
  },
  methods: {

  },
};
</script>
<style scoped>
.demo-box-vertical {
  height: 300px
}


.demo-box {
  width: 600px
}
</style>
    
```

#### doc/demo/min-max.vue

```vue
<template>
  <div class="demo-box" style="width: 600px">

    <mtd-row type="flex" class="demo-slider-row" style="width: 100%" justify="center">
      <mtd-col :span="16">
        <mtd-slider v-model="value1" :min="min" :max="max" />
      </mtd-col>
      <mtd-col :span="6" :offset="2">
        MIN值 <mtd-input-number v-model="min" :controls="false" :min="0" :max="100" />
        MAX值 <mtd-input-number v-model="max" :controls="false" :min="0" />
      </mtd-col>
    </mtd-row>
  </div>
</template>
<script>
export default {
  data() {
    return {
      value1: 20,
      min: 0,
      max: undefined
    };
  },
  methods: {

  },
};
</script>
    
<style scoped lang="scss">
.demo-slider-row {

  .mtd-col {
    display: inline-flex;
    align-items: center;

    .mtd-slider {
      width: 100%;
    }
  }
}
</style>
  
```

#### doc/demo/modelValue.vue

```vue
<template>
  <div class="demo-box" style="width: 600px">
    <div>

      是否开启range <mtd-switch v-model="isRange" />
      modelValue <mtd-input-number v-if="!isRange" v-model="value" :controls="false" :min="0" :max="100" />
      <template v-else>
        modelValue <mtd-input-number v-model="value1[0]" :controls="false" :min="0" :max="100" />
        <span style="margin: 0 4px">—</span>
        <mtd-input-number v-model="value1[1]" :controls="false" :min="0" :max="100" />
      </template>
    </div>
    <br />
    <mtd-slider v-if="!isRange" v-model="value" />
    <mtd-slider v-else v-model="value1" range />
  </div>
</template>
<script>
export default {
  data() {
    return {
      value: 20,
      value1: [20, 20],
      isRange: false
    };
  },
  methods: {

  },
};
</script>
    
```

#### doc/demo/no-shade.vue

```vue
<template>
  <div class="demo-box">
    <div>
      是否开启range: <mtd-switch v-model="isRange" />
      no-shade: <mtd-switch v-model="noShade" />
    </div>
    <br />
    <mtd-slider v-model="value" :range="isRange" :no-shade="noShade" />
  </div>
</template>
<script>
export default {
  data() {
    return {
      value: 20,
      isRange: false,
      noShade: false
    };
  },
  methods: {

  },
};
</script>
<style scoped></style>
    
```

#### doc/demo/range.vue

```vue
<template>
  <div :class="vertical ? 'demo-box-vertical' : 'demo-box'">
    <div>
      是否开启range: <mtd-switch v-model="isRange" />
      是否垂直展示: <mtd-switch v-model="vertical" />
    </div>
    <br />
    <mtd-slider v-model="value" :range="isRange" :vertical="vertical" />
  </div>
</template>
<script>
export default {
  data() {
    return {
      value: 20,
      isRange: false,
      vertical: false
    };
  },
  methods: {

  },
};
</script>
<style scoped>
.demo-box-vertical {
  height: 300px
}


.demo-box {
  width: 600px
}
</style>
    
```

#### doc/demo/size.vue

```vue
<template>
	<div>
		<div>
			<mtd-radio-group v-model="size">
				<mtd-radio value="large">大</mtd-radio>
				<mtd-radio value="">默认</mtd-radio>
			</mtd-radio-group>

		</div>
		<br />
		<mtd-slider v-model="value" :size="size" />
	</div>
</template>
<script>
export default {
	data() {
		return {
			value: 20,
			size: ''
		};
	},
};
</script>
    
```

#### doc/demo/step.vue

```vue
<template>
  <div class="demo-box" style="width: 600px">
    <div>
      步长：<mtd-input-number v-model="step" :controls="false" :min="0" :max="100" />
      区间范围(设置min-max)：<mtd-input-number v-model="min" :controls="false" :min="0" :max="100" />-<mtd-input-number
        v-model="max" :controls="false" :min="0" :max="100" />
    </div>
    <br />
    <mtd-slider v-model="value" :step="step" :max="max" :min="min" />
  </div>
</template>
<script>
export default {
  data() {
    return {
      value: 20,
      step: 0,
      max: 100,
      min: 0
    };
  },
  methods: {
  },
};
</script>
    
```

#### doc/demo/steps.vue

```vue
<template>
  <div class="demo-box">
    <div>
      步长：<mtd-input-number v-model="step" :controls="false" :min="0" :max="100" />
      离散点(英文逗号分隔)：<mtd-input v-model="stepsStr" />
      区间范围(设置min-max)：<mtd-input-number v-model="min" :controls="false" :min="0" :max="100" />-<mtd-input-number
        v-model="max" :controls="false" :min="0" :max="100" />
    </div>
    <br />
    <mtd-slider v-model="value" :step="step" :max="max" :min="min" :steps="steps" />
  </div>
</template>
<script>
export default {
  data() {
    return {
      value: 20,
      step: 0,
      stepsStr: '0, 25, 50, 75, 100',
      max: 100,
      min: 0
    };
  },
  computed: {
    steps() {
      return this.stepsStr.split(',').map(i => +i)
    }
  },
  methods: {
  },
};
</script>
```

#### doc/demo/thresholds.vue

```vue
<template>
  <div class="demo-box">
    <mtd-textarea v-model="thresholdsValue" style="width: 400px" rows="6" @blur="blur" />
    <mtd-slider style="width: 240px;" v-model="value2" :thresholds="thresholds" />
  </div>
</template>
<script>
export default {
  data() {
    return {
      value1: 50,
      value2: 50,
      thresholdsValue: `{
        "values": [25, 50, 75, 100],
        "colors": ["#FF4A47", "#FF9D00", "#00BA73", "#166FF7"]
      }`,
      thresholds: {
        "values": [25, 50, 75, 100],
        "colors": ["#FF4A47", "#FF9D00", "#00BA73", "#166FF7"]
      }
    };
  },
  methods: {
    blur() {
      this.thresholds = JSON.parse(this.thresholdsValue)
    }
  }
};
</script>

<style scoped>
.demo-box {
  display: flex;
  align-items: center;
  width: 650px;
  justify-content: space-between;
}
</style>
```

#### doc/demo/toFixed.vue

```vue
<template>
  <div
    class="demo-box"
    style="width: 500px"
  >
    <!-- 基本使用 -->
    <mtd-row
      type="flex"
      class="demo-slider-row"
      style="width: 100%"
    >
      <mtd-col :span="16">
        <mtd-slider
          v-model="value"
          :min="0"
          :max="1"
          :step="0.1"
          :precision="1"
          @change="handleChange"
        />
      </mtd-col>
      <mtd-col
        :span="4"
        :offset="2"
      >
        <mtd-input-number
          v-model="value"
          controls-position="right"
          :min="0"
          :max="1"
          :step="0.1"
        />
      </mtd-col>
    </mtd-row>

    <!-- 范围选择 -->
    <mtd-row
      type="flex"
      class="demo-slider-row"
      style="width: 100%"
    >
      <mtd-col :span="16">
        <mtd-slider
          v-model="rangeValue"
          :min="0"
          :max="1"
          :step="0.01"
          :precision="2"
          :range="true"
          @change="handleChange"
        />
      </mtd-col>
      <mtd-col
        :span="8"
        :offset="2"
      >
        <mtd-input-number
          v-model="rangeValue[0]"
          :min="0"
          :max="1"
          :step="0.01"
          controls-position="right"
        />
        <mtd-input-number
          v-model="rangeValue[1]"
          :min="0"
          :max="1"
          :step="0.01"
          controls-position="right"
        />
      </mtd-col>
    </mtd-row>

    <!-- 垂直方向 -->
    <mtd-row
      type="flex"
      class="demo-slider-row"
      style="height: 300px"
    >
      <mtd-slider
        v-model="verticalValue"
        :min="0"
        :max="1"
        :step="0.1"
        :vertical="true"
        @change="handleChange"
        :precision="1"
      />
      <mtd-col
        :span="4"
        :offset="2"
      >
        <mtd-input-number
          v-model="verticalValue"
          :min="0"
          :max="1"
          :step="0.1"
          controls-position="right"
        />
      </mtd-col>
    </mtd-row>

    <!-- 设置步长 -->
    <mtd-row
      type="flex"
      class="demo-slider-row"
      style="width: 100%"
    >
      <mtd-col :span="16">
        <mtd-slider
          v-model="stepValue"
          :min="0"
          :max="1"
          :step="0.01"
          :precision="2"
          @change="handleChange"
        />
      </mtd-col>
      <mtd-col
        :span="4"
        :offset="2"
      >
        <mtd-input-number
          v-model="stepValue"
          controls-position="right"
          :min="0"
          :max="1"
          :step="0.01"
        />
      </mtd-col>
    </mtd-row>
  </div>
</template>
<script>
export default {
  data() {
    return {
      value: 0,
      rangeValue: [0.11, 0.98],
      verticalValue: 1,
      stepValue: 0.5,
    }
  },
  methods: {
    handleChange(v) {
      console.log(v)
    },
  },
}
</script>
<style lang="scss">
.demo-slider-row {
    margin-bottom: 20px;

    .mtd-col {
        display: inline-flex;
        align-items: center;

        .mtd-slider {
            width: 100%;
        }
    }
}
</style>

```

#### doc/demo/tooltip-open.vue

```vue
<template>
  <div class="demo-box">
    <div>
      tooltip-open: <mtd-switch v-model="tooltipOpen" />
    </div>
    <br />
    <mtd-slider v-model="value1" :tooltip-open="tooltipOpen" />
  </div>
</template>
<script>
export default {
  data() {
    return {
      value1: 50,
      tooltipOpen: false,
    };
  },
};
</script>
  
<style scoped>
.demo-box {
  width: 650px;
}
</style>
```

#### doc/demo/vertical.vue

```vue
<template>
  <div :class="vertical ? 'demo-box-vertical' : 'demo-box'">
    <div> 是否垂直展示 <mtd-switch v-model="vertical" /></div>
    <br />
    <mtd-slider v-model="value" :vertical="vertical" />
  </div>
</template>
<script>
export default {
  data() {
    return {
      value: 20,
      vertical: false
    };
  },
};
</script>
<style scoped>
.demo-box-vertical {
  height: 300px
}


.demo-box {
  width: 600px
}
</style>
    
```

#### doc/discrete.vue

```vue
<template>
  <div class="demo-box" style=" padding: 100px 0">
    <mtd-slider v-model="value" :step="10" style="margin-bottom: 64px;" />
    <mtd-slider v-model="value1" :step="10" hide-step-dots />
  </div>
</template>
<script>
export default {
  data() {
    return {
      value: 40,
      value1: 40,
    };
  },
};
</script>

```

#### doc/fixed-value.vue

```vue
<template>
  <div style="width: 600px; padding: 100px 0">
    <mtd-slider :fixed-value="50" v-model="value1" />
  </div>
</template>
<script>
export default {
  data() {
    return {
      value1: 60,
    };
  },
  methods: {

  },
};
</script>
    
```

#### doc/fixedValue.vue

```vue
<template>
  <div class="demo-box" style=" padding: 100px 0">
    <mtd-slider v-model="value" :fixed-value="fixedValue" :format-tooltip="formatTooltip" /><br>
  </div>
</template>
<script>
export default {
  data() {
    return {
      value: 20,
      fixedValue: 20,
    };
  },
  methods: {
    formatTooltip(v) {
      return `${v}%`;
    },
  },
};
</script>

```

#### doc/icon.vue

```vue
<template>
  <div class="slider-demo-box" style="width: 600px; padding: 100px 0">
    <mtd-icon name="mute-o" style="font-size: 20px;" />
    <div class="slider-demo-box-slider">
      <mtd-slider v-model="value1" />
    </div>
    <mtd-icon style="font-size: 20px;" name="volume2" />
  </div>
</template>
<script>
export default {
  data() {
    return {
      value1: 60,
    };
  },
  methods: {

  },
};
</script>
  
<style scoped lang="scss">
.slider-demo-box {
  display: flex;
  align-items: center;

  &-slider {
    flex: 12;
    padding: 0 16px;
  }
}
</style>
```

#### doc/input.vue

```vue
<template>
  <div class="demo-box" style="width: 600px;  padding: 100px 0">
    <div style="display: flex; align-items: center; margin-bottom: 36px;">
      <mtd-slider v-model="value1" style="flex: 1; margin-right: 20px;" />
      <div style="width: 130px;">
        <mtd-input-number v-model="value1" :controls="false" :min="0" :max="100" />
      </div>
    </div>
    <div style="display: flex; align-items: center;">
      <mtd-slider v-model="value2" range fixed-range style="flex: 1; margin-right: 20px;" />
      <div style="width: 130px;">
        <mtd-input-number v-model="value2[0]" :controls="false" :min="0" :max="100" />
        <span style="margin: 0 4px">—</span>
        <mtd-input-number v-model="value2[1]" :controls="false" :min="0" :max="100" />
      </div>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      value1: 20,
      value2: [10, 40],
    };
  },
  methods: {

  },
};
</script>
  
<style scoped lang="scss">
.demo-slider-row {

  .mtd-col {
    display: inline-flex;
    align-items: center;

    .mtd-slider {
      width: 100%;
    }
  }
}
</style>

```

#### doc/marks.vue

```vue
<template>
  <div class="demo-box" style="padding: 100px 0">
    <mtd-row type="flex" class="demo-slider-row" style="width: 100%">
      <mtd-col :span="3">
        <span>温度：</span>
      </mtd-col>
      <mtd-col :span="16">
        <mtd-slider :marks="marks" v-model="value" :formatTooltip="(val) => `${val} ℃`" />
      </mtd-col>
    </mtd-row>
  </div>
</template>
<script>
export default {
  data() {
    return {
      value: 37,
      marks: {
        0: '0°C',
        26: '26°C',
        37: '37°C',
        100: '100°C',
      },
    };
  },
};
</script>

```

#### doc/no-shade.vue

```vue
<template>
  <div class="demo-box" style=" padding: 100px 0">
    <div class="demo-slider">
      轨道无填充
      <mtd-slider v-model="value1" no-shade />
    </div>
    <br>
    <div class="demo-slider">
      轨道为渐变
      <mtd-slider v-model="value2" noShade :thresholds="thresholds2" gradients />
    </div>
    <br>
    <div class="demo-slider">
      轨道为色值
      <mtd-slider v-model="value3" noShade :thresholds="thresholds1" />
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      value1: 10,
      value2: 10,
      value3: 10,
      thresholds1: {
        values: [25, 50, 75, 100],
        colors: ['#62C0FF', '#FFCA54', '#FF9F5E', 'red'],
      },
      thresholds2: {
        values: [0, 100],
        colors: ['#000', '#fff'],
      },
    };
  },
};
</script>

```

#### doc/range.vue

```vue
<template>
  <div class="demo-box" style="width: 600px; padding: 100px 0">
    <div style="margin-bottom: 12px;">固定范围</div>
    <mtd-slider v-model="value2" range fixed-range />
    <br />
    <br />

    <div style="margin-bottom: 12px;">不固定范围</div>
    <mtd-slider v-model="value1" range />
  </div>
</template>
<script>
export default {
  data() {
    return {
      value1: [10, 30],
      value2: [10, 30],
    };
  },
  methods: {

  },
};
</script>

```

#### doc/scale.vue

```vue
<template>
  <div class="demo-box" style=" padding: 100px 0">
    <mtd-slider v-model="value1" :marks="marks1" />
    <br />
    <br />
    <mtd-slider v-model="value2" :step="25" :marks="marks2" />
  </div>
</template>
<script>
export default {
  data() {
    return {
      value1: 40,
      value2: 50,
      marks1: {
        0: '0',
        20: '20',
        40: '40',
        60: '60',
        80: '80',
        100: '100'
      },
      marks2: {
        0: '1分钟',
        25: '15分钟',
        50: '1小时',
        75: '3小时',
        100: '无限大'
      }

    };
  },
};
</script>
  
```

#### doc/size.vue

```vue
<template>
  <div class="demo-box" style=" padding: 100px 0">
    <div class="demo-slider" style="margin-bottom: 20px">
      <p style="margin-bottom: 12px;"> 默认尺寸</p>
      <mtd-slider v-model="value2" />
    </div>
    <br />
    <div class="demo-slider">
      <p style="margin-bottom: 12px;">大尺寸</p>
      <mtd-slider v-model="value" size="large" />
    </div>

  </div>
</template>
<script>
export default {
  data() {
    return {
      value: 10,
      value2: 10,
    };
  },
};
</script>

```

#### doc/status.vue

```vue
<template>
  <div class="demo-box" style=" padding: 100px 0">

    <div class="demo-slider">
      <mtd-slider disabled v-model="value" />
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      value: 30,
    };
  },
};
</script>

```

#### doc/thresholds.vue

```vue
<template>
  <div class="demo-box" style=" padding: 100px 0">
    <p style="margin-bottom: 12px;">轨道为渐变</p>
    <mtd-slider v-model="value1" :thresholds="thresholds1" no-shade gradients />
    <br />
    <p style="margin-bottom: 12px;">轨道为色值</p>
    <mtd-slider v-model="value2" :step="25" no-shade :thresholds="thresholds2" />
  </div>
</template>
<script>
export default {
  data() {
    return {
      value1: 50,
      value2: 50,
      thresholds1: {
        values: [25, 50, 75, 100],
        colors: ['#FF4A47', '#FF9D00', '#00BA73', '#166FF7'],
      },
      thresholds2: {
        values: [25, 50, 75, 100],
        colors: ['#166FF7', '#00BA73', '#FF9D00', '#FF4A47'],
      },
    };
  },
};
</script>

```

#### doc/vertical.vue

```vue
<template>
  <div class="demo-slider-vertical-box">
    <mtd-slider v-model="value" vertical />
    <mtd-slider v-model="value2" vertical range fixed-range />
  </div>
</template>
<script>
export default {
  data() {
    return {
      value: 20,
      value2: [20, 40],
    };
  },
};
</script>
<style scoped>
.demo-slider-vertical-box {
  display: flex;
  justify-content: space-between;
  width: 150px;
  height: 300px;
}
</style>

```

#### index.md

```markdown
<style>
  .demo-box {
    display: inline-block;
    width: 400px;
    text-align: left;
  }
</style>
# 滑块 / Slider
滑块（Slider）本质上是一种信息录入，是帮助用户在连续或间断的区间内，通过滑动来选择合适数值的控件。

## 互动演示
:::include(src="./demo/slider-interactive.vue")
:::

## 代码示例
### 尺寸
滑块提供默认尺寸和大尺寸两种样式。
:::include(src="./doc/size.vue")
:::
### 禁用
:::include(src="./doc/status.vue")
:::

### 垂直滑块
:::include(src="./doc/vertical.vue")
:::

### 范围滑块
:::include(src="./doc/range.vue")
:::

### 带输入框
:::include(src="./doc/input.vue")
:::

### 带图标
滑块可以在滑动条左右两端设定图标来反映数值的强度。
:::include(src="./doc/icon.vue")
:::

### 离散滑块
可自定义滑块步长，方便快速选择预定值。用于无需精准录入、或者仅允许特定数值录入的场景。
:::include(src="./doc/discrete.vue")
:::

### 阈值颜色
当滑块位置超过特定数值时，改变颜色进行提示，方便区分区域状态。
:::include(src="./doc/thresholds.vue")
:::


### 带初始值
如果该值表示偏移量，则可以将填充起点设置为表示原点。允许滑块填充从轨道内部开始。
:::include(src="./doc/fixed-value.vue")
:::

### 带刻度值
当滑块位置超过特定数值时，改变颜色进行提示，方便区分区域状态。
:::include(src="./doc/scale.vue")
:::

## API
<api-doc name="Slider" :doc="require('./api.json')"></api-doc>

```

#### index.ts

```typescript
import Slider from './slider'
import { withInstall } from '@ss/mtd-adapter'
export default withInstall(Slider)

```

#### range-button.tsx

```tsx
import {
  computed,
  defineComponent,
  PropType,
  reactive,
  ref,
  toRefs,
  watch,
  useListeners,
} from '@ss/mtd-adapter'
import useConfig from '@hooks/config'
import MtdTooltip from '@components/tooltip'
import { ITooltip } from '@components/tooltip/types'

export default defineComponent({
  name: 'MtdSliderRangeButton',
  components: {
    MtdTooltip,
  },
  inheritAttrs: true,
  props: {
    value: {
      type: Array as PropType<number[]>,
      default: () => [0, 10],
    },
    vertical: {
      type: Boolean,
      default: false,
    },
    formatTooltip: Function,
    getPercent: Function,
    bars: {
      type: Object,
      default: () => { },
    },
    thresholds: Boolean,
    tooltipOpen: Boolean,
  },
  emits: ['dragging', 'change', 'input', 'drag-start', 'drag-end'],
  setup(props) {
    const config = useConfig()
    const prefix = computed(() => config.getPrefixCls('slider-button'))
    const prefixSlider = computed(() => config.getPrefixCls('slider'))

    const state = reactive({
      hover: false,
      tip: false,
      dragging: false,
      startValue: undefined as number | undefined,
      endValue: undefined as number | undefined,
    })

    const tooltipRef = ref<ITooltip | null>(null)

    const resetListeners = useListeners({
      ['update:visible']: handleTooltipChange,
    })

    //@Methods
    function handleTooltipChange(v: boolean) {
      state.tip = v
    }

    //@Watch
    watch(() => props.value, () => {
      tooltipRef.value?.updatePopper()
    })

    return {
      ...toRefs(state),
      prefix, resetListeners, prefixSlider,
      handleTooltipChange,
      tooltipRef,
    }
  },

  computed: {
    tooltipContent(): string {
      return this.formatTooltip
        ? this.formatTooltip(this.value)
        : this.value.join(' ~ ')
    },
    placement(): string {
      return this.vertical ? 'right' : 'top'
    },
    tooltipVisible(): boolean {
      // 增加slider组件不展示tooltip属性
      if (!this.tooltipOpen) {
        return false
      }
      return this.dragging ? true : this.tip
    },
    barStyle(): any {
      const style: any = this.vertical
        ? { height: this.bars.w, bottom: this.bars.x }
        : { width: this.bars.w, left: this.bars.x }

      if (this.thresholds) {
        style['background-color'] = 'transparent'
        style.zIndex = 1
      }

      return style
    },

  },

  methods: {
    onMouseDown(e: MouseEvent | TouchEvent) {
      e.preventDefault()
      this.$emit('drag-start', e)
      window.addEventListener('mousemove', this.onDragging)
      window.addEventListener('mouseup', this.onDragEnd)
    },
    onMouseOver(e: MouseEvent) {
      this.hover = true
      e.preventDefault()
    },
    onMouseLeave(e: MouseEvent) {
      this.hover = false
      e.preventDefault()
    },
    onDragging(e: MouseEvent) {
      if (!this.dragging) {
        this.startValue = this.value[0]
        this.endValue = this.value[1]
      }
      this.dragging = true
      this.$emit('dragging', e)
      this.$emit('input')
    },
    onDragEnd(e: MouseEvent) {
      this.dragging = false
      this.$emit('drag-end', e)
      if (this.startValue !== this.value[0] || this.endValue !== this.value[1]) {
        this.$emit('input')
        this.$emit('change')
      }
      window.removeEventListener('mousemove', this.onDragging)
      window.removeEventListener('mouseup', this.onDragEnd)
    },
    getInversePercent(percent: string) {
      return `${100 - parseInt(percent)}%`
    },
  },
  render() {
    const {
      tooltipContent, placement, tooltipVisible, vertical, value,
      prefix, resetListeners, dragging, hover, prefixSlider, barStyle,
    } = this

    return <mtd-tooltip
      content={tooltipContent}
      placement={placement}
      visible={tooltipVisible}
      ref="tooltipRef"
      size="small"
      {...resetListeners}
    >
      <div
        class={prefix + '-range'}
        onMousedown={this.onMouseDown}
        onMouseover={this.onMouseOver}
        onMouseleave={this.onMouseLeave}
        onTouchstart={this.onMouseDown}
        onClick={(e: Event) => e.stopPropagation()}
        style={vertical
          ? {
            marginTop: `${this.bars.percentHeight * ((100 - value[1]) / 100)}px`,
            height: this.bars.w,
          }
          : {
            marginLeft: this.getPercent?.(value[0]),
            marginRight: this.getInversePercent(this.getPercent?.(value[1])),
          }
        }
      >
        <div
          class={{
            [prefix]: true,
            [prefix + '-hover']: dragging || hover,
          }}

          style={vertical ? { bottom: this.getPercent?.(value[0]) } : { left: this.getPercent?.(value[0]) }}
        />
        <div
          class={`${prefixSlider}-bar`}
          style={barStyle}
        />
        <div
          class={{
            [prefix]: true,
            [prefix + '-hover']: dragging || hover,
          }}
          style={vertical ? { bottom: this.getPercent?.(value[1]) } : { left: this.getPercent?.(value[1]) }}
        />
      </div>
    </mtd-tooltip >
  },
})
```

#### slider.tsx

```tsx
import {
  computed,
  defineComponent,
  PropType,
  reactive,
  ref,
  toRefs,
  watch,
  getSlotsInRender,
} from '@ss/mtd-adapter'
import useConfig from '@hooks/config'
import MtdSliderButton from './button'
import MtdSliderRangeButton from './range-button'
import {
  Marks,
  Thresholds,
  MarkText,
  FormatTooltip,
} from './types'
import { isArray } from '@utils/type'

import { useFormItem } from '@components/form-item/useFormItem'

export default defineComponent({
  name: 'MtdSlider',
  components: {
    MtdSliderButton,
    MtdSliderRangeButton,
  },
  inheritAttrs: true,
  model: {
    prop: 'modelValue',
    event: 'update:modelValue',
  },
  props: {
    size: {
      type: String,
    },
    marks: {
      type: Object as PropType<Marks>,
    },
    min: {
      type: Number,
      default: 0,
    },
    max: {
      type: Number,
      default: 100,
    },
    modelValue: {
      type: [Number, Array] as PropType<number | number[]>,
      default: 0,
    },
    range: {
      type: Boolean,
      default: false,
    },
    vertical: {
      type: Boolean,
      default: false,
    },
    gradients: {
      type: Boolean,
      default: false,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    step: Number,
    steps: Array as PropType<number[]>,
    thresholds: {
      type: Object as PropType<Thresholds>,
    },
    fixedValue: {
      type: Number,
    },
    fixedRange: {
      type: Boolean,
    },
    noShade: {
      type: Boolean,
      default: false,
    },
    tooltipOpen: {
      type: Boolean,
      default: true,
    },
    formatTooltip: Function as PropType<FormatTooltip>,
    hideStepDots: {
      type: Boolean,
      default: false,
    },
    precision: {
      type: Number,
    },
  },
  emits: ['change', 'after-change', 'input', 'update:modelValue'],

  setup(props, ctx) {
    const { emit } = ctx
    const config = useConfig()
    const prefix = computed(() => config.getPrefixCls('slider'))

    const sliderWrapperRef = ref<HTMLElement | null>(null)

    const state = reactive({
      dragging: false,
      tip: false,
      internalValue: [] as number[],
      rangeWidth: 0,
      dragStartValue: 0,
      tempInternalValue: [] as number[],
    })

    const formItemHook = useFormItem(props, ctx)
    const m_disabled = formItemHook.disabled
    const { m_size } = formItemHook
    // @Created

    // @Computed
    const minValue = computed(() => Math.max(Math.min(...state.internalValue), props.min))
    const maxValue = computed(() => Math.min(Math.max(...state.internalValue), props.max))
    const bars = computed(() => {
      const min = props.range ? minValue.value : props.min
      return {
        w: getPercent(maxValue.value - min, 0),
        x: props.range ? getPercent(minValue.value) : 0,
        percentHeight: sliderWrapperRef?.value?.getBoundingClientRect()?.height || 0,
      }
    })
    const dots = computed(() => {
      if ((!props.step || props.step < 0) && !props.steps) {
        return
      }

      let arr: number[] = props.step ? [] : props.steps!
      if (props.step) {
        let n = props.min + props.step
        while (n < props.max) {
          arr.push(n)
          n += props.step
        }
      }

      arr = arr.filter((v) => v > props.min && v < props.max)
      return arr
    })
    const markTexts = computed(() => {
      let arr: MarkText[]
      if (props.marks) {
        arr = Object.keys(props.marks).map((m: string) => ({
          value: Number(m),
          label: props.marks![m] || m,
        }))
      } else {
        let temp: number[] = [props.min, props.max]
        if (props.fixedValue) {
          temp = [...[props.fixedValue], ...temp]
        }

        if (dots.value) {
          temp = [...dots.value, ...temp]
        }

        if (props.thresholds) {
          temp = [...props.thresholds.values, ...temp]
        }

        arr = temp.map((v) => ({ value: v, label: `${v}` }))
      }

      arr = arr
        .sort((a, b) => a.value - b.value)
        .filter(
          (v, i) => (!arr[i + 1] || v.value !== arr[i + 1].value)
            && v.value >= props.min
            && v.value <= props.max,
        )
      return arr
    })
    const wholeLength = computed<number>(() => props.max - props.min)
    const curFixedValue = computed(() =>
      // 取最大值
      Math.max(props.min, props.fixedValue || 0))
    const wrapperClass = computed(() => [
      prefix.value,
      [
        {
          [`${prefix.value}-disabled`]: m_disabled.value,
          [`${prefix.value}-vertical`]: props.vertical,
          [`${prefix.value}-${m_size.value}`]: m_size.value,
          [`${prefix.value}-no-shade`]: props.noShade,
          [`${prefix.value}-marks`]: props.marks,
        },
      ],
    ])
    const trackStyle = computed(() => {
      const result: any = {}
      if (props.thresholds) {
        const { values, colors } = props.thresholds
        let linearGradientValue: string[] = []
        if (props.gradients) {
          linearGradientValue = values.map((val, index) => `${colors[index]} ${getPercent(val)}`)
        } else {
          const percentArr = values.map((val) => getPercent(val))
          percentArr.forEach((percent, index, arr) => {
            const color = colors[index]
            const percentStart = index > 0 ? arr[index - 1] : '0%'
            const percentEnd = percent
            linearGradientValue.push(`${color} ${percentStart}`)
            linearGradientValue.push(`${color} ${percentEnd}`)
          })
        }

        result.backgroundImage = `linear-gradient(to ${props.vertical ? 'top' : 'right'}, ${linearGradientValue})`
      }

      result[props.vertical ? 'height' : 'width'] = '100%'

      return result
    })

    // @Watch
    watch(() => props.modelValue, () => {
      syncValue()
    }, { immediate: true })
    watch(() => props.range, () => {
      syncValue()
    })

    watch(() => props.fixedRange, () => {
      if (props.fixedRange && props.range) {
        // fixedRange动态变更需要计算rangeWidth
        syncValue()
      }
    })

    // @Methods
    function syncValue() {
      state.internalValue = isArray(props.modelValue)
        ? [...props.modelValue]
        : [props.modelValue || 0]
      if (props.range) {
        state.internalValue.length = 2
        if (props.fixedRange && isArray(props.modelValue)) {
          state.rangeWidth = Math.abs(props.modelValue[1] - props.modelValue[0])
        }
      } else {
        state.internalValue.length = 1
      }

      for (let i = 0; i < state.internalValue.length; i++) {
        // can't use .map()
        const v = state.internalValue[i]
        state.internalValue[i] = !v && v !== 0 ? props.min : v
      }
    }

    function getPercent(value: number, m_b?: number) {
      const begin: number = m_b === undefined ? props.min : m_b
      let n = ((value - begin) * 100) / wholeLength.value
      if (n < 0) {
        n = 0
      } else if (n > 100) {
        n = 100
      }

      return `${n}%`
    }

    function handleClick(e: MouseEvent) {
      if (state.dragging || (props.range && props.fixedRange)) {
        return
      }

      props.vertical ? setPositionY(e) : setPositionX(e)
      handleInput(true)
    }

    function handleButtonDragging(e: MouseEvent, type?: number) {
      state.dragging = true
      props.vertical ? setPositionY(e, type) : setPositionX(e, type)
    }

    function handleDragRangeButtonStart(e: MouseEvent) {
      state.dragStartValue = -1 // 标志需要将这个值进行一次更新
      props.vertical ? setPositionY(e) : setPositionX(e)
    }

    function handleDragEnd() {
      state.dragging = false
      state.dragStartValue = 0
      state.tempInternalValue = []
    }

    function parseValue(value: number) {
      return parseInt(`${value}`, 10)
    }

    function setValue(offset: number, type?: number) {
      if (props.fixedRange && props.range) {
        setRangeValue(offset)
        return
      }

      const dot = getNearestStop(offset)
      const value = dot !== undefined
        ? dot
        : parseValue(props.min + offset * wholeLength.value)

      const index = type === undefined ? getNearestButtonType(value) : type

      state.internalValue = [...state.internalValue] // 触发响应式
      state.internalValue[index] = value
    }

    function setRangeValue(offset: number) {
      const dot = getNearestStop(offset)
      const value = dot !== undefined
        ? dot
        : parseValue(props.min + offset * wholeLength.value)

      if (state.dragStartValue === -1) {
        state.dragStartValue = value
        state.tempInternalValue = state.internalValue
      } else {
        const offsetValue = value - state.dragStartValue
        const newInternalValue_min = Math.min(
          Math.max(Math.min(...state.tempInternalValue) + offsetValue, props.min),
          props.max - state.rangeWidth,
        )
        const newInternalValue_max = newInternalValue_min + state.rangeWidth
        state.internalValue = [newInternalValue_min, newInternalValue_max]
      }
    }

    function setPositionX(e: MouseEvent, type?: number) {
      if (m_disabled.value) {
        return
      }

      const sliderWrapper = sliderWrapperRef.value
      const { left: start, right: end } = sliderWrapper!.getBoundingClientRect()
      const { clientWidth } = (sliderWrapper!)

      const fixedValue = start
        + Math.round(
          ((curFixedValue.value - props.min) / wholeLength.value) * clientWidth,
        )

      const current = e.clientX
      let offset
      if (current <= fixedValue) {
        offset = (curFixedValue.value - props.min) / wholeLength.value
      } else if (e.clientX >= end) {
        offset = 1
      } else {
        offset = (current - start) / clientWidth
      }

      setValue(offset, type)
    }

    function setPositionY(e: MouseEvent, type?: number) {
      if (m_disabled.value) {
        return
      }

      const sliderWrapper = sliderWrapperRef.value
      const { bottom: start, top: end } = sliderWrapper!.getBoundingClientRect()
      const { clientHeight } = (sliderWrapper!)

      const fixedValue = start
        - Math.round(
          ((curFixedValue.value - props.min) / wholeLength.value) * clientHeight,
        )
      const current = e.clientY
      let offset
      if (current >= fixedValue) {
        offset = (curFixedValue.value - props.min) / wholeLength.value
      } else if (current <= end) {
        offset = 1
      } else {
        offset = (start - current) / clientHeight
      }

      setValue(offset, type)
    }

    function getNearestButtonType(value: number) {
      const vs = state.internalValue.map((v, i) => ({
        index: i,
        diff: Math.abs(v - value),
      }))
      vs.sort((a, b) => a.diff - b.diff)
      return vs[0] ? vs[0].index : 1
    }

    function getNearestStop(offset: number) {
      if (!dots.value) {
        return undefined
      }

      const value = offset * wholeLength.value + props.min
      const orders = [props.min, props.max, value, ...dots.value].sort(
        (a, b) => a - b,
      )
      const index = orders.indexOf(value)
      const next = index === orders.length ? props.max : orders[index + 1]
      const pre = index === 0 ? props.min : orders[index - 1]
      return Math.abs(next - value) > Math.abs(pre - value) ? pre : next
    }

    function handleInput(isChange = false) {
      const value = state.internalValue.sort((a, b) => a - b).map(formatValue)
      if (
        value.some(
          (v, i) => v !== props.modelValue && v !== (props.modelValue as number[])[i],
        )
      ) {
        const v = props.range ? value : value[0]
        emit('update:modelValue', v)
        emit('input', v)

        if (isChange) {
          formItemHook.m_handleChange(v)
        }
      }

      syncValue()
    }

    function handleChange() {
      // handleInput(true)
      const value = state.internalValue.sort((a, b) => a - b).map(formatValue)
      const v = props.range ? value : value[0]
      formItemHook.m_handleChange(v)
      syncValue()
    }

    function formatValue(value: number) {
      // 兼容老逻辑，没有使用precision属性或者value为空则不进行精度处理
      if (!props?.precision || !value) {
        return value
      }

      return parseFloat(value.toFixed(props.precision))
    }

    const computedCollection = {
      wrapperClass, bars, dots, minValue, maxValue, wholeLength, markTexts, trackStyle,
    }
    const methodsCollection = {
      handleClick, getPercent, handleChange, handleButtonDragging, handleDragEnd, handleDragRangeButtonStart, handleInput, formatValue,
    }

    return {
      ...toRefs(state),
      ...computedCollection,
      ...methodsCollection,
      prefix,
      sliderWrapperRef,
    }
  },
  render() {
    const {
      prefix, wrapperClass, vertical, bars, thresholds, fixedValue, internalValue,
      formatTooltip, dots, range, minValue, maxValue, min, wholeLength, marks,
      markTexts, trackStyle, fixedRange, tooltipOpen, hideStepDots, formatValue,
    } = this

    return <div class={wrapperClass} ref="sliderWrapperRef" onClick={this.handleClick}>

      <div
        class={`${prefix}-bar-bgc`}
      />

      {(!thresholds)
        && <div
          class={`${prefix}-bar`}
          style={vertical
            ? { height: bars.w, bottom: bars.x }
            : { width: bars.w, left: bars.x }
          }
        />
      }

      {fixedValue
        && <div
          class={`${prefix}-fixed-bar`}
          style={vertical
            ? { height: this.getPercent(fixedValue) }
            : { width: this.getPercent(fixedValue) }
          } />
      }
      {!fixedRange
        ? internalValue.map((item, i) => <mtd-slider-button
          key={`button-${i}`}
          value={formatValue(item)}
          type={i}
          vertical={vertical}
          tooltip-open={tooltipOpen}
          format-tooltip={formatTooltip}
          get-percent={this.getPercent}
          onChange={this.handleChange}
          onInput={this.handleInput}
          onDragging={this.handleButtonDragging}
          onDrag-end={this.handleDragEnd}
        >
          {getSlotsInRender(this, 'handler')}
        </mtd-slider-button>)
        : <mtd-slider-range-button
          value={internalValue.map(formatValue)}
          vertical={vertical}
          bars={bars}
          thresholds={!!thresholds}
          tooltip-open={tooltipOpen}
          format-tooltip={formatTooltip}
          get-percent={this.getPercent}
          onChange={this.handleChange}
          onInput={this.handleInput}
          onDrag-start={this.handleDragRangeButtonStart}
          onDragging={this.handleButtonDragging}
          onDrag-end={this.handleDragEnd}
        >
          {getSlotsInRender(this, 'handler')}
        </mtd-slider-range-button>
      }

      {dots && !hideStepDots && dots.map((item) => <div
        key={`stop-${item}`}
        class={`${prefix}-stop`}
        style={vertical
          ? { bottom: this.getPercent(item) }
          : { left: this.getPercent(item) }
        } />)}

      {thresholds && <div class={`${prefix}-tracks`} >
        {range && <div
          class={`${prefix}-track ${prefix}-track-left`}
          style={
            vertical
              ? { height: `${((minValue - min) * 100) / wholeLength}%` }
              : { width: `${((minValue - min) * 100) / wholeLength}%` }
          }
        />}

        <div class={`${prefix}-track`} style={trackStyle} />

        <div
          class={`${prefix}-track ${prefix}-track-right`}
          style={
            vertical
              ? { height: `${100 - ((maxValue - min) * 100) / wholeLength}%` }
              : { width: `${100 - ((maxValue - min) * 100) / wholeLength}%` }
          }
        />
      </div>}

      <div
        class={`${prefix}-mark`}
        style={vertical ? { height: '100%' } : { width: '100%' }}
      >
        {marks && markTexts.map((item, index) => <span
          class={`${prefix}-mark-text`}
          key={`mark-${index}`}
          style={
            vertical
              ? { bottom: `${((item.value - min) * 100) / wholeLength}%` }
              : { left: `${((item.value - min) * 100) / wholeLength}%` }
          }
        >{item.label}</span>)}
      </div >

    </div >
  },
})

```

#### types.ts

```typescript
import { Component } from '@components/types/component'

export interface Marks {
  [key: string]: string;
}

export interface MarkText {
  value: number;
  label: string;
}
export interface Thresholds {
  values: number[];
  colors: string[];
}

export interface Track {
  color: string;
  value: number;
  w: string;
  x: string;
}

export interface FormatTooltip {
  (value: string): string;
}

export declare interface ISlider extends Component {
  min?: number;
  max?: number;
  value?: number;
  size?: string;
  range?: boolean;
  disabled?: boolean;
  steps?: number[];
  thresholds?: Thresholds;
  fixedValue?: number;
  marks?: Marks;
  formatTooltip?: FormatTooltip;
  vertical?: boolean;
}

declare const Slider: ISlider
export default Slider

```

