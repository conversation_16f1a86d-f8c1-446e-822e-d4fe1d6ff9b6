## 源码解析

### 文件依赖

- demo.md
- doc/base.vue
- doc/controls.vue
- doc/demo/focus-event.vue
- doc/demo/tooltips-options.vue
- doc/format.vue
- doc/no-controls.vue
- doc/other.vue
- doc/precision.vue
- doc/range.vue
- doc/size.vue
- doc/status.vue
- doc/step.vue
- doc/text-align.vue
- doc/unit.vue
- index.md
- index.ts
- input-number.tsx
- usage.json

### 源码内容

#### demo.md

```markdown
# 补充Demo


## focus-event
focus事件和blur事件正常触发，且正确传递event对象。
:::include(src="./doc/demo/focus-event.vue")
:::

## tooltips-options
:::include(src="./doc/demo/tooltips-options.vue"):::

```

#### doc/base.vue

```vue
<template>
  <mtd-input-number v-model="value" :max="10" :min="1" />
</template>

<script>
export default {
  data () {
    return {
      value: 3,
    };
  },
};
</script>

```

#### doc/controls.vue

```vue
<template>
    <div class="demo-flex">
        <div>
            <p>控制按钮在两侧</p>
            <mtd-input-number v-model="value[0]" :max="10" :min="1"/>
        </div>
        <div>
            <P>控制按钮在最右侧</P>
            <mtd-input-number  v-model="value[1]" :max="10" :min="1" controls-position="right"/>
        </div>
    </div>
</template>
<script>
export default{
    data(){
        return{ 
            value:[
                "1",
                "2"
            ],
        }
    }
}
</script>
```

#### doc/demo/focus-event.vue

```vue
<template>
    <div>
        <mtd-input-number v-model="value" :max="10" :min="1" :controls="false" @blur="handleBlur" @focus="handleFocus" />
    </div>
</template>
<script>
export default{
    data(){
        return{
            value:'10'
        }
    },
    methods:{
        handleBlur(e){
            console.log('blur',e.target.value)
        },
        handleFocus(e){
            console.log('focus',e.target.value)
        }
    }
}
</script>
```

#### doc/demo/tooltips-options.vue

```vue
<template>
    <div>
        <div style="margin-bottom: 10px;">
            <mtd-button @click="handleClick">修改tooltips-options</mtd-button>
        </div>
        <div>
            <mtd-input-number v-model="value" :max="10" :min="1" :tooltip-options="tooltipOptions"/>
        </div>
    </div>
</template>
<script>
export default {
  data() {
    return {
      value: '10',
      tooltipOptions: {
        content: '限制输入范围11111',
      }
    }
  },
  methods: {
    handleClick() {
      this.tooltipOptions = {
        content: '限制输入范围22222',
        placement: 'bottom'
      }
    }
  }
}
</script>

```

#### doc/format.vue

```vue
<template>
    <div class="demo-flex">
        <mtd-input-number v-model="value" :formatter="formatter1" :parser="parser1"/>
        <mtd-input-number v-model="value" :formatter="formatter2" :parser="parser2"/>
    </div>
</template>
<script>
export default{
    data(){
        return{
            value:'10'
        }
    },
    methods:{
        formatter1:num => `${num}%`,
        parser1:num => num.replace('%',''),
        formatter2:num => `¥${num}`,
        parser2:num => num.replace('¥','')
    }
}
</script>
```

#### doc/no-controls.vue

```vue
<template>
    <div>
        <mtd-input-number v-model="value" :max="10" :min="1" :controls="false" />
    </div>
</template>
<script>
export default{
    data(){
        return{
            value:'10'
        }
    }
}
</script>
```

#### doc/other.vue

```vue
<!-- <template>
    <div class="one">
        <p>单位样式1</p>
        <mtd-input-number v-model="value" :formatter="formatter1" :parser="parser1" /><br><br>
        <mtd-input-number v-model="value" :formatter="formatter2" :parser="parser2"  /><br><br>
        <mtd-input-number v-model="value" :formatter="formatter3" :parser="parser3" />
    </div>
    <div class="two">
        <p>单位样式2</p>
        <mtd-input-number v-model="value2" />个<br><br>
        <mtd-input-number v-model="value2" controls-position="right"  />元<br><br>
        <mtd-input-group style="width: 100px">
            <mtd-input-number v-model="value2" :controls="false" />
            <template #append>间</template>
        </mtd-input-group>
    </div>
</template>
<script>
export default{
    data(){
        return{
            value:1,
            value2:2,
            value3:3,
            
        }
    },
    methods:{
        formatter1:num => `${num}元`,
        parser1:num => num.replace('元',''),
        formatter2:num => `${num}%`,
        parser2:num => num.replace('%',''),
        formatter3:num => `¥${num}`,
        parser3:num => num.replace('¥',''),
       
        
    },
    } -->
<!-- </script> -->

```

#### doc/precision.vue

```vue
<template>
    <div class="demo-flex">
        <div>
            <mtd-input-number v-model="value" :precision="2" :step="0.1" :max="10" :min="1"/>
        </div>
    </div>
</template>
<script>
export default{
    data(){
        return{
            value:'1'
        }
    }
}
</script>
```

#### doc/range.vue

```vue
<template>
    <div class="demo-flex">
        <div>
            <mtd-input-number :modelValue="value" :max="10" :min="1" 
            allow-empty
            :illegal-clear="false"
            @change="changeNum" 
            @focus="handlefocus" 
            @blur="handleblur" 
            />
        </div>
    </div>
</template>
<script>
export default{
    data(){
        return{
            value:'10'
        }
    },
    methods:{
    changeNum(number){
      console.log("数值变化",number)
    },
    handlefocus(){
      console.log("聚焦状态")
    },
    handleblur(){
      console.log("完成失焦")
    }
    }
}
</script>
```

#### doc/size.vue

```vue
<template>
  <div class="demo-flex">
    <div>
      <p>小号</p>
      <mtd-input-number v-model="value1" size="small" />
    </div>
    <div>
      <p>中号</p>
      <mtd-input-number v-model="value2" />
    </div>
    <div>
      <p>大号</p>
      <mtd-input-number v-model="value3" size="large" />
    </div>
  </div>
</template>

<script>
export default {
  data () {
    return {
      value1: 10,
      value2: 10,
      value3: 10,
    };
  },
};
</script>

```

#### doc/status.vue

```vue
<template>
  <div class="demo-flex">
    <div>
      <mtd-input-number v-model="value1" />
      <br><br><br>
      <mtd-input-number v-model="value1" status="success"/>
      <div class="mtd-form-item-success-tip" style="text-align:left;margin-bottom: 12px;">校验通过</div>
      <mtd-input-number v-model="value1" status="success"/><br><br>
    </div>
    <div>
      <mtd-input-number v-model="value2" disabled />
      <br><br><br>
      <mtd-input-number v-model="value2" status="warning"/>
      <div class="mtd-form-item-warning-tip" style="text-align:left;margin-bottom: 12px;">校验警告</div>
      <mtd-input-number v-model="value2" status="warning"/><br><br>
    </div>
    <div>
        <mtd-input-number v-model="value3" readonly/>
        <br><br><br>
        <mtd-input-number v-model="value3" status="error" placeholder="0"/>
        <div class="mtd-form-item-error-tip" style="text-align:left;margin-bottom: 12px;">输入不可为空</div>
        <mtd-input-number v-model="value3" status="error" placeholder="0"/><br><br>
      
    </div>
  </div>
</template>

<script>
export default {
  data () {
    return {
      value1: 10,
      value2: 10,
      value3: 20,
      value4: '',
    };
  },
};
</script>

```

#### doc/step.vue

```vue
<template>
    <div class="demo-flex">
        <div>
            <mtd-input-number v-model="value" :step="2"/>
        </div>
    </div>
</template>
<script> 
export default{
    data(){
        return{
            value:'2'
        }
    }
}
</script>
```

#### doc/text-align.vue

```vue
<template>
  <div class="demo-flex">
    <div>
      <p>左对齐</p>
      <mtd-input-number v-model="value1" :style="{textAlign: 'left'}" /><br><br>
    </div>
    <div>
      <p>居中对齐</p>
      <mtd-input-number v-model="value2" /><br><br>
    </div>
    <div>
      <p>右对齐</p>
      <mtd-input-number v-model="value3" :style="{textAlign: 'right'}" /><br><br>
    </div>
  </div>
</template>

<script>
export default {
  data () {
    return {
      value1: 10,
      value2: 10,
      value3: 10,
    };
  },
};
</script>

```

#### doc/unit.vue

```vue
<template>
    <div class="demo-flex">
        <div>
            <p>外置</p>
            <mtd-input-number v-model="value" controls-position="right"  style="margin-right: 8px;"/>个
        </div>
        <div>
            <p>内置</p>
            <mtd-input-number style="width: 100px;" class="inputnumber" v-model="value"
            :precision="2"
            :controls="false"/>
        </div>
    </div>
</template>
<script>
export default {
  data() {
    return {
      value: '10'
    }
  },
}
</script>
<style>
.inputnumber::after{
    content: "CNY";
    line-height: 25px;
    font-size: 14px;
    text-align: center;
    padding: 3px 8px;
    color: rgba(17, 25, 37, 0.45);
}
</style>

```

#### index.md

```markdown
# 数字输入框 / InputNumber
数字输入框是用于便捷输入数值的组件，是文本输入框的一种衍生形式，又称步进器。


## 互动演示

<mtd-doc-usage name="input-number" :apiJson="require('./api.json')" :usageJson="require('./usage.json')" >
  <template slot="input-number" slot-scope="{ configProps }">
    <div>
     <mtd-input-number v-bind='configProps'>确认</mtd-input-number>
    </div>
  </template>
   <template #input-number="{ configProps }">
    <div>
     <mtd-input-number v-bind='configProps'/>
    </div>
  </template>
</mtd-doc-usage>


## 类型与用法


### 大小
数字输入框提供了小、中（默认）、大三种尺寸。
:::include(src="./doc/size.vue")
:::

### 控制按钮位置
:::include(src="./doc/controls.vue")
:::

### 无控制按钮
:::include(src="./doc/no-controls.vue")
:::

### 状态
数字输入框状态可分为默认状态、悬停状态、激活状态、禁用状态、只读状态和校验状态。其中校验状态又分为成功、警告和失败三种，可以使用不带提示信息和带提示信息两种形式来展示。
:::include(src="./doc/status.vue")
:::

### 对齐方式
:::include(src="./doc/text-align.vue")
:::

### 自定义格式
支持格式化输入的数值内容。
:::include(src="./doc/format.vue")
:::

### 自定义步数
:::include(src="./doc/step.vue")
:::

### 自定义精度

:::include(src="./doc/precision.vue")
:::

### 单位显示形式
外置单位不能用于控制按钮在两侧的数字输入框，内置单位均可通用。
:::include(src="./doc/unit.vue")
:::

### 限制范围
:::include(src="./doc/range.vue")
:::

## API
<api-doc name="InputNumber" :doc="require('./api.json')"></api-doc>
```

#### index.ts

```typescript
import InputNumber from './input-number'
import { withInstall } from '@ss/mtd-adapter'
export default withInstall(InputNumber)

```

#### input-number.tsx

```tsx
import {
  computed,
  defineComponent,
  PropType,
  reactive,
  ref,
  toRefs,
  watch, classNames, styles,
  useListeners,
  useResetAttrs,
  toProps,
} from '@ss/mtd-adapter'
import useConfig from '@hooks/config'
import { isKeyDown, isKeyUp } from '@components/picker/util'
import { InputStatus } from '@components/input/types'
import { useFormItem } from '@components/form-item/useFormItem'
import MtdIcon from '@components/icon'
import MtdTooltip from '@components/tooltip'

function isEmptyNullUndefined(val: unknown) {
  return val === '' || val === undefined || val === null
}

function isNumber(val: unknown) {
  return isEmptyNullUndefined(val) ? true : !isNaN(parseFloat(val as string))
}

function getPrecision(val?: number | string) {
  if (!val || isEmptyNullUndefined(val)) {
    return 0
  }

  const valString = val.toString()
  const dotPosition = valString.indexOf('.')
  let precision = 0
  if (dotPosition !== -1) {
    precision = valString.length - dotPosition - 1
  }

  return precision
}

export default defineComponent({
  name: 'MtdInputNumber',
  components: {
    MtdIcon,
    MtdTooltip,
  },
  inheritAttrs: false,
  model: {
    prop: 'modelValue',
    event: 'update:modelValue',
  },
  props: {
    modelValue: {
      type: [String, Number],
    },
    placeholder: String,
    name: String,
    size: String,
    disabled: {
      type: Boolean,
      default: false,
    },
    max: {
      type: Number,
      default: Infinity,
    },
    min: {
      type: Number,
      default: -Infinity,
    },
    step: {
      type: Number,
      default: 1,
    },
    formatter: {
      type: Function,
      default: (num: any) => num,
    },
    parser: {
      type: Function,
      default: (num: any) => num,
    },
    controls: {
      type: Boolean,
      default: true,
    },
    precision: {
      type: Number,
      validator: (val: number) => {
        return val === undefined || (val >= 0 && val === parseInt(val.toString(), 10))
      },
    },
    controlsPosition: String,
    status: {
      type: String as PropType<InputStatus>,
    },
    allowEmpty: Boolean,
    illegalClear: Boolean,
    readonly: {
      type: Boolean,
      default: false,
    },
    tooltipOptions: {
      type: Object,
      default: () => ({}),
    },
  },
  emits: ['input', 'change', 'update:modelValue', 'focus', 'blur'],
  setup(props, ctx) {
    const { emit } = ctx

    const config = useConfig()
    const rangeTooltipVisible = ref(false)

    const self = reactive({
      prefix: config.getPrefixCls('input-number'),
    })
    const currentValue = ref<number | string>('')
    const userInput = ref<number | string | null>(null)
    const isFocus = ref<boolean>(false)

    const formItemHook = useFormItem(props, ctx)

    const getValidValueByMinAndMax = (val: number | string) => {
      if (props.allowEmpty && val === '') {
        return ''
      }

      if (Number(val) < props.min) {
        val = props.illegalClear ? '' : props.min
      }

      if (Number(val) > props.max) {
        val = props.illegalClear ? '' : props.max
      }

      return val
    }

    watch(
      () => props.modelValue,
      (val) => {
        if (!isNumber(val)) {
          console.warn('MTD[InputNumber] value shoule be number')
          return
        }

        val = isEmptyNullUndefined(val)
          ? ''
          : getValidValueByMinAndMax(parseFloat(val as string))
        currentValue.value = val as string
        userInput.value = null
      },
      { immediate: true },
    )

    const numPrecision = computed(() => {
      const { modelValue, step, precision } = props
      const stepPrecision = getPrecision(step)
      if (precision !== undefined) {
        if (stepPrecision > precision) {
          console.warn(
            'precision should not be less than the decimal places of step',
          )
        }

        return precision
      } else {
        let inputValuePrecision = getPrecision(modelValue)
        if (
          modelValue !== undefined
          && currentValue
          && typeof currentValue === 'number'
        ) {
          inputValuePrecision = getPrecision(currentValue)
        }

        return Math.max(inputValuePrecision, stepPrecision)
      }
    })

    const commonCrease = (type: 'up' | 'down', val?: number | string) => {
      let newVal = val || 0
      if (!isNumber(newVal)) {
        return currentValue.value
      }

      if (
        isEmptyNullUndefined(val)
        && ((props.min && props.min !== -Infinity) || props.min === 0)
      ) {
        return props.min
      }

      newVal = parseFloat(newVal as string)
      const precisionFactor = Math.pow(10, numPrecision.value)
      const precisionNewVal = precisionFactor * newVal
      const precisionStep = precisionFactor * props.step
      const result = (type === 'up'
        ? precisionNewVal + precisionStep
        : precisionNewVal - precisionStep) / precisionFactor
      return Number(result.toFixed(numPrecision.value))
    }

    const increase = (val: number | string) => commonCrease('up', val)
    const decrease = (val: number | string) => commonCrease('down', val)
    const upHandleDisabled = computed(
      () => Number(increase(currentValue.value)) > props.max,
    )
    const downHandleDisabled = computed(
      () => Number(decrease(currentValue.value)) < props.min,
    )

    const precisionWrapper = (num: string | number) => {
      const { value: numPrecisionValue } = numPrecision
      if (numPrecisionValue !== undefined) {
        const numStr = String(num)
        if (numStr.includes('e') && Number(num) > 1) {
          // 如果是科学计数法，则不做处理，且对于小于1的小数保持原来的处理（不会使用科学计数法）
          return num
        }

        // 超出精度的舍弃，toFixed会四舍五入
        const pointArr = numStr.split('.')
        if (pointArr && Number(pointArr[1]) > numPrecisionValue) {
          num = pointArr[0] + '.' + pointArr[1].substr(0, numPrecisionValue)
        }

        num = Number(num).toFixed(numPrecisionValue)
      }

      return num
    }

    const displayValue = computed(() => {
      // 当用户在 focus 状态时，input 的 displayValue 根据用户的输入来计算
      if (isFocus.value) return userInput.value !== null ? userInput.value : currentValue.value
      if (userInput.value !== null) return (props.formatter as any)(userInput.value)
      let val = currentValue.value
      const parseFloatValue = parseFloat(val as string)
      if (!isNaN(parseFloatValue)) {
        val = precisionWrapper(parseFloatValue)
      }

      val = (props.formatter as any)(val)
      return val
    })

    const handleInput = ({ target }: { target: EventTarget | null }) => {
      let val = (target as HTMLInputElement).value.trim().replace(/。/g, '.')
      val = (props.parser as any)(val)
      userInput.value = val
    }

    const setCurrentValue = (val: string | number | null) => {
      if (props.precision !== undefined) {
        if (!(props.allowEmpty && val === '')) {
          val = precisionWrapper(val as string | number)
        }
      }

      val = getValidValueByMinAndMax(val as number)
      if (currentValue.value === val) {
        return
      }

      // toFixed返回的是字符串
      val = isEmptyNullUndefined(val) ? null : Number(val)
      emit('update:modelValue', val)
      emit('input', val)
      formItemHook.m_handleChange(val)
      userInput.value = null
      currentValue.value = val === null ? '' : val
    }

    const handleChange = () => {
      const val = userInput.value
      if (isNumber(val)) {
        // double parseFloag, -0 -> 0
        const newVal = val === '' ? '' : parseFloat(String(parseFloat(val as string)))
        setCurrentValue(newVal)
      }

      userInput.value = null
    }

    const handleKeydown = (e: KeyboardEvent) => {
      if (isKeyUp(e)) {
        e.preventDefault()
        handleUpStep(e)
      } else if (isKeyDown(e)) {
        e.preventDefault()
        handleDownStep(e)
      }
    }

    const m_disabled = formItemHook.disabled

    const handleUpStep = (e: KeyboardEvent | MouseEvent) => {
      e.preventDefault()
      if (props.readonly) return

      if (m_disabled.value || upHandleDisabled.value) return
      const val = userInput.value || currentValue.value
      const newVal = increase(val)
      setCurrentValue(newVal)
    }

    const handleDownStep = (e: KeyboardEvent | MouseEvent) => {
      e.preventDefault()
      if (props.readonly) return

      if (m_disabled.value || downHandleDisabled.value) return
      const val = userInput.value || currentValue.value
      const newVal = decrease(val)
      setCurrentValue(newVal)
    }


    const handleBlur = (e: FocusEvent) => {
      isFocus.value = false
      ctx.emit('blur', e)
    }

    const handleFocus = (e: FocusEvent) => {
      isFocus.value = true
      ctx.emit('focus', e)
    }

    const m_status = formItemHook.status
    const restAttrs = useResetAttrs(ctx.attrs)

    const resetListeners = useListeners({
      input: handleInput,
      keydown: handleKeydown,
      change: handleChange,
      blur: handleBlur,
      focus: handleFocus,
    }, ['update:modelValue', 'focus', 'blur', 'input', 'change'])

    const handleMouseEnter = (position) => () => {
      // hover左边并且到临界值
      if (position === 'left' && downHandleDisabled.value) {
        rangeTooltipVisible.value = true
        return
      }

      // hover右边并且到临界值
      if (position === 'right' && upHandleDisabled.value) {
        rangeTooltipVisible.value = true
        return
      }
    }

    const handleMouseLeave = () => {
      rangeTooltipVisible.value = false
    }

    return {
      ...toRefs(self),
      m_status,
      m_size: formItemHook.m_size,
      downHandleDisabled,
      upHandleDisabled,
      displayValue,
      m_disabled,
      handleUpStep,
      handleDownStep,
      resetListeners,
      restAttrs,
      handleMouseEnter,
      handleMouseLeave,
      rangeTooltipVisible,
    }
  },
  render() {
    const { m_disabled: disabled, controls, controlsPosition, m_size, downHandleDisabled, upHandleDisabled,
      placeholder, displayValue, prefix, m_status, resetListeners, rangeTooltipVisible, restAttrs, readonly, tooltipOptions,
    } = this

    return <div
      class={classNames(this, {
        [`${prefix}-wrapper`]: true,
        [`${prefix}-disabled`]: disabled,
        [`${prefix}-${m_status}`]: m_status,
        [`${prefix}-without-controls`]: !controls,
        [`${prefix}-controls-right`]: controlsPosition === 'right',
        [`${prefix}-${m_size}`]: !!m_size,
      })}
      style={styles(this)}
    >
      {controls && <span
        class={[{ [`${prefix}-handle-disabled`]: downHandleDisabled },
          `${prefix}-handle`, `${prefix}-handle-down`,
        ]}
        onClick={this.handleDownStep}
        onMouseenter={this.handleMouseEnter('left')}
        onMouseleave={this.handleMouseLeave}
      >
        <mtd-icon name={controlsPosition === 'right' ? 'down' : 'remove'} />
      </span>}
      {controls && <span
        class={[{ [`${prefix}-handle-disabled`]: upHandleDisabled },
          `${prefix}-handle`, `${prefix}-handle-up`,
        ]}
        onClick={this.handleUpStep}
        onMouseenter={this.handleMouseEnter('right')}
        onMouseleave={this.handleMouseLeave}
      >
        <mtd-icon name={controlsPosition === 'right' ? 'up' : 'add'} />
      </span >}

      <mtd-tooltip content="限制输入范围" placement="top" {...toProps(tooltipOptions)} modelValue={rangeTooltipVisible} >
        <input
          class={prefix}
          type="text"
          autocomplete="off"
          placeholder={placeholder}
          value={displayValue}
          disabled={disabled}
          readonly={readonly}
          ref="inputRef"
          {...resetListeners}
          {...restAttrs}
        />
      </mtd-tooltip>
    </div >
  },
})

```

#### usage.json

```json
{
    "componentName": "input-number",
    "desc": "数字输入框",
    "codeTemplate": "<mtd-input-number v-bind='configProps'></mtd-input-number>"
  }
```

