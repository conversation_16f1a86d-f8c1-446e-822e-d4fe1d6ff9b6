## 源码解析

### 文件依赖

- demo/rate-interactive.vue
- demo/usage.json
- doc/base.vue
- doc/color.vue
- doc/comment.vue
- doc/delete.vue
- doc/half.vue
- doc/other-icon.vue
- doc/other.vue
- doc/read-only.vue
- doc/size.vue
- doc/text.vue
- doc/tooltip.vue
- index.md
- index.ts
- rate.tsx
- types.ts

### 源码内容

#### demo/rate-interactive.vue

```vue
<template>
  <mtd-doc-usage name='Rate' :apiJson="apiJson" :usageJson="usage">
    <template #Rate="{ configProps }">
      <mtd-rate v-model="value" v-bind="configProps" />
    </template>
  </mtd-doc-usage>
</template>
<script>
const usage = require('./usage.json')
const apiJson = require('../api.json')
export default {
  data() {
    return {
      usage,
      apiJson,
      value: 3
    };
  },
};
</script>
  
```

#### demo/usage.json

```json
{
    "componentName": "Rate",
    "desc": "评分",
    "codeTemplate": "<mtd-rate v-bind='configProps'></mtd-rate>"
}
```

#### doc/base.vue

```vue
<template>
  <div class="rate-demo-content">
    <mtd-tabs v-model="activeName" type="text" size="small">
      <mtd-tab-pane label="图标型" value="Tab1">
        <mtd-rate v-model="value" @change="handleChange" />
      </mtd-tab-pane>
      <mtd-tab-pane label="数字型" value="Tab2">
        <mtd-rate v-model="value1" :count="9" @change="handleChange" type="number" />
      </mtd-tab-pane>

    </mtd-tabs>
  </div>
</template>
<script>
export default {
  data() {
    return {
      value: 3,
      value1: 5,
      activeName: 'Tab1'
    }
  },
  methods: {
    handleChange(val) {
      console.log('currentRateValue', val)
    },
  },
}
</script>
<style scoped>
.rate-demo-content {
  margin-top: 50px;
  padding: 100px 0;
}

::v-deep .rate-demo-content .mtd-tabs-nav {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  border-bottom: 1px solid #edf0f7;
  padding: 1.5px 16px;
}
</style>


```

#### doc/color.vue

```vue
<template>
  <div style="padding: 100px 0; width: 80%;">
    <mtd-row :gutter="20">
      <mtd-col :span="12">
        <div style="width: max-content; text-align: left;">
          <span class="demonstration" style="margin-bottom: 12px;">不区分不同分值</span>
          <mtd-rate v-model="value1" />
        </div>
      </mtd-col>
      <mtd-col :span="12">
        <div style="width: max-content; text-align: left;">
          <span class="demonstration" style="margin-bottom: 12px;">区分不同分值</span>
          <mtd-rate v-model="value2" :color="{ 0: '#FFCE32', 3: '#F7BA2A', 5: '#FF9900' }" />
        </div>
      </mtd-col>
    </mtd-row>
  </div>
</template>
<script>
export default {
  data() {
    return {
      value1: 1,
      value2: 3,
      value3: 4,
    };
  },
};
</script>

```

#### doc/comment.vue

```vue
<template>
  <div class="rate-demo-content">
    <mtd-tabs v-model="activeName" type="text" size="small">
      <mtd-tab-pane label="图标型" value="Tab1">
        <div style="display: flex;">
          <div style="margin-right: 96px;">
            <mtd-rate v-model="value" @change="handleChange" />
            <div class="rate-demo-info" style="margin-top: 6px;">
              <span>极不满意</span>
              <span>非常满意</span>
            </div>
          </div>
          <div>
            <div class="rate-demo-info" style="margin-bottom: 6px;">
              <span>极不满意</span>
              <span>非常满意</span>
            </div>
            <mtd-rate v-model="value" @change="handleChange" />
          </div>
        </div>

      </mtd-tab-pane>
      <mtd-tab-pane label="数字型" value="Tab2">
        <div style="display: flex;">
          <div style="margin-right: 96px;">
            <mtd-rate v-model="value" :count="10" type="number" @change="handleChange" />
            <div class="rate-demo-info" style="margin-top: 6px;">
              <span>极不满意</span>
              <span>非常满意</span>
            </div>
          </div>
          <div>
            <div class="rate-demo-info" style="margin-bottom: 6px;">
              <span>极不满意</span>
              <span>非常满意</span>
            </div>
            <mtd-rate v-model="value" :count="10" type="number" @change="handleChange" />
          </div>
        </div>
      </mtd-tab-pane>
    </mtd-tabs>
  </div>
</template>
<script>
export default {
  data() {
    return {
      value: 3,
      value1: 5,
      activeName: 'Tab1'
    }
  },
  methods: {
    handleChange(val) {
      console.log('currentRateValue', val)
    },
  },
}
</script>
<style scoped>
.rate-demo-content {
  margin-top: 50px;
  padding: 100px 0;
}

::v-deep .rate-demo-content .mtd-tabs-nav {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  border-bottom: 1px solid #edf0f7;
  padding: 1.5px 16px;
}

.rate-demo-info {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  font-weight: normal;
  line-height: 14px;
  color: rgba(17, 25, 37, 0.45);
  padding: 0 4px;
}
</style>


```

#### doc/delete.vue

```vue
<template>
  <div style="padding: 100px 0; display: flex;">
    <div style="margin-right: 128px; text-align: left;">
      <span style="color: rgba(0, 0, 0, 0.35);  margin-bottom: 12px; display: block; margin-left: 4px;">允许清除</span>
      <mtd-rate v-model="value8" allow-clear />
    </div>
    <div style="text-align: left;">
      <span style="color: rgba(0, 0, 0, 0.35); margin-bottom: 12px; display: block; margin-left: 4px;">不允许清除</span>
      <mtd-rate v-model="value9" :allow-clear="false" />
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      value8: 4,
      value9: 4,
    };
  },
};
</script>

```

#### doc/half.vue

```vue
<template>
  <div style="padding: 100px 0;">
    <mtd-rate v-model="value5" allow-half />
  </div>
</template>
<script>
export default {
  data() {
    return {
      value5: 3.5,
    };
  },
};
</script>

```

#### doc/other-icon.vue

```vue
<template>
  <div style="padding: 100px 0;">
    <mtd-rate v-model="value10" color="#f5483b" void-icon="like-fill" icon="like-fill" />
  </div>
</template>
<script>
export default {
  data() {
    return {
      value10: 1,
    };
  },
};
</script>

```

#### doc/other.vue

```vue
<template>
  <div style="padding: 100px 0;">
    <mtd-rate v-model="value10" :count="10" />
  </div>
</template>
<script>
export default {
  data() {
    return {
      value10: 6,
    };
  },
};
</script>

```

#### doc/read-only.vue

```vue
<template>
  <div style="padding: 100px 0;">
    <mtd-rate v-model="value6" disabled>
    </mtd-rate>
  </div>
</template>
<script>
export default {
  data() {
    return {
      value6: 5,
    }
  },
}
</script>

```

#### doc/size.vue

```vue
<template>
  <div class="rate-demo-content">
    <mtd-tabs v-model="activeName" type="text" size="small">
      <mtd-tab-pane label="图标型" value="Tab1">
        <div style="display: flex; justify-content:space-between;">
          <mtd-rate v-model="value" size="small" @change="handleChange" />
          <mtd-rate v-model="value" @change="handleChange" />
          <mtd-rate v-model="value" size="large" @change="handleChange" />
        </div>
      </mtd-tab-pane>
      <mtd-tab-pane label="数字型" value="Tab2">
        <div style="display: flex; justify-content:space-between;">
          <div>
            <mtd-rate v-model="value" size="small" type="number" @change="handleChange" />
            <div class="rate-demo-info">
              <span>极不满意</span>
              <span>非常满意</span>
            </div>
          </div>

          <div>
            <mtd-rate v-model="value" type="number" @change="handleChange" />
            <div class="rate-demo-info">
              <span>极不满意</span>
              <span>非常满意</span>
            </div>
          </div>
          <div>
            <mtd-rate v-model="value" size="large" type="number" @change="handleChange" />
            <div class="rate-demo-info">
              <span>极不满意</span>
              <span>非常满意</span>
            </div>
          </div>
        </div>
      </mtd-tab-pane>

    </mtd-tabs>
  </div>
</template>
<script>
export default {
  data() {
    return {
      value: 3,
      value1: 5,
      activeName: 'Tab1'
    }
  },
  methods: {
    handleChange(val) {
      console.log('currentRateValue', val)
    },
  },
}
</script>
<style scoped>
.rate-demo-content {
  margin-top: 50px;
  padding: 100px 0;
  width: 70%;
}

::v-deep .rate-demo-content .mtd-tabs-nav {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  border-bottom: 1px solid #edf0f7;
  padding: 1.5px 16px;
}

.rate-demo-info {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  font-weight: normal;
  line-height: 14px;
  color: rgba(17, 25, 37, 0.45);
  margin-top: 6px;
  padding: 0 2px;
}
</style>


```

#### doc/text.vue

```vue
<template>
  <div class="rate-demo-content">
    <mtd-tabs v-model="activeName" type="text" size="small">
      <mtd-tab-pane label="图标型" value="Tab1">
        <div style="display: flex; justify-content:space-between;">
          <mtd-rate v-model="value" :texts="['1分', '2分', '3分', '4分', '5分']" :tooltip-props="toolTipProps" />
          <mtd-rate text-position="top" v-model="value" :texts="['1分', '2分', '3分', '4分', '5分']"
            :tooltip-props="toolTipProps" />
          <mtd-rate text-position="bottom" v-model="value" :texts="['1分', '2分', '3分', '4分', '5分']"
            :tooltip-props="toolTipProps" />
        </div>
      </mtd-tab-pane>
      <mtd-tab-pane label="数字型" value="Tab2">
        <div style="display: flex; justify-content:space-between;">
          <mtd-rate v-model="value" type="number" :texts="['极差', '失望', '一般', '满意', '惊喜']" :tooltip-props="toolTipProps" />
          <mtd-rate text-position="top" type="number" v-model="value" :texts="['极差', '失望', '一般', '满意', '惊喜']"
            :tooltip-props="toolTipProps" />
          <mtd-rate text-position="bottom" type="number" v-model="value" :texts="['极差', '失望', '一般', '满意', '惊喜']"
            :tooltip-props="toolTipProps" />
        </div>
      </mtd-tab-pane>

    </mtd-tabs>
  </div>
</template>
<script>
export default {
  data() {
    return {
      value: 3,
      value1: 5,
      activeName: 'Tab1',
      toolTipProps: {
        modelValue: false
      }
    }
  },
  methods: {
    handleChange(val) {
      console.log('currentRateValue', val)
    },
  },
}
</script>
<style scoped>
.rate-demo-content {
  margin-top: 50px;
  padding: 100px 0;
  width: 70%;
}

::v-deep .rate-demo-content .mtd-tabs-nav {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  border-bottom: 1px solid #edf0f7;
  padding: 1.5px 16px;
}
</style>



```

#### doc/tooltip.vue

```vue
<template>
  <div style="text-align: left; padding: 100px 0;">
    <p style="color: rgba(0, 0, 0, 0.9); margin-left: 4px;">图标型</p>
    <mtd-rate v-model="value" @change="handleChange">
      <template #tooltip="props">
        <span>
          {{ ['极差', '失望', '一般', '优秀', '惊喜'][props.value - 1] }}
        </span>
      </template>
    </mtd-rate>
    <div>
      <br />
    </div>
    <p style="color: rgba(0, 0, 0, 0.9);">数字型</p>
    <mtd-rate v-model="value1" :count="10" type="number" @change="handleChange">
      <template #tooltip="props">
        <span>
          {{ ['极差', '失望', '一般', '优秀', '惊喜', '极差', '失望', '一般', '优秀', '惊喜'][props.value - 1] }}
        </span>
      </template>
    </mtd-rate>
  </div>
</template>
<script>
export default {
  data() {
    return {
      value: 3,
      value1: 0
    }
  },
  methods: {
    handleChange(val) {
      console.log('currentRateValue', val)
    },
  },
}
</script>

```

#### index.md

```markdown
<style lang='scss'>
.demo-rate {
  &.demo-block .source {
    .mtd-rate-text {
      display: inline-block;
      width: 50px;
    }
  }
  .demonstration {
    display: block;
    font-size: 14px;
    color: #636777;
    margin-top: 15px;
  }
}
</style>
# 评分 / Rate

## 互动演示
:::include(src="./demo/rate-interactive.vue")
:::

## 代码示例
### 基础类型
:::include(src="./doc/base.vue")
:::

### 尺寸
评分支持小、中、大三种尺寸。
:::include(src="./doc/size.vue")
:::

### 半选
:::include(src="./doc/half.vue")
:::


### 自定义颜色
评分支持通过 color 自定义颜色，另外也可以自定义不同分值时的颜色。
:::include(src="./doc/color.vue")
:::

### 评分后文案
支持配置评分后文案，即进行评分选择后实时反馈的辅助信息。
:::include(src="./doc/text.vue")
:::

### 文字提示
支持配置文字提示，当悬停在每个评分项时，提示当前选项所代表的含义，如“一般”、“优秀”等。
:::include(src="./doc/tooltip.vue")
:::

### 注释说明
支持配置头尾部的注释说明，通常用来指示评分项从左到右的程度变化，该注释说明支持可选配置在分值的底部或顶部。
:::include(src="./doc/comment.vue")
:::

### 自定义图标
:::include(src="./doc/other-icon.vue")
:::

### 自定义数量
:::include(src="./doc/other.vue")
:::

### 只读
可设置为只读状态，不可交互。
:::include(src="./doc/read-only.vue")
:::

### 清除
支持允许或者禁用清除。
:::include(src="./doc/delete.vue")
:::



## API
<api-doc name="Rate" :doc="require('./api.json')"></api-doc>

```

#### index.ts

```typescript
import Rate from './rate'
import { withInstall } from '@ss/mtd-adapter'
export default withInstall(Rate)

```

#### rate.tsx

```tsx
import {
  defineComponent,
  PropType,
  computed,
  reactive,
  toRefs,
  watch,
  getSlotsInRender,
  getScopedSlotsInRender,
  vSlots,
  Fragment,
  toProps,
} from '@ss/mtd-adapter'
import useConfig from '@hooks/config'
import { isString } from '@utils/type'
import { RateTextPosition, RateType } from './types'
import MtdTooltip from '@components/tooltip'
import { useFormItem } from '@components/form-item/useFormItem'
import { MTDUIComponentSize } from '@components/types'

function parseToKV(v: string | Record<string, any>, count?: number) {
  let map: any = v
  if (isString(v)) {
    map = {
      [count!]: v,
    }
  }
  const keys = Object.keys(map)
  return keys
    .sort((a: any, b: any) => a - b)
    .reduce((s, k) => {
      s[k] = map[k]
      return s
    }, {} as Record<string, any>)
}

function getValueFromMap(v: number, map: Record<string, any>) {
  const keys = Object.keys(map)
  const current = keys.find((key: any, index) => {
    const next: any = keys[index + 1]
    return (index === 0 && v < key) || (v >= key && (v < next || !next))
  })
  return map[current!]
}

export default defineComponent({
  name: 'MtdRate',
  components: {
    MtdTooltip,
  },
  inheritAttrs: true,
  model: {
    prop: 'modelValue',
    event: 'update:modelValue',
  },
  props: {
    modelValue: {
      type: Number,
      default: 0,
    },
    count: {
      type: Number,
      default: 5,
    },
    classes: Object,
    icon: {
      type: [String, Object] as PropType<string | Record<string, any>>,
      default: 'star-rate',
    },
    color: [String, Object] as PropType<string | Record<string, any>>,
    disabled: Boolean,
    voidColor: String,
    disabledVoidColor: String,
    voidIcon: String,
    disabledVoidIcon: String,
    allowHalf: {
      type: Boolean,
      default: false,
    },
    allowClear: {
      type: Boolean,
      default: false,
    },
    texts: {
      type: Array as PropType<string[]>,
    },
    textPosition: {
      type: String as PropType<RateTextPosition>,
      default: 'right',
    },
    tooltipProps: {
      type: Object,
      default: () => { },
    },
    type: {
      type: String as PropType<RateType>,
      default: 'icon',
      validator: (value: string) => ['icon', 'number'].includes(value),
    },
    size: {
      type: String as PropType<MTDUIComponentSize>,
      default: '',
    },
  },
  emits: ['change', 'input', 'update:modelValue'],
  setup(props, ctx) {
    const { emit } = ctx
    const config = useConfig()
    const prefix = computed(() => config.getPrefixCls('rate'))
    const iconPrefix = config.getIconCls

    const state = reactive<{
      currentValue: number,
      hoverIndex: number,
      pointerAtLeftHalf: boolean,
      isHover: boolean,
    }>({
      currentValue: props.modelValue,
      hoverIndex: -1,
      pointerAtLeftHalf: false,
      isHover: false,
    })

    // use form-item
    const formItemHook = useFormItem(props, ctx)
    const m_disabled = formItemHook.disabled
    const m_size = formItemHook.m_size

    // @Computed
    const rateClass = computed(() => {
      if (props.classes) {
        return getValueFromMap(props.modelValue, props.classes)
      }
      return undefined
    })
    const text = computed(() => {
      if (props.texts) {
        // todo: 需要和设计确认是否是向上取整
        return props.texts[Math.ceil(props.modelValue) - 1]
      }
      return ''
    })
    const valueInteger = computed(() => parseInt(props.modelValue as any))
    const valueDecimal = computed(() => props.modelValue * 100 - valueInteger.value * 100)
    const icons = computed<Record<string, any>>(() => parseToKV(props.icon))
    const activeIconClass = computed<string>(() => getValueFromMap(props.modelValue, icons.value))
    const voidIconClass = computed(() => {
      const icon = m_disabled.value ? props.disabledVoidIcon : props.voidIcon
      return icon || activeIconClass.value
    })
    const iconClasses = computed(() => {
      const result: string[] = []
      let i = 0
      let threshold = state.currentValue
      if (
        props.allowHalf &&
        state.currentValue !== Math.floor(state.currentValue)
      ) {
        threshold--
      }
      for (; i < threshold; i++) {
        result.push(activeIconClass.value)
      }
      for (; i < props.count; i++) {
        result.push(voidIconClass.value)
      }
      return result.map(iconName => iconPrefix(iconName))
    })
    const colors = computed(() => props.color ? parseToKV(props.color) : {})
    const activeColor = computed(() => getValueFromMap(state.currentValue, colors.value))
    const decimalStyle = computed(() => {
      let width = ''
      if (m_disabled.value) {
        width = `${valueDecimal.value}%`
      } else if (props.allowHalf) {
        width = '50%'
      }
      return {
        color: activeColor.value,
        width,
      }
    })

    // @Watch
    watch(() => props.modelValue, (val) => {
      state.currentValue = val
      state.pointerAtLeftHalf = (val !== Math.floor(val))
    }, { immediate: true })

    // @Methods
    function getIconStyle(item: number) {
      const voidColor = m_disabled.value ? props.disabledVoidColor : props.voidColor
      const color = item <= state.currentValue ? activeColor.value : voidColor
      return {
        color,
      }
    }

    function getNumberBackgroundColor(item: number) {
      const voidColor = m_disabled.value ? props.disabledVoidColor : props.voidColor
      const color = item <= state.currentValue ? activeColor.value : voidColor
      return {
        'background-color': color,
      }
    }

    function getItemClass(itemValue: number) {
      if (state.currentValue !== parseInt(state.currentValue as any)) {
        // 包含小数
        if (state.currentValue > itemValue) {
          return `${prefix.value}-item-full`
        } else if (state.currentValue + 1 > itemValue) {
          return `${prefix.value}-item-half`
        }
      } else if (state.currentValue >= itemValue) {
        return `${prefix.value}-item-full`
      }
      return ''
    }

    function showDecimalIcon(item: number) {
      const showWhenDisabled =
        m_disabled.value &&
        valueDecimal.value > 0 &&
        item - 1 < props.modelValue &&
        item > props.modelValue

      const showWhenAllowHalf =
        props.allowHalf &&
        state.pointerAtLeftHalf &&
        item - 0.5 <= state.currentValue &&
        item > state.currentValue
      return showWhenDisabled || showWhenAllowHalf
    }

    function setCurrentValue(value: number, event: MouseEvent) {
      state.isHover = true
      if (m_disabled.value) {
        return
      }
      if (props.allowHalf) {
        const target = event.currentTarget as HTMLElement
        state.pointerAtLeftHalf = event.offsetX * 2 <= target.clientWidth
        state.currentValue = state.pointerAtLeftHalf ? value - 0.5 : value
      } else {
        state.currentValue = value
      }
      state.hoverIndex = value
    }

    function resetCurrentValue() {
      if (m_disabled.value) {
        return
      }
      if (props.allowHalf) {
        state.pointerAtLeftHalf =
          props.modelValue !== Math.floor(props.modelValue)
      }
      state.currentValue = props.modelValue
      state.hoverIndex = -1
    }

    function selectValue(value: number) {
      if (m_disabled.value) {
        return
      }
      let nextValue = value
      if (props.allowHalf && state.pointerAtLeftHalf && props.type === 'icon') {
        nextValue = state.currentValue
      }
      if (props.allowClear && nextValue === props.modelValue) {
        nextValue = 0
      }
      resetCurrentValue()
      emit('update:modelValue', nextValue)
      emit('input', nextValue)
      formItemHook.m_handleChange(nextValue)
    }

    function handleMouseleave() {
      state.isHover = false
      resetCurrentValue()
    }


    const computedCollection = {
      rateClass, text, iconClasses, activeIconClass, decimalStyle, m_disabled, m_size,
    }
    const methodsCollection = {
      resetCurrentValue, getItemClass, setCurrentValue, selectValue, getIconStyle, showDecimalIcon, handleMouseleave, getNumberBackgroundColor,
    }

    return {
      prefix, iconPrefix,
      ...toRefs(state),
      ...computedCollection,
      ...methodsCollection,
    }
  },
  render() {
    const {
      prefix, rateClass, m_disabled: disabled, currentValue, text, count, tooltipProps, texts,
      iconClasses, hoverIndex, activeIconClass, decimalStyle, textPosition, type, m_size,
    } = this

    const $render = getScopedSlotsInRender(this, 'default')?.({ value: currentValue })


    return <div
      class={[{
        [`${prefix}-disabled`]: disabled,
        [`${prefix}-${m_size}`]: m_size,
        [`${prefix}-text-${textPosition}`]: true,
      }, rateClass, prefix]}
      aria-valuenow={currentValue}
      aria-valuetext={text}
      aria-valuemin={0}
      aria-valuemax={count}
      tabindex={0}
      onMouseleave={this.handleMouseleave}
    >

      <div class={`${prefix}-items-wrapper`}>
        {new Array(count).fill(0).map((num, index) => {

          const tooltipSlots = {
            content: () => getScopedSlotsInRender(this, 'tooltip')?.({ value: currentValue }) || (texts ? texts[item - 1] : ''),
          }
          const item = index + 1
          const mtdTooltipProps = toProps(tooltipProps)
          return <mtd-tooltip
            {...mtdTooltipProps}
            placement="top"
            class={`${prefix}-item-wrapper`}
            disabled={disabled || (!getScopedSlotsInRender(this, 'tooltip') && !texts)}
            close-delay={0}
            open-delay={0}
            key={item}
            {...vSlots(tooltipSlots)}
            v-slots={tooltipSlots}
          >
            <span class={`${prefix}-item-wrapper`} >
              <span
                class={[this.getItemClass(item), `${prefix}-item`, `${prefix}-item-${type}`]}
                onMousemove={(e: MouseEvent) => this.setCurrentValue(item, e)}
                onClick={this.selectValue.bind(this, item)}
              >
                {type === 'icon' ? <Fragment>
                  <i
                    class={[iconClasses[item - 1], `${prefix}-icon`, { hover: hoverIndex === item }]}
                    style={this.getIconStyle(item)}
                  />

                  <i
                    style={{
                      ...decimalStyle,
                      opacity: this.showDecimalIcon(item) ? 1 : 0,
                    }}
                    class={[`${prefix}-decimal`, this.iconPrefix(activeIconClass)]}
                  />
                </Fragment> :
                  <span
                    class={[`${prefix}-number`, { hover: hoverIndex === item }]}
                    style={this.getNumberBackgroundColor(item)}
                  >
                    {item}
                  </span>
                }
              </span>
            </span>
          </mtd-tooltip>
        })}
      </div>
      {($render || text) &&
        <span class={`${prefix}-text`}>
          {$render || text}
        </span>
      }
    </div>
  },
})


```

#### types.ts

```typescript
export type RateTextPosition = 'right' | 'top' | 'bottom'

import Rate from './index'

export type Rate = InstanceType<typeof Rate>

export type RateType = 'icon' | 'number'

```

