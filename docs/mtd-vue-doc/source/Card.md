## 源码解析

### 文件依赖

- card.tsx
- demo.md
- doc/base.vue
- doc/borderless.vue
- doc/demo/empty-title.vue
- doc/demo/long-title.vue
- doc/easy.vue
- doc/interaction.vue
- doc/loading.vue
- doc/picture.vue
- doc/shadow.vue
- index.md
- index.ts
- types.ts
- usage.json

### 源码内容

#### card.tsx

```tsx
import {
  defineComponent,
  computed,
  getSlotsInRender,
  PropType,
} from '@ss/mtd-adapter'
import { CardLodingType } from './types'
import useConfig from '@hooks/config'
import MtdLoading from '@components/loading'
import MtdSkeleton from '@components/skeleton'

export default defineComponent({
  name: 'MtdCard',
  components: {
    MtdLoading,
    MtdSkeleton,
  },
  inheritAttrs: true,
  props: {
    title: {
      type: String,
    },
    shadow: {
      type: String,
      default: 'always',
    },
    bodyClass: String,
    titleClass: String,
    border: {
      type: Boolean,
      default: true,
    },
    loading: Boolean,
    loadingType: {
      type: String as PropType<CardLodingType>,
      default: 'icon',
    },
  },
  emits: [],
  setup() {
    const config = useConfig()
    const prefix = computed(() => config.getPrefixCls('card'))
    return {
      prefix,
    }
  },
  render() {
    const {
      prefix, shadow, titleClass, bodyClass, title, border, loading, loadingType,
    } = this
    const isHeader = (getSlotsInRender(this, 'title') || title) || getSlotsInRender(this, 'extra')
    const renderLoading = () => {
      const customLoading = getSlotsInRender(this, 'loading')
      if (customLoading) return customLoading
      if (loadingType === 'skeleton') {
        return (
          <div class={`${prefix}-loading-wrapper`}>
            <MtdSkeleton active={true} />
          </div>
        )
      }
      return (
        <div class={`${prefix}-loading-wrapper`}>
          <MtdLoading loading={true} />
        </div>
      )

    }

    return <div class={[
      prefix,
      shadow ? `${prefix}-` + shadow + '-shadow' : `${prefix}-always-shadow`,
      !border ? `${prefix}-border-less` : '',
    ]}>
      <div class={`${prefix}-header`} style={{ border: isHeader ? '' : 'none' }}>
        {(getSlotsInRender(this, 'title') || title) && <div class={[`${prefix}-title`, titleClass]}>
          {getSlotsInRender(this, 'title') || title}
        </div>}
        {getSlotsInRender(this, 'extra') && <div class={`${prefix}-extra`}>
          {getSlotsInRender(this, 'extra')}
        </div>}
      </div>
      <div class={[`${prefix}-body`, bodyClass]}>
        {loading ? renderLoading() : getSlotsInRender(this)}
      </div>
    </div >
  },
})

```

#### demo.md

```markdown
# 卡片 / Card

## 补充 Demo

### empty-title
:::include(src="./doc/demo/empty-title.vue")
:::

### long-title
:::include(src="./doc/demo/long-title")
:::
```

#### doc/base.vue

```vue
<template>
  <div class="demo-box">
    <mtd-card class="card-box" shadow="hover">
      <template #title>
        <div>美团</div>
      </template>
      <div>美团的使命是“帮大家吃得更好，生活更好”，公司聚焦“零售 + 科技”战略，和广大商户与各类合作伙伴一起，努力为消费者提供品质生活，
        推动商品零售和服务零售在需求侧和供给侧的数字化转型。
      </div>
      <template #extra>
        <mtd-button type="text-primary">更多</mtd-button>
      </template>
    </mtd-card>
  </div>
</template>
<style lang="scss" scoped>
  .demo-box {
    display: inline-block;
    width: 328px;
    text-align: left;
    .card-box {
      // width: 100%;
      .title {
        line-height: 22px;
        font-weight: 500;
        font-size: 16px;
        margin-bottom: 8px;
      }
    }
  }
</style>

```

#### doc/borderless.vue

```vue
<template>
  <div class="demo-source">
    <div class="demo-box">
      <mtd-card title="美团" shadow="hover" :border="false">
        <template #default>
          <div>美团的使命是“帮大家吃得更好，生活更好”，公司聚焦“零售 + 科技”战略，和广大商户与各类合作伙伴一起，努力为消费者提供品质生活，
            推动商品零售和服务零售在需求侧和供给侧的数字化转型。
          </div>
        </template>
        <template #extra>
          <mtd-button type="text-primary">更多</mtd-button>
        </template>
      </mtd-card>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.demo-source {
  background-color: rgba(17, 25, 37, 0.05);
  width: 100%;
  height: 100%;

  .demo-box {
    display: inline-block;
    width: 400px;
    text-align: left;

    .card-box {
      .title {
        line-height: 22px;
        font-weight: 500;
        font-size: 16px;
        margin-bottom: 8px;
      }
    }
  }
}
</style>
```

#### doc/demo/empty-title.vue

```vue
<template>
    <div class="demo-box">
      <mtd-card class="card-box" shadow="hover" title="">
        <div>
          公司聚焦“零售 + 科技”战略，和广大商户与各类合作伙伴一起，努力为消费者提供品质生活， 推动商品零售和服务零售在需求侧和供给侧的数字化转型。
        </div>
      </mtd-card> 
    </div>
  </template>
  
  <style lang="scss" scoped>
    .demo-box {
      display: inline-block;
      width: 400px;
      text-align: left;
      .card-box {
        .title {
          line-height: 22px;
          font-weight: 500;
          font-size: 16px;
          margin-bottom: 8px;
        }
      }
    }
  </style>
  
```

#### doc/demo/long-title.vue

```vue
<template>
    <div class="demo-box">
      <mtd-card class="card-box" shadow="hover" title="这是一个稍微长一点的长字符串的标题哦～～～">
        <div>
          公司聚焦“零售 + 科技”战略，和广大商户与各类合作伙伴一起，努力为消费者提供品质生活， 推动商品零售和服务零售在需求侧和供给侧的数字化转型。
        </div>
      </mtd-card> 
    </div>
  </template>
  
  <style lang="scss" scoped>
    .demo-box {
      display: inline-block;
      width: 400px;
      text-align: left;
      .card-box {
        .title {
          line-height: 22px;
          font-weight: 500;
          font-size: 16px;
          margin-bottom: 8px;
        }
      }
    }
  </style>
```

#### doc/easy.vue

```vue
<template>
  <div class="demo-box">
    <mtd-card shadow="hover">
      <div class="top">
        <img class="photo" src="../doc/head1.png">
        <div class="text">帮大家吃的更好，生活更好</div>
      </div>
      <div class="content">公司聚焦“零售 + 科技”战略，和广大商户与各类合作伙伴一起，努力为消费者提供品质生活，
        推动商品零售和服务零售在需求侧和供给侧的数字化转型。
      </div>
      <div class="pos-right">
        <mtd-button class="btn" type="text-primary">更多</mtd-button>
      </div>
    </mtd-card>
  </div>
</template>
<style lang="scss" scoped>
.demo-box {
  display: inline-block;
  width: 400px;
  text-align: left;

  .card-box {
    .title {
      line-height: 22px;
      font-weight: 500;
      font-size: 16px;
      margin-bottom: 8px;
    }
  }

  .top {
    display: flex;
    gap: 8px;
    left: 36px;
    top: 3px;
    align-items: center;

    .text {
      font-size: 14px;
      font-weight: 500;
      line-height: 22px;
    }

    .photo {
      width: 28px;
      height: 28px;
    }
  }

  .content {
    margin-top: 8px;
    margin-bottom: 8px;
  }

  .pos-right {
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
    align-items: center;
    .btn{
      font-weight: normal;
    }
  }
}
</style>

```

#### doc/interaction.vue

```vue
<template>
  <div class="demo-box">
    <mtd-card class="card-box" body-class="card-body-cover">
      <div class="card-info">
        <img class="card-info-pic" src="https://tse2-mm.cn.bing.net/th/id/OIP-C.QfPn3DuAJprYvy2NIcp_MgHaHa?w=170&h=180&c=7&r=0&o=5&dpr=2&pid=1.7">
        <div class="card-info-title">油焖大虾</div>
        <div class="card-info-desc">这是一道历史悠久的名菜，鲜香甜咸四种味道相辅相成，回味无穷。</div>
      </div>
      <div class="card-info-bottom" :style="'padding: 20px'">
        <span>为美食打分</span>
        <!-- <span>评分组件</span> -->
      </div>
    </mtd-card>
  </div>
</template>

<style lang="scss">
  .card-body-cover{
    padding: 0px;
  }
  .card-box {
    width: 400px;
    text-align: left;
    .card-info {
      border-bottom: 1px solid #EEEEEE;
      &::after {
        content: "";
        clear: both;
        display: table;
      }
      &-pic {
        float: left;
        width: 100px;
        height: 100px;
        margin-right: 20px;
      }
      &-title {
        font-weight: 500;
        padding: 10px 0 4px 0;
        font-size: 16px;
        color: #666666;
      }
      &-desc {
        font-size: 14px;
        color: #666666;
      }
    }
    .card-info-bottom {

    }
  }
</style>

```

#### doc/loading.vue

```vue
<template>
  <div class="demo-loading-box">
    <div style="margin-right: 48px;">
      <div class="text-loading">常规加载</div>
      <mtd-card class="card-box" title="美团" shadow="hover" loading>
        <template #loading>
          <div class="loading-wrapper">
            <mtd-loading />
          </div>
        </template>
      </mtd-card>
    </div>

    <div>
      <div class="text-skeleton">骨架屏加载</div>
      <mtd-card class="card-box" title="美团" shadow="hover" loading loading-type="skeleton">
      </mtd-card>
    </div>

  </div>
</template>

<style lang="scss" scoped>
.text-loading,
.text-skeleton {
  color: rgba(17, 25, 37, 0.65);
  text-align: center;
  margin-bottom: 12px;
}

.demo-loading-box {
  display: flex;

  .card-box {
    width: 328px;
    text-align: left;

    .title {
      line-height: 22px;
      font-weight: 500;
      font-size: 16px;
      margin-bottom: 8px;
    }

    .loading-wrapper {
      height: 110px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}
</style>

```

#### doc/picture.vue

```vue
<template>
  <div class="demo-picture-box">
    <mtd-card class="card-box preview" body-class="card-body-cover" shadow="never">
      <div class="textdiv">
        <div class="div-text">图片尺寸自定义</div>
      </div>
      <div class="card-preview-bottom">
        <div class="text-title">卡片标题</div>
        <div class="content">
          卡片内容解释
        </div>
        <div class="preview-footer">
          <div class="photo-container">
            <img class="photo" src="../doc/head4.png">
            <img class="photo photo2" src="../doc/head3.png">
            <img class="photo photo3" src="../doc/head2.png">
          </div>
          <div class="icon-container">
            <mtd-icon name="forward-o" />
          </div>
        </div>

      </div>
    </mtd-card>
  </div>
</template>

<style lang="scss" scoped>
.demo-picture-box {
  display: inline-block;
  width: 400px;
  text-align: left;
  padding: 0;

  .card-box {
    padding: 0;

    .title {
      line-height: 22px;
      font-weight: 500;
      font-size: 16px;
      margin-bottom: 8px;
    }
  }
}

.preview-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 20px;

  .photo-container {
    .photo {
      width: 28px;
      height: 28px;
    }

    .photo2 {
      position: relative;
      left: -4px;
      z-index: 2;
    }

    .photo3 {
      position: relative;
      left: -8px;
      z-index: 3;
    }
  }

  .icon-container {
    font-size: 24px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }

}


.textdiv {
  background-color: #F3F3F4;
  width: 400px;
  height: 243.64px;

  .div-text {
    display: flex;
    justify-content: center;
    /* 水平居中 */
    align-items: center;
    /* 垂直居中 */
    height: 100%;
    /* 容器高度 */
    width: 100%;
    color: #AFB2B6;
    margin-bottom: 4px;
  }
}

::v-deep .card-body-cover {
  padding: 0px;
}

.card-box {
  text-align: left;

  &.preview {
    width: 400px;
  }

  .card-preview-bottom {
    padding: 24px;

    .text-title {
      font-size: 22px;
      font-weight: 600;
      line-height: 32px;
    }

    .content {
      line-height: 24px;
      font-size: 16px;
      margin-top: 4px;
      color: #646971;
    }
  }
}
</style>

```

#### doc/shadow.vue

```vue
<template>
  <mtd-card class="card-box" title="悬浮显示阴影示例" shadow="hover">
    <div>美团的使命是“帮大家吃得更好，生活更好”，公司聚焦“零售 + 科技”战略，和广大商户与各类合作伙伴一起，努力为消费者提供品质生活，
      推动商品零售和服务零售在需求侧和供给侧的数字化转型。
    </div>
    <template #extra>
      <mtd-button type="text-primary">更多</mtd-button>
    </template>
  </mtd-card>


  <mtd-card class="card-box" title="常显阴影示例" shadow="always">
    <div>美团的使命是“帮大家吃得更好，生活更好”，公司聚焦“零售 + 科技”战略，和广大商户与各类合作伙伴一起，努力为消费者提供品质生活，
      推动商品零售和服务零售在需求侧和供给侧的数字化转型。
    </div>
    <template #extra>
      <mtd-button type="text-primary">更多</mtd-button>
    </template>
  </mtd-card>

  <mtd-card class="card-box" title="不显示阴影示例" shadow="never">
    <div>美团的使命是“帮大家吃得更好，生活更好”，公司聚焦“零售 + 科技”战略，和广大商户与各类合作伙伴一起，努力为消费者提供品质生活，
      推动商品零售和服务零售在需求侧和供给侧的数字化转型。
    </div>
    <template #extra>
      <mtd-button type="text-primary">更多</mtd-button>
    </template>
  </mtd-card>
</template>
<style lang="scss" scoped>
.card-box {
  margin-right: 24px;
  text-align: left;
  width: 288px;

  .title {
    line-height: 22px;
    font-weight: 500;
    font-size: 16px;
    margin-bottom: 8px;
  }
}
</style>
```

#### index.md

```markdown
# 卡片 / Card

卡片是用于围绕单个主题相关信息组成的容器

## 互动演示

<mtd-doc-usage name="Card" :apiJson="require('./api.json')"  :usageJson="require('./usage.json')">
  <template slot="Card" slot-scope="{ configProps }">
    <div class="demo-box">
     <mtd-card v-bind='configProps' title="美团">
        <div>美团的使命是“帮大家吃得更好，生活更好”，公司聚焦“零售 + 科技”战略，和广大商户与各类合作伙伴一起，努力为消费者提供品质生活，
        推动商品零售和服务零售在需求侧和供给侧的数字化转型。</div>
     </mtd-card>
    </div>
  </template>
  <template #Card="{ configProps }">
    <div class="demo-box">
     <mtd-card v-bind='configProps' title="美团">
        <div>美团的使命是“帮大家吃得更好，生活更好”，公司聚焦“零售 + 科技”战略，和广大商户与各类合作伙伴一起，努力为消费者提供品质生活，
        推动商品零售和服务零售在需求侧和供给侧的数字化转型。</div> 
        <template #extra>
          <mtd-button class='btn' type="text-primary">更多</mtd-button>
        </template>
     </mtd-card>
    </div>
  </template>
</mtd-doc-usage>
<style lang="scss" scoped>
  .demo-box {
    display: inline-block;
    width: 328px;
    height: 199px;
    text-align: left;
    .btn {
      font-weight: normal;
    }
  }
</style>

## 代码示例

### 基础用法

:::include(src="./doc/base.vue")
:::

### 无边框型

当背景色为灰色时，建议使用无边框型卡片。
:::include(src="./doc/borderless.vue")
:::

### 简洁卡片

简洁卡片指仅包含内容区域的卡片。
:::include(src="./doc/easy.vue")
:::

### 自定义内容区域

用户可根据业务场景具体情况对卡片进行自定义配置。
:::include(src="./doc/picture.vue")
:::

### 阴影

卡片支持配置阴影显示时机，默认悬浮时显示。
:::include(src="./doc/shadow.vue")
:::

### 加载方式

卡片内数据读入前加载时，需要向用户提供加载反馈。建议优先采用骨架屏加载方式，让用户对结果有预期。
:::include(src="./doc/loading.vue")
:::

## API

<api-doc name="Card" :doc="require('./api.json')"></api-doc>

```

#### index.ts

```typescript
import Card from './card'
import { withInstall } from '@ss/mtd-adapter'
export default withInstall(Card)

```

#### types.ts

```typescript
import Card from './index'

export type Card = InstanceType<typeof Card>

export type CardLodingType = 'icon' | 'skeleton'
```

#### usage.json

```json
{
    "componentName": "Card",
    "desc": "卡片",
    "codeTemplate": "<mtd-card v-bind='configProps'></mtd-card>"
  }
```

