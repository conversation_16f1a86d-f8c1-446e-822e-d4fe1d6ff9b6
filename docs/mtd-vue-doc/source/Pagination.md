## 源码解析

### 文件依赖

- computed-type.ts
- demo.md
- demo/pagination-interactive.vue
- doc/base.vue
- doc/full.vue
- doc/page-size.vue
- doc/quick-jumper.vue
- doc/show-total.vue
- doc/simple.vue
- doc/size-changer.vue
- doc/size.vue
- doc/type.vue
- index.md
- index.ts
- options.tsx
- pager.tsx
- pagination.tsx
- types.ts

### 源码内容

#### computed-type.ts

```typescript
/**
 * 计算 type 的值，为了兼容 simple、unborder、fullfill
 * @param props
 */
export const computedType = (props)=>{
  if (props.type && props.type.length > 0) {
    return props.type
  } else {
    if (props.simple) {
      return 'simple'
    } else if (props.unborder) {
      return 'unborder'
    } else if (props.fullfill) {
      return 'fullfill'
    }
  }
}

```

#### demo.md

```markdown
# 分页组件补充 Demo

## 旧版本属性兼容
* unborder / simple / fullfill 属性
:::include(src="./doc/base.vue"):::

## page-size 相关属性
* page-size / page-size-options 属性
:::include(src="./doc/page-size.vue"):::

```

#### demo/pagination-interactive.vue

```vue
<template>
  <div>
    <mtd-doc-usage name="pagination" :apiJson="apiJson" :usageJson="usageJson" @propChange="propChange">
      <template #pagination="{ configProps }">
        <mtd-pagination
          v-bind="configProps"
          :total="300"
          :page-size="pageSize"
          :current-page.sync="currentPage"
          v-model:current-page="currentPage"
          @change="change"
        />
      </template>
    </mtd-doc-usage>
<!--    <mtd-doc-usage name="picker" :apiJson="apiJson" :usageJson="usageJson" @propChange="propChange">-->
<!--      <template #picker="{ configProps }">-->
<!--        <mtd-picker-->
<!--          v-bind="configProps"-->
<!--          placeholder="请选择"-->
<!--          :options="options"-->
<!--          :model-value="value2"-->
<!--          @update:modelValue="handleValueChange"-->
<!--        />-->
<!--      </template>-->
<!--    </mtd-doc-usage>-->
  </div>
</template>
<script>
import apiJson from '../api.json'

export default {
  data() {
    return {
      apiJson: apiJson,
      currentPage: 1,
      pageSize: 10,
      usageJson: {
        componentName: 'pagination',
        desc: '分页',
        codeTemplate: '<mtd-pagination v-bind=\'configProps\'></mtd-pagination>'
      }
    }
  },
  methods: {
    propChange(prop, value) {
      console.log(prop, ' -> ', value)
    },
    change(current, size) {
      this.pageSize = size;
      console.log(current, size)
    },
  }
}
</script>

```

#### doc/base.vue

```vue
<template>
  <div class="demo-vertical-box">
    <div>
      <p>线框型</p>
      <mtd-pagination
        :total="60"
        :show-total="false"
        :current-page.sync="currentPage1"
        v-model:current-page="currentPage1"
        @change="test"
      />
    </div>
    <div>
      <p>填充型</p>
      <mtd-pagination
        fullfill
        :total="60"
        :show-total="false"
        :current-page.sync="currentPage2"
        v-model:current-page="currentPage2"
        @change="test"
      />
    </div>
    <div>
      <p>无边框型</p>
      <mtd-pagination
        unborder
        :total="60"
        :show-total="false"
        :current-page.sync="currentPage3"
        v-model:current-page="currentPage3"
        @change="test"
      />
    </div>
    <div>
      <p>简单型</p>
      <mtd-pagination
        simple
        :total="60"
        :show-total="false"
        :current-page.sync="currentPage4"
        v-model:current-page="currentPage4"
        @change="test"
      />
    </div>
    <div>
      <p>简单型 simple-readonly</p>
      <mtd-pagination
        simple
        simple-readonly
        :total="60"
        :show-total="false"
        :current-page.sync="currentPage5"
        v-model:current-page="currentPage5"
        @change="test"
      />
    </div>
    <div>
      <p>total-page-description-formatter</p>
      <mtd-pagination
        show-total
        :total-page-description-formatter="totalFormatter"
        :total="60"
        :show-total="false"
        :current-page.sync="currentPage5"
        v-model:current-page="currentPage5"
        @change="test"
      />
    </div>
    <div>
      <p>pager-count</p>
      <mtd-pagination
        :pager-count="11"
        :total="689"
        :show-total="false"
        :current-page.sync="currentPage5"
        v-model:current-page="currentPage5"
        @change="test"
      />
    </div>
    <div>
      <p>select-class</p>
      <mtd-pagination
        select-class="custom-pager-item"
        show-size-changer
        :total="689"
        :show-total="false"
        :current-page.sync="currentPage5"
        v-model:current-page="currentPage5"
        @change="test"
      />
    </div>
  </div>
</template>
<script>
export default {
  data () {
    return {
      currentPage1: 1,
      currentPage2: 1,
      currentPage3: 1,
      currentPage4: 1,
      currentPage5: 1,
    }
  },

  methods: {
    totalFormatter({total, pageSize, currentPage}) {
      console.log(`total: ${total}, pageSize: ${pageSize}, currentPage: ${currentPage}`)
      return `共 ${total} 门店`
    },
    test(current, size) {
      console.log('change-->', current, size)
    },
  },
}
</script>

```

#### doc/full.vue

```vue
<template>
  <div style="padding: 100px 0;">
    <mtd-pagination :total="600" show-quick-jumper show-size-changer show-total @change="change"
      :current-page.sync="currentPageMore" v-model:current-page="currentPageMore" :page-size.sync="currentPageSize"
      v-model:page-size="currentPageSize" />
  </div>
</template>
<script>
export default {
  data() {
    return {
      currentPageMore: 1,
      currentPageSize: 10,
    }
  },
  methods: {
    change(current, size) {
      console.log(current, size)
    },
  },
}
</script>

```

#### doc/page-size.vue

```vue
<template>
  <div class="demo-vertical-box">
    <div>
      <mtd-button @click="changePageSize">改变page-size</mtd-button>
    </div>
    <div>
      <p>page-size v-model / .sync</p>
      <mtd-pagination
        show-size-changer
        :total="60"
        :show-total="false"
        :current-page.sync="currentPage1"
        v-model:current-page="currentPage1"
        :page-size.sync="pageSize1"
        v-model:page-size="pageSize1"
        @change="test"
      />
    </div>
    <div>
      <p>page-size-options</p>
      <mtd-pagination
        show-size-changer
        :page-size-options="[10, 20, 30, 40]"
        :total="60"
        :show-total="false"
        :current-page.sync="currentPage2"
        v-model:current-page="currentPage2"
        :page-size.sync="pageSize2"
        v-model:page-size="pageSize2"
        @change="test"
      />
    </div>
  </div>
</template>
<script>
export default {
  data () {
    return {
      currentPage1: 1,
      pageSize1: 20,
      currentPage2: 1,
      pageSize2: 20,
    }
  },

  methods: {
    changePageSize(){
      this.pageSize1 = this.pageSize1 + 10
      this.pageSize2 = this.pageSize2 + 10
    },
    test(current, size) {
      console.log(current, size)
    },
  },
}
</script>

```

#### doc/quick-jumper.vue

```vue
<template>
  <div class="demo-full-width" style="padding: 100px 0;">
    <mtd-pagination show-quick-jumper :total="496" :current-page.sync="currentPage1"
      v-model:current-page="currentPage1" />
  </div>
</template>
<script>
export default {
  data() {
    return {
      currentPage1: 1,
    }
  },
}
</script>

```

#### doc/show-total.vue

```vue
<template>
  <div class="demo-full-width" style="padding: 100px 0;">
    <mtd-pagination show-total :total="496" :current-page.sync="currentPage1" v-model:current-page="currentPage1" />
  </div>
</template>
<script>
export default {
  data() {
    return {
      currentPage1: 1,
    }
  },
}
</script>

```

#### doc/simple.vue

```vue
<template>
  <div class="demo-full-width" style="padding: 100px 0;">
    <mtd-pagination simple :total="60" :current-page.sync="currentPage4" v-model:current-page="currentPage4" />
  </div>
</template>
<script>
export default {
  data() {
    return {
      currentPage4: 1,
    }
  },
}
</script>

```

#### doc/size-changer.vue

```vue
<template>
  <div class="demo-full-width" style="padding: 100px 0;">
    <mtd-pagination show-size-changer :total="496" v-model:page-size="pageSize" :current-page.sync="currentPage1"
      v-model:current-page="currentPage1" />
  </div>
</template>
<script>
export default {
  data() {
    return {
      currentPage1: 1,
      pageSize: 10
    }
  },
}
</script>

```

#### doc/size.vue

```vue
<template>
  <div class="demo-full-width" style="text-align: left;width: auto;">
    <div style="margin-bottom: 48px;">
      <p>小尺寸</p>
      <mtd-pagination size="small" show-total show-quick-jumper show-size-changer :total="496"
        :current-page.sync="currentPage1" v-model:current-page="currentPage1" />
    </div>
    <div>
      <p>默认尺寸</p>
      <mtd-pagination show-total show-quick-jumper show-size-changer :total="496" :current-page.sync="currentPage2"
        v-model:current-page="currentPage2" />
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      currentPage1: 1,
      currentPage2: 1,
    }
  },
}
</script>

```

#### doc/type.vue

```vue
<template>
  <div class="demo-full-width demo-vertical-box pagination-demo ">
    <div style="margin-bottom: 48px;">
      <p>线框型</p>
      <mtd-pagination :total="496" :current-page.sync="currentPage1" v-model:current-page="currentPage1" />
    </div>
    <div style="margin-bottom: 48px;">
      <p>填充型</p>
      <mtd-pagination type="fullfill" :total="496" :current-page.sync="currentPage2"
        v-model:current-page="currentPage2" />
    </div>
    <div>
      <p>无边框型</p>
      <mtd-pagination type="unborder" :total="496" :current-page.sync="currentPage3"
        v-model:current-page="currentPage3" />
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      currentPage1: 2,
      currentPage2: 2,
      currentPage3: 2,
      currentPage4: 2
    }
  }
}
</script>
<style scoped>
.pagination-demo {
  text-align: left;
  width: auto !important;
}
</style>

```

#### index.md

```markdown

# 分页 / Pagination
用于当数据量大时，分开页面展示和浏览数据。

## 互动演示
:::include(src="./demo/pagination-interactive.vue"):::

## 代码示例

### 尺寸
分页支持小尺寸和默认尺寸两种样式。
:::include(src="./doc/size.vue"):::

### 基础类型
:::include(src="./doc/type.vue"):::

### 调整页幅
:::include(src="./doc/size-changer.vue"):::

### 快速跳转
:::include(src="./doc/quick-jumper.vue"):::

### 显示总数
:::include(src="./doc/show-total.vue"):::

### 简洁分页
:::include(src="./doc/simple.vue"):::

### 完整功能型
可自由选择搭配：每页显示条数、快速跳转页码、总条数。
:::include(src="./doc/full.vue"):::

## API
<api-doc name="Pagination" :doc="require('./api.json')"></api-doc>

```

#### index.ts

```typescript
import Pagination from './pagination'
import { withInstall } from '@ss/mtd-adapter'
export default withInstall(Pagination)

```

#### options.tsx

```tsx
import {
  defineComponent,
  computed,
  useResetAttrs,
} from '@ss/mtd-adapter'
import useConfig from '@hooks/config'
import MtdSelect from '@components/select'
import MtdOption from '@components/option'

export default defineComponent({
  name: 'PaginationOptions',
  components: {
    MtdSelect,
    MtdOption,
  },
  inheritAttrs: true,
  props: {
    pageSizeOptions: Array,
    pageSize: Number,
    size: {
      type: String,
    },
    selectProps: {
      type: Object,
      default: () => { },
    },
  },
  emits: ['change'],
  setup(props, { emit }) {
    const config = useConfig()
    const prefix = computed(() => config.getPrefixCls('pagination-options'))

    const restAttrs = useResetAttrs(props.selectProps)

    const handleChange = (val: any) => emit('change', val)

    return {
      prefix, restAttrs, handleChange, t: config.t,
    }

  },
  render() {
    const {
      prefix, restAttrs, pageSize, size, pageSizeOptions,
    } = this
    return <mtd-select
      {...restAttrs}
      class={prefix}
      modelValue={pageSize}
      size={size}
      onChange={this.handleChange}
      placeholder={this.t('el.pagination.placeholder')}
    >
      {pageSizeOptions?.map(item => <mtd-option
        key={item}
        label={item + this.t('el.pagination.pagesize')}
        value={item}
      />)}
    </mtd-select>
  },
})

```

#### pager.tsx

```tsx
import {
  defineComponent,
  computed,
  Fragment,
  vSlots,
  useResetAttrs,
  toRefs,
  reactive,
  PropType,
} from '@ss/mtd-adapter'
import useConfig from '@hooks/config'
import MtdPopover from '@components/popover'
import MtdDropdownMenu from '@components/dropdown-menu'
import MtdDropdownMenuItem from '@components/dropdown-menu-item'
import MtdIcon from '@components/icon'
import MtdVirtual from '@components/virtual'
import { computedType } from './computed-type'

// 根据首尾数字升恒等差数列数组
function getRangeArr(a: number, b: number) {
  if (a < b) {
    return [...Array(b - a + 1)].map((e, i) => a + i)
  } else {
    return [...Array(a - b + 1)].map((e, i) => a - i)
  }
}

export default defineComponent({
  name: 'Pager',
  components: {
    MtdPopover,
    MtdDropdownMenu,
    MtdDropdownMenuItem,
    MtdIcon,
    MtdVirtual,
  },
  inheritAttrs: true,
  props: {
    /**
     * 分页类型
     * @since 1.2.19
     */
    type: {
      type: String as PropType<undefined | 'fullfill' | 'unborder'>,
    },
    /**
     * 无边框分页
     * @deprecated 请使用type="unborder"
     */
    unborder: Boolean,
    /**
     * 填充分页
     * @deprecated 请使用type="fullfill"
     */
    fullfill: Boolean,
    /**
     * 简单分页
     */
    simple: Boolean,
    size: String,
    currentPage: {
      required: true,
      type: Number,
    },
    pageCount: {
      required: true,
      type: Number,
    },
    simpleReadonly: Boolean,
    pagerCount: {
      type: Number,
      validator: (value: number) => {
        return (
          (value | 0) === value && value > 4 && value < 22 && value % 2 === 1
        )
      },
      default: 7,
    },
    popoverOptions: Object,
  },
  emits: ['change'],
  setup(props, { emit }) {
    const config = useConfig()
    const prefix = computed(() => config.getPrefixCls('pager'))
    const iconPrefix = config.getIconCls
    const prefixPagination = computed(() => config.getPrefixCls('pagination'))
    const popoverRestAttrs = useResetAttrs(props.popoverOptions)

    const state = toRefs(reactive({
      hoverQuickNext: false,
      hoverQuickPrev: false,
    }))
    // @Computed
    // 计算是否显示prev
    const showPrevMore = computed(() => {
      return (
        props.pageCount > props.pagerCount &&
        props.currentPage > (props.pagerCount + 1) / 2
      )
    })
    // 计算是否显示next
    const showNextMore = computed(() => {
      return (
        props.pageCount > props.pagerCount &&
        props.currentPage < props.pageCount - (props.pagerCount - 1) / 2
      )
    })
    const pagers = computed(() => {
      const array: number[] = []
      if (!showPrevMore.value && showNextMore.value) {
        for (let i = 2; i < props.pagerCount; i++) {
          array.push(i)
        }
      } else if (showPrevMore.value && !showNextMore.value) {
        const startPage = props.pageCount - (props.pagerCount - 2)
        for (let i = startPage; i < props.pageCount; i++) {
          array.push(i)
        }
      } else if (showPrevMore.value && showNextMore.value) {
        const offset = Math.floor(props.pagerCount / 2) - 1
        const start = props.currentPage - offset
        const end = props.currentPage + offset
        for (let i = start; i <= end; i++) {
          array.push(i)
        }
      } else {
        for (let i = 2; i < props.pageCount; i++) {
          array.push(i)
        }
      }
      return array
    })
    // type 优先级高于，兼容历史版本的 simple、unborder、fullfill 设置
    const m_type = computed(() => {
      return computedType(props)
    })

    const unBorderClass = computed(() => {
      return ['simple', 'unborder'].includes(m_type.value ?? '')
    })

    const m_hasBorder = computed(() => {
      return !['fullfill', 'simple', 'unborder'].includes(m_type.value ?? '')
    })

    // @Methods
    const goTo = (num: number) => {
      const newPage = num > props.pageCount ? props.pageCount : num
      if (newPage !== props.currentPage) {
        emit('change', newPage)
      }
    }
    const prev = () => {
      if (props.currentPage > 1) {
        const page = props.currentPage
        goTo(page - 1)
      }
    }
    const quickPrev = () => {
      const pagerCountOffset = props.pagerCount - 2
      const page = props.currentPage - pagerCountOffset
      goTo(page)
    }

    const handleQuickPrevMouseEnter = () => {
      state.hoverQuickPrev.value = true
    }
    const handleQuickPrevMouseLeave = () => {
      state.hoverQuickPrev.value = false
    }

    const next = () => {
      if (props.currentPage < props.pageCount) {
        const page = props.currentPage
        goTo(page + 1)
      }
    }
    const quickNext = () => {
      // 去掉首尾的个数
      const pagerCountOffset = props.pagerCount - 2
      const page = props.currentPage + pagerCountOffset
      goTo(page)
    }
    const handleQuickNextMouseEnter = () => {
      state.hoverQuickNext.value = true
    }
    const handleQuickNextMouseLeave = () => {
      state.hoverQuickNext.value = false
    }

    const handleKeyUp = (e: KeyboardEvent) => {
      e.keyCode === 13 && goTo(Number((e.target as HTMLInputElement).value))
    }
    const handleChange = (val: number) => {
      goTo(val)
    }

    const computedCollection = {
      showPrevMore, showNextMore, pagers, unBorderClass, popoverRestAttrs,
    }
    const methodsCollection = {
      goTo, prev, quickPrev, next, quickNext, handleKeyUp, handleChange,
    }


    return {
      ...computedCollection,
      ...methodsCollection,
      prefix, prefixPagination, iconPrefix, m_type, m_hasBorder,
      hoverQuickNext: state.hoverQuickNext,
      hoverQuickPrev: state.hoverQuickPrev,
      handleQuickNextMouseEnter,
      handleQuickNextMouseLeave,
      handleQuickPrevMouseEnter,
      handleQuickPrevMouseLeave,
    }

  },
  render() {
    const {
      prefix, unBorderClass, size, currentPage, simpleReadonly, pageCount, m_hasBorder,
      showPrevMore, pagers, showNextMore, iconPrefix, hoverQuickNext, hoverQuickPrev, simple, popoverRestAttrs,
    } = this

    const renderItem = ({ row: opt, index }) => <mtd-dropdown-menu-item
      onClick={this.handleChange.bind(null, opt)}
      selected={currentPage === opt}
    >
      {opt}
    </mtd-dropdown-menu-item>

    const renderPageButton = (pagerArr: number[]) => {
      return <mtd-dropdown-menu>
        <mtd-virtual
          visible={true}
          view-tag="div"
          virtual
          data={pagerArr}
          row-height={36}
          render-item={renderItem}
          style="height:100%"
        />
      </mtd-dropdown-menu>
    }

    const simpleSlots = { content: () => renderPageButton([1, ...pagers, pageCount]) }
    const showPrevMoreSlots = { content: () => renderPageButton(getRangeArr(1, pagers[0])) }
    const showNextMoreSlots = { content: () => renderPageButton(getRangeArr(pagers[pagers.length - 1], pageCount)) }

    return <ul
      class={{
        [prefix]: true,
        [`${prefix}-unborder`]: unBorderClass,
        [`${prefix}-${size}`]: size,
        [`${prefix}-border`]: m_hasBorder,
      }}
    >
      <li
        class={[
          `${prefix}-item`, `${prefix}-prev`, iconPrefix('left-thick'),
          { [`${prefix}-disabled`]: currentPage <= 1 },
        ]}
        onClick={this.prev}
      />

      {simple
        ? <mtd-popover
          {...popoverRestAttrs}
          disabled={simpleReadonly || pageCount <= 1}
          trigger="hover"
          placement="top"
          show-arrow={false}
          popper-class={`${prefix}-simple-popper`}
          {...vSlots(simpleSlots)}
          v-slots={simpleSlots}
        >
          <li class={`${prefix}-simple-item`} >
            <span class={`${prefix}-simple-item-span`}>{currentPage}</span>
            <span class={`${prefix}-simple-item-span`}> / </span>
            <span class={`${prefix}-simple-item-span`}>{pageCount}</span>
          </li>
        </mtd-popover>
        : <Fragment>
          {pageCount > 0 &&
            <li
              class={{
                [`${prefix}-item`]: true,
                [`${prefix}-number`]: true,
                [`${prefix}-active`]: currentPage === 1,
              }}
              onClick={this.goTo.bind(null, 1)}
            >
              1
            </li>
          }

          {showPrevMore &&
            <mtd-popover
              {...popoverRestAttrs}
              trigger="hover"
              placement="top"
              show-arrow={false}
              popper-class={`${prefix}-simple-popper`}
              {...vSlots(showPrevMoreSlots)}
              v-slots={showPrevMoreSlots}
              key="left-popover"
            >
              <li
                class={`${prefix}-item ${prefix}-quickprev`}
                onMouseenter={this.handleQuickPrevMouseEnter}
                onMouseleave={this.handleQuickPrevMouseLeave}
                onClick={this.quickPrev}
                key="-1">
                <mtd-icon name={hoverQuickPrev ? 'fast-backward' : 'ellipsis'} />
              </li>
            </mtd-popover>
          }

          {pagers.map((pager, index) =>
            <li
              class={{
                [`${prefix}-item`]: true,
                [`${prefix}-number`]: true,
                [`${prefix}-active`]: currentPage === pager,
              }}
              onClick={this.goTo.bind(null, pager)}
              key={pager + '-' + index}
            >
              {pager}
            </li>)
          }

          {showNextMore &&
            <mtd-popover
              {...popoverRestAttrs}
              trigger="hover"
              placement="top"
              show-arrow={false}
              popper-class={`${prefix}-simple-popper`}
              {...vSlots(showNextMoreSlots)}
              v-slots={showNextMoreSlots}
              key="right-popover"
            >
              <li
                class={`${prefix}-item ${prefix}-quicknext`}
                onMouseenter={this.handleQuickNextMouseEnter}
                onMouseleave={this.handleQuickNextMouseLeave}
                onClick={this.quickNext}
                key={'-2'}>
                <mtd-icon name={hoverQuickNext ? 'fast-forward' : 'ellipsis'} />
              </li >
            </mtd-popover>
          }

          {pageCount > 1 &&
            <li
              onClick={this.goTo.bind(null, pageCount)}
              class={{
                [`${prefix}-item`]: true,
                [`${prefix}-number`]: true,
                [`${prefix}-active`]: currentPage === pageCount,
              }}
            >
              {pageCount}
            </li >
          }
        </Fragment >
      }

      < li
        class={
          [
            `${prefix}-item ${prefix}-next ${iconPrefix('right-thick')}`,
            { [`${prefix}-disabled`]: currentPage === pageCount },
          ]}
        onClick={this.next}
      />
    </ul >
  },
})

```

#### pagination.tsx

```tsx
import {
  defineComponent,
  computed,
  useResetAttrs,
  PropType,
} from '@ss/mtd-adapter'
import useConfig from '@hooks/config'
import MtdPage from './pager'
import MtdOptions from './options'
import { isFunction } from '@utils/type'
import { TotalPageDescriptionFormatter } from '@components/pagination/types'
import { computedType } from './computed-type'

export default defineComponent({
  name: 'MtdPagination',
  components: {
    MtdPage,
    MtdOptions,
  },
  inheritAttrs: true,
  props: {
    size: {
      type: String,
    },
    /**
     * 分页类型
     * @since 1.2.19
     */
    type: {
      type: String as PropType<undefined | 'fullfill' | 'unborder' | 'simple'>,
    },
    /**
     * 无边框分页
     * @deprecated 请使用type="unborder"
     */
    unborder: Boolean,
    /**
     * 填充分页
     * @deprecated 请使用type="fullfill"
     */
    fullfill: Boolean,
    /**
     * 简单分页
     * @deprecated 请使用type="simple"
     */
    simple: {
      type: Boolean,
      default: false,
    },
    currentPage: {
      type: Number,
      default: 1,
    },
    pageSize: {
      type: Number,
      default: 10,
    },
    pageSizeOptions: {
      type: Array,
      default: () => [10, 20, 50, 100],
    },
    showSizeChanger: {
      type: Boolean,
      default: false,
    },
    showQuickJumper: {
      type: Boolean,
      default: false,
    },
    total: {
      type: Number,
      default: 0,
    },
    showTotal: {
      type: Boolean,
      default: false,
    },
    pagerCount: Number,
    selectClass: String,
    selectProps: Object,
    simpleReadonly: Boolean,
    totalPageDescriptionFormatter: {
      type: Function as PropType<TotalPageDescriptionFormatter>,
    },
    popoverOptions: Object,
  },
  emits: ['update:currentPage', 'update:pageSize', 'change'],
  setup(props, { attrs, emit }) {
    const config = useConfig()
    const prefix = computed(() => config.getPrefixCls('pagination'))
    const restAttrs = useResetAttrs(attrs)
    const defaultPageCount = computed(() => {
      const pageCount = Math.ceil(Number(props.total) / Number(props.pageSize))
      if (!isNaN(pageCount) && pageCount > 0) {
        return pageCount
      }
      return 1
    })

    const handleCurrentChange = (val: number) => {
      const page = ~~(val > defaultPageCount.value
        ? defaultPageCount.value
        : val > 1
          ? val
          : 1)
      if (page !== props.currentPage) {
        emit('update:currentPage', page)
        emit('change', page, props.pageSize)
      }
    }

    const defaultCurrentPage = computed(() => {
      if (isNaN(props.currentPage) || props.currentPage < 1) {
        handleCurrentChange(1)
        return 1
      }
      return props.currentPage
    })

    const defaultPageSizeOptions = computed(() => {
      const pageSizeOptions = props.pageSizeOptions
      if (props.pageSizeOptions.indexOf(props.pageSize) === -1) {
        pageSizeOptions.unshift(props.pageSize)
      }
      return pageSizeOptions
    })

    const handleChange = (e: Event) => {
      // ~~value可以代替parseInt(value)
      handleCurrentChange(Number(~~(e.target as HTMLInputElement).value))
    }

    const handleKeyup = (e: KeyboardEvent) => {
      e.keyCode === 13 &&
        handleCurrentChange(Number((e.target as HTMLInputElement).value))
    }

    const handlePageSizeChange = (val: number) => {
      const pageCount = Math.max(
        1,
        Math.ceil(Number(props.total) / Number(val)),
      )
      emit('update:pageSize', val)
      const currentPage =
        pageCount < defaultCurrentPage.value
          ? pageCount
          : defaultCurrentPage.value

      if (currentPage !== defaultCurrentPage.value) {
        emit('update:currentPage', currentPage)
      }
      emit('change', currentPage, val)

      // (pageCount >= this.defaultCurrentPage) &&
      //   this.$emit('change', this.currentPage, val);
    }

    const computedCollection = {
      defaultPageCount, defaultCurrentPage, defaultPageSizeOptions,
    }
    const methodsCollection = {
      handleCurrentChange, handleChange, handleKeyup, handlePageSizeChange,
    }

    // type 优先级高于，兼容历史版本的 simple、unborder、fullfill 设置
    const m_type = computed(() => {
      return computedType(props)
    })

    const m_unBorderClass = computed(() => {
      return ['simple', 'unborder'].includes(m_type.value ?? '')
    })

    const m_hasBorder = computed(() => {
      return !['fullfill', 'simple', 'unborder'].includes(m_type.value ?? '')
    })

    const m_simple = computed(() => m_type.value === 'simple' || props.simple)

    return {
      t: config.t,
      ...computedCollection,
      ...methodsCollection,
      m_type, m_unBorderClass, m_hasBorder,
      prefix, restAttrs, m_simple,
    }

  },
  render() {
    const {
      prefix, size, restAttrs, defaultCurrentPage, defaultPageCount, pagerCount, simpleReadonly,
      showSizeChanger, selectProps, defaultPageSizeOptions, pageSize, selectClass, showQuickJumper, total, showTotal,
      popoverOptions, totalPageDescriptionFormatter, m_type, m_unBorderClass, m_hasBorder, m_simple,
    } = this
    let totalPageDesc: string
    if (isFunction(totalPageDescriptionFormatter)) {
      totalPageDesc = (totalPageDescriptionFormatter as Function)({ total, currentPage: defaultCurrentPage, pageCount: defaultPageCount })
    } else {
      totalPageDesc = this.t('el.pagination.total', {
        total,
      })
    }
    return <div
      class={{
        [prefix]: true,
        [`${prefix}-border`]: m_hasBorder,
        [`${prefix}-simple`]: m_simple,
        [`${prefix}-fullfill`]: m_type === 'fullfill',
        [`${prefix}-unborder`]: m_unBorderClass,
        [`${prefix}-${size}`]: size,
      }}
    >
      {showTotal &&
        <span class={`${prefix}-total`}>{totalPageDesc}</span>
      }
      <mtd-page
        {...restAttrs}
        popoverOptions={popoverOptions}
        type={m_type}
        size={size}
        simple={m_simple}
        currentPage={defaultCurrentPage}
        pageCount={defaultPageCount}
        pagerCount={pagerCount}
        simpleReadonly={simpleReadonly}
        onChange={this.handleCurrentChange}
      />
      {showSizeChanger &&
        <mtd-options
          size={size}
          selectProps={selectProps}
          pageSizeOptions={defaultPageSizeOptions}
          pageSize={pageSize}
          class={selectClass || ''}
          onChange={this.handlePageSizeChange}
        />
      }
      {showQuickJumper &&
        <span class={`${prefix}-jumper`}>
          <span>{this.t('el.pagination.goto')}</span>
          <input
            class={`${prefix}-jumper-input`}
            type="number"
            value={defaultCurrentPage}
            onKeyup={this.handleKeyup}
            onBlur={this.handleChange}
          />
        </span>
      }

    </div >
  },
})

```

#### types.ts

```typescript
export type TotalPageDescriptionFormatter = (params: { total: number, currentPage: number, pageCount: number }) => string;

```

