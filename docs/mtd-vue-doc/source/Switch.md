## 源码解析

### 文件依赖

- demo.md
- doc/base.vue
- doc/demo/loading.vue
- doc/demo/true-false-value.vue
- doc/icon.vue
- doc/size.vue
- doc/status.vue
- index.md
- index.ts
- switch.tsx
- usage.json

### 源码内容

#### demo.md

```markdown
# 开关补充 Demo

## loading
自定义loading插槽
:::include(src="./doc/demo/loading.vue"):::

## true-false-value
:::include(src="./doc/demo/true-false-value.vue"):::
```

#### doc/base.vue

```vue
<template>
  <mtd-switch v-model="value" />
</template>
<script>
export default {
  data () {
    return {
      value: true,
    };
  },
};
</script>

```

#### doc/demo/loading.vue

```vue
<template>
  <div>
    <span>是否loading</span>
    <mtd-switch v-model="loading" />
  </div>
  <div class="s5">
    <mtd-switch v-model="value" :loading="loading">
      <template #loading>
        <span>loading</span>
      </template>
    </mtd-switch>
  </div>
</template>

<script>
export default {
  data () {
    return {
      loading: false,
      value: false,
    }
  },
}
</script>

```

#### doc/demo/true-false-value.vue

```vue
<template>
  <mtd-switch v-model="value" true-value="2" false-value="3" @change="change"/>
</template>
<script>
export default {
  data() {
    return {
      value: '2',
    };
  },
  methods: {
    change(value) {
      console.log(value)
    }
  }
};
</script>

```

#### doc/icon.vue

```vue
<template>
  <div class="s6">
    <mtd-switch name="switchName" v-model="value5" checked-children="开启" un-checked-children="关闭" />
    <br>
    <br>
    <mtd-switch v-model="value6">
      <template #checkedChildren>
        <span>开启</span>
      </template>
      <template #unCheckedChildren>
        <span>关闭</span>
      </template>
    </mtd-switch>
  </div>
  <div class="s7">
    <mtd-switch v-model="value7">
      <template #checkedChildren>
        <i class="mtdicon mtdicon-check-thick" />
      </template>
      <template #unCheckedChildren>
        <i class="mtdicon mtdicon-close" />
      </template>
    </mtd-switch>
    <br>
    <br>
    <mtd-switch v-model="value8">
      <template #checkedChildren>
        <i class="mtdicon mtdicon-check-thick" />
      </template>
      <template #unCheckedChildren>
        <i class="mtdicon mtdicon-close" />
      </template>
    </mtd-switch>
  </div>
</template>
<script>
export default {
  data() {
    return {
      value5: true,
      value6: false,
      value7: true,
      value8: false
    }
  },
}
</script>

<style lang="scss" scoped>
.s6 {
  margin-left: 280px;
}

.s7 {
  margin-right: 300px;
}
</style>

```

#### doc/size.vue

```vue
<template>
  <div class="s1">
    <mtd-switch v-model="value1" size="small" @change="handleChange"/>
  </div>
  <div class="s2">
    <mtd-switch v-model="value2" />
  </div>
  
</template>

<script>
export default {
  data () {
    return {
      value1: true,
      value2: true,
    }
  },
  methods: {
      handleChange(){
        console.log("change事件被触发")
      }
    }
};
</script>
<style lang="scss" scoped>
.s1{
  margin-left: 280px;
}
.s2{
  margin-right: 300px;
}
</style>

```

#### doc/status.vue

```vue
<template>
  <div class="s3">
    <span class="wz">默认</span>
    <mtd-switch v-model="value3" />
    <mtd-switch v-model="value4" />
  </div>
  <div class="s4">
    <span class="wz">禁用</span>
    <mtd-switch :model-value="true" disabled />
    <mtd-switch :model-value="false" disabled/>
  </div>
  <div class="s5">
    <span class="wz">加载中</span>
    <mtd-switch :model-value="true" loading />
    <mtd-switch :model-value="false" loading />
  </div>
</template>

<script>
export default {
  data () {
    return {
      value3: true,
      value4: false,
    }
  },
}
</script>
<style lang="scss" scoped>
.mtd-switch {
  margin-right: 18px;
}

.wz {
  display: block; 
  margin-bottom: 12px; 
  color: rgba(17, 25, 37, 0.65);
  font-size: 14px;
  font-weight: normal;
  line-height: 22px;
}

.s3, .s4, .s5 {
  text-align: left;
}
</style>

```

#### index.md

```markdown
# 开关 / Switch

开关切换器用于两个互斥状态间的切换和选择。

## 互动演示
<mtd-doc-usage name="switch" renderComponentName='MtdSwitch' :apiJson="require('./api.json')"  :usageJson="require('./usage.json')">
</mtd-doc-usage>

## 代码示例
### 尺寸

开关支持小、大两种尺寸。
:::include(src="./doc/size.vue")
:::


### 状态

开关状态有默认状态、悬停状态、激活（点击）状态、禁用状态、加载状态。
:::include(src="./doc/status.vue")
:::

### 文字或图标

开关内部带文字或图标。
:::include(src="./doc/icon.vue")
:::

## API

<api-doc name="Switch" :doc="require('./api.json')"></api-doc>

```

#### index.ts

```typescript
import Switch from './switch'
import { withInstall } from '@ss/mtd-adapter'
export default withInstall(Switch)

```

#### switch.tsx

```tsx
import {
  defineComponent, computed, PropType,
  getSlotsInRender,
} from '@ss/mtd-adapter'
import useConfig from '@hooks/config'
import { MTDUIComponentSize } from '@components/types'
import LoadingCircle from '@components/loading/circle'
import { useFormItem } from '@components/form-item/useFormItem'

// todo: active: false, loading: true 时 样式错误
export default defineComponent({
  name: 'MtdSwitch',
  components: {
    LoadingCircle,
  },
  inheritAttrs: true,
  model: {
    prop: 'modelValue',
    event: 'update:modelValue',
  },
  props: {
    modelValue: [String, Number, Boolean, Function, Object, Array],
    trueValue: {
      type: [String, Number, Boolean, Function, Object, Array],
      default: true,
    },
    falseValue: {
      type: [String, Number, Boolean, Function, Object, Array],
      default: false,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    size: {
      type: String as PropType<MTDUIComponentSize>,
      default: '',
    },
    loading: {
      type: Boolean,
      default: false,
    },
    name: String,
    checkedChildren: String,
    unCheckedChildren: String,
  },
  emits: ['change', 'input', 'update:modelValue'],
  setup(props, ctx) {
    const { emit } = ctx
    const config = useConfig()
    const prefix = config.getPrefixCls('switch')
    const formItemHook = useFormItem(props, ctx)

    const m_disabled = formItemHook.disabled

    const m_actived = computed(() => {
      return props.modelValue === props.trueValue
    })

    const m_size = formItemHook.m_size

    const wrapperCls = computed(() => [
      `${prefix}`,
      {
        [`${prefix}-active`]: m_actived.value,
        [`${prefix}-disabled`]: m_disabled.value,
        [`${prefix}-${m_size.value}`]: m_size.value,
        [`${prefix}-loading`]: props.loading,
      },
    ])
    const handleClick = (e: Event) => {
      e.preventDefault()
      if (m_disabled.value || props.loading) {
        return false
      }
      const value = m_actived.value ? props.falseValue : props.trueValue
      emit('update:modelValue', value)

      emit('input', value)
      formItemHook.m_handleChange(value)
    }
    return {
      m_actived,
      wrapperCls,
      prefix,
      handleClick,
      m_disabled,
      m_size,
    }
  },
  render() {
    const { m_actived, wrapperCls, prefix, handleClick, m_size } = this
    const {
      loading,
      checkedChildren,
      unCheckedChildren,
    } = this.$props
    return (
      <span class={wrapperCls} onClick={handleClick}>
        <span class={`${prefix}-btn`}>
          {loading &&
            (getSlotsInRender(this, 'loading') ||
              <loading-circle
                thickness={1}
                size={m_size === 'large' ? 15 : 12}
              />
            )
          }
        </span>
        <span class={`${prefix}-inner`}>
          {m_actived
            ? (getSlotsInRender(this, 'checkedChildren') || checkedChildren)
            : (getSlotsInRender(this, 'unCheckedChildren') || unCheckedChildren)}
        </span>
      </span>
    )
  },
})

```

#### usage.json

```json
{
    "componentName": "Switch",
    "desc": "开关",
    "codeTemplate": "<mtd-switch v-bind='configProps'></mtd-switch>"
  }
```

