## 源码解析

### 文件依赖

- affix.tsx
- doc/base.vue
- doc/bottom.vue
- index.md
- index.ts
- tests/affix.spec.js

### 源码内容

#### affix.tsx

```tsx
import {
  defineComponent,
  computed,
  reactive,
  toRefs,
  ref,
  PropType,
  getSlotsInRender,
  vueInstance,
} from '@ss/mtd-adapter'
import useConfig from '@hooks/config'
import { debounce } from '@utils/debounce'
import { warn } from '@utils/log'
import { on, off } from '@utils/dom'
import { isNumber } from '@utils/type'
import { onAMounted, onAUnmounted } from '@hooks/util'
import getElement from '@hooks/getElement'
// todo: 自定义 scrollelement 的情况怎么做。
function getScroll(target: HTMLElement | Window, top?: boolean) {
  const prop = top ? 'pageYOffset' : 'pageXOffset'
  const method = top ? 'scrollTop' : 'scrollLeft'

  let ret: number | undefined = (target as any)[prop]

  if (!isNumber(ret)) {
    ret = window.document.documentElement[method]
  }

  return ret
}

function getOffset(element: HTMLElement) {
  const rect = element.getBoundingClientRect()

  const scrollTop = getScroll(window, true)
  const scrollLeft = getScroll(window)

  const docEl = window.document.body
  const clientTop = docEl.clientTop || 0
  const clientLeft = docEl.clientLeft || 0
  // clientTop和clientLeft兼容性比较好，不需要修改
  return {
    top: rect.top + scrollTop - clientTop,
    left: rect.left + scrollLeft - clientLeft,
  }
}

export interface getTarget {
  (): HTMLElement
  (element?: HTMLElement): HTMLElement
}

function getDefaultTarget() {
  return window
}
export default defineComponent({
  name: 'MtdAffix',
  inheritAttrs: true,
  props: {
    offsetTop: {
      type: Number,
      default: 0,
    },
    offsetBottom: {
      type: Number,
    },
    debounce: {
      type: Number,
      default: 0,
    },
    getTarget: {
      type: Function as PropType<getTarget>,
      default: undefined,
    },
  },
  emits: ['change'],
  setup(props, { emit }) {
    const config = useConfig()
    const prefix = computed(() => config.getPrefixCls('affix'))
    const ins = vueInstance()

    const pointRef = ref<HTMLElement>()
    const state = reactive<{
      affix: boolean
      selfStyles?: any
      slot: boolean
      placeHolderStyle?: any
      containerEl?: HTMLElement | Window
    }>({
      affix: false,
      selfStyles: {},
      slot: false,
      placeHolderStyle: {},
      containerEl: undefined,
    })
    const offsetType = computed(() => props.offsetBottom! >= 0 ? 'bottom' : 'top')
    const containerEl = ref<HTMLElement | Window>()
    const removeResizeRef = ref<() => void>()
    
    // @Methods
    function handleScroll() {
      const affix = state.affix
      const scrollElement = containerEl.value
      const $el = ins.$el
      if (!scrollElement || !$el) {
        return
      }
      const scrollTop = getScroll(scrollElement, true)
      const containerHeight =
        scrollElement === window
          ? ((window as Window).innerHeight || document.body.clientHeight)
          : (scrollElement as HTMLElement).getBoundingClientRect().height

      // todo: 确认下非 window 的情况是否应该这么算
      const elOffset = getOffset($el)
      const elHeight = pointRef.value!.offsetHeight
      if (affix) {
        switch (offsetType.value) {
          case 'top':
            if ((elOffset.top - props.offsetTop) > scrollTop) {
              state.slot = false
              state.placeHolderStyle = {}
              state.affix = false
              state.selfStyles = undefined
              emit('change', false)
            } else {
              const containerRect = $el.getBoundingClientRect()
              // 虽然已经固定,但是窗口可能被缩放,因此要实时更新元素宽度以适应缩放
              state.selfStyles.width = `${containerRect.width}px`
              state.placeHolderStyle.width = `${containerRect.width}px`
            }
            break
          case 'bottom':
            if (
              elOffset.top + props.offsetBottom! + elHeight <
              scrollTop + containerHeight
            ) {
              state.affix = false
              state.slot = false
              state.placeHolderStyle = {}
              state.selfStyles = undefined
              emit('change', false)
            } else {
              const containerRect = $el.getBoundingClientRect()
              // 虽然已经固定,但是窗口可能被缩放,因此要实时更新元素宽度以适应缩放
              state.selfStyles.width = `${containerRect.width}px`
              state.placeHolderStyle.width = `${containerRect.width}px`
            }
            break
        }
      } else {
        switch (offsetType.value) {
          case 'top':
            if (elOffset.top - props.offsetTop < scrollTop) {
              state.affix = true
              const point = pointRef.value as HTMLElement
              const pointRect = point.getBoundingClientRect()
              state.placeHolderStyle = {
                width: pointRect.width + 'px',
                height: pointRect.height + 'px',
              }
              state.slot = true
              state.selfStyles = {
                top: `${props.offsetTop}px`,
                width: `${pointRect.width}px`,
              }
              emit('change', true)
            }
            break
          case 'bottom':
            if (
              elOffset.top + props.offsetBottom! + elHeight >
              scrollTop + containerHeight
            ) {
              const point = pointRef.value as HTMLElement
              const pointRect = point.getBoundingClientRect()
              state.affix = true
              state.placeHolderStyle = {
                width: pointRect.width + 'px',
                height: pointRect.height + 'px',
              }
              state.slot = true
              state.selfStyles = {
                bottom: `${props.offsetBottom}px`,
                width: `${pointRect.width}px`,
              }
              emit('change', true)
            }
            break
        }
      }
    }

    const debounceScroll = props.debounce ? debounce(props.debounce, handleScroll) : handleScroll
    onAMounted(() => {
      containerEl.value = (props.getTarget || getDefaultTarget)(ins.$el)
      if (containerEl.value) {
        on(containerEl.value, 'scroll', debounceScroll)
        const resizeListener = on(containerEl.value, 'resize', debounceScroll)
        removeResizeRef.value = resizeListener
        debounceScroll()
      } else {
        warn('Affix', 'getTarget prop must return an HTMLElement')
      }
    })
    onAUnmounted(() => {
      if (containerEl.value) {
        off(containerEl.value, 'scroll', debounceScroll)
        removeResizeRef.value?.()
      }
    })
    return {
      ...toRefs(state),
      offsetType,
      handleScroll,
      debounceScroll,
      prefix, pointRef,
    }
  },
  render() {
    const {
      prefix, affix, selfStyles, slot, placeHolderStyle,
    } = this
    return <div>
      <div ref='pointRef' class={{ [`${prefix}`]: affix }} style={selfStyles}>
        {getSlotsInRender(this)}
      </div>
      <div v-show={slot} style={placeHolderStyle} />
    </div>
  },
})

```

#### doc/base.vue

```vue
<template>
  <div>
    <mtd-affix :offset-top="140" :get-target="getTarget" @change="change">
      <span style="background-color: #666; color: #FFF">
        offset top 140
      </span>
    </mtd-affix>
  </div>
</template>

<script>
export default {
  methods: {
    getTarget() {
      return document.querySelector('.app');
    },
    change(status) {
      console.log('状态变化:', status)
    }
  },
};
</script>

```

#### doc/bottom.vue

```vue
<template>
  <div>
    <div style="height: 1000px" />
    <mtd-affix :offset-bottom="100" :get-target="getTarget">
      <span style="background-color: #666; color: #FFF">
        offset bottom 100
       </span>
    </mtd-affix>
  </div>
</template>

<script>
export default {
  methods: {
    getTarget () {
      return document.querySelector('.app');
    },
  },
};
</script>

```

#### index.md

```markdown
# 固钉 / Affix
## 类型与用法
### 固定在距离顶部140px
:::include(src="./doc/base.vue")
:::

### 固定在距离底部100px
:::include(src="./doc/bottom.vue")
:::

## API
<api-doc name="Affix" :doc="require('./api.json')"></api-doc>

```

#### index.ts

```typescript
import Affix from './affix'
import { withInstall } from '@ss/mtd-adapter'
export default withInstall(Affix)

```

#### tests/affix.spec.js

```javascript
import Affix from '@components/affix'
import {
  createDemoTest,
  mount,
  triggerClick,
  wait,
  waitImmediate,
} from '@tests/utils'

describe('Affix', function () {
  createDemoTest('affix')
  it('created', function () {
    const wrapper = mount(Affix)
    expect(wrapper.exists()).toBe(true)
  })
})

```

