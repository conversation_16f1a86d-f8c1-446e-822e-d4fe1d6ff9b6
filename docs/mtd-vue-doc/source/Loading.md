## 源码解析

### 文件依赖

- circle.tsx
- demo/loading-interaction.vue
- demo/usage.json
- doc/base.vue
- doc/container.vue
- doc/delay.vue
- doc/fullscreen.vue
- doc/position.vue
- doc/size.vue
- doc/type.vue
- index.md
- index.ts
- line-scale.tsx
- loading.tsx

### 源码内容

#### circle.tsx

```tsx
import { useConfig } from '@components/config-provider'
import { computed, defineComponent, ref } from '@ss/mtd-adapter'

function getArcLength(percent: number, radius: number) {
  return percent * 2 * radius * Math.PI
}

export default defineComponent({
  name: 'MtdLoadingCircle',
  inheritAttrs: true,
  props: {
    thickness: {
      type: Number,
      default: 2,
    },
    size: {
      type: Number,
      default: 20,
    },
    color: {
      type: String,
      default: 'currentColor',
    },
    disableShrink: {
      type: Boolean,
      default: false,
    },
  },
  setup() {
    const config = useConfig()
    const prefixMTD = computed(() => config.getPrefix())
    const scalePathTimer = ref<undefined | number>(undefined)
    return {
      scalePathTimer, prefixMTD,
    }
  },
  computed: {
    sizeNumber(): number {
      return this.size
    },
    diameter(): number {
      return getArcLength(1, (this.sizeNumber - this.thickness) / 2)
    },
    radius(): number {
      return (this.sizeNumber - this.thickness) / 2
    },
    center(): number {
      return this.sizeNumber / 2
    },
    style() {
      return {
        width: `${this.sizeNumber}px`,
        height: `${this.sizeNumber}px`,
      }
    },
  },
  watch: {
    disableShrink(n) {
      !n ? this.scalePath(this.$refs.path) : clearTimeout(this.scalePathTimer)
    },
  },
  mounted() {
    const {
      $refs: { path },
      disableShrink,
    } = this
    if (!disableShrink) {
      this.scalePath(path)
    }
  },
  beforeUnmount() {
    clearTimeout(this.scalePathTimer)
  },
  beforeDestroy() {
    clearTimeout(this.scalePathTimer)
  },
  methods: {
    scalePath(path: any, step = 0) {
      const { diameter } = this
      step = step % 3
      switch (step) {
        case 0:
          path.style.transitionDuration = '0ms'
          path.style.strokeDasharray = `3 ${diameter}`
          path.style.strokeDashoffset = '0'
          break
        case 1:
          path.style.transitionDuration = '700ms'
          path.style.strokeDasharray = `${0.7 * diameter} ${diameter}`
          path.style.strokeDashoffset = -0.3 * diameter
          break
        case 2:
          path.style.transitionDuration = '700ms' // 多出100ms用于显示最后一点
          path.style.strokeDasharray = `${0.7 * diameter} ${diameter}`
          path.style.strokeDashoffset = 3 - diameter
          // return
          break
      }
      this.scalePathTimer = setTimeout(
        () => {
          this.scalePath(path, step + 1)
        },
        step ? 700 : 100,
      )
    },
  },
  render() {
    const { style, sizeNumber, center, radius, thickness, color, prefixMTD } = this
    return <svg
      style={style}
      class={`${prefixMTD}-loading-circle`}
      viewBox={`0 0 ${sizeNumber} ${sizeNumber}`}
    >
      <circle
        class=''
        ref='path'
        cx={center}
        cy={center}
        r={radius}
        fill='none'
        stroke-linecap='round'
        stroke-width={thickness}
        stroke={color}
        style='stroke-dasharray: 25px 1000px; stroke-dashoffset: 0px;'
      />
    </svg>
  },
})

```

#### demo/loading-interaction.vue

```vue
<template>
  <mtd-doc-usage name='Loading' :apiJson="apiJson" :usageJson="usage" @propChange="propChange">
    <template #Loading="{ configProps }">
      <mtd-loading v-bind="configProps" />
    </template>
  </mtd-doc-usage>
</template>
<script>
const usage = require('./usage.json')
const apiJson = require('../api.json')
export default {
  data() {
    return {
      usage,
      apiJson
    };
  },
  methods: {
    propChange(propertyName, val) {
      console.log('prop changed: , propertyName: ' + propertyName + ', val: ' + val)
    }
  }
};
</script>
  
```

#### demo/usage.json

```json
{
  "componentName": "Loading",
  "desc": "加载",
  "codeTemplate": "<mtd-loading v-bind='configProps'></mtd-loading>"
}
```

#### doc/base.vue

```vue
<template>
  <div style="padding: 100px 0;">
    <mtd-loading message-position="right" />
  </div>
</template>

```

#### doc/container.vue

```vue
<template>
  <div class="demo-full-width">
    <mtd-switch v-model="loading" />
    <br><br>
    <mtd-loading :loading="loading" :delay="500" message-position="right">
      <div class="loading-div">
        <span>被包裹的div</span>
      </div>
    </mtd-loading>
  </div>
</template>
<script>
export default {
  data() {
    return {
      loading: false,
    }
  },
}
</script>
<style scoped>
.loading-div{
  height: 200px;
  width: 100%;
  background: rgba(0, 136, 214, 0.5);
  color: rgb(0, 109, 172);

  display: flex;
  align-items: center;
  justify-content: center;
}
</style>

```

#### doc/delay.vue

```vue
<template>
  <div class="demo-full-width">
    <mtd-loading :loading="loading" message-position="right">
      <div>
        <p>当前加载信息当前加载信息当前加载信息</p>
        <p>当前加载信息当前加载信息当前加载信息</p>
        <p>当前加载信息当前加载信息当前加载信息</p>
        <p>当前加载信息当前加载信息当前加载信息</p>
      </div>
    </mtd-loading>
    <div class="loading-div">
      <mtd-button @click="() => handleClick(500)">延迟500ms加载</mtd-button>
      <mtd-button @click="() => handleClick(0)" style="margin-left: 32px">无延迟加载</mtd-button>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      loading: false,
    };
  },
  methods: {
    handleClick(e) {
      this.loading = true;
      setTimeout(() => {
        this.loading = false;
      }, e);
    },
  },
};
</script>
<style scoped>
.loading-div {
  margin-top: 35px;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>

```

#### doc/fullscreen.vue

```vue
<template>
  <div style="padding: 100px 0;">
    <mtd-switch v-model="loading" />
    <mtd-loading :loading="loading" fullscreen />
  </div>
</template>
<script>
export default {
  data() {
    return {
      loading: false,
    }
  },
  watch: {
    loading(v) {
      if (v) {
        const timer = setTimeout(() => {
          this.loading = false;
          clearTimeout(timer);
        }, 3000);
      }
    },
  },
}
</script>

```

#### doc/position.vue

```vue
<template>
  <div class="demo-wrapper">
    <div style="margin-right: 128px;">
      <div>横向布局</div>
      <br />
      <mtd-loading message-position="right" />
    </div>
    <div>
      <div>纵向布局</div>
      <br />
      <mtd-loading />
    </div>
  </div>
</template>
<style lang="scss" scoped>
.demo-wrapper {
  display: flex;
  margin-left: 100px;
  margin-right: 100px;
}
</style>

```

#### doc/size.vue

```vue
<template>
  <div class="demo-wrapper">
    <mtd-tabs v-model="tab" type="text" size="small">
      <mtd-tab-pane label="横向布局" value="tab1">
        <mtd-loading size="small" message-position="right" />
        <mtd-loading message-position="right" />
        <mtd-loading size="large" message-position="right" />
      </mtd-tab-pane>

      <mtd-tab-pane label="纵向布局" value="tab2">
        <mtd-loading size="small" />
        <mtd-loading />
        <mtd-loading size="large" />
      </mtd-tab-pane>
    </mtd-tabs>
  </div>
</template>

<script>
export default {
  data() {
    return {
      tab: "tab1",
    };
  },
};
</script>

<style lang="scss" scoped>
.demo-wrapper {
  width: 100%;

  ::v-deep .mtd-tab-pane {
    padding: 130px 0;
    display: flex;
    justify-content: space-around;
  }
  ::v-deep .mtd-tabs-nav {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    border-bottom: 1px solid #edf0f7;
    padding: 1.5px 16px;
  }
}
</style>

```

#### doc/type.vue

```vue
<template>
  <div style="padding: 100px 0;">
    <div class="demo-loading-text">
      <mtd-loading />
    </div>
    <div class="demo-loading-text">
      <mtd-loading>
        <template #indicator>
          <div class="line-spinner-container">
            <div class="line"></div>
            <div class="line"></div>
            <div class="line"></div>
            <div class="line"></div>
            <div class="line"></div>
            <div class="line"></div>
            <div class="line"></div>
            <div class="line"></div>
            <div class="line"></div>
            <div class="line"></div>
            <div class="line"></div>
            <div class="line"></div>
          </div>
        </template>
      </mtd-loading>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.demo-loading-text {
  display: inline-block;
  vertical-align: middle;

  i {
    margin-right: 4px;
  }
}

.demo-loading-text+.demo-loading-text {
  margin-left: 80px;
}

.line-spinner-container {
  --uib-size: 20px;
  --uib-color: black;
  --uib-speed: 1s;
  --uib-stroke: 1.3px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  height: var(--uib-size);
  width: 100%;
}

.line {
  position: absolute;
  top: 0;
  left: calc(50% - var(--uib-stroke) / 2);
  display: flex;
  align-items: flex-start;
  height: 100%;
  width: var(--uib-stroke);
}

.line::before {
  content: '';
  height: 22%;
  width: 100%;
  border-radius: calc(var(--uib-stroke) / 2);
  background-color: var(--uib-color);
  animation: pulse calc(var(--uib-speed)) ease-in-out infinite;
  transition: background-color 0.3s ease;
  transform-origin: center bottom;
}

.line:nth-child(1) {
  transform: rotate(calc(360deg / -12 * 1));

  &::before {
    animation-delay: calc(var(--uib-speed) / -12 * 1);
  }
}

.line:nth-child(2) {
  transform: rotate(calc(360deg / -12 * 2));

  &::before {
    animation-delay: calc(var(--uib-speed) / -12 * 2);
  }
}

.line:nth-child(3) {
  transform: rotate(calc(360deg / -12 * 3));

  &::before {
    animation-delay: calc(var(--uib-speed) / -12 * 3);
  }
}

.line:nth-child(4) {
  transform: rotate(calc(360deg / -12 * 4));

  &::before {
    animation-delay: calc(var(--uib-speed) / -12 * 4);
  }
}

.line:nth-child(5) {
  transform: rotate(calc(360deg / -12 * 5));

  &::before {
    animation-delay: calc(var(--uib-speed) / -12 * 5);
  }
}

.line:nth-child(6) {
  transform: rotate(calc(360deg / -12 * 6));

  &::before {
    animation-delay: calc(var(--uib-speed) / -12 * 6);
  }
}

.line:nth-child(7) {
  transform: rotate(calc(360deg / -12 * 7));

  &::before {
    animation-delay: calc(var(--uib-speed) / -12 * 7);
  }
}

.line:nth-child(8) {
  transform: rotate(calc(360deg / -12 * 8));

  &::before {
    animation-delay: calc(var(--uib-speed) / -12 * 8);
  }
}

.line:nth-child(9) {
  transform: rotate(calc(360deg / -12 * 9));

  &::before {
    animation-delay: calc(var(--uib-speed) / -12 * 9);
  }
}

.line:nth-child(10) {
  transform: rotate(calc(360deg / -12 * 10));

  &::before {
    animation-delay: calc(var(--uib-speed) / -12 * 10);
  }
}

.line:nth-child(11) {
  transform: rotate(calc(360deg / -12 * 11));

  &::before {
    animation-delay: calc(var(--uib-speed) / -12 * 11);
  }
}

@keyframes pulse {

  0%,
  80%,
  100% {
    transform: scaleY(0.75);
    opacity: 0;
  }

  20% {
    transform: scaleY(1);
    opacity: 1;
  }
}
</style>

```

#### index.md

```markdown
# 加载 / Loading
加载用于在网络慢或数据多时，表示正在加载数据的状态。

## 互动演示
:::include(src="./demo/loading-interaction.vue")
::: 

## 代码示例

### 基础用法
:::include(src="./doc/base.vue")
::: 

### 描述文案位置
:::include(src="./doc/position.vue")
::: 

### 自定义指示符
:::include(src="./doc/type.vue")
:::

### 尺寸
:::include(src="./doc/size.vue")
:::

### 容器加载
:::include(src="./doc/container.vue")
:::

### 延迟加载
设置一个延迟时间，低于此时间不显示加载。建议延迟时间不超过500ms，避免用户有明显的等待感。
:::include(src="./doc/delay.vue")
:::

### 全局加载
:::include(src="./doc/fullscreen.vue")
:::

## API
<api-doc name="Loading" :doc="require('./api.json')"></api-doc>

```

#### index.ts

```typescript
import Loading from './loading'
import { withInstall } from '@ss/mtd-adapter'
export default withInstall(Loading)

```

#### line-scale.tsx

```tsx
import { computed, defineComponent } from '@ss/mtd-adapter'
import { useConfig } from '@components/config-provider'



export default defineComponent({
  name: 'LineScaleLoading',
  inheritAttrs: true,
  setup() {
    const config = useConfig()
    const prefixMTD = computed(() => config.getPrefix())
    return {
      prefixMTD,
    }
  },
  render() {
    const { prefixMTD } = this
    return <div class={`${prefixMTD}-loading-line-scale`}>
      <div />
      <div />
      <div />
      <div />
    </div>
  },
})



```

#### loading.tsx

```tsx
import {
  computed,
  defineComponent,
  PropType,
  ref,
  watch, classNames, styles, defineEmits,
  useResetAttrs,
  getSlotsInRender,
} from '@ss/mtd-adapter'
import useConfig from '@hooks/config'
import LineScale from './line-scale'
import MtdCircle from './circle'
import { getPropsText } from '@utils/util'
import { lock, unlock } from '@utils/lock-scroll'

const SizeMapping = {
  'circle': {
    small: {
      size: 14,
      thickness: 1,
    },
    normal: {
      size: 20,
      thickness: 1.5,
    },
    large: {
      size: 28,
      thickness: 2,
    },
  },
  ['line-scale']: {
    small: {
      size: 12,
      thickness: 1,
    },
    normal: {
      size: 18,
      thickness: 1,
    },
    large: {
      size: 24,
      thickness: 2,
    },
  },
}

type Timer = number | undefined;
type loadingSize = 'small' | 'large' | 'normal';
type LoadingType = 'line-scale' | 'circle';
type MessagePosition = 'bottom' | 'right';

export default defineComponent({
  name: 'MtdLoading',
  components: {
    LineScale,
    MtdCircle,
  },
  inheritAttrs: false,
  props: {
    type: {
      type: String as PropType<LoadingType>,
      default: 'circle',
      validator: (v: string) => {
        return !v || ['line-scale', 'circle'].indexOf(v) > -1
      },
    },
    message: {
      type: String,
    },
    messagePosition: {
      type: String as PropType<MessagePosition>,
      default: 'bottom',
      validator: (v: string) => {
        return !v || ['bottom', 'right'].indexOf(v) > -1
      },
    },
    showMessage: {
      type: Boolean,
      default: true,
    },
    loading: {
      type: Boolean,
      default: true,
    },
    delay: {
      type: Number,
      default: 0,
    },
    size: {
      type: String as PropType<loadingSize>,
      default: 'normal',
      validator: (v: string) => {
        return !v || ['small', 'large', 'normal'].indexOf(v) > -1
      },
    },
    fullscreen: {
      type: Boolean,
      default: false,
    },
    /*     style: Object as PropType<any>,
    class: [String, Array, Object], */
  },
  emits: [],
  setup(props, ctx) {
    const config = useConfig()
    const prefix = computed(() => config.getPrefixCls('loading'))
    const m_message = computed(() => getPropsText(props.message, config.t('el.loading.message')))
    const prefixMTD = computed(() => config.getPrefix())

    const resetAttrs = useResetAttrs(ctx.attrs, true)

    const loadingTimer = ref<Timer>()
    const isLoading = ref(false)

    // @Computed
    const loadingComponent = computed(() => props.type === 'line-scale' ? LineScale : MtdCircle)

    // @Watch
    watch(() => props.loading, (v: boolean) => {
      setLoading(v)
      if (v && props.fullscreen) {
        lock(document.body, prefixMTD.value ?? 'mtd')
      } else {
        unlock(document.body, prefixMTD.value ?? 'mtd')
      }
    }, { immediate: true })

    // @Methods
    function setLoading(v: boolean) {
      clearTimeout(loadingTimer.value)
      if (isLoading.value === v) {
        return
      }
      if (props.delay) {
        loadingTimer.value = setTimeout(() => {
          isLoading.value = props.loading
        }, props.delay)
      } else {
        isLoading.value = props.loading
      }
    }

    /* @BeforeMounted */
    clearTimeout(loadingTimer.value)

    return {
      prefix, isLoading, loadingComponent, resetAttrs, m_message,
    }
  },
  render() {
    const { prefix, type, size, m_message, isLoading: loading,
      resetAttrs, showMessage, messagePosition, fullscreen } = this
    const COMP = this.loadingComponent as any
    const mapping = SizeMapping[type] || SizeMapping.circle
    const sizeProp = mapping[size] || mapping.normal

    const renderMsg = getSlotsInRender(this, 'message') || m_message

    const indicatorRender = () => {
      return <div class={{
        [`${prefix}`]: true,
        [`${prefix}-flex`]: messagePosition === 'right',
        [`${prefix}-${size}`]: true,
        // vue2的class会自动继承， vue3环境下此处需要手动继承
        // 这里resetAttrs的ts类型被判断为vue2环境的useResetAttrs返回的类型
        [(resetAttrs as any).class ?? '']: true,
      }} >
        {getSlotsInRender(this, 'indicator') ||
          <COMP
            {...resetAttrs}
            size={sizeProp.size}
            thickness={sizeProp.thickness}
          />}
        {showMessage && <div class={{
          [`${prefix}-message`]: true,
          [`${prefix}-message-${size}`]: true,
          [`${prefix}-message-${size}-right`]: messagePosition === 'right',
        }}>{renderMsg}</div>}
      </div>
    }

    if (fullscreen) {
      if (!loading) {
        return <div class={prefix} />
      }
      return <div class={`${prefix}-fullscreen-container ${loading ? `${prefix}-blur` : ''}`}>
        {loading && indicatorRender()}
      </div>
    }

    if (!getSlotsInRender(this)) {
      if (!loading) {
        return <div class={prefix} />
      }
      return indicatorRender()
    }

    return <div
      class={classNames(this, `${prefix}-nested`)}
      style={styles(this)}
    >
      {loading && indicatorRender()}
      <div
        key="container"
        class={`${prefix}-container ${loading ? `${prefix}-blur` : ''}`}
      >
        {getSlotsInRender(this)}
      </div>
    </div>
  },
})


```

