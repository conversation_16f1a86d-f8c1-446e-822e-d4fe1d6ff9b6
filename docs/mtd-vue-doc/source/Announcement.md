## 源码解析

### 文件依赖

- announcement.tsx
- doc/assist.vue
- doc/base.vue
- doc/button-or-link.vue
- doc/close.vue
- doc/collapse.vue
- doc/global-top.vue
- doc/icon.vue
- doc/link.vue
- doc/local-top.vue
- doc/no-close.vue
- doc/title.vue
- doc/type.vue
- index.md
- index.ts
- types.ts
- usage.json

### 源码内容

#### announcement.tsx

```tsx
import {
  defineComponent,
  PropType,
  computed,
  reactive,
  toRefs,
  getSlotsInRender,
  Transition,
} from '@ss/mtd-adapter'
import useConfig from '@hooks/config'
import { AnnouncementType } from './types'
import MtdIcon from '@components/icon'

const TypeMap = {
  success: 'success-circle',
  info: 'info-circle',
  warning: 'warning-circle',
  error: 'error-circle',
  secondary: 'info-circle',
  notice: 'volume1',
}

export default defineComponent({
  name: 'MtdAnnouncement',
  components: {
    MtdIcon,
  },
  inheritAttrs: true,
  props: {
    title: {
      type: String,
      default: '',
    },
    description: {
      type: String,
      default: '',
    },
    type: {
      type: String as PropType<AnnouncementType>,
      default: 'warning',
    },
    closeable: Boolean, // 兼容历史
    closable: Boolean,

    showIcon: Boolean,
    icon: String,
  },

  emits: ['close'],
  setup(props, { slots, emit }) {
    const config = useConfig()
    const prefix = computed(() => config.getPrefixCls('announcement'))
    const iconPrefix = config.getIconCls

    const state = reactive({
      visible: true,
    })

    // @Computed
    const canClosed = computed(() => props.closable || props.closeable)
    const typeClass = computed(() => ({
      [`${prefix.value}-${props.type}`]: props.type,
      [`${prefix.value}-closable`]: props.closable,
      [`${prefix.value}-has-description`]: props.description || slots.description,
    }))

    const m_iconClass = computed(() => {
      if (props.icon) return iconPrefix(props.icon)
      return props.type ? iconPrefix(TypeMap[props.type]) : iconPrefix('warning')
    })

    // @Methods
    function close() {
      state.visible = false
      emit('close')
    }

    return {
      prefix,
      iconPrefix,
      ...toRefs(state),
      canClosed,
      typeClass,
      m_iconClass,
      close,
    }
  },
  render() {
    const {
      title, visible, showIcon, description,
      prefix, canClosed, typeClass, m_iconClass, icon,
    } = this

    const renderIcon = () => {
      const slotIcon = getSlotsInRender(this, 'icon')
      if (!showIcon && !icon && !slotIcon) return null
      return slotIcon || <i class={[`${prefix}-icon`, m_iconClass]} />
    }

    return <Transition name={`${prefix}-fade`}>
      <div class={[prefix, typeClass]} v-show={visible}>
        {renderIcon()}
        <div class={`${prefix}-content`}>
          <div
            class={{
              [`${prefix}-title`]: true,
              [`${prefix}-title-large`]: description || getSlotsInRender(this, 'description'),
            }}
          >
            {getSlotsInRender(this) || title}
          </div>
          {(description || getSlotsInRender(this, 'description'))
            && <div class={`${prefix}-description`}>
              {getSlotsInRender(this, 'description') || description}
            </div>
          }
          <div class={`${prefix}-close`} v-show={canClosed} onClick={this.close}>
            {getSlotsInRender(this, 'close') || <mtd-icon name={'close-thick'} />}
          </div>
        </div >
      </div >
    </Transition>
  },
})

```

#### doc/assist.vue

```vue
<template>
  <div class="demo-full-width">
    <mtd-announcement
      title="常规信息"
      description="这是一条公告的辅助信息。"
      type="info"
      show-icon />
    <mtd-announcement
      title="成功信息"
      description="这是一条公告的辅助信息。"
      type="success"
      show-icon />
    <mtd-announcement
      title="警告信息"
      description="这是一条公告的辅助信息。"
      type="warning"
      show-icon />
    <mtd-announcement
      title="错误信息"
      description="这是一条公告的辅助信息。"
      type="error"
      show-icon />
      <mtd-announcement
      title="次要信息"
      description="这是一条公告的辅助信息。"
      type="secondary"
      show-icon />
  </div>
</template>

```

#### doc/base.vue

```vue
<template>
  <mtd-announcement
    title="公告信息"
    type="warning" />
</template>

```

#### doc/button-or-link.vue

```vue
<template>
  <mtd-announcement title="公告信息" type="warning" :closable="true" show-icon>
    <div>
      <span>平台推出了新的隐私政策。</span>
      <span style="float:right">
        <mtd-button type="text-primary"><a href="">拒绝</a></mtd-button>
        <mtd-button type="text-primary" style="margin-left: 12px;margin-right: 6px"><a href="">接受</a></mtd-button>
      </span>
    </div>
  </mtd-announcement>
</template>

<style lang="scss">
.demo-announcement-link:hover {
  text-decoration: underline;
  cursor: pointer;
}
</style>

```

#### doc/close.vue

```vue
<template>
    <mtd-announcement title="提示信息" type="warning" show-icon closable @close="handleClose" />
</template>
<script>
export default {
    methods: {
        handleClose() {
            console.log("警告提示已被关闭")
        }
    }
}
</script>


```

#### doc/collapse.vue

```vue
<template>
  <div class="demo-wrapper">
    <mtd-announcement title="公告标题" show-icon closable>
      <template #description>
        <span v-if="!isContentOverflow || isContentExpanded">{{ fullDescription }}</span>
        <span v-else>{{ shortDescription }}</span>
        <div class="collapse" v-if="isContentOverflow" @click="toggleContent">{{ toggleText }}</div>
      </template>
    </mtd-announcement>
  </div>
</template>

<script>
export default {
  data() {
    return {
      isContentOverflow: false,
      isContentExpanded: false,
      fullDescription: `美团的使命是“帮大家吃的更好，生活更好”，公司聚焦“零售+科技”
      战略，和广大商户与各类合作伙伴一起，努力为消费者提供品质生活，推动商品零售和服务零售
      在需求侧和供给侧的数字化转型。2018年9月20日，美团正式在港交所挂牌上市，美团将继续努力，为大家服务`, // 完整的描述文本
      maxLength: 131,
    };
  },
  computed: {
    shortDescription() {
      // 返回截断的描述文本，加上省略号
      return this.fullDescription.slice(0, this.maxLength) + '...';
    },
    toggleText() {
      // 切换按钮的文本
      return this.isContentExpanded ? '收起' : '展开';
    }
  },
  methods: {
    toggleContent(event) {
      // 阻止默认行为并切换内容展开状态
      event.preventDefault();
      this.isContentExpanded = !this.isContentExpanded;
    }
  },
  mounted() {
    // 在组件挂载后判断内容是否超出长度限制
    this.isContentOverflow = this.fullDescription.length > this.maxLength;
  }
};
</script>
<style lang="scss" scoped>
.demo-wrapper {
  .collapse {
    margin-top: 4px;
    color: #166FF7;
    cursor: pointer;
  }
}
</style>
```

#### doc/global-top.vue

```vue
<template>
  <div class="demo-wrapper">
    <mtd-tabs v-model="activeName" type="text" size="small">
      <mtd-tab-pane label="页面顶部" value="页面顶部">
        <div class="top-announcement-t">
          <div class="your-web-t source1">
            <img style="width: 100%;" src="./browser.png">
            <div class="his-content-t his-topbar-t">
              <mtd-announcement title="公告信息" type="warning" :closable="false" show-icon />
              <div class="items-t">
                <div class="first-t"></div>
                <div class="item-t"></div>
                <div class="item-t"></div>
                <div class="item-t"></div>
                <div class="item-t"></div>
                <div class="last-t"></div>
              </div>
            </div>
          </div>
        </div>
      </mtd-tab-pane>

      <mtd-tab-pane label="内容区域顶部" value="内容区域顶部">
        <div class="your-web-t source-t">
          <img style="width: 100%;" src="./browser.png">
          <div class="his-web-t">
            <div class="his-sidebar-t" />
            <div class="right-t">
              <div class="his-content-t his-topbar-t">
                <mtd-announcement class="right-announcement-t" title="提示信息" type="warning" :closable="false" show-icon />
              </div>
              <div class="item2-t">
                <div class="first-2-t"></div>
                <div class="item-2-1-t"></div>
                <div class="item-2-t"></div>
                <div class="item-2-t"></div>
                <div class="item-2-t"></div>
                <div class="last-2-t"></div>
              </div>
            </div>
          </div>
        </div>
      </mtd-tab-pane>

      <mtd-tab-pane label="内容区域内部" value="内容区域内部">
        <div class="your-web-t source-t">
          <img style="width: 100%;" src="./browser.png">
          <div class="his-web-t">
            <div class="his-sidebar-t" />
            <div class="item3-t">
              <div class="item3-top-t">
                <div class="first-3-t"></div>
                <div class="item-3-t"></div>
                <div class="item-3-t"></div>
                <div class="item-3-t"></div>
              </div>
              <div class="his-content-t">
                <mtd-announcement class="center-announcement-t" title="公告信息" type="warning" :closable="false" show-icon />
              </div>
              <div class="item-last-t">
                <div class="last-3-1-t"></div>
                <div class="last-3-1-t"></div>
              </div>
            </div>
          </div>
        </div>
      </mtd-tab-pane>
    </mtd-tabs>
  </div>
</template>
<script>
export default {
  data() {
    return {
      activeName: '页面顶部'
    }
  }
}
</script>
<style lang="scss" scoped>
.his-web-t {
  display: flex;
}

.your-web-t {
  width: 400px;
  height: 240px;
  border: 1px solid #eaeaea;
  border-radius: 1px;
  display: flex;
  flex-direction: column;
}

.items-t {
  display: grid;
  margin-left: 50px;

  .first-t {
    margin-top: 16px;
    width: 78px;
    height: 20px;
    border-radius: 1px;
    background: rgba(0, 0, 0, 0.04);
  }

  .item-t {
    width: 300px;
    height: 16px;
    border-radius: 1px;
    background: rgba(0, 0, 0, 0.04);
    margin-top: 8px
  }

  .last-t {
    width: 150px;
    height: 16px;
    border-radius: 1px;
    background: rgba(0, 0, 0, 0.04);
    margin-top: 8px
  }
}

.his-sidebar-t {
  height: 210px;
  width: 80px;
  background: rgba(0, 0, 0, 0.04);
  border-radius: 6px;
  margin-top: 8px;
  margin-left: 12px;
}

.right-t {
  margin-left: 8px;

  .right-announcement-t {
    width: 288px;
    height: 36px;
    top: 8px;
    border-radius: 6px;
  }
}

.item2-t {
  width: 288px;
  height: 162px;
  margin-top: 24px;
  display: grid;

  .first-2-t {
    width: 65px;
    height: 20px;
    border-radius: 1px;
    background: rgba(0, 0, 0, 0.04);
  }

  .item-2-1-t {
    margin-top: 6px;
    width: 288px;
    height: 16px;
    background: rgba(0, 0, 0, 0.04);
  }

  .item-2-t {
    width: 288px;
    height: 16px;
    border-radius: 1px;
    background: rgba(0, 0, 0, 0.04);
  }

  .last-2-t {
    width: 147px;
    height: 16px;
    border-radius: 1px;
    background: rgba(0, 0, 0, 0.04);

  }
}

.item3-t {
  width: 288px;
  height: 196px;
  margin-top: 10px;
  margin-left: 8px;

  .center-announcement-t {
    width: 288px;
    height: 36px;
    border-radius: 6px;
    top: 12px;
  }

  .item3-top-t {
    width: 88px;

    .first-3-t {
      width: 69px;
      height: 20px;
      border-radius: 1px;
      background: rgba(0, 0, 0, 0.04);
    }

    .item-3-t {
      width: 288px;
      height: 17px;
      border-radius: 1px;
      background: rgba(0, 0, 0, 0.04);
      margin-top: 10px;
    }

    .last-3-1-t {
      width: 288px;
      height: 17px;
      border-radius: 1px;
      background: rgba(0, 0, 0, 0.04);
      margin-top: 10px;
    }
  }

  .item-last-t {
    width: 288px;
    height: 40px;
    margin-top: 25px;

    .last-3-1-t {
      width: 288px;
      height: 16px;
      border-radius: 1px;
      background: rgba(0, 0, 0, 0.04);
      margin-top: 8px;
    }
  }
}

.demo-announcement {
  ::v-deep .demo-source {
    padding: 0 16px;
    width: auto;
  }
}

.demo-wrapper {
  width: 100%;
  margin: 0;

  ::v-deep .mtd-tab-pane {
    padding: 30px;
    display: flex;
    justify-content: space-around;
  }

  ::v-deep .mtd-tabs-nav {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    border-bottom: 1px solid #edf0f7;
    padding: 1.5px 16px;
  }
}
</style>


```

#### doc/icon.vue

```vue
<template>
    <mtd-announcement title="这里是公告信息" icon="bell-o">
        <!-- <template #icon>
            <i class="mtdicon mtdicon-bell-o"></i>
        </template> -->
    </mtd-announcement>
</template>
```

#### doc/link.vue

```vue
<template>
    <mtd-announcement title="公告信息" show-icon closable>
        <span>平台推出了新的<a href="https://mtd.meituan.com/">隐私政策</a></span>
    </mtd-announcement>
</template>
<style scoped>
a {
  text-decoration: underline;
}
</style>
```

#### doc/local-top.vue

```vue
<template>
<div class="row">
  <div class="top-announcment">
    <span>内容区域顶部</span>
  </div>
  <div class="your-web source">
    <img style="width: 100%;" src="./browser.png"><div class="his-web">
    <div class="his-sidebar" />
    <div class="his-content his-topbar">
      <mtd-announcement
        title="公告信息"
        type="warning"
        :closable="false" 
        show-icon/>
    </div>
  </div>
  </div>
  <div class="inner-announcement">
    <span>内容区域内部</span>
  </div>
  <div class="your-web source">
    <img style="width: 100%;" src="./browser.png"><div class="his-web">
    <div class="his-sidebar"/>
    <div class="his-content">
      <mtd-announcement
        title="公告信息"
        type="warning"
        :closable="false"
        show-icon />
    </div>
  </div>
  </div>
</div>
</template>

```

#### doc/no-close.vue

```vue
<template>
  <mtd-announcement
    title="公告信息"
    type="warning"
    :closable="false" />
</template>

```

#### doc/title.vue

```vue
<template>
  <div class="demo-full-width">
    <mtd-announcement
      type="info"
      description="这是一条默认信息公告。"
      show-icon
    >
      <template #default>
        默认信息标题
      </template>
    </mtd-announcement>
    <mtd-announcement
      type="success"
      title="成功信息标题"
      show-icon
    >
      <template #description>
        这是一条成功信息公告。
      </template>
    </mtd-announcement>
    <mtd-announcement
      type="warning"
      title="警告信息标题"
      description="这是一条警告信息公示。"
      show-icon
    />
    <mtd-announcement
      type="error"
      title="失败信息标题"
      description="这是一条失败信息公示。"
      show-icon
    />
    <mtd-announcement
      type="secondary"
      title="次要信息标题"
      description="这是一条次要信息公告。"
      show-icon
    />
    <mtd-announcement
      type="notice"
      title="公告信息标题"
      description="这是一条公告信息。"
      show-icon
    />
  </div>
</template>

```

#### doc/type.vue

```vue
<template>
  <div class="demo-full-width">
    <mtd-announcement
      title="默认信息"
      type="info"
      show-icon
    />
    <mtd-announcement
      title="成功信息"
      type="success"
      show-icon
    />
    <mtd-announcement
      title="警告信息"
      type="warning"
      show-icon
    />
    <mtd-announcement
      title="失败信息"
      type="error"
      show-icon
    />
    <mtd-announcement
      title="次要信息"
      type="secondary"
      show-icon
    />
    <mtd-announcement
      title="公告信息"
      type="notice"
      show-icon
    />
  </div>
</template>

```

#### index.md

```markdown
<style lang='scss'>
.demo-announcement {
  .mtd-announcement:first-child {
    margin: 0;
  }
  .mtd-announcement + .mtd-announcement{
    margin-top: 20px;
  }
}
</style>

# 警告提示/ Announcement
警告提示用于展示需要用户注意的信息。

## 互动演示

<mtd-doc-usage name="announcement" :apiJson="require('./api.json')"  :usageJson="require('./usage.json')">
  <template slot="Announcement" slot-scope="{ configProps }">
    <div>
     <mtd-announcement class="demo-full-width" v-bind='configProps'>提示信息内容</mtd-announcement>
    </div>
  </template>
  <template #Announcement="{ configProps }">
    <div>
     <mtd-announcement class="demo-full-width" v-bind='configProps'>提示信息内容</mtd-announcement>
    </div>
  </template>
</mtd-doc-usage>



## 代码示例
### 信息状态

警告提供五种样式，传达不同的信息状态。
:::include(src="./doc/type.vue")
:::

### 带标题

:::include(src="./doc/title.vue")
:::

### 带操作

支持用户对警告条配置相关操作。
:::include(src="./doc/button-or-link.vue")
:::


### 可折叠

辅助信息过长时支持通过展示收起将文案折叠。
:::include(src="./doc/collapse.vue")
:::

### 可关闭

警告信息默认不可关闭，需要常显，但支持用户配置为可关闭。
:::include(src="./doc/close.vue")
:::


### 展示位置

:::include(src="./doc/global-top.vue")
:::

### 带链接

:::include(src="./doc/link.vue")
:::

### 自定义图标

:::include(src="./doc/icon.vue")
:::


## API
<api-doc name="Announcement" :doc="require('./api.json')"></api-doc>

```

#### index.ts

```typescript
import Announcement from './announcement'
import { withInstall } from '@ss/mtd-adapter'
export default withInstall(Announcement)

```

#### types.ts

```typescript
export type AnnouncementType = 'warning' | 'error' | 'info' | 'success' | 'secondary' | 'notice'

```

#### usage.json

```json
{
    "componentName": "Announcement",
    "desc": "警告提示",
    "codeTemplate": "<mtd-announcement v-bind='configProps'>提示信息内容</mtd-announcement>"
  }
```

