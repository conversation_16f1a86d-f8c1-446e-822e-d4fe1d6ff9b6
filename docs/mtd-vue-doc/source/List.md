## 源码解析

### 文件依赖

- demo.md
- demo/list-interactive.vue
- doc/classify.vue
- doc/collapse.vue
- doc/contextmenu.vue
- doc/demo/group-slot.vue
- doc/demo/slot.vue
- doc/list-item-meta.vue
- doc/load-more.vue
- doc/navbar.vue
- doc/no-border.vue
- doc/normal.vue
- doc/operation.vue
- doc/scroll-load-more.vue
- doc/select.vue
- doc/size.vue
- doc/sortable.vue
- index.md
- index.ts
- list.tsx

### 源码内容

#### demo.md

```markdown
<style lang="scss">
.mtd-list-item-meta-main-title {
  text-align: left;
}
</style>

# 列表 / List 补充Demo

## mtd-list-item-meta插槽补充
:::include(src="./doc/demo/slot.vue")
:::

## mtd-list-item-group插槽补充
:::include(src="./doc/demo/group-slot.vue")
:::

```

#### demo/list-interactive.vue

```vue

<template>
  <div>
    <mtd-doc-usage name="list" :apiJson="apiJson" :usageJson="usageJson" @propChange="propChange">
      <template #list="{ configProps }">
        <div class="slot-container">
          <mtd-list v-bind="configProps" style="height: 186px; overflow: auto;">
            <mtd-list-item-meta 
              v-for="(item, index) in configProps.listData"
              :key="index"
              :title="item.title"
              :description="item.description"
              :image="item.avatar"
              :extra="item.extra"
            > 
            </mtd-list-item-meta>
          </mtd-list>
        </div>
      </template>
    </mtd-doc-usage>
  </div>
</template>
<script>
import apiJson from '../api.json'

export default {
  data() {
    return {
      apiJson: apiJson,
      usageJson: {
        componentName: "list",
        desc: "列表",
        codeTemplate: "<mtd-list v-bind='configProps'></mtd-list>",
        listData: new Array(3).fill({
          title: 'lilu06',
          description: '企业产品设计中心-基础研发产品设计组',
          extra: '2018.02.45',
          avatar: require('../doc/avatar.png'),
        })
      }
    }
  },
  methods: {
    propChange(propertyName, val) {
      console.log('prop changed: , propertyName: ' + propertyName + ', val: ' + val)
    }
  }
}
</script>
<style lang="scss" scoped>
.slot-container {
  width: 440px;
}
</style>
```

#### doc/classify.vue

```vue
<template>
  <div class="demo-full-width">
    <mtd-list size="normal">
      <mtd-list-item-group header="大数据小组">
        <mtd-list-item-meta
          v-for="(item, index) in data"
          :description="item.description"
          :title="item.title"
          :extra="item.extra"
          :image="item.avatar"
          :key="index">
        </mtd-list-item-meta>
      </mtd-list-item-group>
      <mtd-list-item-group header="大中台小组">
        <mtd-list-item-meta
          v-for="(item, index) in data"
          :description="item.description"
          :title="item.title"
          :extra="item.extra"
          :image="item.avatar"
          :key="index">
        </mtd-list-item-meta>
      </mtd-list-item-group>
    </mtd-list>
  </div>
</template>
<script>
export default {
  data () {
    return {
      data: [
        {
          title: 'lilu06',
          description: '企业产品设计中心-基础研发产品设计组',
          extra: '2018.02.45',
          isEdit: false,
          avatar: require('./avatar.png'),
        },
        {
          title: 'lilu06',
          description: '企业产品设计中心-基础研发产品设计组',
          extra: '2018.02.45',
          isEdit: false,
          avatar: require('./avatar.png'),
        },
        {
          title: 'lilu06',
          description: '企业产品设计中心-基础研发产品设计组',
          extra: '2018.02.45',
          isEdit: false,
          avatar: require('./avatar.png'),
        },
      ],
    };
  },
};
</script>

```

#### doc/collapse.vue

```vue
<template>
  <div>
    <div @click="toggleExpended" class="demo-collapse-poem" style="margin-bottom:4px"
      :class="{ 'expended': expended }">
      通知中心 <i style="color: #D3D8E4" class="mtdicon mtdicon-down" />
    </div>
    <mtd-collapse-transition>
      <div v-show="expended">
        <mtd-card  style="width: 500px;margin: 0 auto" class="demo-card-list">
          <mtd-list :split="false">
            <mtd-list-item
              v-for="(item, index) in data"
              :key="index">
              <div class="demo-content">
                <img class="demo-content-avatar" :src="item.avatar">
                <div class="demo-content-main">
                  <div class="demo-content-title">{{ item.title }}</div>
                  <div class="demo-content-description">{{ item.description }}</div>
                </div>
                <div class="demo-extra">{{ item.extra }}</div>
              </div>
            </mtd-list-item>
          </mtd-list>
        </mtd-card>
      </div>
    </mtd-collapse-transition>
  </div>
</template>
<script>
export default {
  data () {
    return {
      expended: false,
      data: new Array(5).fill({
          title: 'lilu06',
          description: '企业产品设计中心-基础研发产品设计组',
          extra: '2018.02.45',
          isEdit: false,
          avatar: require('./avatar.png'),
        }),
    };
  },
  methods: {
    toggleExpended () {
      this.expended = !this.expended;
    },
  },
};
</script>
<style lang='scss' scoped>
  .demo-collapse-poem {
    cursor: pointer;

    .mtdicon-down {
      transition: transform .3s;
      transform: rotate(0deg);
    }

    &.expended {
      .mtdicon-down {
        transform: rotate(180deg);
      }
    }
  }

  .demo-card-list {
    .mtd-card-body {
      padding: 4px;

    }
    .mtd-list {
      border: none;
    }
    .mtd-list-item{
      border-radius: 4px;
    }
  }
</style>

```

#### doc/contextmenu.vue

```vue
<template>
  <mtd-list size="large">
    <mtd-list-item
      v-for="(item, index) in data"
      :key="index">
      <mtd-contextmenu style="cursor: pointer;" :options="[
      {
        label: '编辑',
        handle: () => handleToggle(index),
      },
      {
        label: '删除',
        handle: () => handleRemove(index),
      },
    ]">
      <div class="demo-content" style="align-items:center;">
        <img class="demo-content-avatar" :src="item.avatar">
        <div class="demo-content-main">
          <div class="demo-content-title" v-show="!item.isEdit">{{ item.title }}</div>
          <mtd-input class="demo-content-title" size="small" v-show="item.isEdit"/>
        </div>
        <i :class="`mtdicon-${item.isEdit ? 'success' : 'edit'}-o demo-editable`" @click="handleToggle(index)" />
      </div>
      </mtd-contextmenu>
    </mtd-list-item>
  </mtd-list>
</template>
<script>
export default {
  data () {
    return {
      editable: false,
      data: [
        {
          title: 'lilu01',
          description: '企业产品设计中心-基础研发产品设计组',
          extra: '2018.02.45',
          isEdit: false,
          avatar: require('./avatar.png'),
        },
        {
          title: 'lilu02',
          description: '企业产品设计中心-基础研发产品设计组',
          extra: '2018.02.45',
          isEdit: false,
          avatar: require('./avatar.png'),
        },
        {
          title: 'lilu03',
          description: '企业产品设计中心-基础研发产品设计组',
          extra: '2018.02.45',
          isEdit: false,
          avatar: require('./avatar.png'),
        },
      ],
      contextmenu: []
    };
  },
  created() {
  },
  methods: {
    handleToggle (index) {
      this.$set(this.data[index], 'isEdit', !this.data[index].isEdit);
    },
    handleRemove (index) {
      this.data.splice(index,1)
    }
  },
};
</script>

```

#### doc/demo/group-slot.vue

```vue
<template>
  <div class="demo-full-width">
    <mtd-list>
      <mtd-list-item-group>
        <template #header>自定义slot header</template>
        <mtd-list-item-meta
          v-for="(item, index) in data"
          :description="item.description"
          :title="item.title"
          :extra="item.extra"
          :image="item.avatar"
          :key="index">
        </mtd-list-item-meta>
      </mtd-list-item-group>
    </mtd-list>
  </div>
</template>
<script>
export default {
  data () {
    return {
      data: [
        {
          title: 'lilu06',
          description: '企业产品设计中心-基础研发产品设计组',
          extra: '2018.02.45',
          isEdit: false,
          avatar: require('../avatar.png'),
        },
        {
          title: 'lilu06',
          description: '企业产品设计中心-基础研发产品设计组',
          extra: '2018.02.45',
          isEdit: false,
          avatar: require('../avatar.png'),
        },
        {
          title: 'lilu06',
          description: '企业产品设计中心-基础研发产品设计组',
          extra: '2018.02.45',
          isEdit: false,
          avatar: require('../avatar.png'),
        },
      ],
    };
  },
};
</script>

```

#### doc/demo/slot.vue

```vue
<template>
  <mtd-list size="large">
    <mtd-list-item-meta
      v-for="(item, index) in data"
      :description="item.description"
      :title="item.title"
      :extra="item.extra"
      :image="item.avatar"
      :key="index">
      <template #image>
        <img src="../avatar.png" />
      </template>
      <template #description>
        <span style="color: red">自定义description</span>
      </template>
      <template #title>
        <span style="color: blue">自定义title</span>
      </template>
      <template #extra>
        <span style="color: green">extra</span>
      </template>
    </mtd-list-item-meta>
  </mtd-list>
</template>
<script>
export default {
  data () {
    return {
      editable: false,
      loading: false,
      data: [
        {
          title: 'lilu06',
          description: '企业产品设计中心-基础研发产品设计组',
          extra: '2018.02.45',
          isEdit: false,
          avatar: require('../avatar.png'),
        },
        {
          title: 'lilu06',
          description: '企业产品设计中心-基础研发产品设计组',
          extra: '2018.02.45',
          isEdit: false,
          avatar: require('../avatar.png'),
        },
        {
          title: 'lilu06',
          description: '企业产品设计中心-基础研发产品设计组',
          extra: '2018.02.45',
          isEdit: false,
          avatar: require('../avatar.png'),
        },
      ],
    };
  },
};
</script>

```

#### doc/list-item-meta.vue

```vue
<template>
  <mtd-list size="normal" style="width: 880px;">
    <mtd-list-item-meta
      v-for="(item, index) in data"
      :description="item.description"
      :title="item.title"
      :extra="item.extra"
      :image="item.avatar"
      :key="index">
    </mtd-list-item-meta>
  </mtd-list>
</template>
<script>
export default {
  data () {
    return {
      editable: false,
      loading: false,
      data: [
        {
          title: 'lilu06',
          description: '企业产品设计中心-基础研发产品设计组',
          extra: '2018.02.45',
          isEdit: false,
          avatar: require('../doc/avatar.png'),
        },
        {
          title: 'lilu06',
          description: '企业产品设计中心-基础研发产品设计组',
          extra: '2018.02.45',
          isEdit: false,
          avatar: require('../doc/avatar.png'),
        },
        {
          title: 'lilu06',
          description: '企业产品设计中心-基础研发产品设计组',
          extra: '2018.02.45',
          isEdit: false,
          avatar: require('../doc/avatar.png'),
        },
      ],
    };
  },
};
</script>

```

#### doc/load-more.vue

```vue
<template>
  <div style="width: 100%;">
    <mtd-tabs v-model="avtiveTab" type="text" size="small">
      <mtd-tab-pane label="点击加载" value="click">
        <mtd-list :loading="loading" style="width: 880px;" >
          <mtd-list-item-meta
            v-for="(item, index) in data"
            :description="item.description"
            :title="item.title"
            :extra="item.extra"
            :image="item.avatar"
            :key="index">
          </mtd-list-item-meta>
          <template #loadMore>
            <div @click="handleloadMore" class="demo-more">加载更多</div>
          </template>
        </mtd-list>
      </mtd-tab-pane>
      <mtd-tab-pane label="滚动加载" value="scroll">
        <mtd-list style="height: 250px; overflow:auto; width: 880px;" :loading="loading" @scroll="handleScroll">
          <mtd-list-item-meta
            v-for="(item, index) in data2"
            :description="item.description"
            :title="item.title"
            :extra="item.extra"
            :image="item.avatar"
            :key="index">
          </mtd-list-item-meta>
        </mtd-list>
      </mtd-tab-pane>
    </mtd-tabs>
  </div>

</template>
<script>
export default {
  data () {
    return {
      avtiveTab: 'click',
      loading: false,
      data: [
        {
          title: 'lilu06',
          description: '企业产品设计中心-基础研发产品设计组',
          extra: '2018.02.45',
          isEdit: false,
          avatar: require('./avatar.png'),
        },
        {
          title: 'lilu06',
          description: '企业产品设计中心-基础研发产品设计组',
          extra: '2018.02.45',
          isEdit: false,
          avatar: require('./avatar.png'),
        },
        {
          title: 'lilu06',
          description: '企业产品设计中心-基础研发产品设计组',
          extra: '2018.02.45',
          isEdit: false,
          avatar: require('./avatar.png'),
        },
        {
          title: 'lilu06',
          description: '企业产品设计中心-基础研发产品设计组',
          extra: '2018.02.45',
          isEdit: false,
          avatar: require('./avatar.png'),
        },
      ],
      data2: [
        {
          title: 'lilu06',
          description: '企业产品设计中心-基础研发产品设计组',
          extra: '2018.02.45',
          isEdit: false,
          avatar: require('./avatar.png'),
        },
        {
          title: 'lilu06',
          description: '企业产品设计中心-基础研发产品设计组',
          extra: '2018.02.45',
          isEdit: false,
          avatar: require('./avatar.png'),
        },
        {
          title: 'lilu06',
          description: '企业产品设计中心-基础研发产品设计组',
          extra: '2018.02.45',
          isEdit: false,
          avatar: require('./avatar.png'),
        },
        {
          title: 'lilu06',
          description: '企业产品设计中心-基础研发产品设计组',
          extra: '2018.02.45',
          isEdit: false,
          avatar: require('./avatar.png'),
        },
      ],
    }
  },
  methods: {
    handleScroll({ $event, scrollTop, scrollBottom }) {
      if (scrollBottom <= 0) {
        this.handleloadMore2()
      }
      console.log($event, scrollTop, scrollBottom);
    },
    handleloadMore () {
      this.loading = true
      setTimeout(() => {
        this.data.push(
          {
            title: 'lilu06',
            description: '企业产品设计中心-基础研发产品设计组',
            extra: '2018.02.45',
            avatar: require('./avatar.png'),
          },
        )
        this.loading = false
      }, 500)
    },
    handleloadMore2 () {
      this.loading = true
      setTimeout(() => {
        this.data2.push(
          {
            title: 'lilu06',
            description: '企业产品设计中心-基础研发产品设计组',
            extra: '2018.02.45',
            avatar: require('./avatar.png'),
          },
        )
        this.loading = false
      }, 500)
    },
  },
}
</script>
<style scoped lang="scss">
::v-deep {
  .demo-source {
    padding: 0;
  }
}

.slot-tree-icon-container {
  display: flex;
  align-items: center;
  .slot-tree-icon-size {
    font-size: 16px;
  }
}

::v-deep .mtd-tab-pane {
  margin: 80px auto 50px auto;
  display: flex;
  justify-content: space-around;
}

::v-deep .mtd-tabs-nav {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  border-bottom: 1px solid #edf0f7;
  padding: 1.5px 16px;
}

</style>
```

#### doc/navbar.vue

```vue
<template>
  <mtd-list size="normal">
    <mtd-list-item-meta
      v-for="(item, index) in data"
      :description="item.description"
      :title="item.title"
      :extra="item.extra"
      :image="item.avatar"
      @click.native="handleClick"
      :key="index">
    </mtd-list-item-meta>
  </mtd-list>
</template>
<script>
export default {
  data () {
    return {
      data: [
        {
          title: 'lilu06',
          description: '企业产品设计中心-基础研发产品设计组',
          extra: '2018.02.45',
          isEdit: false,
          avatar: require('./avatar.png'),
        },
        {
          title: 'lilu06',
          description: '企业产品设计中心-基础研发产品设计组',
          extra: '2018.02.45',
          isEdit: false,
          avatar: require('./avatar.png'),
        },
        {
          title: 'lilu06',
          description: '企业产品设计中心-基础研发产品设计组',
          extra: '2018.02.45',
          isEdit: false,
          avatar: require('./avatar.png'),
        },
      ],
    };
  },
  methods: {
    handleClick () {
      this.$router.push('/components/button');
    },
  },
};
</script>

```

#### doc/no-border.vue

```vue
<template>
  <mtd-list size="normal" :border="false" style="width: 880px;background-color: #ffffff;">
    <mtd-list-item-meta
      v-for="(item, index) in data"
      :description="item.description"
      :title="item.title"
      :extra="item.extra"
      :image="item.avatar"
      :key="index">
    </mtd-list-item-meta>
  </mtd-list>
</template>
<script>
export default {
  data () {
    return {
      editable: false,
      loading: false,
      data: [
        {
          title: 'lilu06',
          description: '企业产品设计中心-基础研发产品设计组',
          extra: '2018.02.45',
          isEdit: false,
          avatar: require('../doc/avatar.png'),
        },
        {
          title: 'lilu06',
          description: '企业产品设计中心-基础研发产品设计组',
          extra: '2018.02.45',
          isEdit: false,
          avatar: require('../doc/avatar.png'),
        },
        {
          title: 'lilu06',
          description: '企业产品设计中心-基础研发产品设计组',
          extra: '2018.02.45',
          isEdit: false,
          avatar: require('../doc/avatar.png'),
        },
      ],
    };
  },
};
</script>
<style scoped lang="scss">
::v-deep .demo-source {
  background-color: #F9FAFC;
}
</style>
```

#### doc/normal.vue

```vue
<template>
  <mtd-list size="normal">
    <mtd-list-item
      v-for="(item, index) in data"
      :key="index">
      <div class="demo-content">
        <img class="demo-content-avatar" :src="item.avatar">
        <div class="demo-content-username" >{{'user ' + index}}</div>
      </div>
    </mtd-list-item>
  </mtd-list>
</template>
<script>
export default {
  data () {
    return {
      editable: false,
      loading: false,
      data: [
        {
          title: 'lilu06',
          description: '企业产品设计中心-基础研发产品设计组',
          extra: '2018.02.45',
          isEdit: false,
          avatar: require('./avatar.png'),
        },
        {
          title: 'lilu06',
          description: '企业产品设计中心-基础研发产品设计组',
          extra: '2018.02.45',
          isEdit: false,
          avatar: require('./avatar.png'),
        },
        {
          title: 'lilu06',
          description: '企业产品设计中心-基础研发产品设计组',
          extra: '2018.02.45',
          isEdit: false,
          avatar: require('./avatar.png'),
        },
      ],
    };
  },
};
</script>

```

#### doc/operation.vue

```vue
<template>
  <div style="width: 100%;" class="">
    <mtd-tabs v-model="avtiveTab" type="text" size="small">
      <mtd-tab-pane label="按钮操作" value="button">
        <mtd-list size="normal" style="width: 880px;">
          <mtd-list-item-meta
            v-for="(item, index) in data"
            :description="item.description"
            :title="item.title"
            :image="item.avatar"
            :key="index">
            <template #title>
              <div>
                <div v-show="!item.isEdit">{{ item.title }}</div>
                <mtd-input v-model="item.title" v-show="item.isEdit" @enter="handleEnter(index)"/>
              </div>
            </template>
            <template #extra>
              <div class="extra-icon-box">
                <i style="font-size: 16px; color: #111925;" :class="`${item.isEdit ? 'mtdicon-check' : 'mtdicon-edit-o'} demo-editable`" @click="handleToggle(index)" />
                <i class="mtdicon-delete-o" style="margin-left: 12px; cursor: pointer; font-size: 16px; color: #111925;" @click="handleDelete(index)" />
              </div>
            </template>
          </mtd-list-item-meta>
        </mtd-list>
      </mtd-tab-pane>
      <mtd-tab-pane label="右键操作" value="contextMenu">
        <mtd-list size="normal" style="width: 880px;">
          <mtd-contextmenu class="mtd-context-menu" style="cursor: pointer;" :options="[
            {
              label: '编辑',
              handle: (ele) => handleToggle(findElementIndex(ele)),
            },
            {
              label: '删除',
              handle: (ele) => handleDelete(findElementIndex(ele)),
            },
          ]">
            <mtd-list-item-meta
              v-for="(item, index) in data"
              :description="item.description"
              :title="item.title"
              :image="item.avatar"
              :extra="item.extra"
              :key="index">
              <template #title>
                <div>
                  <div v-show="!item.isEdit">{{ item.title }}</div>
                  <mtd-input v-model="item.title" v-show="item.isEdit" @enter="handleEnter(index)"/>
                </div>
              </template>
            </mtd-list-item-meta>
          </mtd-contextmenu>
        </mtd-list>
      </mtd-tab-pane>
    </mtd-tabs>


  </div>

</template>
<script>

function findElementIndex(contextEle) {
  let listItemElement = null
  let listElement = null

  function recursion(element) {
    if (element.className.includes('mtd-context-menu')) {
      listElement = element
      return
    }
    if (element.className.includes('mtd-list-item')) {
      listItemElement = element
    }
    recursion(element.parentElement)
  }

  recursion(contextEle)

  const index = [...listElement.childNodes].findIndex(ele => ele === listItemElement)

  return index
}
export default {
  data () {
    return {
      avtiveTab: 'button',
      editable: false,
      loading: false,
      data: [
        {
          title: '列表内容列表内容',
          extra: '2018.02.45',
          isEdit: false,
          avatar: require('../doc/avatar.png'),
        },
        {
          title: '列表内容列表内容',
          extra: '2018.02.45',
          isEdit: false,
          avatar: require('../doc/avatar.png'),
        },
        {
          title: '列表内容列表内容',
          extra: '2018.02.45',
          isEdit: false,
          avatar: require('../doc/avatar.png'),
        },
      ],
    };
  },
  methods: {
    findElementIndex,
    handleToggle (index){

      this.$set(this.data[index], 'isEdit', !this.data[index].isEdit);
    },
    handleDelete (index){
      this.data.splice(index, 1);
    },
    handleEnter (index){
      // console.log('index---', index)
      this.$set(this.data[index], 'isEdit', false);
    }
  }
};
</script>
<style scoped lang="scss">
::v-deep {
  .demo-source {
    padding: 0;
  }
}


::v-deep .mtd-tab-pane {
  margin: 80px auto 50px auto;
  display: flex;
  justify-content: space-around;
}

::v-deep .mtd-tabs-nav {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  border-bottom: 1px solid #edf0f7;
  padding: 1.5px 16px;
}


.slot-tree-icon-container {
  display: flex;
  align-items: center;
  .slot-tree-icon-size {
    font-size: 16px;
  }
}

::v-deep .mtd-list-item-meta-content {
  display: flex;
  align-items: center;
}
.extra-icon-box {
  display: flex;
}
</style>
```

#### doc/scroll-load-more.vue

```vue
<template>
  <mtd-list 
    :loading="loading" 
    style="height: 160px; overflow:auto" 
    ref="listRef"
    @scroll="handleScroll"
  >
    <mtd-list-item
      v-for="(item, index) in data"
      :key="index">
      <div class="demo-content">
        <img class="demo-content-avatar" :src="item.avatar">
        <div class="demo-content-main">
          <div class="demo-content-title">{{ `menber` + index }}</div>
        </div>
      </div>
    </mtd-list-item>
  </mtd-list>
</template>
<script>
export default {
  data () {
    return {
      loading: false,
      data: new Array(5).fill({
          title: 'lilu06',
          description: '企业产品设计中心-基础研发产品设计组',
          extra: '2018.02.45',
          isEdit: false,
          avatar: require('./avatar.png'),
        }),
    };
  },
  methods: {
    handleScroll({ $event, scrollTop, scrollBottom }) {
      if (scrollBottom <= 0) {
        this.handleloadMore()
      }
      console.log($event, scrollTop, scrollBottom);
    },
    handleloadMore () {
      this.loading = true;
      setTimeout(() => {
        this.data.push(
          {
            title: 'lilu06',
            description: '企业产品设计中心-基础研发产品设计组',
            extra: '2018.02.45',
            avatar: require('./avatar.png'),
          },
        );
        this.loading = false;
      }, 2000);
    },
  },
};
</script>

```

#### doc/select.vue

```vue
<template>
  <div class="demo-full-width">
    <mtd-tabs v-model="avtiveTab" style="margin-left: 12px;" type="text" size="small">
      <mtd-tab-pane label="多选" value="radio">
        <mtd-list size="normal" style="width: 880px;">
          <mtd-checkbox-group :model-value="checkValues" name="checkbox" @change="handleChange" >
            <mtd-list-item-meta
              v-for="(item, index) in data"
              :description="item.description"
              :title="item.title"
              :extra="item.extra"
              :image="item.avatar"
              :key="index">
              <template #prefix>
                <mtd-checkbox :value="item.title" style="margin-right: 16px"></mtd-checkbox>
              </template>
            </mtd-list-item-meta>
          </mtd-checkbox-group>
        </mtd-list>
      </mtd-tab-pane>
      <mtd-tab-pane label="单选" value="checkbox">
        <mtd-list size="normal" style="width: 880px;">
          <mtd-radio-group :model-value="radioValue" name="radiobox" @change="handleRadioChange" style="display: block;">
            <mtd-list-item-meta
              v-for="(item, index) in data"
              :description="item.description"
              :title="item.title"
              :extra="item.extra"
              :image="item.avatar"
              :key="index">
              <template #prefix>
                <mtd-radio :value="item.title" style="width: 30px; display: block;"></mtd-radio>
              </template>
            </mtd-list-item-meta>
          </mtd-radio-group>
        </mtd-list>
      </mtd-tab-pane>
    </mtd-tabs>
  </div>

</template>
<script>

export default {
  data () {
    return {
      avtiveTab: 'radio',
      checkValues: ['lilu06-1'],
      radioValue: 'lilu06-1',
      data: [
        {
          title: 'lilu06-1',
          description: '企业产品设计中心-基础研发产品设计组',
          extra: '2018.02.45',
          isEdit: false,
          avatar: require('./avatar.png'),
        },
        {
          title: 'lilu06-2',
          description: '企业产品设计中心-基础研发产品设计组',
          extra: '2018.02.45',
          isEdit: false,
          avatar: require('./avatar.png'),
        },
        {
          title: 'lilu06-3',
          description: '企业产品设计中心-基础研发产品设计组',
          extra: '2018.02.45',
          isEdit: false,
          avatar: require('./avatar.png'),
        },
      ],
    };
  },
  methods: {
    handleChange(v) {
      this.checkValues = [...v]
    },
    handleRadioChange(v) {
      this.radioValue = v
    } 
  },
  watch: {
  }
};
</script>
<style scoped lang="scss">
::v-deep {
  .demo-source {
    padding: 0;
  }
}


::v-deep .mtd-tab-pane {
  margin: 80px auto 50px auto;
  display: flex;
  justify-content: space-around;
}

::v-deep .mtd-tabs-nav {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  border-bottom: 1px solid #edf0f7;
  padding: 1.5px 16px;
}

/* .demo-radio-group-vertical{
  display: inline-flex;
  flex-direction: column;
  
  .mtd-radio-normal{
    margin-bottom: 8px;
  }
} */
</style>
```

#### doc/size.vue

```vue
<template>
  <div class="demo-full-width">
    <mtd-tabs v-model="avtiveTab" type="text" size="small">
      <mtd-tab-pane label="小号" value="small">
        <mtd-list style="width: 880px;" size="small">
          <mtd-list-item-meta
            v-for="(item, index) in data"
            :title="item.title"
            :image="item.avatar"
            extra="2018.02.45"
            :key="index"
          >
          </mtd-list-item-meta>
        </mtd-list>
      </mtd-tab-pane>
      <mtd-tab-pane label="中号" :value="undefined">
        <mtd-list style="width: 880px;" :size="undefined">
          <mtd-list-item-meta
            v-for="(item, index) in data"
            :title="item.title"
            :image="item.avatar"
            extra="2018.02.45"
            :key="index"
          >
          </mtd-list-item-meta>
        </mtd-list>
      </mtd-tab-pane>
      <mtd-tab-pane label="大号" value="large">
        <mtd-list style="width: 880px;" size="large">
          <mtd-list-item-meta
            v-for="(item, index) in data"
            :title="item.title"
            :image="item.avatar"
            extra="2018.02.45"
            :key="index"
          >
          </mtd-list-item-meta>
        </mtd-list>
      </mtd-tab-pane>
    </mtd-tabs>
  </div>
</template>
<script>
export default {
  data () {
    return {
      avtiveTab: undefined,
      data: new Array(5).fill({
        title: '企业产品设计中心-基础研发产品设计组',
        extra: '2018.02.45',
        isEdit: false,
        avatar: require('./avatar.png'),
      }),
    };
  },
};
</script>
<style scoped lang="scss">
::v-deep {
  .demo-source {
    padding: 0;
  }
}

::v-deep .mtd-tab-pane {
  margin: 80px auto 50px auto;
  display: flex;
  justify-content: space-around;
}

::v-deep .mtd-tabs-nav {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  border-bottom: 1px solid #edf0f7;
  padding: 1.5px 16px;
}

.slot-tree-icon-container {
  display: flex;
  align-items: center;
  .slot-tree-icon-size {
    font-size: 16px;
  }
}

</style>
```

#### doc/sortable.vue

```vue
<template>
  <mtd-list size="normal" id="mtd-list-sortable-demo">
    <mtd-list-item-meta
      v-for="(item, index) in data"
      :description="item.description"
      :title="item.title"
      :extra="item.extra"
      :image="item.avatar"
      :key="index">
      <template #prefix>
        <mtd-icon name="handle" style="color: rgba(0, 0, 0, 0.35);" class="drag-table-row-icon"/>
      </template>
    </mtd-list-item-meta>
  </mtd-list>
</template>
<script>
import Sortable from 'sortablejs'

export default {
  data () {
    return {
      editable: false,
      loading: false,
      dragging: false,
      data: [
        {
          title: 'lilu06-1',
          description: '企业产品设计中心-基础研发产品设计组',
          extra: '2018.02.45',
          isEdit: false,
          avatar: require('./avatar.png'),
        },
        {
          title: 'lilu06-2',
          description: '企业产品设计中心-基础研发产品设计组',
          extra: '2018.02.45',
          isEdit: false,
          avatar: require('./avatar.png'),
        },
        {
          title: 'lilu06-3',
          description: '企业产品设计中心-基础研发产品设计组',
          extra: '2018.02.45',
          isEdit: false,
          avatar: require('./avatar.png'),
        },
      ],
    };
  },
  mounted() {
    const el = document.querySelector('#mtd-list-sortable-demo')

    new Sortable(el, {
      onStart: (e) => {
        this.dragging = true
      },
      onEnd: (e) => {
        this.dragging = false
        console.log('当前行的被拖拽前的顺序',e.oldIndex) // 当前行的被拖拽前的顺序
        console.log('当前行的被拖拽后的顺序',e.newIndex) // 当前行的被拖拽后的顺序
      },
      // ghostClass: 'drag-table-row-target', // 配置 停靠位置的样式
      handle:".drag-table-row-icon", // 配置 指定拖拽锚点才能拖动父元素，否则是整行支持拖动
      animation: 150,
    })
  },
};
</script>
<style scoped lang="scss">
/* 拖拽停靠位置高亮 */
.drag-table-row-target {
  pointer-events: none;
  background-color: rgb(22, 111, 247,0.1);
}

/* 这个样式是为了 拖拽的时候 隐藏hover样式 */
.table-is-dragging {
  background-color: transparent;
} 

/* 拖拽热点样式 */
.drag-table-row-icon {
  cursor: move;
  margin-right: 12px;
}
</style>
```

#### index.md

```markdown
<style lang="scss">
.mtd-list {
  text-align: left;
}
.demo-content {
  display: flex;
  align-items: center;
  position: relative;
}
.demo-content-avatar {
  width: 28px;
  height: 28px;
  border-radius: 50%;

  &-small {
    width: 20px;
    height: 20px;
  }

  &-large {
    width: 36px;
    height: 36px;
  }
}

.demo-content-username {
  margin-left: 10px;
}

.demo-more {
    text-align: center;
    margin: 9px 0;
    cursor: pointer;
    color: #166FF7;
}
.demo-content-title {
  font-weight:500;
  color: #464646;
}
.demo-content-description {
  font-size: 12px;
  color: #ADADAD;
}
.demo-content-main {
  flex: 1 0;
  margin-left: 16px;
}
.demo-extra {
  font-size: 12px;
  color: #ADADAD;
  position: absolute;
  right: 0;
  top: 0;
}
.demo-small {
  font-size: 12px;
}
.demo-normal {
  font-size: 14px;
}
.demo-large {
  font-size: 16px;
}
.demo-editable {
  color: rgba(0,0,0,0.9);
  cursor: pointer;
}
</style>
# 列表 / List
多组同类、同级复杂数据信息的纵向排列，满足信息展示和操作指引等交互场景

## 互动演示
:::include(src="./demo/list-interactive.vue")
:::

## 代码示例
### 尺寸
列表提供了小、中（默认）、大三种尺寸。
:::include(src="./doc/size.vue")
:::

### 列表元素
列表元素包含主要信息、次要信息、辅助信息、图片。
:::include(src="./doc/list-item-meta.vue")
:::

### 无边框
:::include(src="./doc/no-border.vue")
:::

### 可操作
可配合操作按钮进行使用。
:::include(src="./doc/operation.vue")
:::

### 可拖拽
:::include(src="./doc/sortable.vue")
:::

### 可选择
:::include(src="./doc/select.vue")
:::

### 分类列表
列表可以按类型将信息进行分类
:::include(src="./doc/classify.vue")
:::

### 导航（作为导航目录）
:::include(src="./doc/navbar.vue")
:::

### 加载方式
:::include(src="./doc/load-more.vue")
:::

## API
<api-doc name="List" :doc="require('./api.json')"></api-doc>
<api-doc name="ListItem" :doc="require('./../list-item/api.json')"></api-doc>

<api-doc name="ListItemMeta" :doc="require('./../list-item-meta/api.json')"></api-doc>
<api-doc name="ListItemGroup" :doc="require('./../list-item-group/api.json')"></api-doc>

```

#### index.ts

```typescript
import List from './list'
import { withInstall } from '@ss/mtd-adapter'
export default withInstall(List)

```

#### list.tsx

```tsx
import {
  computed,
  defineComponent,
  getSlotsInRender,
} from '@ss/mtd-adapter'
import useConfig from '@hooks/config'
import MtdLoading from '@components/loading'
export default defineComponent({
  name: 'MtdList',
  components: {
    MtdLoading,
  },
  inheritAttrs: true,
  props: {
    size: {
      type: String,
      default: 'normal',
    },
    loading: {
      type: Boolean,
      default: false,
    },
    split: {
      type: Boolean,
      default: true,
    },
    border: {
      type: Boolean,
      default: true,
    },
    loadingText: {
      type: String,
      default: '正在加载中',
    },
  },
  emits: ['scroll'],
  setup() {
    const config = useConfig()
    const prefix = computed(() => config.getPrefixCls('list'))
    return {
      prefix,
    }
  },
  methods: {
    handleScroll(e: WheelEvent | Event) {
      const listElement = this.$el as HTMLElement
      const { scrollTop, scrollHeight } = listElement
      this.$emit('scroll', {
        $event: e,
        scrollTop,
        scrollBottom: scrollHeight - listElement.getBoundingClientRect().height - scrollTop,
      })
    },
  },
  render() {
    const {
      prefix, size, loading, split, border, loadingText,
    } = this
    return <div
      class={{
        [prefix]: true,
        [`${prefix}-${size}`]: size,
        [`${prefix}-split`]: split,
        [`${prefix}-no-border`]: !border,
      }}
      onScroll={this.handleScroll}
    >
      {getSlotsInRender(this)}

      {!loading
        ? getSlotsInRender(this, 'loadMore')
        : <div class={`${prefix}-loading`}>
          <mtd-loading message-position="right" message={loadingText}/>
        </div>
      }
    </div >
  },
})

```

