## 源码解析

### 文件依赖

- breadcrumb-item-group.tsx
- breadcrumb-item.tsx
- breadcrumb.tsx
- demo/breadcrumb-interactive.vue
- doc/base.vue
- doc/child-node.vue
- doc/current-style.vue
- doc/custom-separator.vue
- doc/dropmenu.vue
- doc/icon.vue
- doc/size.vue
- index.md
- index.ts
- tests/__snapshots__/breadcrumb.spec.jsx.snap
- tests/breadcrumb.spec.jsx

### 源码内容

#### breadcrumb-item-group.tsx

```tsx
import { defineComponent, getSlotsInRender, vSlots } from '@ss/mtd-adapter'
import useConfig from '@hooks/config'
import { useBreadcrumb } from './breadcrumb'
import { createError } from '@utils/log'
import DropdownMenu from '@components/dropdown-menu'
import Dropdown from '@components/dropdown'
import MtdIcon from '@components/icon'

export default defineComponent({
  name: 'MtdBreadcrumbItemGroup',
  components: {
    DropdownMenu,
    Dropdown,
    MtdIcon,
  },
  inheritAttrs: true,
  props: {
    text: {
      type: String,
      default: '...',
    },
  },
  setup() {
    const config = useConfig()
    const prefix = config.getPrefixCls('breadcrumb-item-group')
    const breadcrumb = useBreadcrumb()
    if (!breadcrumb) {
      throw createError('MtdBreadcrumbItemGroup', 'MtdBreadcrumbItemGroup must be used in MtdBreadcrumb')
    }

    return {
      separator: breadcrumb.separator,
      separatorIcon: breadcrumb.separatorIcon,
      separatorClass: breadcrumb.separatorClass,
      breadcrumbPrefix: breadcrumb.breadcrumbPrefix,
      prefix,
      getIconCls: config.getIconCls,
    }
  },
  render() {
    const {
      text, prefix,
      separatorClass, separator,
      breadcrumbPrefix,
      separatorIcon,
    } = this

    const children = <span class={`${breadcrumbPrefix}-inner`}>
      {getSlotsInRender(this, 'text') || text}
    </span>

    const slots = {
      dropdown: () => <DropdownMenu>{getSlotsInRender(this)}</DropdownMenu>,
    }

    return (
      <span class={[`${prefix}`]}>
        <Dropdown
          {...vSlots(slots)}
          v-slots={slots}
        >
          {children}
        </Dropdown>
        <span class={[`${breadcrumbPrefix}-separator`, separatorClass]}>
          {separator || <mtd-icon name={separatorIcon || 'slash' } />}
        </span>
      </span>
    )
  },
})

```

#### breadcrumb-item.tsx

```tsx
import { defineComponent, useResetAttrs, getSlotsInRender, classNames, styles, RouterLink } from '@ss/mtd-adapter'
import useConfig from '@hooks/config'
import { useBreadcrumb } from './breadcrumb'
import { createError } from '@utils/log'
import MtdIcon from '@components/icon'

export default defineComponent({
  name: 'MtdBreadcrumbItem',
  components: {
    MtdIcon,
  },
  inheritAttrs: false,
  props: {
    tag: {
      type: String,
      default: 'span',
    },
    icon: String,
    disabled: Boolean,
  },
  setup(props, { attrs }) {
    const config = useConfig()
    const prefix = config.getPrefixCls('breadcrumb-item')
    const iconPrefix = config.getIconCls

    const breadcrumb = useBreadcrumb()
    if (!breadcrumb) {
      throw createError('MtdBreadcrumbItem', 'MtdBreadcrumbItem must be used in MtdBreadcrumb')
    }

    const resetAttrs = useResetAttrs(attrs)

    return {
      separator: breadcrumb.separator,
      separatorClass: breadcrumb.separatorClass,
      breadcrumbPrefix: breadcrumb.breadcrumbPrefix,
      separatorIcon: breadcrumb.separatorIcon,
      prefix, iconPrefix,
      resetAttrs,
    }
  },
  render() {
    const {
      prefix, iconPrefix,
      resetAttrs,
      separatorClass,
      separator,
      separatorIcon,
      breadcrumbPrefix,
      disabled,
    } = this
    const { tag, icon } = this.$props
    const Component = (tag as any) === 'router-link' ? RouterLink : tag
    const isIconOnly = icon && !getSlotsInRender(this)

    return <span
      class={classNames(this, prefix)}
      style={styles(this)}
    >
      <Component
        class={`${breadcrumbPrefix}-inner ${disabled ? `${prefix}-disabled` : ''}`}
        style={undefined}
        {...resetAttrs}
      >
        {icon && <i class={[`${prefix}-icon`, iconPrefix(icon), isIconOnly ? `${prefix}-icon-only` : '']} />}
        {getSlotsInRender(this)}
      </Component>
      <span class={[`${breadcrumbPrefix}-separator`, separatorClass]}>
        {separator || <mtd-icon name={separatorIcon || 'slash' } />}
      </span>
    </span>
  },
})

```

#### breadcrumb.tsx

```tsx
import {
  computed,
  defineComponent,
  inject,
  PropType,
  provide,
  Ref,
  getSlotsInRender,
} from '@ss/mtd-adapter'
import useConfig from '@hooks/config'
import { MTDUIComponentSize } from '@components/types'

interface BreadcrumbContext {
  separator: Ref<string | undefined>;
  separatorClass: Ref<string>;
  separatorIcon?: Ref<string | undefined>;
  size?: Ref<MTDUIComponentSize>;
  breadcrumbPrefix: string;
}

export function useBreadcrumbProvider(context: BreadcrumbContext) {
  provide<BreadcrumbContext>('breadcrumb', context)
}

export function useBreadcrumb(): BreadcrumbContext | undefined {
  return inject<BreadcrumbContext>('breadcrumb')
}

export default defineComponent({
  name: 'MtdBreadcrumb',
  inheritAttrs: true,
  props: {
    separator: {
      type: String,
    },
    separatorIcon: {
      type: String,
    },
    separatorClass: {
      type: String,
    },
    size: {
      type: String as PropType<MTDUIComponentSize>,
      default: '',
      validator: (v: string) => {
        return ['', 'small', 'large'].indexOf(v) > -1
      }, 
    },
    currentStyle: {
      type: String,
      default: 'normal',
      validator: (v: string) => {
        return ['none', 'normal', 'bold'].indexOf(v) > -1
      },
    },
  },
  setup(props) {
    const config = useConfig()
    const prefix = config.getPrefixCls('breadcrumb')

    useBreadcrumbProvider({
      separator: computed(() => props.separator),
      separatorClass: computed(() => props.separatorClass || ''),
      size: computed(() => props.size),
      breadcrumbPrefix: prefix,
      separatorIcon: computed(() => props.separatorIcon),
    })
    return { prefix }
  },
  render() {
    const { prefix, currentStyle } = this
    const { size } = this.$props
    return (
      <div class={[prefix, `${prefix}-cur-${currentStyle}`, size ? `${prefix}-${size}` : '']}>
        {getSlotsInRender(this)}
      </div>
    )
  },
})

```

#### demo/breadcrumb-interactive.vue

```vue

<template>
  <div>
    <mtd-doc-usage name="breadcrumb" :apiJson="apiJson" :usageJson="usageJson" @propChange="propChange">
      <template #breadcrumb="{ configProps }">
        <div class="slot-container">
          <mtd-breadcrumb v-bind="configProps">
            <mtd-breadcrumb-item>面包屑1</mtd-breadcrumb-item>
            <mtd-breadcrumb-item>面包屑2</mtd-breadcrumb-item>
            <mtd-breadcrumb-item>面包屑3</mtd-breadcrumb-item>
          </mtd-breadcrumb>
        </div>
      </template>
    </mtd-doc-usage>
  </div>
</template>
<script>
import apiJson from '../api.json'

export default {
  data() {
    return {
      apiJson: apiJson,
      usageJson: {
        componentName: "breadcrumb",
        desc: "面包屑",
        codeTemplate: "<mtd-breadcrumb v-bind='configProps'></mtd-breadcrumb>"
      }
    }
  },
  methods: {
    propChange(propertyName, val) {
      console.log('prop changed: , propertyName: ' + propertyName + ', val: ' + val)
    }
  }
}
</script>
<style lang="scss" scoped>
  
</style>
  
```

#### doc/base.vue

```vue
<template>
  <mtd-breadcrumb>
    <mtd-breadcrumb-item tag="router-link" to="/">面包屑1</mtd-breadcrumb-item>
    <mtd-breadcrumb-item tag="router-link" to="/">面包屑2</mtd-breadcrumb-item>
    <mtd-breadcrumb-item>面包屑3</mtd-breadcrumb-item>
  </mtd-breadcrumb>
</template>

```

#### doc/child-node.vue

```vue
<template>
    <mtd-breadcrumb style="margin-bottom:32px">
      <mtd-breadcrumb-item tag="router-link" to="/"
        >面包屑1</mtd-breadcrumb-item
      >
      <mtd-breadcrumb-item-group>
        <template #text>
          <div>
            <span>面包屑2</span>
            <i class="mtdicon mtdicon-down-thick" style="font-size:16px;margin-left:4px;vertical-align: -15%" />
          </div>
        </template>
        <mtd-dropdown-menu-item tag="router-link" to="/">
          面包屑2.1
        </mtd-dropdown-menu-item>
        <mtd-dropdown-menu-item tag="router-link" to="/">
          面包屑2.2
        </mtd-dropdown-menu-item>
      </mtd-breadcrumb-item-group>
      <mtd-breadcrumb-item tag="router-link" to="/"
        >面包屑3</mtd-breadcrumb-item
      >
    </mtd-breadcrumb>
</template>
```

#### doc/current-style.vue

```vue
<template>
  <div class="demo-breadcrumb-group source">
    <div>
      <p style="text-align: left;">隐藏当前</p>
      <mtd-breadcrumb current-style="none">
        <mtd-breadcrumb-item tag="router-link" to="/"
          >面包屑1</mtd-breadcrumb-item
        >
        <mtd-breadcrumb-item tag="router-link" to="/"
          >面包屑2</mtd-breadcrumb-item
        >
        <mtd-breadcrumb-item tag="router-link" to="/"
          >面包屑3</mtd-breadcrumb-item
        >
      </mtd-breadcrumb>
    </div>
    <div>
      <p style="text-align: left;">作为标题（加粗）</p>
      <mtd-breadcrumb current-style="bold">
        <mtd-breadcrumb-item tag="router-link" to="/"
          >面包屑1</mtd-breadcrumb-item
        >
        <mtd-breadcrumb-item tag="router-link" to="/"
          >面包屑2</mtd-breadcrumb-item
        >
        <mtd-breadcrumb-item tag="router-link" to="/"
          >面包屑3</mtd-breadcrumb-item
        >
      </mtd-breadcrumb>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.demo-breadcrumb-group {
  width: 80%;
  display: flex;
  justify-content: space-around;
  margin: 80px auto;
}
</style>

```

#### doc/custom-separator.vue

```vue
<template>
  <div class="demo-breadcrumb-group source">
    <div>
      <p style="text-align: left;">默认斜线</p>
      <mtd-breadcrumb>
        <mtd-breadcrumb-item tag="router-link" to="/">面包屑1</mtd-breadcrumb-item>
        <mtd-breadcrumb-item tag="router-link" to="/">面包屑2</mtd-breadcrumb-item>
        <mtd-breadcrumb-item>面包屑3</mtd-breadcrumb-item>
      </mtd-breadcrumb>
    </div>
    <div>
      <p style="text-align: left;">自定义</p>
      <mtd-breadcrumb separator-icon="right-thick">
        <mtd-breadcrumb-item tag="router-link" to="/">面包屑1</mtd-breadcrumb-item>
        <mtd-breadcrumb-item tag="router-link" to="/">面包屑2</mtd-breadcrumb-item>
        <mtd-breadcrumb-item>面包屑3</mtd-breadcrumb-item>
      </mtd-breadcrumb>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.demo-breadcrumb-group {
  width: 80%;
  display: flex;
  justify-content: space-around;
  margin: 80px auto;
}
</style>

```

#### doc/dropmenu.vue

```vue
<template>
  <div class="demo-breadcrumb-group source">
    <div>
      <p style="text-align: left;">前部折叠</p>
      <mtd-breadcrumb>
        <mtd-breadcrumb-item-group text="...">
          <mtd-dropdown-menu-item tag="router-link" to="/">
            面包屑1
          </mtd-dropdown-menu-item>
          <mtd-dropdown-menu-item tag="router-link" to="/">
            面包屑2
          </mtd-dropdown-menu-item>
        </mtd-breadcrumb-item-group>
        <mtd-breadcrumb-item tag="router-link" to="/"
          >面包屑3</mtd-breadcrumb-item
        >
        <mtd-breadcrumb-item tag="router-link" to="/"
          >面包屑4</mtd-breadcrumb-item
        >
        <mtd-breadcrumb-item tag="router-link" to="/"
          >面包屑5</mtd-breadcrumb-item
        >
      </mtd-breadcrumb>
    </div>
    <div>
      <p style="text-align: left;">中部折叠</p>
      <mtd-breadcrumb style="margin-bottom:32px">
        <mtd-breadcrumb-item tag="router-link" to="/"
          >面包屑1</mtd-breadcrumb-item
        >
        <mtd-breadcrumb-item-group>
          <mtd-dropdown-menu-item tag="router-link" to="/">
            面包屑2
          </mtd-dropdown-menu-item>
          <mtd-dropdown-menu-item tag="router-link" to="/">
            面包屑3
          </mtd-dropdown-menu-item>
        </mtd-breadcrumb-item-group>
        <mtd-breadcrumb-item tag="router-link" to="/"
          >面包屑4</mtd-breadcrumb-item
        >
        <mtd-breadcrumb-item tag="router-link" to="/"
          >面包屑5</mtd-breadcrumb-item
        >
      </mtd-breadcrumb>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.demo-breadcrumb-group {
  width: 80%;
  display: flex;
  justify-content: space-around;
  margin: 80px auto;
}
</style>

```

#### doc/icon.vue

```vue
<template>
  <div class="breadcrumb-container">
    <mtd-breadcrumb>
      <mtd-breadcrumb-item
        tag="router-link"
        to="/"
        icon="home-o"
      />
      <mtd-breadcrumb-item tag="router-link" to="/" icon="mtdicon-smile-o
">面包屑1</mtd-breadcrumb-item>
      <mtd-breadcrumb-item tag="router-link" to="/" icon="mtdicon-smile-o
">面包屑2</mtd-breadcrumb-item>
<mtd-breadcrumb-item tag="router-link" to="/" icon="mtdicon-smile-o
">面包屑3</mtd-breadcrumb-item>
    </mtd-breadcrumb>
  </div>
  
</template>
<style scoped lang="scss">
.breadcrumb-container {
  margin: 80px auto;
}
</style>
```

#### doc/size.vue

```vue
<template>
  <div class="demo-breadcrumb-group source">
    <div>
      <p style="text-align: left;">小尺寸</p>
      <mtd-breadcrumb size="small">
        <mtd-breadcrumb-item tag="router-link" to="/"
          >面包屑1</mtd-breadcrumb-item
        >
        <mtd-breadcrumb-item tag="router-link" to="/"
          >面包屑2</mtd-breadcrumb-item
        >
        <mtd-breadcrumb-item tag="router-link" to="/"
          >面包屑3</mtd-breadcrumb-item
        >
      </mtd-breadcrumb>
    </div>
    <div>
      <p style="text-align: left;">中尺寸</p>
      <mtd-breadcrumb>
        <mtd-breadcrumb-item tag="router-link" to="/"
          >面包屑1</mtd-breadcrumb-item
        >
        <mtd-breadcrumb-item tag="router-link" to="/"
          >面包屑2</mtd-breadcrumb-item
        >
        <mtd-breadcrumb-item tag="router-link" to="/"
          >面包屑3</mtd-breadcrumb-item
        >
    </mtd-breadcrumb>
    </div>
    <div>
      <p style="text-align: left;">大尺寸</p>
      <mtd-breadcrumb size="large">
        <mtd-breadcrumb-item tag="router-link" to="/"
          >面包屑1</mtd-breadcrumb-item
        >
        <mtd-breadcrumb-item tag="router-link" to="/"
          >面包屑2</mtd-breadcrumb-item
        >
        <mtd-breadcrumb-item tag="router-link" to="/"
          >面包屑3</mtd-breadcrumb-item
        >
      </mtd-breadcrumb>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.demo-breadcrumb-group {
  width: 100%;
  display: flex;
  justify-content: space-around;
  margin: 80px auto;
}
</style>

```

#### index.md

```markdown
# 面包屑 / Breadcrumb

用于展示当前页面的路径或层级，并且能够快速返回之前的任意页面。

## 互动演示
:::include(src="./demo/breadcrumb-interactive.vue")
:::

## 代码示例
### 尺寸
面包屑支持小、中、大三种尺寸。
:::include(src="./doc/size.vue")
:::

### 带图标
面包屑导航中的每个层级可以配合图标一起使用，其中首个层级作为「首页」允许只保留图标。
:::include(src="./doc/icon.vue")
:::

### 自定义分隔符
面包屑导航中每个层级间的分隔符默认为「/」，并允许自定义替换为其他图标。
:::include(src="./doc/custom-separator.vue")
:::


### 折叠
面包屑导航中可以在「中部」和「前部」进行层级的折叠，并通过下拉组件展示折叠的节点，折叠情况下最少暴露三个层级。
:::include(src="./doc/dropmenu.vue")
:::


### 子节点下拉
多个平行层级的子节点可以通过节点下拉快速切换。
:::include(src="./doc/child-node.vue")
:::


### 当前节点
当前节点允许被隐藏或加粗使用
:::include(src="./doc/current-style.vue")
:::


## API

<api-doc name="Breadcrumb" :doc="require('./api.json')"></api-doc>
<api-doc name="BreadcrumbItemGroup" :doc="require('../breadcrumb-item-group/api.json')"></api-doc>
<api-doc name="BreadcrumbItem" :doc="require('../breadcrumb-item/api.json')"></api-doc>

```

#### index.ts

```typescript
import Breadcrumb from './breadcrumb'
import { withInstall } from '@ss/mtd-adapter'

export default withInstall(Breadcrumb)

```

#### tests/__snapshots__/breadcrumb.spec.jsx.snap

```plaintext
// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Breadcrumb 动态切换 separator 1`] = `<div class="mtd-breadcrumb"><span class="mtd-breadcrumb-item"><span class="mtd-breadcrumb-inner ">首页</span><span class="mtd-breadcrumb-separator">/</span></span><span class="mtd-breadcrumb-item"><span class="mtd-breadcrumb-inner ">列表页</span><span class="mtd-breadcrumb-separator">/</span></span><span class="mtd-breadcrumb-item"><span class="mtd-breadcrumb-inner ">详情页</span><span class="mtd-breadcrumb-separator">/</span></span></div>`;

```

#### tests/breadcrumb.spec.jsx

```jsx
import Breadcrumb from '@components/breadcrumb'
import BreadcrumbItem from '@components/breadcrumb-item'
import { createDemoTest, mount, waitImmediate } from '@tests/utils';

describe('Breadcrumb', function () {
  createDemoTest('breadcrumb')
  it('动态切换 separator', async function () {
    const wrapper = mount({
      data () {
        return {
          separator: '>'
        }
      },
      render() {
        return <Breadcrumb separator={this.separator}>
          <BreadcrumbItem>首页</BreadcrumbItem>
          <BreadcrumbItem>列表页</BreadcrumbItem>
          <BreadcrumbItem>详情页</BreadcrumbItem>
        </Breadcrumb>
      }
    })
    expect(wrapper.find('.mtd-breadcrumb-separator').text()).toBe('>')
    await wrapper.setData({ separator: '/' })
    expect(wrapper.find('.mtd-breadcrumb-separator').text()).toBe('/')
    expect(wrapper.html()).toMatchSnapshot()
  })
})

```

