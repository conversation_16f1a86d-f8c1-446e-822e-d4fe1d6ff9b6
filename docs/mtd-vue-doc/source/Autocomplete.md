## 源码解析

### 文件依赖

- autocomplete.tsx
- demo.md
- doc/base.vue
- doc/custom.vue
- doc/customOption.vue
- doc/demo/methods.vue
- doc/demo/no-validate.vue
- doc/filter.vue
- doc/remote.vue
- doc/super-custom.vue
- index.md
- index.ts
- option-wrap.tsx

### 源码内容

#### autocomplete.tsx

```tsx
import {
  computed,
  defineComponent,
  onMounted,
  PropType,
  ref,
  watch,
  useResetAttrs,
  useListeners,
  getSlotsInRender,
  vSlots,
  toProps,
  getAllScopedSlots,
  normalizeSlots,
} from '@ss/mtd-adapter'
import useConfig from '@hooks/config'

import {
  isKeyEsc,
  isKeyTab, isKey,
} from '@components/picker/util'
import MtdPicker from '@components/picker'
import MtdInput from '@components/input'
import { focus as domFocus } from '@utils/dom'
import MtdOption from '@components/option'
import { useFormItem } from '@components/form-item/useFormItem'
/*
 * import NavigationMixin from '@components/select/navigation-mixin'
 * import { AutocompleteData, IOption } from 'types/autocomplete'
 * import { INavigationMixin, SelectProvider } from 'types/select'
 */

export default defineComponent({
  name: 'MtdAutocomplete',
  components: {
    MtdPicker,
    MtdInput,
    MtdOption,
  },
  inheritAttrs: true,
  model: {
    prop: 'modelValue',
    event: 'update:modelValue',
  },
  props: {
    modelValue: [String, Number],
    prefixIcon: String,
    suffixIcon: String,
    size: String,
    data: {
      type: Array as PropType<string[]>,
      default: () => [],
    },
    clearable: Boolean,
    disabled: Boolean,
    // disabled: Boolean, // defined in Input
    readonly: Boolean,
    popperClass: String,
    filterMethod: {
      type: [Function, Boolean],
      default: null,
    },
    appendToContainer: {
      type: Boolean,
      default: true,
    },
    getPopupContainer: Function,
    popperOptions: Object,
    formNoValidate: Boolean,
  },
  emits: ['search', 'blur', 'focus', 'input', 'change', 'select', 'clear', 'update:visible', 'update:modelValue',
    'click-prefix', 'click-suffix',
  ],
  setup(props, { emit, attrs }) {
    const config = useConfig()

    const prefix = computed(() => config.getPrefixCls('autocomplete'))
    const myAttrs = useResetAttrs(attrs, true)
    const formItemHook = useFormItem(props, { emit, attrs })


    const m_size = formItemHook.m_size
    const m_disabled = formItemHook.disabled

    const dataOptions = computed(() => {
      return props.data.map((val: string) => {
        return {
          value: val,
          label: val,
        }
      })
    })

    const dropdownVisible = ref(false)
    const minWidth = ref<string | number>(0)

    const renderedRef = ref<any | typeof MtdInput>(null)
    const pickerRef = ref<any | null>(null)

    watch(dropdownVisible, visible => {
      if (visible) {
        computedInputWidth()
      } else {
        // renderedRef.value && renderedRef.value.reset()
      }
    })
    onMounted(() => {
      renderedRef.value = pickerRef.value && pickerRef.value.$refs.renderedRef
      computedInputWidth()
    })

    watch(() => props.formNoValidate, (newVal) => {
      if (newVal) {
        formItemHook.clearValidate()
      }
    })

    // const noop = () => {}
    const toggleDropdown = (v: any) => {
      if (v === undefined) {
        v = !dropdownVisible.value
      }

      if (dropdownVisible.value === v) {
        return
      }

      dropdownVisible.value = v
      emit('update:visible', v)
    }

    const openDropdown = () => { toggleDropdown(true) }

    const closeDropdown = () => { toggleDropdown(false) }

    // private
    const handleValueChange = (v: any) => {
      emit('update:modelValue', v)
      emit('input', v)
      formItemHook.m_handleChange(v)
      emit('select', v)
      if (dropdownVisible.value) {
        closeDropdown()
      }
    }

    const handleClear = () => {
      const value = ''
      emit('clear')
      emit('update:modelValue', value)
      emit('input', value)
      formItemHook.m_handleChange(value)
    }

    const handleFocus = (e: Event) => {
      if (!dropdownVisible.value) {
        emit('focus', e)
        openDropdown()
      }
    }

    const handleBlur = (e: Event) => {
      if (!dropdownVisible.value) {
        // renderedRef.value && renderedRef.value.reset()
        formItemHook.m_handleBlur(e)
      }
    }

    const handleClickoutside = () => {
      closeDropdown()
    }

    const handleKeydown = (e: KeyboardEvent) => {
      if (!dropdownVisible.value) {
        if (!isKey('space', e) && !isKey('tab', e)
          && !isKey('esc', e)) {
          openDropdown()
        }
      } else if (isKeyEsc(e) || isKeyTab(e)) {
        closeDropdown()
        return
      }

      pickerRef.value && pickerRef.value.handleKeydown(e)
    }

    const handleInput = (v: string) => {
      emit('update:modelValue', v)
      emit('input', v)
      formItemHook.m_handleChange(v)
      emit('search', v)

      if (typeof props.filterMethod === 'function') {
        pickerRef.value.filter(v)
      }
    }

    const handleQuery = (v: string) => {
      emit('update:modelValue', v)
      emit('input', v)
      formItemHook.m_handleChange(v)
      emit('search', v)
    }

    const computedInputWidth = () => {
      if (renderedRef.value && !minWidth.value) {
        const w = renderedRef.value.$el.getBoundingClientRect().width // $el 💣直接取得风险
        if (w) {
          minWidth.value = `${w}px`
        }
      }
    }

    const handlePrefixClick = (e: Event) => {
      domFocus(renderedRef)
      emit('click-prefix', e)
    }

    const handleSuffixClick = (e: Event) => {
      domFocus(renderedRef)
      emit('click-suffix', e)
    }

    // public method
    const focus = () => {
      renderedRef.value.focus()
    }

    // public method
    const blur = () => {
      closeDropdown()
      renderedRef.value.blur()
    }

    // public method
    const toggle = (v) => {
      toggleDropdown(v)
    }

    const resetListeners = useListeners({
      ['update:modelValue']: handleInput,
      clear: handleClear,
      open: openDropdown,
      keydown: handleKeydown,
      focus: handleFocus,
      blur: handleBlur,
      'click-prefix': handlePrefixClick,
      'click-suffix': handleSuffixClick,
    })


    return {
      prefix,
      dropdownVisible,
      minWidth,
      pickerRef,
      renderedRef,
      dataOptions,
      myAttrs,
      resetListeners,
      m_size,
      m_disabled,
      handleClickoutside,
      handleClear,
      handleValueChange,
      handlePrefixClick,
      handleSuffixClick,
      handleInput,
      handleQuery,
      openDropdown,
      toggleDropdown,
      handleKeydown,
      handleFocus,
      handleBlur,
      focus,
      blur,
      toggle,
    }
  },
  methods: {
    renderInput() {
      const props = toProps({
        ...this.$props,
        size: this.m_size,
        disabled: this.m_disabled,
        formNoValidate: true,
      })
      const slots = getAllScopedSlots(this)

      return <mtd-input
        ref="renderedRef"
        {...props}
        {...vSlots(slots)}
        {...this.resetListeners}
        {...this.myAttrs}
        v-slots={slots}
      />
    },
  },
  render() {
    const {
      prefix, dataOptions, dropdownVisible, minWidth,
    } = this

    const props = toProps({
      ...this.$attrs,
      ...this.$props,
      options: dataOptions,
      prefix,
      showFilterInput: false,
      visible: dropdownVisible && (!!getSlotsInRender(this) || dataOptions.length > 0),
      filterable: typeof this.$props.filterMethod === 'function',
      panelStyle: {
        minWidth,
      },
      from: 'autocomplete',
    })

    let slots: Record<string, any> = {
      rendered: this.renderInput,
    }

    if (getSlotsInRender(this)) {
      slots.default = () => getSlotsInRender(this)
    }

    slots = normalizeSlots(this, slots)

    return <mtd-picker
      {...props}
      v-slots={slots}
      {...vSlots(slots)}
      ref="pickerRef"
      on={{
        clickoutside: this.handleClickoutside,
        ['update:modelValue']: this.handleValueChange,
      }}
    />
  },
})

```

#### demo.md

```markdown
# 选择器 / AutoComplete

## 补充 Demo

### form-no-validate

:::include(src="./doc/demo/no-validate.vue")
:::

### Methods

:::include(src="./doc/demo/methods.vue")
:::

```

#### doc/base.vue

```vue
<template>
  <mtd-autocomplete :data="data" v-model="autoValue" placeholder="请输入"
    @search="handleSearch" @select="handleSelect" />
</template>
<script>
export default {
  data () {
    return {
      data: ['a','b'],
      autoValue: '',
    };
  },
  methods: {
    handleSearch (value) {
      console.log(value)
      this.data = value.split('').map((v, index) => {
        return value.slice(0, index + 1);
      });
    },
    handleSelect (value) {
      console.log('select', value);
    },
  },
};
</script>

```

#### doc/custom.vue

```vue
<template>
  <mtd-autocomplete v-model="autoValue"
    @search="handleSearch"
    placeholder="请输入">
    <mtd-option v-for="(item, i) in options"
      :key="i"
      :label="item.label"
      :value="item.value" />
  </mtd-autocomplete>
</template>
<script>
export default {
  data () {
    return {
      options: [],
      autoValue: '',
    };
  },
  methods: {
    handleSearch (value) {
      this.options = value.split('').map((v, index) => {
        return {
          label: value.slice(0, index + 1),
          value: value.slice(0, index + 1),
        };
      });
    },
  },
};
</script>

```

#### doc/customOption.vue

```vue
<template>
  <mtd-autocomplete  v-model="autoValue" placeholder="请输入"
    @search="handleSearch" @select="handleSelect" >
      <mtd-option v-for="email in data" 
      :key="email" 
      :label="email"
      :value="email" >
          {{ email }}
      </mtd-option>
    </mtd-autocomplete>
</template>
<script>
export default {
  data () {
    return {
      data: [],
      autoValue: '',
    };
  },
  methods: {
    handleSearch(value) {
      let result;
      if (!value || value.indexOf('@') >= 0) {
        result = [];
      } else {
        result = ['gmail.com', '163.com', 'qq.com'].map(domain => `${value}@${domain}`);
      }
      this.data = result;
    },
    handleSelect (value) {
      console.log('select', value);
    },
  },
};
</script>

```

#### doc/demo/methods.vue

```vue
<template>
  <div>
    <div>
      <mtd-button @click="focus">focus,1s后调用blur</mtd-button>
      <mtd-button @click="toggle">点击后，1s自动关闭</mtd-button>
    </div>

    <mtd-autocomplete ref="select" :data="data" style="width: 200px" @update:visible="visible" @blur="handleblur">
    </mtd-autocomplete>
  </div>
</template>
<script>
export default {
  data() {
    return {
      data: ['a', 'b'],
      value: '',
    };
  },
  methods: {
    focus() {
      this.$refs.select.focus()
      setTimeout(() => {
        this.blur()
      }, 1000)
    },
    blur() {
      this.$refs.select.blur()
    },
    toggle() {
      this.$refs.select.toggle()
      setTimeout(() => {
        this.$refs.select.toggle()
      }, 1000)
    },
    visible(v) {
      console.log('visible', v)
    },
    handleblur(e){
      console.log('blur',e)
    }
  }
};
</script>

```

#### doc/demo/no-validate.vue

```vue
<template>
  <div>
    <p>设置form-no-validate</p>
    <div>
      <mtd-radio-group v-model="validate">
        <mtd-radio :value="true">true</mtd-radio>
        <mtd-radio :value="false">false</mtd-radio>
      </mtd-radio-group>
    </div>
    <div>
      <mtd-form :model="formCustom" :rules="ruleCustom">
        <mtd-form-item label="选择器，change时触发校验" prop="select" :label-width="200">
          <mtd-autocomplete v-model="formCustom.select" :data="options" :form-no-validate="validate" />
        </mtd-form-item>
        <mtd-form-item label="选择器，blur时触发校验" prop="select2" :label-width="200">
          <mtd-autocomplete v-model="formCustom.select2" :data="options" :form-no-validate="validate" />
        </mtd-form-item>
      </mtd-form>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      validate: false,
      formCustom: {
        select: '',
        select2: '',
      },
      ruleCustom: {
        select: { validator: () => { console.log('change'); return false }, trigger: 'change', message: 'change校验失败' },
        select2: { validator: () => { console.log('blur'); return false }, trigger: 'blur', message: 'blur校验失败' },
      },
      options: ['a', 'b'],
    }
  },
  methods: {
    handleblur(e) {
      console.log('blur', e)
    }
  }
}
</script>

```

#### doc/filter.vue

```vue
<template>
  <mtd-autocomplete
:data="options" v-model="autoValue" placeholder="请输入"
    :filter-method="filterMethod" />
</template>
<script>
export default {
  data () {
    return {
      options: ['mtd', 'meituan', 'mtdmtd'],
      autoValue: '',
    }
  },
  methods: {
    filterMethod (value, option) {
      if (!value) return true
      return value.slice(0, value.length) === option.slice(0, value.length)
    },
  },
}
</script>
```

#### doc/remote.vue

```vue
<template>
  <mtd-autocomplete :data="options"
    v-model="value" placeholder="请输入"
    @search="handleSearch" />
</template>
<script>
export default {
  data () {
    return {
      options: [],
      value: '',
    };
  },
  methods: {
    handleSearch (query) {
      clearTimeout(this.timer);
      if (!query) {
        this.options = [];
      } else {
        this.timer = setTimeout(() => {
          this.options = query.split('').map((v, index) => {
            return query.slice(0, index + 1);
          });
        }, 1000);
      }
    },
  },
};
</script>

```

#### doc/super-custom.vue

```vue
<template>
  <mtd-autocomplete v-model="autoValue" placeholder="请输入">
    <div class="mtd-demo-autocomplete-super"
      v-for="(item, i) in options" :key="i">
      <div class="mtd-demo-autocomplete-super-title">
        <span class="mtd-progress-success">选项</span>
        <span class="mtd-demo-autocomplete-super-more">更多</span>
      </div>
      <mtd-option :key="i"
        :value="item.content">
        <span>{{ item.content }}</span>
        <span class="mtd-demo-autocomplete-super-count"> {{ item.count }} </span>
      </mtd-option>
    </div>
  </mtd-autocomplete>
</template>
<script>
export default {
  data () {
    return {
      options: [{
        content: 'mtd',
        count: 1,
      }, {
        content: 'meituan',
        count: 2,
      }, {
        content: 'autocomplete',
        count: 3,
      }],
      autoValue: '',
    };
  },
};
</script>

<style lang="scss">
.mtd-demo-autocomplete-super {
  &-title {
    padding: 5px 10px 0 10px;
  }
  &-more {
    float: right;
  }
  &-count {
    float: right;
    color: #ccc;
  }
  border-bottom: 1px solid #f5f6f7;
}
</style>

```

#### index.md

```markdown
# 自动完成 / Autocomplete

## 基础样式
通过 `data` 属性设置自动完成的数据源。
:::include(src="doc/base.vue") 通过 data 设置自动完成的数据源。
:::

## 类型与用法

### 自定义选项
除了使用 `data`，还可以直接传入 `Option` 组件作为 `slot`
:::include(src="./doc/custom.vue")
:::

:::include(src="./doc/customOption.vue")
:::

完全自定义 Option，显示复杂的布局。
:::include(src="./doc/super-custom.vue")
:::

过滤函数，配合`data`属性使用
:::include(src="./doc/filter.vue")
:::

### 远程提示
:::include(src="./doc/remote.vue")
:::

## API
<api-doc name="Autocomplete" :doc="require('./api.json')"></api-doc>
```

#### index.ts

```typescript
import Autocomplete from './autocomplete'
import { withInstall } from '@ss/mtd-adapter'
export default withInstall(Autocomplete)

```

#### option-wrap.tsx

```tsx
import { computed, defineComponent, markRaw, getSlotsInRender } from '@ss/mtd-adapter'
import { useConfig } from '@components/config-provider'
export default defineComponent({
  setup() {
    const config = useConfig()
    const prefix = computed(() => config.getPrefixCls('autocomplete-option-wrap'))
    const children = markRaw([]) as any[] | undefined
    return {
      prefix, children,
    }
  },
  updated() {
    this.$emit('updated', this.children)
  },
  unmounted() {
    this.$emit('updated', [])
  },
  render() {
    const children = getSlotsInRender(this)
    this.children = children
    return <div class={this.prefix}>{children}</div>
  },
})

```

