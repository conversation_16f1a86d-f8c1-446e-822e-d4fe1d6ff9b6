# compatibility

## 兼容性

### 浏览器兼容性
现代浏览器 & (IE 版本待 Vue 3.0 支持)

**对于 IE 系列浏览器，需要提供相应的 Polyfill 支持，建议使用 [babel-preset-env](https://babeljs.io/docs/en/next/babel-preset-env.html) 来解决浏览器兼容问题。**

主要 API 列表:
- [Array.prototype.every](https://developer.mozilla.org/zh-CN/docs/Web/JavaScript/Reference/Global_Objects/Array/every)
- [Array.prototype.find](https://developer.mozilla.org/zh-CN/docs/Web/JavaScript/Reference/Global_Objects/Array/find)
- [Array.prototype.findIndex](https://developer.mozilla.org/zh-CN/docs/Web/JavaScript/Reference/Global_Objects/Array/findIndex)
- [Array.prototype.reduce](https://developer.mozilla.org/zh-CN/docs/Web/JavaScript/Reference/Global_Objects/Array/reduce)
- [Array.prototype.includes](https://developer.mozilla.org/zh-CN/docs/Web/JavaScript/Reference/Global_Objects/Array/includes)  <small>备注: 0.3.2 版本起移除此 API 使用</small>
- [Array.prototype.some](https://developer.mozilla.org/zh-CN/docs/Web/JavaScript/Reference/Global_Objects/Array/some)


### Vue 版本兼容
Vue 2.5.0+
主要 API 列表:
- [provide / inject](https://cn.vuejs.org/v2/api/#provide-inject) <small>备注: 由于需要支持可选项，故最低版本在 2.5.0+</small>
- [inheritAttrs](https://cn.vuejs.org/v2/api/#inheritAttrs)
- [$scopedSlots](https://cn.vuejs.org/v2/api/#vm-scopedSlots)
- [$listeners](https://cn.vuejs.org/v2/api/#vm-listeners)
- [$attrs](https://cn.vuejs.org/v2/api/#vm-attrs)

# 贡献指南
内网 [仓库地址](http://git.sankuai.com/v1/bj/projects/ss/repos/mtd-vue/browse)

## Pull Request 规范
- fork 一份到自己的项目下，不要直接在仓库下建分支
- PR 信息以 [组件名]: [描述信息] 的形式填写，例如 Button: fix xxx bug。
- 编写组件前请先同至少两名维护成员沟通组件属性，确定属性后请先将确定的属性写入 index.md 后编写单元测试 （具体开发流程见下文）
- 如果是修复 bug 请在 commit 中给出描述信息 或 issue 地址
- 确保所有的测试都是通过的 npm run test
- 确保你的代码跑过 lint 检查 npm run lint ( lint 会在 git commit 时自动运行 )
- 合并代码必须要经过 review

## 分支管理
- 推荐使用以下命名来管理分支：
- 功能开发以 `feature/功能名` 命名
- 组件开发以 `components/组件名` 命名
- 修复 bug 以  `bugfix/bug(bugId或简短名)` 命名
- 紧急修复以 `hotfix/bug` 命名。

## 开发环境依赖
node 8+, npm 5+, 由于使用 package-lock.json 请使用 `npm install` 或者 `mnpm install` 安装依赖
查看 demo
```
npm start
```
打包代码
```
npm run build
```

## 组件开发
使用命令快速创建组件 ( 执行前请先进行 `npm install` )
```
npm run create <componentName> [chineseName]
```

## 组件开发流程
- 确定属性拆分，各组件的作用
- 确定组件属性、事件、插槽等
- 编写 demo 文档，文档应该包含: 设计场景实现的伪代码、相关组件属性、事件、插槽的定义，至少2名维护人员共同 review 文档， review 应该关注以下几点：
  - 场景用例是不是典型，是否覆盖到设计稿中的所有场景
  - 场景实现是不是合理
  - 属性、事件定义是不是合理，能不能清楚的描述出属性的作用
  - 与其他组件库不同的点，说明不同的原因
- 编写单元测试（先编写测试用例 review，后完善具体测试）并提交到各自仓库，相关人员 review 单元测试，review 应该关注以下几点：
  - 测试用例完整性，是否覆盖住所有对外属性
  - 测试用例的合理性，是否有漏掉某些属性场景
  - 测试用例的正确性，是否能够通过对比得出验证测试用例正确
- 开发编写 组件功能 + 样式
  - 提交组件 PR 到 develop，提交信息命名以 [组件名]: [描述信息/功能] 的形式填写
- 设计 review 交互、功能样式，开发人员 review 代码质量，通过后合并

# CSS 规范
- 命名规范：
  类似 BEM 命名规则使用 [namespace]-[componentName]-[local] 的写法，其中 componentName，local 如果是驼峰的话，也改为使用 - 连接

- **组件内所有 class 命名请遵守，包括内部组件**
- 状态类直接加形容词，不需要 is
  ```sass
  // 不推荐
  .button-is-disable{
  }
  // 推荐
  .button-disabled{
  }
  ```
- 类名应该语义化
  ```sass
  // 不推荐
  .button-50{
  }
  // 推荐
  .button-lg{
  }
  ```
- 尽量少的使用 标签选择器
- 尽可能使用缩写，但是应该注意某些缩写可能引起的问题
  ```
  // 不推荐
  .button{
    padding-bottom: 20px;
    padding-left: 10px;
    padding-right: 10px;
    padding-top: 0;

    font-family: palatino, georgia, serif;
    font-size: 100%;
    line-height: 1.6;
  }
  // 推荐
  .button{
    padding: 0 10px 20px;
    font: 100%/1.6 palatino, georgia, serif;
  }
  ```
- color 应该使用小写 16进制，尽可能的使用缩写，可以使用 rgba，但是不能使用 rgb
  ```
  color: #fff;
  color: #c3c3c3;
  ```
- 避免使用 will-change 和 @media，如果 需要使用 @media 则需要将 @media 与相关样式放在一起
  ```
  // 不推荐
  .button{

  }
  @media screen {
    .button{}
  }

  // 推荐
  .button{
    @media screen{

    }
  }
  ```
- 使用公共 reset 消除浏览器差异及默认样式
- 绝对不要使用 important
- font-weight 属性必须使用使用相关变量表示
  大致对应如下:  [参考](https://aotu.io/notes/2016/11/08/css3fontweight/index.html)
  ```
  100 - Thin
  200 - Extra Light (Ultra Light)
  300 - Light
  400 - Regular (Normal、Book、Roman)
  500 - Medium
  600 - Semi Bold (Demi Bold)
  700 - Bold
  800 - Extra Bold (Ultra Bold)
  900 - Black (Heavy)
  ```
  其对应 `sass` 变量如下:
  ```sass
  $font-weight-regular: 400!default;
  $font-weight-medium: 500!default;
  $font-weight-semi-bold: 600!default;
  ```
- 有级联关系的变量应该引用自基础变量
  ```sass
  $control-height: 30px!default;
  $control-lg-height: 40px!default;

  $button-height: $control-height!default;
  $button-lg-height: $control-lg-height!default;

  $input-height: $control-height!default;
  $input-lg-height: $control-lg-height!default;
  ```
- 组件变量中，组件类型应该直接接在组件名后面，程度描述应该在具体属性之前，默认程度则不需要带上程度信息：
  ```sass
  // [componentName]-<type>-<describe>-[css]
  $btn-height: 30px!default;
  $btn-large-height: 40px!default;
  $btn-small-height: 28px!default;

  // primary button
  $btn-primary-color: #fff!default;
  $btn-primary-disabled-color: #eee!default;
  ```
- 全局范围内公共变量（不属于某一个单个组件的变量），程度描述应该在具体属性之后，默认程度则需要带上 normal：
  ```sass
  // [css]-[describe]
  $font-size-normal: 14px!default;
  $font-size-small: 12px!default;
  $font-size-large: 16px!default;
  ```
- `z-index` 需要有相关规范，并且书写在一起，组件对外输出时，需要明确告知 `z-index`
- 不允许使用 margin-top，如果有上边距需求使用 padding
- 具体样式中不允许直接出现具体颜色，颜色应该都是变量
- 不允许出现魔数，数字要么是设计给的变量，要么是计算得出，需要体现出过程
- 枚举类型样式应该使用 mixin 生成
- 同一主题、大小、类型下样式应该在一起
  ```
  // 推荐
  $tabs-small-size: 12px!default;
  $tabs-small-min-width: 56px;

  $tabs-normal-size: 14px!default;
  $tabs-normal-min-width: 66px;

  $tabs-large-size: 16px!default;
  $tabs-large-min-width: 80px;

  // 不推荐
  $tabs-small-size: 12px!default;
  $tabs-normal-size: 14px!default;
  $tabs-large-size: 16px!default;
  $tabs-small-min-width: 56px;
  $tabs-normal-min-width: 66px;
  $tabs-large-min-width: 80px;
  ```
- `box-shadow` 应该整体为变量，而不只是颜色
  ```sass
  // 推荐
  $dropdown-box-shadow: 0 0 4px 0 rgba(27, 51, 115, 0.35)!default;
  // 不推荐
  $dropdown-box-shadow-color: rgba(27, 51, 115, 0.35)!default;
  ```

# 自定义主题
`theme-chalk` 使用 `scss` 编写。你可以通过 `scss` 变量来实现自定义主题，或者使用覆盖样式达到自定义效果，**不推荐直接 fork 组件样式源文件**

<!-- ## 使用定制主题工具 (待工具支持)：
进入[主题定制工具官网](https://mtdui.sankuai.com/theme#/sites)，具体步骤：新建站点 => 进入详情 => 主题管理 => 配置主题 => 发布。

发布成功后大象通知会给你返回一个主题包名@bfe/mtd-theme-xx，安装包，然后在项目入口文件引入自定义的主题样式。
```
  mnpm i @bfe/mtd-theme-xx; // 安装主题包
  import '@bfe/mtd-theme-xx/index.css'; // 入口文件引入样式
``` -->

## 在项目中修改 `scss` 变量
**请尽量只使用基础变量，组件级别变量正在梳理中**

你可以新建一个样式文件，例如 `custom-theme.scss` 写入想要更改的变量，然后引入 `theme-chalk` 样式，如:
```
/* 改变 button 默认圆角大小 */
$button-radius: 2px;

/* 必须要改变字体路径 */
$icon-font-path: '~@ss/mtd-vue3/src/theme-chalk/fonts';
@import '@ss/mtd-vue3/src/theme-chalk/index.scss';
```
**覆盖字体路径变量是必需的，将其赋值为 mtd-vue 中 icon 图标所在的相对路径即可。**

之后，在项目中，引入以上样式文件即可：
```
import Vue from 'vue'
import MTD from '@ss/mtd-vue3'
import './custom-theme.scss'

Vue.use(MTD)
```
### 按需加载实现自定义主题
按需加载实现自定义主题也是使用 `scss` 变量覆盖的方式，不同的是当我们手动实现按需加载时能够灵活的指定组件样式的路径如:
```
// file: custom-button.scss
$button-radius: 10px;

/* 必须要改变字体路径 */
$icon-font-path: '~@ss/mtd-vue3/src/theme-chalk/fonts';
@import '@ss/mtd-vue3/src/theme-chalk/button.scss';

// entry.js
import Button from '@ss/mtd-vue3/lib/button'
import './custom-button.scss'

```
如果使用 [babel-plugin-component](https://github.com/ElementUI/babel-plugin-component) 插件实现按需的话，只需要修改插件 `styleLibraryName` 的配置:
```
{
  "plugins": [
    [
      "component",
      {
        "libraryName": "@ss/mtd-vue3",
        "styleLibraryName": "theme-chalk"
      }
    ]
  ]
}
```

# development

## 目录结构
```
├── build # 编译配置及脚本
├── config # 配置
├── examples # demo 项目
├── components # 组件源码
├── src # 公共模块源码
├── test # 测试相关配置及结果
├── wiki # 文档
├── components.json # 组件定义
└── package.json
```
## 单元测试
```
npm run unit:watch
```

## 本地开发
先 `fork` 然后 `git clone`
```
npm init
npm start
```
之后访问 [http://localhost:8081](http://localhost:8081)

## 语法检查
```
npm run lint
```

# 如何使用
## NPM 安装
使用 mnpm 的方式安装。
```
mnpm i @ss/mtd-vue3
```

## 全局引入

```js

import { createApp } from 'vue';
import MTD from '@ss/mtd-vue3';
import App from './App.vue';

// 引入样式
import '@ss/mtd-vue3/lib/theme-chalk/index.css'
const app = createApp(App);

app
  .use(MTD) // 引入MTD
  .mount('#app');

```
## 按需引入

```js
import { createApp } from 'vue';
import { Button } from '@ss/mtd-vue3';
import App from './App.vue';

const app = createApp(App);

// 引入button样式
import '@ss/mtd-vue3/lib/theme-chalk/button.css'
// 局部引入Button
app.component('MtdButton', Button);

app.mount('#app');

```


## 切换主题

如果我们期望使用别的主题，有以下的方式切换——我们以美团UI黄主题为例：


### 一般情况
```js
// 将你代码中的css路径切换主题命名前缀即可
// theme-chalk ---> theme-yellow

import '@ss/mtd-vue3/lib/theme-yellow/index.css' // 全局引入样式

import '@ss/mtd-vue3/lib/theme-yellow/button.css' // 按需引入样式
```


### 全局化配置方式引入了组件库
[接入方式](https://km.sankuai.com/collabpage/1623493022#id-%E4%B8%89%E3%80%81%E3%80%90%E5%85%B1%E5%AD%98%E6%B4%BE%E3%80%91%E5%A6%82%E6%9E%9C%E4%BD%A0%E5%B7%B2%E7%BB%8F%E6%9C%89%E7%BB%84%E4%BB%B6%E5%BA%93%EF%BC%8C%E4%BD%86%E6%98%AF%E5%90%8C%E6%97%B6%E6%83%B3%E5%BC%95%E5%85%A5%20%E7%BE%8E%E5%9B%A2UI%20%E7%BB%84%E4%BB%B6%E5%BA%93%E9%83%A8%E5%88%86%E7%BB%84%E4%BB%B6)

修改scss变量的环节中，将主题前缀进行替换

```scss

// 将你代码中的css路径切换主题命名前缀即可
// theme-chalk ---> theme-yellow

$prefix: 'mtdu'; // 修改统一样式前缀
$icon-prefix: 'mtduicon';
$icon-font-family: 'mtduicon'; // 修改图标的 font-family 属性名
$icon-font-path: '~@ss/mtd-vue3/src/theme-yellow/fonts'; // ⚠️⚠️⚠️必填必填必填必填必填必填必填！！！！！！！！！！！！！！！！
@import '@ss/mtd-vue3/src/theme-yellow/index.scss'

```

## 使用ts类型
需要利用InstanceType来获取

```ts

import { Form } from '@ss/mtd-vue2'
type FormRef = InstanceType<typeof Form>

(this!.$refs.form as FormRef).clearValidate()

```

# 国际化
MTD 组件 默认 使用中文，如果你希望使用其他语言，你可以参考下面的方案。

## 全局配置
使用 MTD.config 的方式进行全局配置。
```
import MTD from '@ss/mtd-vue3'

import en from '@ss/mtd-vue3/es/locale/lang/en'

MTD.config({
  locale: en,
});
```

## ConfigProvider
使用 [ConfigProvider](/components/config-provider) 组件进行全局配置。

```ts

<template>
  <mtd-config-provider :locale="locale">
    <app />
  </mtd-config-provider>
</template>

<script>
import { defineComponent } from 'vue'
import en from '@ss/mtd-vue3/es/locale/lang/en'

export default defineComponent({
  setup() {
    return {
      locale: en,
    }
  },
})
</script>

```

## 支持的语言

- 简体中文（zh-cn）
- 繁体中文（zh-tw）
- 英语（en）
- 韩语（ko）
- 日语（ja）
- 泰语（th）

# JavaScript 规范
## Lint
必须跑过 lint，具体参照 .eslintrc.js ，部分规则如下:
- 2 空格缩进，带分号
- 变量及函数以驼峰命名
- 禁用 var 声明
- 优先使用 const 声明
- 组件的属性必须要给类型定义
- 组件以 template - script - style 顺序定义
- 组件内顺序以 name - props -data - computed - watch - methods (具体参照 vue/order-in-components)
- 必须要有 v-key 在 v-for 中
- 数组和对象键值对在多行模式必须带尾逗号，单行模式不能带尾逗号

## 命名
- `script`、`js` 中驼峰命名，文档、模板中以 `-` 连接

- 数组类型以复数形式命名
  `options` 对应为 `array` 类型,
  `option` 对应为 `object` 类型

- 统一命名规则
  - 动态组件统一使用 `tag` 属性来指定生成的标签类型
  - 状态对应属性应该为直接的状态名
    如: loading 状态对应属性名应该为 `loading`
    disabled 状态对应属性名应该为 `disabled`
  - 分子以上组件，内部出现多个组件有相同状态则以 `[组件名][状态名]` 驼峰形式对外暴露
  - 内部组件对外暴露 `class` 属性以 `[组件名]Class` 命名

- 内部组件命名也应该符合命名规范

- 内部组件属性透传命名建议以 `[组件名]Props` 名称命名
  例如: 导航组件使用 `logoProps` 属性对 `logo` 标签进行属性传递

- 内部响应事件函数应该以 `handle[EventName]` 命名, `update` 事件以 `update[PropName]` 命名，如果内部有多个相同的事件名，则以`handle[Element/ComponentName][EventnName]` 命名

- 子组件有更改属性需求时，对外发送 `update:PropName` 事件，并且将新值当做第一个参数
  例如:导航组件中会出现一个下拉菜单的需求，下拉显示与否应该只根据 `visible` 属性值而定，当用户点击菜单时，只对外抛出 `updata:visible` 事件，如果此事件并没有改变 `visible` 的值，则下拉依然不会显示。

- `slot` 命名应该明确表明该 `slot` 对应元素的意义
  例如: `<slot name='loading' />` 表明此插槽用来显示自定义的 `loading` 元素

## 属性、事件
- 样式相关枚举类型属性不做强校验、功能相关强校验

- 样式相关需求优先考虑 `css` 或 `scss` 变量方式，如果 `css`、`scss` 实现不了，则需要在 `js` 实现时添加注释说明原因

- 优先考虑支持 `v-model`、`.sync`
  表单类使用 `v-model`，更改属性时 `.sync`

- 上层组件需要支持内部组件属性透传
  例如: `navbar` 组件内包含 `logo` 元素，如果不对 `logo` 元素进行属性透传，外界将失去对 `logo` 的控制

- 组件作用对应某一个原生标签时，需要使用 `v-bind="$attrs"`
  例如: `mtd-input` 组件是对原生 `input` 组件的封装，内部实现时需要在 `input` 标签上使用 `v-bind="$attrs"`，将非 `mtd-input` 组件中定义的属性传递给原生 `input` 标签

- 组件内部不要出现魔数，如果确实有需求需要使用，必须添加注释，描述清楚数值的作用及来源，如果可能更改(该值可能出现自定义的需求)，则将其作为属性，默认值为当前值

- 组件避免出现不同属性控制相同功能
  例如: 分页组件: 可能会有 `total` (总数)、 `pageCount` (总页码数)、`pageSize` (每页页数)。组件内可根据 `pageCount` 属性生成页码，也可以根据 `1 + Math.ceil(total/pageSize)` 来计算出总页码，此时有 2 组属性可以确定总页码数，由于 `total` 与 `pageSize` 包含其他常用功能，故采用 `total`、`pageSize` 计算来确定总页数

- 组件弱控制，避免使用内部状态，控制权交与使用者
  例如: 导航组件的下拉显示状态，是由使用者控制，虽然内置 `click`、`hover` 触发方式，但是触发时只是对外发送 `update:visible` 事件，并未展示出下拉内容，内容的展示仅仅是根据 `visible` 属性的决定

  - 优先使用计算方式得出当前所需内部属性 (vue 中的 computed)
  例如: `tabs` 组件中，`tab` 组件会有 `active` 的状态，`active` 的状态应该由计算属性得出来，而不是通过 `watch` 来改变内部变量
  ```javascript
  // better
  get active () {
    return tabsValue === this.value
  }
  // not
  watch {
    tabsValue (n) {
      this.active = n === this.value
    }
  }
  ```
  - 避免出现内部属性的使用 (vue 的 data 函数， react 的 state)
  某些场景下依然会有需要内部属性的情况，例如: `InputNumber` 组件中需要当输入错误，失去焦点的时候还原成上一次成功的值。

- 内部事件需要讨论确定是否对外传递
  对外传递可能会带来组件升级上的成本，如果确定之后场景都会保留此事件则可以对外发送
  例如:  `tabs` 组件内有翻页功能，当点击翻页按钮时，按钮会往 `tabs` 组件内发送对应的事件，此事件除了 `tabs` 响应外， `tabs` 组件还需要对外抛出

- 在父子组件通信、属性方法定义时，当不需要方法的返回值时，原则上都应该使用事件的方式

- 组件应该支持常用的原生事件，原生事件第一个参数应该是 `event` 对象
  比如说 `button` 组件组件应该支持 `click` 事件，并且第一个参数是 `event`，对于组件不支持的原生事件，使用 `.native` 描述符

- 原生类型事件，其行为应该同原生事件
  例如: `compositionstart`、`compositionend` 事件，应该表现同原生，不应该对外发送 `change` 类型事件

- `change`、`update` 类型事件第一个参数是 `新值`,第二个参数是 `旧值`

- 事件、方法参数应该避免超过3个，且越常用的参数应该越靠前

- 方法中最后一个参数不应该是 bool 类型，应该将所有 bool 类型参数改为 object
  ```
  // 不推荐:
  function doSomthing (param, replace /* bool */)
  // 推荐:
  function doSomthing (param, { replace /* bool */ })
  ```

- 统一使用 `事件` 方式向父级通信，父级通过更改 `prop` 做出回应，插槽方式除外
  例如: `tabs` 组件内，子组件 `tab` 点击时，需要往父组件抛出 `click` 事件， `tabs` 组件根据事件响应更改 `tab` 的激活属性

## 样式
- template 中不要出现 `id` 属性
- 对内部组件提供 class 支持
- 内部所有样式需要符合命名规范
- 自定义样式需求优先考虑 `css` 覆盖、`scss` 变量覆盖方式实现
- 样式相关计算优先使用 scss 实现
- 对于可变元素使用 slot

## 注释
- `setTimeout`、`nextTick` 等异步方法需要说明原因
- 重要的 `if` 判断添加注释
- 样式相关计算需要添加注释说明原因及场景

## 测试
- 测试应该覆盖所有对外暴露的属性、事件
- 单一测试用例应该做到可变量唯一
- 测试文件名应该功能代码文件名，并且以 `.spec.js` 结尾
- 无法测试的点，请添加一个空的测试用例，并在里面添加注释说明原因

## 其他
- 复杂功能、样式应该单独拆分成小组件
- 原子组件应该满足单一原则
- 避免使用选择器来获取元素
- 避免深层次 `watch`
- 完整的组件应该包含：实现、测试、文档、 `ts` 的 `type` 定义、样式
- 表单类型组件应该包含 `name` 属性，并传递给原生标签
- 避免对全局作用域的影响
- 禁止扩展原生类型

# Vue 2.x 版本迁移
## 介绍
本指南主要是为有 mtd-vue 经验的用户提供的，他们希望了解在 Vue 3.0 中 mtd 的新功能和更改。

## 语法变动
### slot & scopedSlot
Vue3 不在区分 slot 和 scopedSlot，所以在 mtd-vue-next 中，也必须按照 Vue3 语法来进行传递，下面是一个简单的例子
```html
<template>
  <mtd-tooltip>
    <template v-slot:content>
      文本提示
    </template>
  </mtd-tooltip>
</template>
```

### .sync
Vue3 将不在支持 `.sync` 修饰符，用户可以使用 `v-model:属性名` 来替代原有的 `属性名.sync`，并且事件名由原有的 连接符命名(如：`update:expanded-keys`)更改为驼峰式命名 (如: `update:expandedKeys`)

**仅有 update 事件更改了命名规则，其他事件仍然是连接符命名如: `toggle-expand`**

### v-model
`v-model` 对应的事件名从 Vue 2.x 的 `input` 事件变更为 Vue 3.x `update:modelValue` 事件

## 重大改变
### Modal 组件
现在 `Modal` 组件在 **首次打开** 前都不会在挂载内部的 `children` 节点，这意味着以下的代码:
```html
<mtd-modal>
  <mtd-form ref="form">
    something other
  </mtd-form>
</mtd-modal>
```
在首次 `Modal` 组件显示之前，通过 `this.$refs.form` 是获取不到正确的组件对象的。有类似的场景请在调用实例方法前增加非空判断。如 `this.$refs.form && this.$refs.form.validator()`
> 在此处的场景中我们推荐使用 Modal 的 :destroy-on-close="true" 属性让弹框在关闭时销毁内部的组件，而非使用 ref

### Select、Picker 组件
现在 Select、Picker 组件在 **首次打开** 时不再会触发 remote-method 方法，参考以下代码:
```html
<mtd-select filterable remote :remote-method="handler">
  <mtd-option v-for="opt in options" :key="opt.key" :value="opt.vlaue" :label="opt.label"></mtd-option>
</mtd-select>
<script>
  export default {
    data () {
      return { options: [] };
    },
    methods: {
      handler (query) {
        // do something
      }
    }
  }
</script>
```
如果有类似的场景，可以响应 `update:visible` 事件来实现之前的逻辑。
*注:  `remote-method` 的参数仍然可能是空字符串的情况*

### Tabs 组件
正如 0.x 版本中的提示，现在 `tabs` 不会在进行自动的激活匹配，这意味的以下下代码将不在显示激活状态:
```html
<mtd-tabs>
  <mtd-tab-pane label="Tab1" value="Tab1">
    示例内容: <br />
    明月几时有？把酒问青天。不知天上宫阙，今夕是何年。
  </mtd-tab-pane>
  <mtd-tab-pane label="Tab2" value="Tab2">
    示例内容: <br />
    我欲乘风归去，又恐琼楼玉宇，高处不胜寒。起舞弄清影，何似在人间！
  </mtd-tab-pane>
  <mtd-tab-pane label="Tab3" value="Tab3">
    示例内容: <br />
    转朱阁，低绮户，照无眠。 不应有恨，何事长向别时圆？
  </mtd-tab-pane>
  <mtd-tab-pane label="Tab4" value="Tab4" disabled>
    示例内容: <br />
    人有悲欢离合，月有阴晴圆缺，此事古难全。但愿人长久，千里共婵娟。
  </mtd-tab-pane>
</mtd-tabs>
```
*注: 上述代码缺少 `modelValue` 属性或者 `v-model` 配置*，且当没有正确匹配项时也不会在主动的激活第一项 `tab`

### TimePicker 组件
- 移除了 `element-id` 属性
- 不会在自动的进行类型推断，参考以下代码:
```html
<mtd-time-picker v-model="value"></mtd-time-picker>
<script>
  export default {
    data () {
      return {
        value: '',
      };
    },
  };
</script>
```
在 `@ss/mtd-vue@0.x` 版本中将会输出 `string` 类型的结果，但是在 `@ss/mtd-vue-next@3.x` 版本中默认将总是返回 `Date` 类型的结果，用户必须配置 `value-format` 属性来得到想要的格式类型如:
```html
<mtd-time-picker v-model="value" :value-format="HH:mm:ss"></mtd-time-picker>
```

### DatePicker 组件
同 `TimePicker` 组件，现在将不会自动推断 `modelValue` 值类型，用户可以配置 `value-format` 来得到想要的数据类型，参考 [示例](/components/date-picker#日期格式)

### Table 组件
- 移除了 `disableMouseEvent` 属性
- 事件名 `selection-change` 变更为 `update:selection` 参数与原有保持一致
- 多级表头语法变动，现在多级表头必须使用 `TableColumn` 组件的 `v-slot:group` 来实现，具体可查看 [示例](/components/table#多级表头)

### TableColumn 组件
- 移除了 `renderHeader` 属性，类似需求可使用 `v-slot:header`, 参考 [示例](/components/table#自定义表头)

### Tooltip 组件
移除了 dom `mtd-tooltip-rel` 节点，例如以下代码:
```html
<mtd-tooltip content="文字内容" default-visible placement="top">
  <span style="cursor: pointer;">示例</span>
</mtd-tooltip>
```
在 `@ss/mtd-vue@0.x` 版本中将生成如下的 dom 结构:
```html
<span class="mtd-tooltip-rel"><span style="cursor: pointer;">示例</span></span>
```
而在 `@ss/mtd-vue-next` 版本中则将 `mtd-tooltip-rel` 节点移除，其 dom 结构如下:
```html
<span style="cursor: pointer;">示例</span>
```

### Popover 组件
同 `Tooltip` 组件类似，移除了 dom `mtd-popover-rel` 节点

### Dropdown 组件
同 `Tooltip` 组件类似，移除了 dom `mtd-dropdown` 节点

### AnchorLink 组件
与 Vue Router 不同，现在 AnchorLink 组件接受的 `href` 属性仍然是必须 `encode` 的地址，如:
```html
<mtd-anchor-link href="#%E5%9B%BA%E5%AE%9A%E6%A8%A1%E5%BC%8F" title="固定模式"></mtd-anchor-link>
```

### ColorPicker 组件
在 `@ss/mtd-vue-next` 中我们移除了 `ColorPicker` 组件，后续我们将以独立的 `ColorPicker` 组件包的方式提供该组件的支持

# popper-options

<script>
export default {
  data () {
    return {
      value1: [],
    };
  },
};

</script>
# 常见下拉框配置说明
mtd-vue 默认情况下会使用以下配置
```
popperOptions = {
  computeStyle: {
    gpuAcceleration: false,
  },
  preventOverflow: {
    boundariesElement: 'window',
    priority: ['left', 'right', 'bottom'],
  },
  hide: {
    enabled: false,
  },
}
```
来构造下拉框，从 0.3.2 版本开始可以使用 `popper-options` 属性来添加自己的配置，我们会将配置进行合并，其详细配置信息可参考 [popper modifiers](https://popper.js.org/docs/v1/#modifiers)。为方便用户使用，我们归纳出几种常见的场景配置:

**注：你可以通过改变浏览器窗口大小，滚动等方式来体验不同配置的区别**
<!-- **注：为了更直观的展示出各配置的不同，我们将示例中的边界区域判断元素改为 scrollParent 块。请使用时删除 `boundariesElement` 配置** -->

## 默认行为
:::demo
```html
<template>
<div id="demo-default">
  <mtd-date-picker type="datetimerange"
    v-model="value1"
    placeholder="选择时间"
    :popper-options="{
    }"
  />
</div>
<template>
```
:::

## 禁用自适应
禁用后下拉出现的位置将始终与 `placement` 属性配置保持一致

*注: 有可能会撑大容器元素*
:::demo
```html
<template>
<div id="demo-flip-disabled">
  <mtd-date-picker type="datetimerange"
    v-model="value1"
    placeholder="选择时间"
    :popper-options="{
      flip: { enabled: false },
    }"
  />
</div>
<template>
```
:::

## 允许分离
默认情况下下拉与输入框是紧挨着的，这种行为要求容器有足够的空间用于展示，当空间小时，可能会出现下拉框内容展示不全的情况。此时我们推荐使用此配置来解决由于空间不足而导致的下拉框展示内容不全问题。
:::demo
```html
<template>
<div id="demo-together-disabled">
  <mtd-date-picker type="datetimerange"
    v-model="value1"
    placeholder="选择时间"
    :append-to-container="false"
    :popper-options="{
      preventOverflow: {
        priority: ['left', 'right', 'bottom', 'top'],
      },
      keepTogether: { enabled: false },
    }"
  />
</div>
<template>
```
:::

<div style="height: 500px;">
<!-- 仅为了撑高页面 -->
</div>